#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断AI推理交易问题的脚本
解决：1. 有推理结果但没有产生订单
     2. 持仓数显示不准确
"""

import sqlite3
import json
from datetime import datetime, timedelta
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_ai_trades_table():
    """检查AI交易表的数据"""
    try:
        # 尝试多个可能的数据库路径
        db_paths = [
            'instance/matetrade4.db',
            'matetrade4.db',
            'trading_system.db',
            'instance/trading_system.db'
        ]

        conn = None
        for db_path in db_paths:
            try:
                conn = sqlite3.connect(db_path)
                print(f"✅ 连接到数据库: {db_path}")
                break
            except:
                continue

        if not conn:
            print("❌ 无法连接到任何数据库文件")
            return False
        cursor = conn.cursor()
        
        print("🔍 检查AI交易表...")
        
        # 检查表是否存在
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='ai_trades'
        """)
        
        if not cursor.fetchone():
            print("❌ ai_trades表不存在")
            return False
        
        # 检查表结构
        cursor.execute("PRAGMA table_info(ai_trades)")
        columns = cursor.fetchall()
        print(f"✅ ai_trades表存在，包含{len(columns)}个字段:")
        for col in columns:
            print(f"   - {col[1]} ({col[2]})")
        
        # 检查数据
        cursor.execute("SELECT COUNT(*) FROM ai_trades")
        total_count = cursor.fetchone()[0]
        print(f"📊 总交易记录数: {total_count}")
        
        cursor.execute("SELECT COUNT(*) FROM ai_trades WHERE status = 'open'")
        open_count = cursor.fetchone()[0]
        print(f"📊 开仓记录数: {open_count}")
        
        cursor.execute("SELECT COUNT(*) FROM ai_trades WHERE status = 'closed'")
        closed_count = cursor.fetchone()[0]
        print(f"📊 平仓记录数: {closed_count}")
        
        # 显示最近的交易记录
        cursor.execute("""
            SELECT id, symbol, action, lot_size, status, created_at 
            FROM ai_trades 
            ORDER BY created_at DESC 
            LIMIT 5
        """)
        recent_trades = cursor.fetchall()
        
        if recent_trades:
            print("\n📋 最近5条交易记录:")
            for trade in recent_trades:
                print(f"   {trade[0][:8]}... | {trade[1]} | {trade[2]} | {trade[3]} | {trade[4]} | {trade[5]}")
        else:
            print("📋 没有交易记录")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 检查AI交易表失败: {e}")
        return False

def check_auto_trading_sessions():
    """检查自动交易会话"""
    try:
        # 尝试多个可能的数据库路径
        db_paths = [
            'instance/matetrade4.db',
            'matetrade4.db',
            'trading_system.db',
            'instance/trading_system.db'
        ]

        conn = None
        for db_path in db_paths:
            try:
                conn = sqlite3.connect(db_path)
                break
            except:
                continue

        if not conn:
            print("❌ 无法连接到任何数据库文件")
            return False
        cursor = conn.cursor()
        
        print("\n🔍 检查自动交易会话...")
        
        # 检查表是否存在
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='ai_trading_sessions'
        """)
        
        if not cursor.fetchone():
            print("❌ ai_trading_sessions表不存在")
            return False
        
        # 检查活跃会话
        cursor.execute("SELECT COUNT(*) FROM ai_trading_sessions WHERE status = 'active'")
        active_sessions = cursor.fetchone()[0]
        print(f"📊 活跃交易会话数: {active_sessions}")
        
        # 显示最近的会话
        cursor.execute("""
            SELECT id, user_id, model_id, status, created_at, trading_config
            FROM ai_trading_sessions 
            ORDER BY created_at DESC 
            LIMIT 3
        """)
        recent_sessions = cursor.fetchall()
        
        if recent_sessions:
            print("\n📋 最近3个交易会话:")
            for session in recent_sessions:
                config = json.loads(session[5]) if session[5] else {}
                print(f"   {session[0][:8]}... | 用户{session[1]} | 模型{session[2][:8]}... | {session[3]} | {session[4]}")
                print(f"      配置: 最小置信度={config.get('min_confidence', 'N/A')}, 最大持仓={config.get('max_positions', 'N/A')}")
        else:
            print("📋 没有交易会话记录")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 检查自动交易会话失败: {e}")
        return False

def analyze_trading_conditions():
    """分析交易条件"""
    print("\n💡 交易条件分析")
    print("=" * 50)
    
    print("📋 推理结果: SELL 95% 置信度")
    print("📋 问题: 没有产生订单")
    print()
    
    print("🔍 可能的原因:")
    print("1. 自动交易未启动")
    print("   - 检查是否点击了'开始AI交易'按钮")
    print("   - 确认自动交易状态为'运行中'")
    print()
    
    print("2. 置信度配置问题")
    print("   - 检查最小置信度设置是否高于95%")
    print("   - 默认最小置信度通常为80%")
    print()
    
    print("3. 持仓数限制")
    print("   - 检查当前持仓数是否已达到最大持仓数")
    print("   - 默认最大持仓数为4")
    print()
    
    print("4. MT5连接问题")
    print("   - 检查MT5是否连接正常")
    print("   - 确认MT5终端正在运行")
    print()
    
    print("5. 交易时间限制")
    print("   - 检查当前时间是否在允许的交易时间段内")
    print("   - 16:00:00可能不在交易时间段内")

def provide_solutions():
    """提供解决方案"""
    print("\n🔧 解决方案")
    print("=" * 50)
    
    print("1. 检查自动交易状态:")
    print("   - 在模型推理页面查看'AI自动交易'按钮状态")
    print("   - 如果显示'开始AI交易'，点击启动")
    print("   - 如果显示'停止AI交易'，说明已启动")
    print()
    
    print("2. 检查交易配置:")
    print("   - 最小置信度设置不要超过95%")
    print("   - 建议设置为80%或更低")
    print("   - 最大持仓数建议设置为4-8个")
    print()
    
    print("3. 检查MT5连接:")
    print("   - 确保MT5终端正在运行")
    print("   - 在页面上方查看MT5连接状态")
    print("   - 如果连接异常，点击'重新连接'")
    print()
    
    print("4. 检查浏览器控制台:")
    print("   - 按F12打开开发者工具")
    print("   - 查看Console标签页是否有错误信息")
    print("   - 特别关注交易相关的错误")

def clean_invalid_positions():
    """清理无效的持仓记录"""
    try:
        # 尝试多个可能的数据库路径
        db_paths = [
            'instance/matetrade4.db',
            'matetrade4.db',
            'trading_system.db',
            'instance/trading_system.db'
        ]

        conn = None
        for db_path in db_paths:
            try:
                conn = sqlite3.connect(db_path)
                break
            except:
                continue

        if not conn:
            print("❌ 无法连接到任何数据库文件")
            return
        cursor = conn.cursor()
        
        print("\n🧹 清理无效持仓记录...")
        
        # 查找没有order_id的开仓记录
        cursor.execute("""
            SELECT COUNT(*) FROM ai_trades 
            WHERE status = 'open' AND (order_id IS NULL OR order_id = '')
        """)
        invalid_count = cursor.fetchone()[0]
        
        if invalid_count > 0:
            print(f"发现{invalid_count}条无效的开仓记录（没有order_id）")
            
            # 将这些记录标记为已关闭
            cursor.execute("""
                UPDATE ai_trades 
                SET status = 'closed', closed_at = ?, profit = 0
                WHERE status = 'open' AND (order_id IS NULL OR order_id = '')
            """, (datetime.now().isoformat(),))
            
            affected_rows = cursor.rowcount
            conn.commit()
            
            print(f"✅ 已清理{affected_rows}条无效记录")
        else:
            print("✅ 没有发现无效的持仓记录")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 清理无效持仓记录失败: {e}")

def main():
    """主函数"""
    print("🔧 AI推理交易问题诊断工具")
    print("=" * 80)
    
    print("📋 问题描述:")
    print("1. 推理结果正常（SELL 95%置信度）但没有产生订单")
    print("2. 持仓数显示1但实际没有持仓")
    print()
    
    # 检查数据库表
    check_ai_trades_table()
    check_auto_trading_sessions()
    
    # 分析问题
    analyze_trading_conditions()
    
    # 提供解决方案
    provide_solutions()
    
    # 清理无效数据
    clean_invalid_positions()
    
    print("\n📊 诊断完成")
    print("=" * 80)
    print("💡 建议按照上述解决方案逐一检查和修复问题")

if __name__ == "__main__":
    main()
