<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MT5账户资金检查工具</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .checker-container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status-card {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        .status-good { border-left: 4px solid #28a745; background: #f8fff9; }
        .status-warning { border-left: 4px solid #ffc107; background: #fffdf5; }
        .status-danger { border-left: 4px solid #dc3545; background: #fff5f5; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
        .warning { color: #ffc107; }
        .funds-display {
            font-size: 1.2em;
            font-weight: bold;
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
        }
        .funds-good { background: #d4edda; color: #155724; }
        .funds-warning { background: #fff3cd; color: #856404; }
        .funds-danger { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <h1>MT5账户资金检查工具</h1>
    <p class="text-muted">解决"资金不足：需要保证金$203.21，可用$0.00"问题</p>
    
    <div class="checker-container">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h3>账户状态检查</h3>
            <button class="btn btn-primary" onclick="checkAccountStatus()">
                <i class="fas fa-sync"></i>
                检查账户状态
            </button>
        </div>
        
        <div id="accountStatus">
            <div class="text-center text-muted py-4">
                <i class="fas fa-search fa-2x mb-2"></i>
                <p>点击"检查账户状态"开始诊断</p>
            </div>
        </div>
    </div>
    
    <div class="checker-container">
        <h3>保证金计算器</h3>
        <p class="text-muted">计算不同交易的保证金需求</p>
        
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label">交易品种</label>
                    <select class="form-select" id="symbolSelect">
                        <option value="XAUUSD">XAUUSD (黄金)</option>
                        <option value="EURUSD">EURUSD (欧美)</option>
                        <option value="GBPUSD">GBPUSD (镑美)</option>
                        <option value="USDJPY">USDJPY (美日)</option>
                        <option value="USDCHF">USDCHF (美瑞)</option>
                    </select>
                </div>
                <div class="mb-3">
                    <label class="form-label">交易手数</label>
                    <input type="number" class="form-control" id="volumeInput" 
                           value="0.01" min="0.01" max="10" step="0.01">
                </div>
                <button class="btn btn-outline-primary" onclick="calculateMargin()">
                    <i class="fas fa-calculator"></i>
                    计算保证金
                </button>
            </div>
            <div class="col-md-6">
                <div id="marginResult">
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-calculator fa-2x mb-2"></i>
                        <p>选择参数后点击计算</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="checker-container">
        <h3>常见问题解决方案</h3>
        
        <div class="accordion" id="solutionsAccordion">
            <div class="accordion-item">
                <h2 class="accordion-header">
                    <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#solution1">
                        💰 账户余额为零或不足
                    </button>
                </h2>
                <div id="solution1" class="accordion-collapse collapse show" data-bs-parent="#solutionsAccordion">
                    <div class="accordion-body">
                        <h6>解决方案：</h6>
                        <ol>
                            <li><strong>向账户充值</strong>
                                <ul>
                                    <li>登录经纪商网站或客户端</li>
                                    <li>选择入金/充值选项</li>
                                    <li>建议最低充值：$100-500</li>
                                </ul>
                            </li>
                            <li><strong>检查账户类型</strong>
                                <ul>
                                    <li>确认是否使用了正确的账户</li>
                                    <li>模拟账户通常有虚拟资金</li>
                                    <li>真实账户需要实际充值</li>
                                </ul>
                            </li>
                            <li><strong>联系客服</strong>
                                <ul>
                                    <li>如果充值后仍显示零余额</li>
                                    <li>可能是系统延迟或技术问题</li>
                                </ul>
                            </li>
                        </ol>
                    </div>
                </div>
            </div>
            
            <div class="accordion-item">
                <h2 class="accordion-header">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#solution2">
                        📊 可用保证金不足
                    </button>
                </h2>
                <div id="solution2" class="accordion-collapse collapse" data-bs-parent="#solutionsAccordion">
                    <div class="accordion-body">
                        <h6>解决方案：</h6>
                        <ol>
                            <li><strong>平掉部分持仓</strong>
                                <ul>
                                    <li>优先平掉亏损较大的持仓</li>
                                    <li>释放被占用的保证金</li>
                                    <li>保留盈利的持仓</li>
                                </ul>
                            </li>
                            <li><strong>降低交易手数</strong>
                                <ul>
                                    <li>从0.01手开始交易</li>
                                    <li>根据可用保证金调整手数</li>
                                    <li>避免过度杠杆</li>
                                </ul>
                            </li>
                            <li><strong>选择低保证金品种</strong>
                                <ul>
                                    <li>主要外汇对保证金需求较低</li>
                                    <li>避免黄金等高保证金品种</li>
                                    <li>查看品种规格了解保证金要求</li>
                                </ul>
                            </li>
                        </ol>
                    </div>
                </div>
            </div>
            
            <div class="accordion-item">
                <h2 class="accordion-header">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#solution3">
                        🔐 交易权限被禁用
                    </button>
                </h2>
                <div id="solution3" class="accordion-collapse collapse" data-bs-parent="#solutionsAccordion">
                    <div class="accordion-body">
                        <h6>解决方案：</h6>
                        <ol>
                            <li><strong>启用MT5自动交易</strong>
                                <ul>
                                    <li>工具 → 选项 → EA交易</li>
                                    <li>勾选"允许自动交易"</li>
                                    <li>勾选"允许DLL导入"</li>
                                </ul>
                            </li>
                            <li><strong>检查账户权限</strong>
                                <ul>
                                    <li>联系经纪商确认账户状态</li>
                                    <li>确认账户类型支持API交易</li>
                                    <li>检查是否需要签署额外协议</li>
                                </ul>
                            </li>
                            <li><strong>重启MT5客户端</strong>
                                <ul>
                                    <li>完全关闭MT5</li>
                                    <li>以管理员身份重新启动</li>
                                    <li>重新登录账户</li>
                                </ul>
                            </li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="checker-container">
        <h3>交易建议</h3>
        <div class="row">
            <div class="col-md-6">
                <div class="status-card status-good">
                    <h6><i class="fas fa-lightbulb text-success"></i> 资金管理建议</h6>
                    <ul class="mb-0">
                        <li>单笔交易风险不超过账户余额的2%</li>
                        <li>同时持仓不超过5个</li>
                        <li>从小手数开始交易（0.01手）</li>
                        <li>设置合理的止损止盈</li>
                    </ul>
                </div>
            </div>
            <div class="col-md-6">
                <div class="status-card status-warning">
                    <h6><i class="fas fa-exclamation-triangle text-warning"></i> 风险提示</h6>
                    <ul class="mb-0">
                        <li>外汇交易存在高风险</li>
                        <li>可能损失全部投资资金</li>
                        <li>建议先在模拟账户练习</li>
                        <li>不要投入无法承受损失的资金</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 检查账户状态
        function checkAccountStatus() {
            const statusDiv = document.getElementById('accountStatus');
            statusDiv.innerHTML = '<div class="text-center"><div class="spinner-border" role="status"></div><p class="mt-2">正在检查账户状态...</p></div>';
            
            fetch('/api/mt5/account-info')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayAccountStatus(data.account_info);
                    } else {
                        displayError(data.error);
                    }
                })
                .catch(error => {
                    displayError('请求失败: ' + error.message);
                });
        }
        
        // 显示账户状态
        function displayAccountStatus(accountInfo) {
            const statusDiv = document.getElementById('accountStatus');
            
            // 判断状态
            let statusClass = 'status-good';
            let statusIcon = 'fas fa-check-circle text-success';
            let statusText = '账户状态正常';
            
            if (accountInfo.balance <= 0) {
                statusClass = 'status-danger';
                statusIcon = 'fas fa-times-circle text-danger';
                statusText = '账户余额不足';
            } else if (accountInfo.margin_free <= 0) {
                statusClass = 'status-warning';
                statusIcon = 'fas fa-exclamation-triangle text-warning';
                statusText = '可用保证金不足';
            } else if (!accountInfo.trade_allowed) {
                statusClass = 'status-warning';
                statusIcon = 'fas fa-ban text-warning';
                statusText = '交易权限被禁用';
            }
            
            // 资金显示样式
            let fundsClass = 'funds-good';
            if (accountInfo.margin_free <= 0) {
                fundsClass = 'funds-danger';
            } else if (accountInfo.margin_free < 100) {
                fundsClass = 'funds-warning';
            }
            
            statusDiv.innerHTML = `
                <div class="status-card ${statusClass}">
                    <h5><i class="${statusIcon}"></i> ${statusText}</h5>
                    
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <h6>账户信息</h6>
                            <p><strong>账户号码:</strong> ${accountInfo.login}</p>
                            <p><strong>服务器:</strong> ${accountInfo.server}</p>
                            <p><strong>货币:</strong> ${accountInfo.currency}</p>
                            <p><strong>杠杆:</strong> 1:${accountInfo.leverage}</p>
                        </div>
                        <div class="col-md-6">
                            <h6>资金状况</h6>
                            <div class="funds-display ${fundsClass}">
                                余额: $${accountInfo.balance.toFixed(2)}
                            </div>
                            <div class="funds-display ${fundsClass}">
                                可用保证金: $${accountInfo.margin_free.toFixed(2)}
                            </div>
                            <p><strong>已用保证金:</strong> $${accountInfo.margin.toFixed(2)}</p>
                            <p><strong>保证金比例:</strong> ${accountInfo.margin_level.toFixed(2)}%</p>
                        </div>
                    </div>
                    
                    ${generateRecommendations(accountInfo)}
                </div>
            `;
        }
        
        // 生成建议
        function generateRecommendations(accountInfo) {
            let recommendations = '<div class="mt-3"><h6>建议:</h6><ul>';
            
            if (accountInfo.balance <= 0) {
                recommendations += '<li class="text-danger">请向账户充值</li>';
                recommendations += '<li>建议最低充值: $100-500</li>';
            } else if (accountInfo.margin_free <= 0) {
                recommendations += '<li class="text-warning">平掉部分持仓释放保证金</li>';
                recommendations += '<li>或向账户充值增加可用保证金</li>';
            } else if (accountInfo.margin_free < 100) {
                recommendations += '<li class="text-warning">可用保证金较少，建议小手数交易</li>';
                recommendations += '<li>建议交易手数: 0.01手</li>';
            } else {
                recommendations += '<li class="text-success">账户状态良好，可以正常交易</li>';
                const maxLots = Math.min(accountInfo.margin_free / 1000, 1.0);
                recommendations += `<li>建议最大手数: ${maxLots.toFixed(2)}手</li>`;
            }
            
            recommendations += '</ul></div>';
            return recommendations;
        }
        
        // 显示错误
        function displayError(error) {
            const statusDiv = document.getElementById('accountStatus');
            statusDiv.innerHTML = `
                <div class="status-card status-danger">
                    <h5><i class="fas fa-times-circle text-danger"></i> 检查失败</h5>
                    <p class="text-danger">${error}</p>
                    <div class="mt-3">
                        <h6>可能原因:</h6>
                        <ul>
                            <li>MT5客户端未启动或未连接</li>
                            <li>账户未登录</li>
                            <li>网络连接问题</li>
                            <li>服务器连接异常</li>
                        </ul>
                    </div>
                </div>
            `;
        }
        
        // 计算保证金
        function calculateMargin() {
            const symbol = document.getElementById('symbolSelect').value;
            const volume = parseFloat(document.getElementById('volumeInput').value);
            
            const resultDiv = document.getElementById('marginResult');
            resultDiv.innerHTML = '<div class="text-center"><div class="spinner-border spinner-border-sm" role="status"></div><p class="mt-2">计算中...</p></div>';
            
            fetch('/api/mt5/calculate-margin', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    symbol: symbol,
                    volume: volume,
                    order_type: 'buy'
                })
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayMarginResult(data, symbol, volume);
                    } else {
                        resultDiv.innerHTML = `<div class="alert alert-danger">计算失败: ${data.error}</div>`;
                    }
                })
                .catch(error => {
                    resultDiv.innerHTML = `<div class="alert alert-danger">请求失败: ${error.message}</div>`;
                });
        }
        
        // 显示保证金计算结果
        function displayMarginResult(data, symbol, volume) {
            const resultDiv = document.getElementById('marginResult');
            
            const canTrade = data.can_trade;
            const statusClass = canTrade ? 'alert-success' : 'alert-danger';
            const statusIcon = canTrade ? 'fas fa-check-circle' : 'fas fa-times-circle';
            const statusText = canTrade ? '可以交易' : '资金不足';
            
            resultDiv.innerHTML = `
                <div class="alert ${statusClass}">
                    <h6><i class="${statusIcon}"></i> ${statusText}</h6>
                    <p><strong>交易品种:</strong> ${symbol}</p>
                    <p><strong>交易手数:</strong> ${volume}</p>
                    <p><strong>所需保证金:</strong> $${data.margin_required.toFixed(2)}</p>
                    <p><strong>可用保证金:</strong> $${data.margin_available.toFixed(2)}</p>
                    
                    ${!canTrade ? `
                        <hr>
                        <p class="mb-0"><strong>建议:</strong></p>
                        <ul class="mb-0">
                            <li>降低交易手数至 ${data.max_volume.toFixed(2)} 手</li>
                            <li>或向账户充值 $${data.shortage.toFixed(2)}</li>
                        </ul>
                    ` : ''}
                </div>
            `;
        }
        
        // 页面加载时自动检查
        document.addEventListener('DOMContentLoaded', function() {
            console.log('MT5资金检查工具已加载');
        });
    </script>
</body>
</html>
