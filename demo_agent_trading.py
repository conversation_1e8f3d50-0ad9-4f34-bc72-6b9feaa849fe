#!/usr/bin/env python3
"""
智能体交易功能演示
展示完整的AI驱动交易流程
"""

import requests
import json
import time
from datetime import datetime

def demo_agent_trading():
    """演示智能体交易功能"""
    print("🤖 智能体交易系统演示")
    print("=" * 60)
    print("这是一个由外部AI大模型驱动的智能交易系统")
    print("系统将结合用户交易策略、AI分析和市场数据进行自动交易")
    print()
    
    base_url = "http://127.0.0.1:5000"
    session = requests.Session()
    
    # 登录
    print("🔐 登录系统...")
    login_data = {'username': 'admin', 'password': 'admin123'}
    response = session.post(f"{base_url}/login", data=login_data)
    
    if response.status_code != 200:
        print("❌ 登录失败")
        return
    
    print("✅ 登录成功")
    print()
    
    # 创建智能体交易策略
    print("📝 创建智能体交易策略...")
    print("用户输入交易心得和策略偏好...")
    
    user_strategy = """
我的交易理念和策略：

1. 交易风格：
   - 偏好中长线交易，持仓时间通常2-8小时
   - 重视趋势跟踪，不喜欢频繁交易
   - 风险控制优先，盈利其次

2. 技术分析偏好：
   - 主要使用移动平均线判断趋势
   - RSI指标辅助判断超买超卖
   - 支撑阻力位作为进出场参考
   - 当RSI < 30时考虑买入，RSI > 70时考虑卖出

3. 风险管理：
   - 单笔交易风险不超过账户的2%
   - 设置止损位，通常为入场价格的1.5-2%
   - 止盈目标为风险的2-3倍
   - 连续亏损3次后暂停交易

4. 货币对偏好：
   - 主要交易EUR/USD和GBP/USD
   - 偶尔交易USD/JPY
   - 避免交易波动过大的货币对

5. 交易时间：
   - 偏好欧洲和美国交易时段
   - 避免在重要经济数据发布时交易
   - 周五下午通常不开新仓

6. 心理因素：
   - 保持冷静，不因情绪影响决策
   - 严格执行交易计划
   - 定期回顾和优化策略
    """
    
    strategy_data = {
        'name': '智能体交易策略-演示',
        'description': '结合用户交易心得和AI分析的智能交易策略',
        'user_strategy': user_strategy,
        'risk_tolerance': 'moderate',
        'max_daily_trades': 5,
        'max_position_size': 1500.0,
        'trading_hours': '9:00-18:00',
        'trading_symbols': ['EURUSD', 'GBPUSD', 'USDJPY'],
        'ai_model_config': {
            'model': 'deepseek_v3',
            'temperature': 0.7
        }
    }
    
    response = session.post(f"{base_url}/api/agent-trading/strategies", 
                           headers={'Content-Type': 'application/json'},
                           data=json.dumps(strategy_data))
    
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            strategy_id = result['strategy_id']
            print(f"✅ 策略创建成功 (ID: {strategy_id})")
            print(f"   策略名称: {strategy_data['name']}")
            print(f"   风险等级: {strategy_data['risk_tolerance']}")
            print(f"   交易货币对: {', '.join(strategy_data['trading_symbols'])}")
            print(f"   AI模型: {strategy_data['ai_model_config']['model']}")
        else:
            print(f"❌ 策略创建失败: {result.get('error')}")
            return
    else:
        print(f"❌ 请求失败: {response.status_code}")
        return
    
    print()
    
    # 启动智能体交易
    print("🚀 启动智能体交易...")
    
    response = session.post(f"{base_url}/api/agent-trading/start",
                           headers={'Content-Type': 'application/json'},
                           data=json.dumps({'strategy_id': strategy_id}))
    
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            session_id = result['session_id']
            print(f"✅ 智能体交易已启动 (会话ID: {session_id})")
            print("   系统开始监控市场并准备执行AI决策...")
        else:
            print(f"❌ 启动失败: {result.get('error')}")
            return
    else:
        print(f"❌ 请求失败: {response.status_code}")
        return
    
    print()
    
    # 模拟多次AI决策执行
    print("🧠 执行AI决策循环...")
    print("系统将结合用户策略、市场分析和AI模型进行决策...")
    print()
    
    for i in range(3):
        print(f"📊 第 {i+1} 次AI决策分析...")
        
        response = session.post(f"{base_url}/api/agent-trading/execute",
                               headers={'Content-Type': 'application/json'},
                               data=json.dumps({'session_id': session_id}))
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ AI决策执行成功")
                
                # 显示市场分析
                market_context = result.get('market_context', {})
                if market_context.get('symbols'):
                    print("   📈 市场分析:")
                    for symbol, data in market_context['symbols'].items():
                        print(f"      {symbol}: 价格 ${data['current_price']:.5f}, "
                              f"趋势 {data['trend']}, RSI {data['rsi']}")
                
                # 显示AI决策
                decisions = result.get('decisions', {})
                if decisions.get('decisions'):
                    print("   🤖 AI决策:")
                    for decision in decisions['decisions']:
                        action_emoji = "📈" if decision['action'] == 'buy' else "📉" if decision['action'] == 'sell' else "⏸️"
                        print(f"      {action_emoji} {decision['symbol']}: {decision['action']} "
                              f"(信心度: {decision['confidence']:.0%})")
                        print(f"         理由: {decision['reasoning']}")
                
                # 显示执行结果
                executions = result.get('executions', [])
                if executions:
                    print("   ⚡ 执行结果:")
                    for execution in executions:
                        if execution['success']:
                            print(f"      ✅ {execution['symbol']} {execution['action']} "
                                  f"{execution['amount']} @ ${execution['price']:.5f}")
                        else:
                            print(f"      ❌ {execution['symbol']} 执行失败")
                else:
                    print("   ⏸️ 本次决策为观望，未执行交易")
                
            else:
                print(f"❌ 决策执行失败: {result.get('error')}")
        else:
            print(f"❌ 请求失败: {response.status_code}")
        
        print()
        time.sleep(2)  # 等待2秒模拟真实交易间隔
    
    # 评估交易表现
    print("📊 评估交易表现...")
    
    response = session.post(f"{base_url}/api/agent-trading/evaluate",
                           headers={'Content-Type': 'application/json'},
                           data=json.dumps({'session_id': session_id}))
    
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            evaluation = result['evaluation']
            print("✅ 交易表现评估完成:")
            print(f"   📈 总交易次数: {evaluation['total_trades']}")
            print(f"   🎯 胜率: {evaluation['win_rate']:.1%}")
            print(f"   💰 总盈亏: ${evaluation['total_profit']:.2f}")
            print(f"   📊 浮动盈亏: ${evaluation['floating_pnl']:.2f}")
            print(f"   📉 最大回撤: {evaluation['max_drawdown']:.1%}")
            print(f"   ⚖️ 盈亏比: {evaluation['profit_factor']:.2f}")
            
            if evaluation['total_trades'] > 0:
                print(f"   📊 平均每笔盈亏: ${evaluation['avg_profit_per_trade']:.2f}")
        else:
            print(f"❌ 评估失败: {result.get('error')}")
    else:
        print(f"❌ 请求失败: {response.status_code}")
    
    print()
    
    # 停止交易
    print("⏹️ 停止智能体交易...")
    
    response = session.post(f"{base_url}/api/agent-trading/stop",
                           headers={'Content-Type': 'application/json'},
                           data=json.dumps({'session_id': session_id}))
    
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            print("✅ 智能体交易已停止")
        else:
            print(f"❌ 停止失败: {result.get('error')}")
    else:
        print(f"❌ 请求失败: {response.status_code}")
    
    print()
    print("=" * 60)
    print("🎉 智能体交易演示完成！")
    print()
    print("💡 系统特点总结:")
    print("✅ 结合用户交易心得和AI分析")
    print("✅ 支持多种外部AI大模型")
    print("✅ 智能风险控制和资金管理")
    print("✅ 实时市场分析和决策执行")
    print("✅ 完整的性能评估和优化")
    print("✅ 闭环的交易管理流程")
    print()
    print("🌐 访问 http://127.0.0.1:5000/trading/agent 体验完整功能")

if __name__ == "__main__":
    demo_agent_trading()
