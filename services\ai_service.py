import requests
import json
from models import AIModelConfig, MarketData
from datetime import datetime, timedelta
import yfinance as yf
import pandas as pd
import numpy as np

class AIService:
    def __init__(self):
        # 符号映射表 - 将常用交易符号映射到yfinance支持的符号
        self.symbol_mapping = {
            # 黄金相关
            'XAUUSD': 'GC=F',  # 黄金期货
            'XAUUSD=X': 'GC=F',
            'XAU/USD': 'GC=F',
            'GOLD': 'GC=F',
            'GLD': 'GLD',  # 黄金ETF

            # 外汇对
            'EURUSD': 'EURUSD=X',
            'GBPUSD': 'GBPUSD=X',
            'USDJPY': 'USDJPY=X',
            'AUDUSD': 'AUDUSD=X',
            'USDCAD': 'USDCAD=X',
            'USDCHF': 'USDCHF=X',
            'NZDUSD': 'NZDUSD=X',
            'EURGBP': 'EURGBP=X',
            'EURJPY': 'EURJPY=X',
            'GBPJPY': 'GBPJPY=X',

            # 加密货币
            'BTCUSD': 'BTC-USD',
            'ETHUSD': 'ETH-USD',
            'BTC/USD': 'BTC-USD',
            'ETH/USD': 'ETH-USD',

            # 大宗商品
            'CRUDE': 'CL=F',  # 原油期货
            'WTI': 'CL=F',
            'BRENT': 'BZ=F',
            'SILVER': 'SI=F',  # 白银期货
            'XAGUSD': 'SI=F',

            # 股指
            'SPX': '^GSPC',  # 标普500
            'DJI': '^DJI',   # 道琼斯
            'NDX': '^IXIC',  # 纳斯达克
            'DAX': '^GDAXI', # 德国DAX
            'FTSE': '^FTSE', # 英国富时100
            'NIKKEI': '^N225', # 日经225
        }

        # 备选符号列表
        self.alternative_symbols = {
            'XAUUSD=X': ['GC=F', 'GLD', 'IAU'],
            'XAUUSD': ['GC=F', 'GLD', 'IAU'],
            'BTCUSD': ['BTC-USD', 'GBTC'],
            'CRUDE': ['CL=F', 'USO', 'UCO'],
        }

        # 符号类型和支持的间隔
        self.symbol_interval_support = {
            # 期货合约 - 通常不支持高频数据
            'futures': {
                'symbols': ['GC=F', 'SI=F', 'CL=F', 'BZ=F', 'NG=F'],
                'supported_intervals': ['1d', '1h', '30m'],
                'fallback_interval': '1h'
            },
            # ETF - 支持大部分间隔
            'etf': {
                'symbols': ['GLD', 'SLV', 'IAU', 'USO'],
                'supported_intervals': ['1d', '1h', '30m', '15m', '5m'],
                'fallback_interval': '15m'
            },
            # 外汇 - 支持高频数据
            'forex': {
                'symbols': ['EURUSD=X', 'GBPUSD=X', 'USDJPY=X', 'AUDUSD=X'],
                'supported_intervals': ['1d', '1h', '30m', '15m', '5m'],
                'fallback_interval': '15m'
            },
            # 加密货币 - 支持高频数据
            'crypto': {
                'symbols': ['BTC-USD', 'ETH-USD'],
                'supported_intervals': ['1d', '1h', '30m', '15m', '5m', '1m'],
                'fallback_interval': '5m'
            },
            # 股票 - 支持大部分间隔
            'stock': {
                'symbols': ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA'],
                'supported_intervals': ['1d', '1h', '30m', '15m', '5m'],
                'fallback_interval': '15m'
            }
        }

        self.models = {
            'deepseek-v3': {
                'api_key': '***********************************',
                'model': 'deepseek-chat',
                'base_url': 'https://api.deepseek.com/v1'
            },
            'kimi': {
                'api_key': 'sk-KGnG5G6MSkPGdOEEPtx3ZDf7F3PTvHPrVRIlrzZlRXRSoO41',
                'model': 'moonshot-v1-128k',
                'base_url': 'https://api.moonshot.cn/v1'
            },
            'zhipu': {
                'api_key': '21d5bd420bfc42c3910b0d612e371edf.kjNvmlCODNgFGy4a',
                'model': 'glm-4-flash-250414',
                'base_url': 'https://open.bigmodel.cn/api/paas/v4'
            },
            'zhipu-z1': {
                'api_key': '21d5bd420bfc42c3910b0d612e371edf.kjNvmlCODNgFGy4a',
                'model': 'glm-z1-flash',
                'base_url': 'https://open.bigmodel.cn/api/paas/v4'
            },
            '智谱清言': {
                'api_key': '21d5bd420bfc42c3910b0d612e371edf.kjNvmlCODNgFGy4a',
                'model': 'glm-4-flash-250414',
                'base_url': 'https://open.bigmodel.cn/api/paas/v4'
            },
            '智谱清言_z1': {
                'api_key': '21d5bd420bfc42c3910b0d612e371edf.kjNvmlCODNgFGy4a',
                'model': 'glm-z1-flash',
                'base_url': 'https://open.bigmodel.cn/api/paas/v4'
            },
            '智谱清言-Z1': {
                'api_key': '21d5bd420bfc42c3910b0d612e371edf.kjNvmlCODNgFGy4a',
                'model': 'glm-z1-flash',
                'base_url': 'https://open.bigmodel.cn/api/paas/v4'
            },
            'deepseek-r1': {
                'api_key': '***********************************',
                'model': 'deepseek-reasoner',
                'base_url': 'https://api.deepseek.com/v1'
            }
        }

    def get_market_data(self, symbol, period='1mo', interval='1d'):
        """获取真实市场数据 - 支持符号映射和备选方案"""
        original_symbol = symbol

        # 第一步：尝试符号映射
        if symbol in self.symbol_mapping:
            mapped_symbol = self.symbol_mapping[symbol]
            print(f"符号映射: {symbol} → {mapped_symbol}")
            symbol = mapped_symbol

        # 第二步：尝试获取主要符号的数据
        data = self._try_get_data(symbol, period, interval)
        if data is not None:
            return data

        # 第三步：如果主要符号失败，尝试备选符号
        if original_symbol in self.alternative_symbols:
            print(f"主要符号 {symbol} 失败，尝试备选符号...")
            for alt_symbol in self.alternative_symbols[original_symbol]:
                print(f"尝试备选符号: {alt_symbol}")
                data = self._try_get_data(alt_symbol, period, interval)
                if data is not None:
                    print(f"成功使用备选符号 {alt_symbol} 获取数据")
                    return data

        # 第四步：如果是黄金相关符号，尝试更多备选方案
        if any(gold_term in original_symbol.upper() for gold_term in ['XAU', 'GOLD']):
            # 更新黄金备选符号，优先使用更稳定的符号
            gold_alternatives = [
                'GC=F',    # 黄金期货（主要）
                '^GOLD',   # 黄金指数
                'GOLD',    # 黄金相关股票
                'GLD',     # SPDR黄金ETF
                'IAU',     # iShares黄金ETF
                'SGOL',    # Aberdeen黄金ETF
                'AAAU',    # Goldman Sachs黄金ETF
                'PHYS'     # Sprott黄金信托
            ]
            print(f"检测到黄金相关符号，尝试黄金备选方案...")
            for alt_symbol in gold_alternatives:
                print(f"尝试黄金备选符号: {alt_symbol}")
                # 尝试不同的时间周期，从短期开始
                for test_period in ['1mo', '3mo', '6mo', '1y']:
                    data = self._try_get_data(alt_symbol, test_period, interval)
                    if data is not None:
                        print(f"成功使用黄金备选符号 {alt_symbol} (周期: {test_period}) 获取数据")
                        return data

        # 系统严禁使用模拟数据，所有数据源失败时返回None
        logger.error(f"❌ 所有真实数据源都无法获取 {original_symbol} 的数据，系统严禁使用模拟数据")
        return None

    def get_market_data_with_symbol(self, symbol, period='1wk', interval='1h'):
        """获取市场数据并返回实际使用的符号"""
        original_symbol = symbol

        print(f"请求数据参数: 符号={symbol}, 周期={period}, 间隔={interval}")

        # 第一步：尝试符号映射
        if symbol in self.symbol_mapping:
            mapped_symbol = self.symbol_mapping[symbol]
            print(f"符号映射: {symbol} → {mapped_symbol}")
            symbol = mapped_symbol

        # 第二步：尝试获取主要符号的数据
        data = self._try_get_data(symbol, period, interval)
        if data is not None:
            return (data, symbol)

        # 第三步：如果主要符号失败，尝试备选符号
        if original_symbol in self.alternative_symbols:
            print(f"主要符号 {symbol} 失败，尝试备选符号...")
            for alt_symbol in self.alternative_symbols[original_symbol]:
                print(f"尝试备选符号: {alt_symbol}")
                data = self._try_get_data(alt_symbol, period, interval)
                if data is not None:
                    print(f"成功使用备选符号 {alt_symbol} 获取数据")
                    return (data, alt_symbol)

        # 第四步：如果是黄金相关符号，尝试更多备选方案
        if any(gold_term in original_symbol.upper() for gold_term in ['XAU', 'GOLD']):
            gold_alternatives = ['GC=F', 'GLD', 'IAU', 'SGOL', 'AAAU']
            print(f"检测到黄金相关符号，尝试黄金备选方案...")
            for alt_symbol in gold_alternatives:
                print(f"尝试黄金备选符号: {alt_symbol}")
                data = self._try_get_data(alt_symbol, period, interval)
                if data is not None:
                    print(f"成功使用黄金备选符号 {alt_symbol} 获取数据")
                    return (data, alt_symbol)

        print(f"所有尝试都失败，无法获取 {original_symbol} 的市场数据")
        return None

    def get_symbol_type(self, symbol):
        """获取符号类型"""
        for symbol_type, config in self.symbol_interval_support.items():
            if symbol in config['symbols']:
                return symbol_type

        # 根据符号格式推断类型
        if symbol.endswith('=F'):
            return 'futures'
        elif symbol.endswith('=X'):
            return 'forex'
        elif '-USD' in symbol:
            return 'crypto'
        elif symbol in ['GLD', 'SLV', 'IAU', 'USO', 'UCO']:
            return 'etf'
        else:
            return 'stock'

    def check_interval_compatibility(self, symbol, interval):
        """检查符号是否支持指定的间隔"""
        symbol_type = self.get_symbol_type(symbol)

        if symbol_type in self.symbol_interval_support:
            config = self.symbol_interval_support[symbol_type]
            supported = interval in config['supported_intervals']
            fallback = config['fallback_interval']

            print(f"符号 {symbol} 类型: {symbol_type}, 请求间隔: {interval}, 支持: {supported}")

            if not supported:
                print(f"间隔 {interval} 不支持，建议使用: {fallback}")
                return False, fallback

            return True, interval

        # 默认情况
        return True, interval

    def get_compatible_interval(self, symbol, interval):
        """获取兼容的间隔"""
        supported, compatible_interval = self.check_interval_compatibility(symbol, interval)

        if not supported:
            print(f"为符号 {symbol} 调整间隔: {interval} → {compatible_interval}")
            return compatible_interval

        return interval

    def _try_get_data(self, symbol, period, interval):
        """尝试获取单个符号的数据"""
        try:
            # 检查间隔兼容性
            compatible_interval = self.get_compatible_interval(symbol, interval)

            print(f"正在获取 {symbol} 的市场数据，周期: {period}, 间隔: {compatible_interval}")
            ticker = yf.Ticker(symbol)
            data = ticker.history(period=period, interval=compatible_interval)

            if data.empty:
                print(f"警告: {symbol} 没有返回任何数据")

                # 如果使用兼容间隔仍然失败，尝试更保守的间隔
                if compatible_interval != '1d':
                    print(f"尝试使用日K线数据作为最后备选...")
                    data = ticker.history(period=period, interval='1d')

                    if not data.empty:
                        print(f"成功使用日K线获取 {symbol} 的数据")
                        return data

                return None

            print(f"成功获取 {symbol} 的数据，共 {len(data)} 条记录")
            print(f"数据时间范围: {data.index[0]} 到 {data.index[-1]}")

            return data
        except Exception as e:
            print(f"获取 {symbol} 数据失败: {e}")

            # 如果出现异常，尝试使用日K线作为备选
            if interval != '1d':
                try:
                    print(f"尝试使用日K线作为备选...")
                    ticker = yf.Ticker(symbol)
                    data = ticker.history(period=period, interval='1d')

                    if not data.empty:
                        print(f"成功使用日K线获取 {symbol} 的数据")
                        return data
                except Exception as e2:
                    print(f"日K线备选也失败: {e2}")

            return None

    def _generate_fallback_data(self, symbol, period, interval):
        """备选数据方案已禁用"""
        print(f"❌ 无法为 {symbol} 生成数据 - 模拟数据已禁用，请使用真实数据源")
        return None

    def calculate_technical_indicators(self, data):
        """计算技术指标 - 增强版"""
        if data is None or data.empty:
            return {}

        indicators = {}

        # 检查数据长度
        data_length = len(data)
        if data_length < 2:
            print(f"数据长度不足: {data_length}")
            return {}

        try:
            # 导入技术指标服务
            from services.technical_indicators import TechnicalIndicators
            tech_indicators = TechnicalIndicators()

            # 移动平均线 - 安全计算
            if data_length >= 5:
                sma_5 = tech_indicators.sma(data['Close'], 5)
                if not sma_5.empty:
                    indicators['sma_5'] = sma_5.iloc[-1]

            if data_length >= 10:
                sma_10 = tech_indicators.sma(data['Close'], 10)
                if not sma_10.empty:
                    indicators['sma_10'] = sma_10.iloc[-1]

            if data_length >= 20:
                sma_20 = tech_indicators.sma(data['Close'], 20)
                if not sma_20.empty:
                    indicators['sma_20'] = sma_20.iloc[-1]

            if data_length >= 50:
                sma_50 = tech_indicators.sma(data['Close'], 50)
                if not sma_50.empty:
                    indicators['sma_50'] = sma_50.iloc[-1]

            # EMA - 安全计算
            if data_length >= 12:
                ema_12 = tech_indicators.ema(data['Close'], 12)
                if not ema_12.empty:
                    indicators['ema_12'] = ema_12.iloc[-1]

            if data_length >= 26:
                ema_26 = tech_indicators.ema(data['Close'], 26)
                if not ema_26.empty:
                    indicators['ema_26'] = ema_26.iloc[-1]

            # MACD - 安全计算
            if data_length >= 26:
                try:
                    macd_data = tech_indicators.macd(data['Close'])
                    if isinstance(macd_data, dict):
                        for key in ['macd', 'signal', 'histogram']:
                            if key in macd_data and not macd_data[key].empty:
                                indicators[f'macd_{key}' if key != 'macd' else key] = macd_data[key].iloc[-1]
                except Exception as e:
                    print(f"计算MACD时出错: {e}")

            # RSI - 安全计算
            if data_length >= 14:
                try:
                    rsi = tech_indicators.rsi(data['Close'], 14)
                    if not rsi.empty:
                        indicators['rsi'] = rsi.iloc[-1]
                except Exception as e:
                    print(f"计算RSI时出错: {e}")

            # 布林带 - 安全计算
            if data_length >= 20:
                try:
                    bb_data = tech_indicators.bollinger_bands(data['Close'], 20, 2)
                    if isinstance(bb_data, dict):
                        for key in ['upper', 'middle', 'lower']:
                            if key in bb_data and not bb_data[key].empty:
                                indicators[f'bb_{key}'] = bb_data[key].iloc[-1]
                except Exception as e:
                    print(f"计算布林带时出错: {e}")

            # 随机指标 - 安全计算
            if data_length >= 14:
                try:
                    stoch_data = tech_indicators.stochastic(data['High'], data['Low'], data['Close'])
                    if isinstance(stoch_data, dict):
                        if '%K' in stoch_data and not stoch_data['%K'].empty:
                            indicators['stoch_k'] = stoch_data['%K'].iloc[-1]
                        if '%D' in stoch_data and not stoch_data['%D'].empty:
                            indicators['stoch_d'] = stoch_data['%D'].iloc[-1]
                except Exception as e:
                    print(f"计算随机指标时出错: {e}")

            # 威廉指标
            indicators['williams_r'] = tech_indicators.williams_r(data['High'], data['Low'], data['Close']).iloc[-1]

            # ATR
            indicators['atr'] = tech_indicators.atr(data['High'], data['Low'], data['Close']).iloc[-1]

            # CCI
            indicators['cci'] = tech_indicators.cci(data['High'], data['Low'], data['Close']).iloc[-1]

            # 成交量指标
            indicators['volume_sma'] = tech_indicators.sma(data['Volume'], 20).iloc[-1]
            indicators['obv'] = tech_indicators.obv(data['Close'], data['Volume']).iloc[-1]

            # ADX
            adx_data = tech_indicators.adx(data['High'], data['Low'], data['Close'])
            indicators['adx'] = adx_data['ADX'].iloc[-1]
            indicators['di_plus'] = adx_data['DI+'].iloc[-1]
            indicators['di_minus'] = adx_data['DI-'].iloc[-1]

        except Exception as e:
            print(f"计算技术指标时出错: {e}")
            # 回退到基础计算方法
            indicators = self._calculate_basic_indicators(data)

        return indicators

    def _calculate_basic_indicators(self, data):
        """基础技术指标计算（回退方法）"""
        indicators = {}

        try:
            data_length = len(data)

            # 移动平均线 - 安全计算
            if data_length >= 20:
                sma_20 = data['Close'].rolling(window=20).mean()
                if not sma_20.empty and not pd.isna(sma_20.iloc[-1]):
                    indicators['sma_20'] = sma_20.iloc[-1]

            if data_length >= 50:
                sma_50 = data['Close'].rolling(window=50).mean()
                if not sma_50.empty and not pd.isna(sma_50.iloc[-1]):
                    indicators['sma_50'] = sma_50.iloc[-1]

            # EMA - 安全计算
            if data_length >= 12:
                ema_12 = data['Close'].ewm(span=12).mean()
                if not ema_12.empty and not pd.isna(ema_12.iloc[-1]):
                    indicators['ema_12'] = ema_12.iloc[-1]

            if data_length >= 26:
                ema_26 = data['Close'].ewm(span=26).mean()
                if not ema_26.empty and not pd.isna(ema_26.iloc[-1]):
                    indicators['ema_26'] = ema_26.iloc[-1]

            # MACD - 安全计算
            if data_length >= 26:
                try:
                    ema_12 = data['Close'].ewm(span=12).mean()
                    ema_26 = data['Close'].ewm(span=26).mean()
                    macd_line = ema_12 - ema_26
                    macd_signal = macd_line.ewm(span=9).mean()

                    if not macd_line.empty and not pd.isna(macd_line.iloc[-1]):
                        indicators['macd'] = macd_line.iloc[-1]
                    if not macd_signal.empty and not pd.isna(macd_signal.iloc[-1]):
                        indicators['macd_signal'] = macd_signal.iloc[-1]
                    if not macd_line.empty and not macd_signal.empty:
                        histogram = macd_line - macd_signal
                        if not histogram.empty and not pd.isna(histogram.iloc[-1]):
                            indicators['macd_histogram'] = histogram.iloc[-1]
                except Exception as e:
                    print(f"计算MACD时出错: {e}")

            # RSI - 安全计算
            if data_length >= 15:  # 需要至少15个数据点来计算14期RSI
                try:
                    delta = data['Close'].diff()
                    gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
                    loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()

                    # 避免除零错误
                    rs = gain / loss.replace(0, 0.0001)
                    rsi = 100 - (100 / (1 + rs))

                    if not rsi.empty and not pd.isna(rsi.iloc[-1]):
                        indicators['rsi'] = rsi.iloc[-1]
                except Exception as e:
                    print(f"计算RSI时出错: {e}")

            # 布林带 - 安全计算
            if data_length >= 20:
                try:
                    sma_20 = data['Close'].rolling(window=20).mean()
                    std_20 = data['Close'].rolling(window=20).std()

                    if not sma_20.empty and not std_20.empty:
                        bb_upper = sma_20 + (std_20 * 2)
                        bb_lower = sma_20 - (std_20 * 2)

                        if not pd.isna(bb_upper.iloc[-1]):
                            indicators['bb_upper'] = bb_upper.iloc[-1]
                        if not pd.isna(sma_20.iloc[-1]):
                            indicators['bb_middle'] = sma_20.iloc[-1]
                        if not pd.isna(bb_lower.iloc[-1]):
                            indicators['bb_lower'] = bb_lower.iloc[-1]
                except Exception as e:
                    print(f"计算布林带时出错: {e}")

            # 成交量移动平均 - 安全计算
            if data_length >= 20 and 'Volume' in data.columns:
                try:
                    volume_sma = data['Volume'].rolling(window=20).mean()
                    if not volume_sma.empty and not pd.isna(volume_sma.iloc[-1]):
                        indicators['volume_sma'] = volume_sma.iloc[-1]
                except Exception as e:
                    print(f"计算成交量移动平均时出错: {e}")

        except Exception as e:
            print(f"计算基础技术指标时出错: {e}")

        return indicators

    def _get_model_config_from_db(self, model_name):
        """从数据库获取AI模型配置"""
        try:
            from models import AIModelConfig

            # 按名称查找模型
            model = AIModelConfig.query.filter_by(name=model_name).first()
            if not model:
                print(f"❌ 数据库中未找到模型: {model_name}")
                return None

            if not model.is_active:
                print(f"❌ 模型未激活: {model_name}")
                return None

            if not model.api_key:
                print(f"❌ 模型API密钥未配置: {model_name}")
                return None

            # 构建模型配置
            config = {
                'api_key': model.api_key,
                'model': model.model_name if model.model_name else self._infer_model_id(model_name),
                'base_url': model.base_url if model.base_url else self._infer_base_url(model_name)
            }

            print(f"✅ 从数据库获取模型配置: {model_name}")
            print(f"   - API密钥: {'已配置' if model.api_key else '未配置'}")
            print(f"   - 模型ID: {config['model']}")
            print(f"   - API地址: {config['base_url']}")

            return config

        except Exception as e:
            print(f"❌ 获取数据库模型配置失败: {e}")
            return None

    def _infer_model_id(self, model_name):
        """根据模型名称推断模型ID"""
        name_lower = model_name.lower()

        if 'deepseek' in name_lower:
            if 'r1' in name_lower:
                return 'deepseek-reasoner'
            else:
                return 'deepseek-chat'
        elif 'kimi' in name_lower:
            return 'moonshot-v1-128k'
        elif '智谱' in model_name or 'zhipu' in name_lower or 'glm' in name_lower:
            # 检查是否是Z1版本（支持多种命名方式）
            if 'z1' in name_lower or '_z1' in name_lower or '-z1' in name_lower:
                return 'glm-z1-flash'
            else:
                return 'glm-4-flash-250414'
        elif 'gpt' in name_lower:
            return 'gpt-4'
        elif 'claude' in name_lower:
            return 'claude-3-sonnet-20240229'
        else:
            return model_name

    def _infer_base_url(self, model_name):
        """根据模型名称推断API基础URL"""
        name_lower = model_name.lower()

        if 'deepseek' in name_lower:
            return 'https://api.deepseek.com/v1'
        elif 'kimi' in name_lower:
            return 'https://api.moonshot.cn/v1'
        elif '智谱' in model_name or 'zhipu' in name_lower or 'glm' in name_lower:
            # 智谱清言的所有变体都使用相同的API地址
            return 'https://open.bigmodel.cn/api/paas/v4'
        elif 'gpt' in name_lower or 'openai' in name_lower:
            return 'https://api.openai.com/v1'
        elif 'claude' in name_lower:
            return 'https://api.anthropic.com'
        else:
            return 'https://api.openai.com/v1'  # 默认

    def call_ai_model(self, model_name, prompt, system_prompt=None):
        """调用AI模型"""
        # 首先尝试从硬编码配置中查找
        config = None
        if model_name in self.models:
            config = self.models[model_name]
        else:
            # 如果硬编码中没有，尝试从数据库获取
            config = self._get_model_config_from_db(model_name)
            if not config:
                raise ValueError(f"不支持的模型: {model_name}")

        print(f"🤖 调用AI模型: {model_name}")
        print(f"📋 模型配置: {config.get('model', 'Unknown')}")

        # 检查网络连接
        if not self._check_network_connection(config['base_url']):
            return f"AI分析失败: 无法连接到 {config['base_url']}，请检查网络连接"

        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {config["api_key"]}'
        }

        messages = []
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": prompt})

        data = {
            "model": config["model"],
            "messages": messages,
            "temperature": 0.7,
            "max_tokens": 2000
        }

        # 重试机制
        max_retries = 3
        retry_delay = 5  # 秒

        for attempt in range(max_retries):
            try:
                print(f"🔄 尝试调用AI模型 (第{attempt + 1}次/共{max_retries}次)")

                # 根据模型调整超时时间
                timeout = 60 if 'deepseek' in model_name.lower() else 45

                response = requests.post(
                    f"{config['base_url']}/chat/completions",
                    headers=headers,
                    json=data,
                    timeout=timeout
                )
                response.raise_for_status()
                result = response.json()

                print(f"✅ AI模型调用成功 (第{attempt + 1}次尝试)")
                return result['choices'][0]['message']['content']

            except requests.exceptions.Timeout as e:
                print(f"⏰ AI模型调用超时 (第{attempt + 1}次): {e}")
                if attempt < max_retries - 1:
                    print(f"🔄 等待{retry_delay}秒后重试...")
                    import time
                    time.sleep(retry_delay)
                    retry_delay *= 2  # 指数退避
                else:
                    print(f"❌ AI模型调用最终超时，已重试{max_retries}次")
                    return f"AI分析超时: 网络连接超时，请稍后重试"

            except requests.exceptions.ConnectionError as e:
                print(f"🌐 AI模型连接错误 (第{attempt + 1}次): {e}")
                if attempt < max_retries - 1:
                    print(f"🔄 等待{retry_delay}秒后重试...")
                    import time
                    time.sleep(retry_delay)
                    retry_delay *= 2
                else:
                    print(f"❌ AI模型连接最终失败，已重试{max_retries}次")
                    return f"AI分析连接失败: 无法连接到AI服务，请检查网络"

            except requests.exceptions.HTTPError as e:
                print(f"🚫 AI模型HTTP错误 (第{attempt + 1}次): {e}")
                if e.response.status_code == 429:  # 速率限制
                    if attempt < max_retries - 1:
                        print(f"🔄 遇到速率限制，等待{retry_delay * 2}秒后重试...")
                        import time
                        time.sleep(retry_delay * 2)
                    else:
                        return f"AI分析失败: API调用频率过高，请稍后重试"
                elif e.response.status_code == 401:  # 认证错误
                    return f"AI分析失败: API密钥无效或已过期"
                elif e.response.status_code == 403:  # 权限错误
                    return f"AI分析失败: API权限不足"
                else:
                    if attempt < max_retries - 1:
                        print(f"🔄 等待{retry_delay}秒后重试...")
                        import time
                        time.sleep(retry_delay)
                    else:
                        return f"AI分析失败: HTTP错误 {e.response.status_code}"

            except Exception as e:
                print(f"❌ AI模型调用异常 (第{attempt + 1}次): {e}")
                if attempt < max_retries - 1:
                    print(f"🔄 等待{retry_delay}秒后重试...")
                    import time
                    time.sleep(retry_delay)
                else:
                    print(f"❌ AI模型调用最终失败，已重试{max_retries}次")
                    return f"AI分析失败: {str(e)}"

    def analyze_market(self, symbol, model_id, analysis_type='technical', period='1wk', interval='1h'):
        """市场分析"""
        # 保存原始符号
        original_symbol = symbol

        # 获取市场数据
        market_data_result = self.get_market_data_with_symbol(symbol, period, interval)
        if market_data_result is None:
            return {
                'symbol': symbol,
                'original_symbol': original_symbol,
                'error': '无法获取市场数据',
                'current_price': 0,
                'price_change': 0,
                'indicators': {},
                'ai_analysis': '无法获取市场数据，请检查交易品种代码是否正确',
                'timestamp': datetime.now().isoformat()
            }

        market_data, actual_symbol = market_data_result
        if market_data.empty:
            return {
                'symbol': symbol,
                'error': '无法获取市场数据',
                'current_price': 0,
                'price_change': 0,
                'indicators': {},
                'ai_analysis': '无法获取市场数据，请检查交易品种代码是否正确',
                'timestamp': datetime.now().isoformat()
            }

        # 检查数据长度是否足够
        if len(market_data) < 2:
            return {
                'symbol': symbol,
                'error': '市场数据不足',
                'current_price': market_data['Close'].iloc[-1] if len(market_data) > 0 else 0,
                'price_change': 0,
                'indicators': {},
                'ai_analysis': '市场数据不足，无法进行有效分析',
                'timestamp': datetime.now().isoformat()
            }

        # 计算技术指标
        indicators = self.calculate_technical_indicators(market_data)

        # 安全地获取价格数据
        try:
            current_price = market_data['Close'].iloc[-1]
            previous_price = market_data['Close'].iloc[-2]
            price_change = ((current_price - previous_price) / previous_price) * 100
        except (IndexError, KeyError) as e:
            print(f"获取价格数据时出错: {e}")
            current_price = market_data['Close'].iloc[-1] if len(market_data) > 0 else 0
            price_change = 0

        if analysis_type == 'technical':
            prompt = f"""
            请分析以下技术指标数据，给出交易建议：

            股票代码: {symbol}
            当前价格: {current_price:.2f}
            价格变化: {price_change:.2f}%

            技术指标:
            - SMA20: {indicators.get('sma_20', 'N/A'):.2f}
            - SMA50: {indicators.get('sma_50', 'N/A'):.2f}
            - EMA12: {indicators.get('ema_12', 'N/A'):.2f}
            - EMA26: {indicators.get('ema_26', 'N/A'):.2f}
            - MACD: {indicators.get('macd', 'N/A'):.4f}
            - MACD信号线: {indicators.get('macd_signal', 'N/A'):.4f}
            - RSI: {indicators.get('rsi', 'N/A'):.2f}
            - 布林带上轨: {indicators.get('bb_upper', 'N/A'):.2f}
            - 布林带中轨: {indicators.get('bb_middle', 'N/A'):.2f}
            - 布林带下轨: {indicators.get('bb_lower', 'N/A'):.2f}

            请提供：
            1. 技术分析总结
            2. 交易建议（买入/卖出/持有）
            3. 风险评估
            4. 目标价位和止损位
            """
        else:
            prompt = f"""
            请对股票 {symbol} 进行基本面分析，当前价格 {current_price:.2f}，
            价格变化 {price_change:.2f}%。

            请提供基本面分析建议，包括：
            1. 行业分析
            2. 公司基本面评估
            3. 投资建议
            4. 风险因素
            """

        system_prompt = "你是一个专业的金融分析师，请基于提供的数据给出专业的分析和建议。"

        # 调用AI模型
        analysis_result = self.call_ai_model(model_id, prompt, system_prompt)

        # 检查是否使用了符号映射或备选符号
        symbol_note = ""
        if actual_symbol != original_symbol:
            if original_symbol in self.symbol_mapping:
                symbol_note = f"注意：已将 {original_symbol} 映射为 {actual_symbol} 进行分析"
            else:
                symbol_note = f"注意：原符号 {original_symbol} 无数据，已使用 {actual_symbol} 作为替代"

        return {
            'symbol': actual_symbol,
            'original_symbol': original_symbol,
            'symbol_note': symbol_note,
            'current_price': current_price,
            'price_change': price_change,
            'indicators': indicators,
            'ai_analysis': analysis_result,
            'timestamp': datetime.now().isoformat()
        }

    def generate_trading_strategy(self, model_id, strategy_type, parameters):
        """生成交易策略"""
        prompt = f"""
        请帮我生成一个{strategy_type}交易策略，参数如下：
        {json.dumps(parameters, indent=2, ensure_ascii=False)}

        请提供：
        1. 策略逻辑描述
        2. 入场条件
        3. 出场条件
        4. 风险管理规则
        5. 预期收益和风险评估
        """

        system_prompt = "你是一个专业的量化交易策略开发专家，请基于用户需求设计有效的交易策略。"

        return self.call_ai_model(model_id, prompt, system_prompt)

    def _check_network_connection(self, base_url: str, timeout: int = 10) -> bool:
        """检查网络连接"""
        try:
            import urllib.parse
            parsed_url = urllib.parse.urlparse(base_url)
            host = parsed_url.hostname
            port = parsed_url.port or (443 if parsed_url.scheme == 'https' else 80)

            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(timeout)
            result = sock.connect_ex((host, port))
            sock.close()

            if result == 0:
                print(f"✅ 网络连接正常: {host}:{port}")
                return True
            else:
                print(f"❌ 网络连接失败: {host}:{port}")
                return False

        except Exception as e:
            print(f"❌ 网络连接检查异常: {e}")
            return False

# 创建全局实例
ai_service = AIService()
