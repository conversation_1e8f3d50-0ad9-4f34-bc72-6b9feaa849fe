#!/usr/bin/env python3
"""
深度学习训练卡住问题彻底修复
"""

import sqlite3
import torch
import psutil
import os
import time
import threading
from datetime import datetime, timedelta

def kill_stuck_processes():
    """终止卡住的训练进程"""
    print("🔄 终止卡住的训练进程")
    print("=" * 60)
    
    killed_count = 0
    
    try:
        for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'cpu_percent', 'create_time']):
            try:
                if 'python' in proc.info['name'].lower():
                    cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                    
                    # 检查是否是训练相关进程
                    if any(keyword in cmdline.lower() for keyword in ['training', 'deep_learning', 'torch']):
                        create_time = datetime.fromtimestamp(proc.info['create_time'])
                        running_time = datetime.now() - create_time
                        cpu_percent = proc.info['cpu_percent']
                        
                        # 如果进程运行超过10分钟且CPU使用率低于5%，认为是卡住的
                        if running_time > timedelta(minutes=10) and cpu_percent < 5:
                            print(f"🔴 终止卡住进程: PID {proc.info['pid']} (运行时间: {running_time}, CPU: {cpu_percent}%)")
                            
                            # 尝试优雅终止
                            try:
                                proc.terminate()
                                proc.wait(timeout=5)
                                print(f"  ✅ 进程已优雅终止")
                            except psutil.TimeoutExpired:
                                # 强制终止
                                proc.kill()
                                print(f"  ⚡ 进程已强制终止")
                            
                            killed_count += 1
                            
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        print(f"✅ 共终止 {killed_count} 个卡住的进程")
        return killed_count > 0
        
    except Exception as e:
        print(f"❌ 终止进程失败: {e}")
        return False

def reset_stuck_database_tasks():
    """重置数据库中卡住的训练任务"""
    print(f"\n📋 重置数据库中卡住的任务")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 查找卡住的任务（超过10分钟没更新的running任务）
        cursor.execute('''
            SELECT id, updated_at FROM training_tasks 
            WHERE status = 'running'
        ''')
        
        running_tasks = cursor.fetchall()
        stuck_tasks = []
        
        for task_id, updated_at in running_tasks:
            if updated_at:
                try:
                    last_update = datetime.fromisoformat(updated_at.replace('Z', '+00:00'))
                    now = datetime.now()
                    time_since_update = now - last_update
                    
                    if time_since_update > timedelta(minutes=10):
                        stuck_tasks.append(task_id)
                        print(f"🔴 发现卡住任务: {task_id[:8]}... (已 {time_since_update} 没更新)")
                except:
                    # 如果时间解析失败，也认为是卡住的
                    stuck_tasks.append(task_id)
        
        # 重置卡住的任务
        if stuck_tasks:
            for task_id in stuck_tasks:
                cursor.execute('''
                    UPDATE training_tasks 
                    SET status = 'failed', 
                        logs = COALESCE(logs, '') || char(10) || '[' || datetime('now') || '] 任务卡住已自动重置',
                        updated_at = datetime('now')
                    WHERE id = ?
                ''', (task_id,))
                
                print(f"✅ 任务 {task_id[:8]}... 已重置为失败状态")
            
            conn.commit()
            print(f"✅ 共重置 {len(stuck_tasks)} 个卡住的任务")
        else:
            print("✅ 没有发现卡住的任务")
        
        conn.close()
        return len(stuck_tasks)
        
    except Exception as e:
        print(f"❌ 重置数据库任务失败: {e}")
        return 0

def clear_gpu_memory_comprehensive():
    """全面清理GPU内存"""
    print(f"\n🧹 全面清理GPU内存")
    print("=" * 60)
    
    try:
        if torch.cuda.is_available():
            device_count = torch.cuda.device_count()
            
            for i in range(device_count):
                print(f"清理GPU {i}...")
                
                # 设置当前设备
                torch.cuda.set_device(i)
                
                # 获取清理前的内存状态
                before_allocated = torch.cuda.memory_allocated(i) / 1024**3
                before_reserved = torch.cuda.memory_reserved(i) / 1024**3
                
                # 清理缓存
                torch.cuda.empty_cache()
                
                # 强制垃圾回收
                import gc
                gc.collect()
                
                # 再次清理缓存
                torch.cuda.empty_cache()
                
                # 获取清理后的内存状态
                after_allocated = torch.cuda.memory_allocated(i) / 1024**3
                after_reserved = torch.cuda.memory_reserved(i) / 1024**3
                
                print(f"  GPU {i} 内存清理:")
                print(f"    清理前: 已分配 {before_allocated:.2f}GB, 已保留 {before_reserved:.2f}GB")
                print(f"    清理后: 已分配 {after_allocated:.2f}GB, 已保留 {after_reserved:.2f}GB")
                print(f"    释放内存: {(before_reserved - after_reserved):.2f}GB")
            
            print("✅ GPU内存清理完成")
            return True
        else:
            print("❌ CUDA不可用")
            return False
            
    except Exception as e:
        print(f"❌ GPU内存清理失败: {e}")
        return False

def optimize_system_for_training():
    """优化系统设置以防止训练卡住"""
    print(f"\n⚙️ 优化系统设置")
    print("=" * 60)
    
    optimizations = []
    
    try:
        # 1. 设置PyTorch环境变量
        os.environ['CUDA_LAUNCH_BLOCKING'] = '0'  # 异步CUDA操作
        os.environ['TORCH_USE_CUDA_DSA'] = '1'    # 启用CUDA设备端断言
        os.environ['CUDA_VISIBLE_DEVICES'] = '0'  # 只使用第一个GPU
        optimizations.append("设置CUDA环境变量")
        
        # 2. 设置PyTorch线程数
        torch.set_num_threads(4)  # 限制CPU线程数
        optimizations.append("限制PyTorch CPU线程数为4")
        
        # 3. 启用CUDA内存池
        if torch.cuda.is_available():
            # 设置内存分配策略
            torch.cuda.set_per_process_memory_fraction(0.8)  # 限制GPU内存使用为80%
            optimizations.append("限制GPU内存使用为80%")
            
            # 启用内存映射分配器
            torch.backends.cuda.matmul.allow_tf32 = True
            torch.backends.cudnn.allow_tf32 = True
            optimizations.append("启用TF32加速")
        
        # 4. 设置数据加载器优化
        torch.multiprocessing.set_sharing_strategy('file_system')
        optimizations.append("优化多进程共享策略")
        
        for opt in optimizations:
            print(f"  ✅ {opt}")
        
        print(f"✅ 系统优化完成 ({len(optimizations)} 项)")
        return True
        
    except Exception as e:
        print(f"❌ 系统优化失败: {e}")
        return False

def create_training_monitor():
    """创建训练监控脚本"""
    print(f"\n📊 创建训练监控脚本")
    print("=" * 60)
    
    monitor_script = '''#!/usr/bin/env python3
"""
训练监控脚本 - 自动检测和处理卡住的训练
"""

import sqlite3
import time
import psutil
import torch
from datetime import datetime, timedelta

def monitor_training():
    """监控训练状态"""
    while True:
        try:
            # 检查数据库中的训练任务
            conn = sqlite3.connect('trading_system.db')
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT id, updated_at FROM training_tasks
                WHERE status = 'running'
            """)
            
            running_tasks = cursor.fetchall()
            
            for task_id, updated_at in running_tasks:
                if updated_at:
                    try:
                        last_update = datetime.fromisoformat(updated_at.replace('Z', '+00:00'))
                        now = datetime.now()
                        time_since_update = now - last_update
                        
                        # 如果超过15分钟没更新，重置任务
                        if time_since_update > timedelta(minutes=15):
                            print(f"⚠️ 检测到卡住任务: {task_id[:8]}... (已 {time_since_update} 没更新)")
                            
                            cursor.execute("""
                                UPDATE training_tasks
                                SET status = 'failed',
                                    logs = COALESCE(logs, '') || char(10) || '[' || datetime('now') || '] 监控检测到任务卡住，已自动重置',
                                    updated_at = datetime('now')
                                WHERE id = ?
                            """, (task_id,))
                            
                            conn.commit()
                            print(f"✅ 任务 {task_id[:8]}... 已自动重置")
                    except:
                        pass
            
            conn.close()
            
            # 清理GPU内存
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            
            # 等待60秒后再次检查
            time.sleep(60)
            
        except Exception as e:
            print(f"❌ 监控异常: {e}")
            time.sleep(60)

if __name__ == '__main__':
    print("🔍 启动训练监控...")
    monitor_training()
'''
    
    try:
        with open('training_monitor.py', 'w', encoding='utf-8') as f:
            f.write(monitor_script)
        
        print("✅ 训练监控脚本已创建: training_monitor.py")
        print("💡 使用方法: python training_monitor.py")
        return True
        
    except Exception as e:
        print(f"❌ 创建监控脚本失败: {e}")
        return False

def test_training_environment():
    """测试训练环境是否正常"""
    print(f"\n🧪 测试训练环境")
    print("=" * 60)
    
    try:
        # 1. 测试GPU
        if torch.cuda.is_available():
            device = torch.device('cuda')
            print(f"✅ GPU可用: {torch.cuda.get_device_name(0)}")
            
            # 测试GPU计算
            x = torch.randn(1000, 1000).to(device)
            y = torch.randn(1000, 1000).to(device)
            z = torch.mm(x, y)
            
            print(f"✅ GPU计算测试通过")
            
            # 清理测试数据
            del x, y, z
            torch.cuda.empty_cache()
            
        else:
            print("❌ GPU不可用")
            return False
        
        # 2. 测试数据库连接
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        cursor.execute('SELECT COUNT(*) FROM training_tasks')
        count = cursor.fetchone()[0]
        conn.close()
        print(f"✅ 数据库连接正常 (共 {count} 个训练任务)")
        
        # 3. 测试系统资源
        memory = psutil.virtual_memory()
        if memory.percent < 90:
            print(f"✅ 系统内存充足 ({memory.percent:.1f}% 使用)")
        else:
            print(f"⚠️ 系统内存使用率较高 ({memory.percent:.1f}%)")
        
        print("✅ 训练环境测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 训练环境测试失败: {e}")
        return False

def main():
    """主修复函数"""
    print("🔧 深度学习训练卡住问题彻底修复")
    print("=" * 80)
    
    print("📋 修复步骤:")
    print("1. 终止卡住的训练进程")
    print("2. 重置数据库中卡住的任务")
    print("3. 全面清理GPU内存")
    print("4. 优化系统设置")
    print("5. 创建训练监控脚本")
    print("6. 测试训练环境")
    
    # 执行修复步骤
    step1 = kill_stuck_processes()
    step2 = reset_stuck_database_tasks()
    step3 = clear_gpu_memory_comprehensive()
    step4 = optimize_system_for_training()
    step5 = create_training_monitor()
    step6 = test_training_environment()
    
    # 总结修复结果
    print(f"\n📊 修复结果总结")
    print("=" * 80)
    
    success_count = sum([step1, step2 > 0, step3, step4, step5, step6])
    
    print(f"终止卡住进程: {'✅' if step1 else '⚠️'}")
    print(f"重置卡住任务: {'✅' if step2 > 0 else '⚠️'} ({step2} 个)")
    print(f"清理GPU内存: {'✅' if step3 else '❌'}")
    print(f"优化系统设置: {'✅' if step4 else '❌'}")
    print(f"创建监控脚本: {'✅' if step5 else '❌'}")
    print(f"测试训练环境: {'✅' if step6 else '❌'}")
    
    if success_count >= 4:
        print(f"\n🎉 修复成功! ({success_count}/6 步骤完成)")
        print(f"\n💡 使用建议:")
        print(f"1. 现在可以重新开始训练")
        print(f"2. 建议使用较小的批次大小 (16-32)")
        print(f"3. 启动训练监控: python training_monitor.py")
        print(f"4. 定期检查GPU内存使用情况")
        print(f"5. 如果再次卡住，重新运行此脚本")
    else:
        print(f"\n⚠️ 部分修复可能需要手动处理")
        print(f"建议:")
        print(f"1. 重启系统以完全清理资源")
        print(f"2. 检查GPU驱动程序")
        print(f"3. 更新PyTorch版本")

if __name__ == '__main__':
    main()
