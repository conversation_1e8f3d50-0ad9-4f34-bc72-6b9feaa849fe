#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度清理所有持仓数据
检查所有可能的数据源并清理
"""

import sqlite3
import os
import json

def check_all_databases():
    """检查所有可能的数据库"""
    print("🔍 检查所有可能的数据库")
    print("=" * 50)
    
    # 所有可能的数据库路径
    db_paths = [
        'instance/matetrade4.db',
        'matetrade4.db', 
        'trading_system.db',
        'instance/trading_system.db',
        'data/trading.db',
        'database.db',
        'instance/database.db'
    ]
    
    found_databases = []
    
    for path in db_paths:
        if os.path.exists(path):
            try:
                conn = sqlite3.connect(path)
                cursor = conn.cursor()
                
                # 检查是否有ai_trades表
                cursor.execute("""
                    SELECT name FROM sqlite_master 
                    WHERE type='table' AND name='ai_trades'
                """)
                
                if cursor.fetchone():
                    cursor.execute("SELECT COUNT(*) FROM ai_trades")
                    count = cursor.fetchone()[0]
                    found_databases.append((path, count))
                    print(f"✅ 找到数据库: {path} (ai_trades记录数: {count})")
                else:
                    print(f"ℹ️  数据库存在但无ai_trades表: {path}")
                
                conn.close()
                
            except Exception as e:
                print(f"❌ 检查数据库失败 {path}: {e}")
    
    return found_databases

def check_other_tables():
    """检查其他可能存储持仓数据的表"""
    print("\n🔍 检查其他可能的数据表")
    print("=" * 50)
    
    db_path = 'instance/matetrade4.db'
    if not os.path.exists(db_path):
        print("❌ 主数据库不存在")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取所有表名
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table'
        """)
        
        tables = cursor.fetchall()
        print(f"📋 数据库中的所有表:")
        
        for table in tables:
            table_name = table[0]
            print(f"   - {table_name}")
            
            # 检查可能包含持仓数据的表
            if any(keyword in table_name.lower() for keyword in ['trade', 'position', 'order', 'deal']):
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                    count = cursor.fetchone()[0]
                    print(f"     记录数: {count}")
                    
                    if count > 0:
                        # 显示表结构
                        cursor.execute(f"PRAGMA table_info({table_name})")
                        columns = cursor.fetchall()
                        print(f"     字段: {[col[1] for col in columns]}")
                        
                        # 显示前几条记录
                        cursor.execute(f"SELECT * FROM {table_name} LIMIT 3")
                        records = cursor.fetchall()
                        if records:
                            print(f"     示例数据:")
                            for i, record in enumerate(records):
                                print(f"       {i+1}: {record}")
                
                except Exception as e:
                    print(f"     ❌ 检查表失败: {e}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查数据库表失败: {e}")

def clear_all_trading_data():
    """清理所有交易相关数据"""
    print("\n🔧 清理所有交易相关数据")
    print("=" * 50)
    
    db_path = 'instance/matetrade4.db'
    if not os.path.exists(db_path):
        print("❌ 主数据库不存在")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 清理ai_trades表
        cursor.execute("DELETE FROM ai_trades")
        ai_trades_deleted = cursor.rowcount
        print(f"✅ 清理ai_trades表: {ai_trades_deleted}条记录")
        
        # 检查并清理其他可能的交易表
        tables_to_check = [
            'trades', 'positions', 'orders', 'deals', 
            'trading_history', 'position_history',
            'mt5_trades', 'mt5_positions'
        ]
        
        for table_name in tables_to_check:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                if count > 0:
                    cursor.execute(f"DELETE FROM {table_name}")
                    deleted = cursor.rowcount
                    print(f"✅ 清理{table_name}表: {deleted}条记录")
            except:
                # 表不存在，跳过
                pass
        
        conn.commit()
        conn.close()
        
        print("✅ 所有交易数据清理完成")
        return True
        
    except Exception as e:
        print(f"❌ 清理数据失败: {e}")
        return False

def check_api_endpoints():
    """检查API端点可能返回的数据"""
    print("\n🔍 检查API端点")
    print("=" * 50)
    
    try:
        import requests
        
        # 测试相关API端点
        endpoints = [
            '/api/deep-learning/trading-statistics',
            '/api/deep-learning/position-details',
            '/api/mt5/positions'
        ]
        
        for endpoint in endpoints:
            try:
                response = requests.get(f'http://127.0.0.1:5000{endpoint}', timeout=5)
                if response.status_code == 200:
                    data = response.json()
                    print(f"✅ {endpoint}: {json.dumps(data, indent=2, ensure_ascii=False)}")
                else:
                    print(f"❌ {endpoint}: HTTP {response.status_code}")
            except Exception as e:
                print(f"❌ {endpoint}: {e}")
                
    except ImportError:
        print("ℹ️  requests库未安装，跳过API检查")
    except Exception as e:
        print(f"❌ API检查失败: {e}")

def check_frontend_cache():
    """检查前端可能的缓存问题"""
    print("\n🔍 检查前端缓存问题")
    print("=" * 50)
    
    print("💡 可能的前端缓存问题:")
    print("1. 浏览器缓存了旧的API响应")
    print("2. JavaScript变量中缓存了旧数据")
    print("3. 页面没有正确刷新数据")
    print()
    
    print("🔧 建议的解决方案:")
    print("1. 强制刷新浏览器 (Ctrl+F5)")
    print("2. 清除浏览器缓存")
    print("3. 重启应用程序")
    print("4. 检查浏览器控制台的错误信息")
    print("5. 检查网络请求是否返回正确数据")

def generate_cleanup_script():
    """生成前端清理脚本"""
    print("\n🔧 生成前端清理脚本")
    print("=" * 50)
    
    script = """
// 在浏览器控制台中执行以下代码来清理前端缓存

// 1. 清理交易统计数据
tradingStatistics = {
    todayTrades: 0,
    currentPositions: 0,
    totalProfit: 0
};

// 2. 更新显示
document.getElementById('todayTrades').textContent = '0';
document.getElementById('currentPositions').textContent = '0';

// 3. 隐藏持仓详情
const positionSection = document.getElementById('positionDetailsSection');
if (positionSection) {
    positionSection.style.display = 'none';
}

// 4. 清空持仓卡片容器
const cardsContainer = document.getElementById('positionCardsContainer');
if (cardsContainer) {
    cardsContainer.innerHTML = '<div class="col-12 text-center text-muted py-4" id="noPositionsMessage"><i class="fas fa-inbox fa-3x mb-3"></i><p>暂无持仓</p></div>';
}

// 5. 强制刷新交易统计
updateTradingStatistics();

console.log('✅ 前端数据已清理');
"""
    
    print("📋 前端清理脚本:")
    print(script)
    
    # 保存到文件
    with open('frontend_cleanup.js', 'w', encoding='utf-8') as f:
        f.write(script)
    
    print("✅ 脚本已保存到 frontend_cleanup.js")

def main():
    """主函数"""
    print("🔧 深度清理持仓数据工具")
    print("=" * 80)
    
    print("📋 问题描述:")
    print("• 界面仍显示3个XAUUSD持仓")
    print("• 之前的数据库清理可能不完整")
    print("• 可能存在前端缓存问题")
    print()
    
    # 检查所有数据库
    databases = check_all_databases()
    
    # 检查其他表
    check_other_tables()
    
    # 清理所有数据
    cleanup_success = clear_all_trading_data()
    
    # 检查API端点
    check_api_endpoints()
    
    # 检查前端缓存
    check_frontend_cache()
    
    # 生成清理脚本
    generate_cleanup_script()
    
    print("\n📊 深度清理总结")
    print("=" * 80)
    
    if cleanup_success:
        print("✅ 数据库清理完成")
        print("✅ 所有交易相关表已清空")
        print("✅ 前端清理脚本已生成")
        print()
        print("💡 下一步操作:")
        print("1. 重启应用程序")
        print("2. 强制刷新浏览器 (Ctrl+F5)")
        print("3. 如果仍有问题，在浏览器控制台执行 frontend_cleanup.js 中的代码")
        print("4. 检查浏览器网络请求，确认API返回正确数据")
    else:
        print("❌ 数据库清理失败")
        print("💡 请检查数据库权限和连接")
    
    return 0 if cleanup_success else 1

if __name__ == "__main__":
    exit(main())
