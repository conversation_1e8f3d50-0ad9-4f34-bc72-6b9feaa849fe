#!/usr/bin/env python3
"""
诊断AI推理交易有推理结果但没有产生订单的问题
"""

import requests
import json
import time

def login_session():
    """登录并返回session"""
    session = requests.Session()
    login_data = {'username': 'admin', 'password': 'admin123'}
    
    try:
        response = session.post('http://127.0.0.1:5000/login', data=login_data)
        if response.status_code == 200:
            print("✅ 登录成功")
            return session
        else:
            print(f"❌ 登录失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return None

def check_trading_config(session):
    """检查当前交易配置"""
    print("🔍 检查交易配置")
    print("=" * 50)
    
    # 这里我们需要模拟获取页面配置，因为配置在前端
    # 根据您的描述，推理结果是SELL，95%置信度
    
    print("📊 推理结果分析:")
    print("   预测: SELL")
    print("   置信度: 95.0%")
    print("   时间: 04:00:00")
    print("   目标价格: 3289.42")
    
    print("\n🔧 需要检查的交易条件:")
    print("   1. 最低置信度要求")
    print("   2. 当前持仓数 vs 最大持仓数")
    print("   3. 自动交易是否启用")
    print("   4. 交易时间段限制")
    print("   5. MT5连接状态")
    print("   6. 交易模型选择状态")
    
    return True

def check_auto_trading_status(session):
    """检查自动交易状态"""
    print("\n🔍 检查自动交易状态")
    print("=" * 50)
    
    try:
        response = session.get('http://127.0.0.1:5000/api/deep-learning/auto-trading/status')
        
        if response.status_code == 200:
            result = response.json()
            print(f"📊 自动交易状态: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            if result.get('success'):
                is_active = result.get('active', False)
                print(f"   自动交易状态: {'✅ 活跃' if is_active else '❌ 未启动'}")
                
                if is_active:
                    model_info = result.get('model_info', {})
                    print(f"   使用模型: {model_info.get('name', 'N/A')}")
                    print(f"   交易品种: {model_info.get('symbol', 'N/A')}")
                    return True
                else:
                    print("   ⚠️ 自动交易未启动 - 这可能是没有下单的原因")
                    return False
            else:
                print(f"   ❌ 获取状态失败: {result.get('error')}")
                return False
        else:
            print(f"   ❌ HTTP请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 检查自动交易状态失败: {e}")
        return False

def check_mt5_connection(session):
    """检查MT5连接状态"""
    print("\n🔍 检查MT5连接状态")
    print("=" * 50)
    
    try:
        response = session.get('http://127.0.0.1:5000/api/mt5/status')
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('connected'):
                print("✅ MT5连接正常")
                account_info = result.get('account_info', {}).get('account_info', {})
                print(f"   账户类型: {account_info.get('account_type', 'N/A')}")
                print(f"   余额: {account_info.get('balance', 'N/A')}")
                print(f"   允许交易: {account_info.get('trade_allowed', 'N/A')}")
                return True
            else:
                print("❌ MT5连接异常")
                return False
        else:
            print(f"❌ 无法获取MT5状态: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 检查MT5连接失败: {e}")
        return False

def check_current_positions(session):
    """检查当前持仓"""
    print("\n🔍 检查当前持仓")
    print("=" * 50)
    
    try:
        response = session.get('http://127.0.0.1:5000/api/deep-learning/trading-statistics')
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                stats = result.get('statistics', {})
                current_positions = stats.get('current_positions', 0)
                today_trades = stats.get('today_trades', 0)
                
                print(f"📊 交易统计:")
                print(f"   当前持仓数: {current_positions}")
                print(f"   今日交易数: {today_trades}")
                
                # 假设最大持仓数是4（根据之前的修改）
                max_positions = 4
                print(f"   最大持仓数: {max_positions}")
                
                if current_positions >= max_positions:
                    print(f"   ⚠️ 已达到最大持仓数限制 - 这可能是没有下单的原因")
                    return False
                else:
                    print(f"   ✅ 持仓数未达到上限，可以继续交易")
                    return True
            else:
                print(f"❌ 获取统计失败: {result.get('error')}")
                return False
        else:
            print(f"❌ 无法获取交易统计: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 检查当前持仓失败: {e}")
        return False

def test_trade_execution_api(session):
    """测试交易执行API"""
    print("\n🔍 测试交易执行API")
    print("=" * 50)
    
    try:
        # 模拟交易数据（基于您提供的推理结果）
        test_trade_data = {
            'symbol': 'XAUUSD',
            'action': 'SELL',
            'lot_size': 0.01,
            'stop_loss_pips': 50,
            'take_profit_pips': 100,
            'inference_result': {
                'prediction': 'SELL',
                'confidence': 0.95,
                'current_price': 3289.42,
                'timestamp': '04:00:00'
            },
            'trading_config': {
                'lot_size': 0.01,
                'min_confidence': 0.3,
                'max_positions': 4,
                'stop_loss_pips': 50,
                'take_profit_pips': 100
            }
        }
        
        print(f"📊 测试交易数据:")
        print(f"   品种: {test_trade_data['symbol']}")
        print(f"   方向: {test_trade_data['action']}")
        print(f"   手数: {test_trade_data['lot_size']}")
        print(f"   置信度: {test_trade_data['inference_result']['confidence']*100}%")
        
        print(f"\n🚀 发送交易执行请求...")
        
        response = session.post('http://127.0.0.1:5000/api/deep-learning/execute-trade', 
                               json=test_trade_data)
        
        print(f"   HTTP状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"   API响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            if result.get('success'):
                print(f"   ✅ 交易执行API正常工作")
                return True
            else:
                print(f"   ❌ 交易执行失败: {result.get('error')}")
                return False
        else:
            print(f"   ❌ HTTP请求失败")
            print(f"   响应内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试交易执行API失败: {e}")
        return False

def analyze_possible_causes():
    """分析可能的原因"""
    print("\n💡 可能的原因分析")
    print("=" * 50)
    
    print("🔍 根据推理结果 SELL 95% 置信度但没有下单，可能的原因:")
    print()
    
    print("1️⃣ 自动交易未启动")
    print("   • 检查是否点击了'开始AI交易'按钮")
    print("   • 确认自动交易状态为'运行中'")
    print()
    
    print("2️⃣ 置信度配置问题")
    print("   • 当前最低置信度要求可能高于95%")
    print("   • 检查配置中的最低置信度设置")
    print()
    
    print("3️⃣ 持仓数限制")
    print("   • 当前持仓数可能已达到最大持仓数限制")
    print("   • 默认最大持仓数为4个")
    print()
    
    print("4️⃣ 交易时间限制")
    print("   • 04:00:00可能不在允许的交易时间段内")
    print("   • 检查交易时间段配置")
    print()
    
    print("5️⃣ MT5连接问题")
    print("   • MT5可能未连接或连接异常")
    print("   • 检查MT5终端状态")
    print()
    
    print("6️⃣ 交易模式配置")
    print("   • 可能设置为'仅信号'模式而不是'自动交易'")
    print("   • 检查交易模式设置")
    print()
    
    print("7️⃣ 前端JavaScript错误")
    print("   • 浏览器控制台可能有JavaScript错误")
    print("   • 检查浏览器开发者工具的控制台")

def provide_solutions():
    """提供解决方案"""
    print("\n🔧 解决方案建议")
    print("=" * 50)
    
    print("📋 立即检查步骤:")
    print()
    
    print("1️⃣ 检查自动交易状态")
    print("   • 进入AI推理交易页面")
    print("   • 确认显示'交易状态：运行中'")
    print("   • 如果显示'未启动'，点击'开始AI交易'")
    print()
    
    print("2️⃣ 检查浏览器控制台")
    print("   • 按F12打开开发者工具")
    print("   • 查看Console标签页")
    print("   • 寻找错误信息或交易相关日志")
    print()
    
    print("3️⃣ 检查配置设置")
    print("   • 最低置信度：应该 ≤ 95%")
    print("   • 最大持仓数：检查当前持仓是否已满")
    print("   • 交易时间段：确认04:00在允许时间内")
    print()
    
    print("4️⃣ 重新启动交易")
    print("   • 停止当前自动交易")
    print("   • 等待几秒钟")
    print("   • 重新启动自动交易")
    print()
    
    print("5️⃣ 检查MT5状态")
    print("   • 确认MT5终端正在运行")
    print("   • 检查网络连接")
    print("   • 确认账户允许交易")

def main():
    """主函数"""
    print("🔧 诊断AI推理交易无订单问题")
    print("=" * 80)
    
    print("📋 问题描述:")
    print("• 推理结果正常：SELL 95% 置信度")
    print("• 但没有产生实际订单")
    print("• 需要检查交易执行链路")
    
    # 1. 登录
    session = login_session()
    if not session:
        return
    
    # 2. 检查各个环节
    config_ok = check_trading_config(session)
    auto_trading_ok = check_auto_trading_status(session)
    mt5_ok = check_mt5_connection(session)
    positions_ok = check_current_positions(session)
    api_ok = test_trade_execution_api(session)
    
    # 3. 分析原因
    analyze_possible_causes()
    
    # 4. 提供解决方案
    provide_solutions()
    
    # 5. 总结
    print(f"\n📊 诊断结果总结")
    print("=" * 80)
    
    checks = [
        ("交易配置", config_ok),
        ("自动交易状态", auto_trading_ok),
        ("MT5连接", mt5_ok),
        ("持仓检查", positions_ok),
        ("交易API", api_ok)
    ]
    
    for name, result in checks:
        status = "✅ 正常" if result else "❌ 异常"
        print(f"   {name}: {status}")
    
    failed_checks = [name for name, result in checks if not result]
    
    if failed_checks:
        print(f"\n⚠️ 发现问题环节: {', '.join(failed_checks)}")
        print(f"💡 建议优先检查这些环节")
    else:
        print(f"\n✅ 所有检查项目正常")
        print(f"💡 问题可能在前端JavaScript或配置细节")

if __name__ == '__main__':
    main()
