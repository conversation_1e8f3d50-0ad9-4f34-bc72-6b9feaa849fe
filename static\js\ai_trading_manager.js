/*!
 * AI交易全局状态管理器
 * 解决页面跳转时AI交易状态丢失的问题
 */

// 全局AI交易管理器
window.AITradingManager = (function() {
    'use strict';

    // 私有变量
    let aiTradingInterval = null;
    let timeCheckInterval = null;
    let runningTimeInterval = null;
    let isInitialized = false;

    // 状态键名
    const STATE_KEY = 'aiTradingGlobalState';
    const DEMO_STATE_KEY = 'aiTradingDemoState';
    const REAL_STATE_KEY = 'aiTradingRealState';

    // 默认状态
    const defaultState = {
        active: false,
        strategyId: null,
        riskLevel: 'medium',
        minLotSize: 0.01,
        maxLotSize: 0.04,
        tradingSymbol: 'EURUSD',
        tradingTimeSlot: '9:00-18:00',
        accountType: 'demo',
        startTime: null,
        lastExecutionTime: null,
        executionCount: 0,
        todayTradeCount: 0,
        maxTradeCount: 30, // 单次运行交易次数限制
        currentRunTradeCount: 0, // 当前运行已执行的交易次数
        newOrdersDisabled: false, // 是否禁止新增订单
        positionManagementOnly: false // 是否仅进行持仓管理模式
    };

    // 获取状态存储键
    function getStateKey(accountType) {
        return accountType === 'demo' ? DEMO_STATE_KEY : REAL_STATE_KEY;
    }

    // 保存状态到localStorage
    function saveState(state) {
        try {
            const stateKey = getStateKey(state.accountType);
            localStorage.setItem(stateKey, JSON.stringify(state));
            localStorage.setItem(STATE_KEY, JSON.stringify(state)); // 通用状态
            console.log('AI交易状态已保存:', state);
        } catch (error) {
            console.error('保存AI交易状态失败:', error);
        }
    }

    // 从localStorage加载状态
    function loadState(accountType = 'demo') {
        try {
            const stateKey = getStateKey(accountType);
            const saved = localStorage.getItem(stateKey);
            if (saved) {
                const state = JSON.parse(saved);
                return { ...defaultState, ...state };
            }
        } catch (error) {
            console.error('加载AI交易状态失败:', error);
        }
        return { ...defaultState, accountType };
    }

    // 清除状态
    function clearState(accountType) {
        const stateKey = getStateKey(accountType);
        localStorage.removeItem(stateKey);
        if (accountType === getCurrentAccountType()) {
            localStorage.removeItem(STATE_KEY);
        }
    }

    // 获取当前账户类型
    function getCurrentAccountType() {
        // 根据当前页面URL判断账户类型
        if (window.location.pathname.includes('/trading/demo')) {
            return 'demo';
        } else if (window.location.pathname.includes('/trading/real')) {
            return 'real';
        }
        return 'demo'; // 默认为demo
    }

    // 获取当前选择的账户ID
    function getCurrentAccountId() {
        try {
            // 尝试从页面获取当前选择的账户ID
            const accountSelect = document.getElementById('accountSelect');
            if (accountSelect && accountSelect.value) {
                console.log('✅ 获取到账户ID:', accountSelect.value);
                return accountSelect.value;
            }

            // 如果没有选择账户，对于真实交易，尝试使用默认的MT5账户
            const accountType = getCurrentAccountType();
            if (accountType === 'real') {
                console.log('⚠️ 真实交易未选择账户，使用默认MT5账户');
                return 'mt5_default'; // 使用默认MT5账户标识
            }

            console.warn('⚠️ 未选择账户，返回null');
            return null;
        } catch (error) {
            console.error('❌ 获取当前账户ID失败:', error);
            return null;
        }
    }

    // 检查是否在交易时间内 (使用中国标准时间)
    function isInTradingTime(timeSlot) {
        if (!timeSlot || timeSlot === 'all' || timeSlot === '24小时') return true;

        // 获取中国标准时间 (UTC+8)
        const now = new Date();
        const utc = now.getTime() + (now.getTimezoneOffset() * 60000);
        const chinaTime = new Date(utc + (8 * 3600000)); // UTC+8

        const currentTime = chinaTime.getHours() * 100 + chinaTime.getMinutes();

        console.log(`当前中国标准时间: ${chinaTime.toLocaleString('zh-CN', {timeZone: 'Asia/Shanghai'})}`);
        console.log(`当前时间数值: ${currentTime}, 交易时间段: ${timeSlot}`);

        const timeRanges = {
            '9:00-12:00': [900, 1200],
            '13:00-16:00': [1300, 1600],
            '15:00-18:00': [1500, 1800],
            '9:00-18:00': [900, 1800],
            '24小时': [0, 2359]
        };

        const range = timeRanges[timeSlot];
        if (!range) {
            console.log(`未知的时间段: ${timeSlot}, 默认允许交易`);
            return true;
        }

        const inRange = currentTime >= range[0] && currentTime <= range[1];
        console.log(`时间段检查: ${range[0]}-${range[1]}, 当前: ${currentTime}, 结果: ${inRange}`);

        return inRange;
    }

    // 执行AI交易决策
    function executeAITradingDecision(state) {
        console.log('执行AI交易决策...', state);

        // 如果处于持仓管理模式，只进行持仓管理，不执行新的交易决策
        if (state.positionManagementOnly) {
            console.log('🔄 持仓管理模式：检查和管理现有持仓...');
            manageExistingPositions(state);
            return;
        }

        const apiEndpoint = state.accountType === 'demo' ?
            '/api/ai-trading/analyze' : '/api/ai-trading/analyze';

        fetch(apiEndpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                strategy_id: state.strategyId,
                risk_level: state.riskLevel,
                min_lot_size: state.minLotSize,
                max_lot_size: state.maxLotSize,
                trading_symbol: state.tradingSymbol,
                account_type: state.accountType,
                position_management_only: state.positionManagementOnly || false
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success && data.recommendation) {
                const rec = data.recommendation;

                // 如果AI建议执行交易
                if (rec.action !== 'hold') {
                    executeAITrade(rec, state).catch(error => {
                        console.error('❌ 执行AI交易异常:', error);
                    });
                } else {
                    console.log('AI策略建议持有，暂不交易');
                }

                // 更新执行统计
                state.lastExecutionTime = Date.now();
                state.executionCount++;
                saveState(state);

            } else {
                console.error('AI分析失败:', data.error);
            }
        })
        .catch(error => {
            console.error('AI交易分析请求失败:', error);
        });
    }

    // 获取当前市场价格
    async function getCurrentMarketPrice(symbol) {
        try {
            const response = await fetch(`/api/market-price/${symbol}`);
            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    return data.price;
                }
            }
        } catch (error) {
            console.error('获取市场价格失败:', error);
        }
        return null;
    }

    // 获取默认价格（基于交易品种）
    function getDefaultPrice(symbol) {
        // 根据不同的交易品种返回合理的默认价格
        if (symbol.includes('XAU') || symbol.includes('GOLD')) {
            return 2000; // 黄金默认价格
        } else if (symbol.includes('XAG') || symbol.includes('SILVER')) {
            return 25; // 白银默认价格
        } else if (symbol.includes('USD') || symbol.includes('EUR') || symbol.includes('GBP')) {
            return 1.1; // 外汇默认价格
        } else if (symbol.includes('JPY')) {
            return 110; // 日元相关默认价格
        } else {
            return 1.0; // 其他品种默认价格
        }
    }

    // 验证止损止盈价格的合理性
    function validateStopLossTakeProfit(currentPrice, stopLoss, takeProfit, action) {
        if (!currentPrice || currentPrice <= 0) {
            return { valid: false, reason: '当前价格无效' };
        }

        if (!stopLoss || !takeProfit || stopLoss <= 0 || takeProfit <= 0) {
            return { valid: false, reason: '止损止盈价格无效' };
        }

        // 检查买单的止损止盈逻辑
        if (action === 'buy') {
            if (stopLoss >= currentPrice) {
                return { valid: false, reason: '买单止损价格应低于当前价格' };
            }
            if (takeProfit <= currentPrice) {
                return { valid: false, reason: '买单止盈价格应高于当前价格' };
            }
        } else if (action === 'sell') {
            if (stopLoss <= currentPrice) {
                return { valid: false, reason: '卖单止损价格应高于当前价格' };
            }
            if (takeProfit >= currentPrice) {
                return { valid: false, reason: '卖单止盈价格应低于当前价格' };
            }
        }

        // 检查价格差异是否合理（不能太小或太大）
        const stopLossDiff = Math.abs(currentPrice - stopLoss) / currentPrice;
        const takeProfitDiff = Math.abs(currentPrice - takeProfit) / currentPrice;

        if (stopLossDiff < 0.001) { // 小于0.1%
            return { valid: false, reason: '止损价格与当前价格太接近' };
        }
        if (takeProfitDiff < 0.001) { // 小于0.1%
            return { valid: false, reason: '止盈价格与当前价格太接近' };
        }
        if (stopLossDiff > 0.1) { // 大于10%
            return { valid: false, reason: '止损价格与当前价格差异过大' };
        }
        if (takeProfitDiff > 0.1) { // 大于10%
            return { valid: false, reason: '止盈价格与当前价格差异过大' };
        }

        return { valid: true, reason: '价格验证通过' };
    }

    // 执行AI推荐的交易
    async function executeAITrade(recommendation, state) {
        // 使用用户选择的货币对，而不是AI推荐的货币对
        const symbol = state.tradingSymbol || recommendation.symbol;

        // 计算智能止盈止损
        const stopLossPercent = getStopLossPercent(state.riskLevel);
        const takeProfitPercent = getTakeProfitPercent(state.riskLevel);

        // 获取当前市场价格
        let currentPrice;
        try {
            currentPrice = await getCurrentMarketPrice(symbol);
            if (!currentPrice) {
                console.warn('⚠️ 无法获取当前价格，使用默认价格');
                currentPrice = getDefaultPrice(symbol);
            }
        } catch (error) {
            console.error('❌ 获取价格异常:', error);
            currentPrice = getDefaultPrice(symbol);
        }

        console.log(`📊 ${symbol} 当前价格: ${currentPrice}`);

        let stopLoss, takeProfit;
        if (recommendation.action === 'buy') {
            stopLoss = currentPrice * (1 - stopLossPercent / 100);
            takeProfit = currentPrice * (1 + takeProfitPercent / 100);
        } else { // sell
            stopLoss = currentPrice * (1 + stopLossPercent / 100);
            takeProfit = currentPrice * (1 - takeProfitPercent / 100);
        }

        // 确保止损止盈价格合理并验证
        stopLoss = Math.round(stopLoss * 100) / 100;
        takeProfit = Math.round(takeProfit * 100) / 100;

        // 验证止损止盈价格的合理性
        const priceValidation = validateStopLossTakeProfit(currentPrice, stopLoss, takeProfit, recommendation.action);
        if (!priceValidation.valid) {
            console.warn('⚠️ 止损止盈价格不合理，禁用止损止盈:', priceValidation.reason);
            stopLoss = 0;
            takeProfit = 0;
        }

        const tradeData = {
            symbol: symbol, // 优先使用用户选择的货币对
            side: recommendation.action,
            amount: recommendation.amount,
            type: 'market',
            ai_generated: true,
            strategy_id: recommendation.strategy_id,
            stop_loss: stopLoss, // 使用计算的止损价格
            take_profit: takeProfit, // 使用计算的止盈价格
            account_type: state.accountType, // 添加账户类型
            account_id: getCurrentAccountId() // 添加账户ID
        };

        console.log('执行AI交易 (用户选择的货币对):', tradeData);
        console.log('用户选择的货币对:', state.tradingSymbol);
        console.log('AI推荐的货币对:', recommendation.symbol);
        console.log(`止损价格: ${stopLoss}, 止盈价格: ${takeProfit}, 当前价格: ${currentPrice}`);

        // 真实交易使用通用的place-order端点，模拟交易使用demo-trading/order端点
        const orderEndpoint = state.accountType === 'demo' ?
            '/api/demo-trading/order' : '/api/place-order';

        fetch(orderEndpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(tradeData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('✅ AI交易执行成功:', data);
                console.log('📊 订单详情:', data.order);
                console.log('💬 执行消息:', data.message);

                // 更新交易计数
                state.todayTradeCount++;
                state.currentRunTradeCount++;
                saveState(state);

                console.log(`📈 交易计数更新: 今日 ${state.todayTradeCount}, 本次运行 ${state.currentRunTradeCount}`);

                // 通知UI刷新数据
                notifyUIUpdate('tradeExecuted', {
                    state: state,
                    orderData: data.order,
                    accountType: state.accountType
                });

                // 检查是否达到单次运行交易次数限制
                if (state.maxTradeCount && state.currentRunTradeCount >= state.maxTradeCount && !state.positionManagementOnly) {
                    console.log(`🛑 已达到单次运行交易次数限制: ${state.currentRunTradeCount}/${state.maxTradeCount}`);
                    console.log(`🔄 切换到持仓管理模式，停止新增订单，继续管理现有持仓`);

                    // 切换到持仓管理模式，而不是完全停止
                    state.newOrdersDisabled = true;
                    state.positionManagementOnly = true;
                    saveState(state);

                    // 通知UI进入持仓管理模式
                    notifyUIUpdate('tradeCountLimitReached', {
                        currentCount: state.currentRunTradeCount,
                        limit: state.maxTradeCount,
                        state: state,
                        positionManagementMode: true
                    });

                    // 继续执行，但只进行持仓管理
                }

                // 通知UI更新
                notifyUIUpdate('tradeExecuted', { trade: data, state: state });

            } else {
                console.error('❌ AI交易执行失败:', data.error);
                console.error('📋 失败的交易数据:', tradeData);

                // 通知UI显示错误
                notifyUIUpdate('tradeError', {
                    error: data.error,
                    tradeData: tradeData
                });
            }
        })
        .catch(error => {
            console.error('❌ AI交易请求失败:', error);
            console.error('📋 请求的交易数据:', tradeData);
            console.error('🔗 请求的端点:', orderEndpoint);

            // 通知UI显示网络错误
            notifyUIUpdate('tradeNetworkError', {
                error: error.message,
                tradeData: tradeData
            });
        });
    }

    // 管理现有持仓
    function manageExistingPositions(state) {
        console.log('🔄 管理现有持仓...');

        // 获取当前持仓
        const positionsEndpoint = state.accountType === 'demo' ?
            '/api/demo-trading/positions' : '/api/trading-accounts/positions';

        fetch(positionsEndpoint)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.positions && data.positions.length > 0) {
                console.log(`📊 当前有 ${data.positions.length} 个持仓需要管理`);

                // 检查每个持仓的止盈止损条件
                data.positions.forEach(position => {
                    checkPositionStopLossTakeProfit(position, state);
                });

                // 通知UI更新持仓信息
                notifyUIUpdate('positionsManaged', {
                    positions: data.positions,
                    state: state
                });
            } else {
                console.log('📭 没有需要管理的持仓');

                // 如果没有持仓了，可以完全停止AI交易
                if (state.positionManagementOnly) {
                    console.log('✅ 所有持仓已处理完毕，停止AI交易');
                    stopAITrading(state.accountType);
                    notifyUIUpdate('allPositionsClosed', { state: state });
                }
            }
        })
        .catch(error => {
            console.error('获取持仓信息失败:', error);
        });
    }

    // 检查持仓的止盈止损条件
    function checkPositionStopLossTakeProfit(position, state) {
        // 这里可以实现智能止盈止损逻辑
        // 暂时只记录日志，具体实现可以根据需要添加
        console.log(`🔍 检查持仓 ${position.trade_id} 的止盈止损条件`);

        // 可以在这里添加：
        // 1. 检查当前价格与开仓价格的差异
        // 2. 根据AI策略决定是否平仓
        // 3. 执行止盈止损操作
    }

    // 通知UI更新
    function notifyUIUpdate(event, data) {
        // 触发自定义事件，让页面监听并更新UI
        window.dispatchEvent(new CustomEvent('aiTradingUpdate', {
            detail: { event, data }
        }));
    }

    // 检查交易时间
    function checkTradingTime(state) {
        if (!isInTradingTime(state.tradingTimeSlot)) {
            console.log('超出交易时间段，自动停止AI交易');
            stopAITrading(state.accountType);
            notifyUIUpdate('tradingTimeStopped', { timeSlot: state.tradingTimeSlot });
        }
    }

    // 启动AI交易
    function startAITrading(config) {
        const accountType = config.accountType || getCurrentAccountType();
        const state = {
            ...defaultState,
            ...config,
            accountType: accountType,
            active: true,
            startTime: Date.now(),
            currentRunTradeCount: 0 // 重置当前运行交易次数
        };

        // 检查交易时间
        if (!isInTradingTime(state.tradingTimeSlot)) {
            notifyUIUpdate('tradingTimeError', { timeSlot: state.tradingTimeSlot });
            return false;
        }

        // 停止之前的交易（如果有）
        stopAITrading(accountType);

        // 保存状态
        saveState(state);

        // 设置交易执行定时器
        const interval = state.accountType === 'demo' ? 30000 : 60000; // demo 30秒，real 60秒
        aiTradingInterval = setInterval(() => {
            const currentState = loadState(accountType);
            if (currentState.active) {
                checkTradingTime(currentState);
                if (currentState.active) {
                    executeAITradingDecision(currentState);
                    // 同时更新持仓状态
                    updatePositions(currentState.accountType);
                }
            } else {
                clearInterval(aiTradingInterval);
                aiTradingInterval = null;
            }
        }, interval);

        // 设置时间检查定时器
        timeCheckInterval = setInterval(() => {
            const currentState = loadState(accountType);
            if (currentState.active) {
                checkTradingTime(currentState);
            } else {
                clearInterval(timeCheckInterval);
                timeCheckInterval = null;
            }
        }, 60000); // 每分钟检查一次

        // 设置运行时间更新定时器
        runningTimeInterval = setInterval(() => {
            notifyUIUpdate('runningTimeUpdate', { state: loadState(accountType) });
        }, 1000);

        // 立即执行一次
        executeAITradingDecision(state);

        // 通知UI更新
        notifyUIUpdate('tradingStarted', { state: state });

        console.log('AI交易已启动:', state);
        return true;
    }

    // 停止AI交易
    function stopAITrading(accountType) {
        accountType = accountType || getCurrentAccountType();

        // 清除定时器
        if (aiTradingInterval) {
            clearInterval(aiTradingInterval);
            aiTradingInterval = null;
        }

        if (timeCheckInterval) {
            clearInterval(timeCheckInterval);
            timeCheckInterval = null;
        }

        if (runningTimeInterval) {
            clearInterval(runningTimeInterval);
            runningTimeInterval = null;
        }

        // 更新状态
        const state = loadState(accountType);
        state.active = false;
        saveState(state);

        // 通知UI更新
        notifyUIUpdate('tradingStopped', { state: state });

        console.log('AI交易已停止:', accountType);
    }

    // 获取当前状态
    function getCurrentState(accountType) {
        return loadState(accountType || getCurrentAccountType());
    }

    // 检查是否正在运行
    function isRunning(accountType) {
        const state = loadState(accountType || getCurrentAccountType());
        return state.active;
    }

    // 初始化管理器
    function initialize() {
        if (isInitialized) return;

        console.log('🔄 初始化AI交易管理器...');

        // 检查是否有活跃的交易需要恢复
        const accountType = getCurrentAccountType();
        const state = loadState(accountType);

        console.log('📊 加载的AI交易状态:', state);

        if (state.active) {
            console.log('✅ 检测到活跃的AI交易，正在恢复...');

            // 检查交易时间
            if (isInTradingTime(state.tradingTimeSlot)) {
                console.log('✅ 交易时间有效，恢复AI交易...');

                // 恢复交易，但不重置计数器
                const restoreConfig = {
                    ...state,
                    // 保持原有的交易次数统计
                    currentRunTradeCount: state.currentRunTradeCount || 0
                };

                // 直接恢复状态而不是重新启动
                restoreAITradingState(restoreConfig);
            } else {
                console.log('⏰ 超出交易时间，停止AI交易');
                // 超出交易时间，停止交易
                state.active = false;
                saveState(state);
                notifyUIUpdate('tradingTimeExpired', { state: state });
            }
        } else {
            console.log('ℹ️ 没有活跃的AI交易需要恢复');
        }

        isInitialized = true;
        console.log('🎉 AI交易管理器初始化完成');
    }

    // 恢复AI交易状态（不重置计数器）
    function restoreAITradingState(state) {
        console.log('🔄 恢复AI交易状态:', state);

        // 保存当前状态
        saveState(state);

        // 设置交易执行定时器
        const interval = state.accountType === 'demo' ? 30000 : 60000;
        aiTradingInterval = setInterval(() => {
            const currentState = loadState(state.accountType);
            if (currentState.active) {
                checkTradingTime(currentState);
                if (currentState.active) {
                    executeAITradingDecision(currentState);
                    updatePositions(currentState.accountType);
                }
            } else {
                clearInterval(aiTradingInterval);
                aiTradingInterval = null;
            }
        }, interval);

        // 设置时间检查定时器
        timeCheckInterval = setInterval(() => {
            const currentState = loadState(state.accountType);
            if (currentState.active) {
                checkTradingTime(currentState);
            } else {
                clearInterval(timeCheckInterval);
                timeCheckInterval = null;
            }
        }, 60000);

        // 设置运行时间更新定时器
        runningTimeInterval = setInterval(() => {
            notifyUIUpdate('runningTimeUpdate', { state: loadState(state.accountType) });
        }, 1000);

        // 通知UI更新（恢复状态）
        notifyUIUpdate('tradingRestored', { state: state });

        console.log('✅ AI交易状态恢复完成');
    }

    // 页面卸载时的清理
    function cleanup() {
        // 注意：不要在这里停止交易，只清理定时器
        // 交易状态应该保持，以便在其他页面恢复
        console.log('AI交易管理器清理...');
    }

    // 更新持仓状态
    function updatePositions(accountType) {
        const endpoint = accountType === 'demo' ?
            '/api/demo-trading/update-positions' : '/api/real-trading/update-positions';

        fetch(endpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 如果有自动平仓的交易，通知UI
                if (data.closed_positions > 0) {
                    console.log(`自动平仓 ${data.closed_positions} 个持仓:`, data.closed_trades);
                    notifyUIUpdate('positionsUpdated', {
                        closedPositions: data.closed_positions,
                        closedTrades: data.closed_trades
                    });
                }
            }
        })
        .catch(error => {
            console.error('更新持仓状态失败:', error);
        });
    }

    // 根据风险等级获取止损百分比
    function getStopLossPercent(riskLevel) {
        const stopLossConfig = {
            'conservative': 1.0,  // 保守型: 1%止损
            'moderate': 1.0,      // 稳健型: 1%止损
            'aggressive': 1.5     // 激进型: 1.5%止损
        };
        return stopLossConfig[riskLevel] || 1.0;
    }

    // 根据风险等级获取止盈百分比
    function getTakeProfitPercent(riskLevel) {
        const takeProfitConfig = {
            'conservative': 1.5,  // 保守型: 1.5%止盈
            'moderate': 2.0,      // 稳健型: 2%止盈
            'aggressive': 3.0     // 激进型: 3%止盈
        };
        return takeProfitConfig[riskLevel] || 2.0;
    }

    // 监听页面卸载事件
    window.addEventListener('beforeunload', cleanup);

    // 公共API
    return {
        initialize: initialize,
        startAITrading: startAITrading,
        stopAITrading: stopAITrading,
        getCurrentState: getCurrentState,
        isRunning: isRunning,
        clearState: clearState,
        updatePositions: updatePositions,

        // 工具方法
        isInTradingTime: isInTradingTime,
        getCurrentAccountType: getCurrentAccountType
    };
})();

// 延迟初始化，等待页面状态恢复完成
document.addEventListener('DOMContentLoaded', function() {
    // 延迟初始化，确保页面状态恢复完成
    setTimeout(() => {
        console.log('🔄 延迟初始化AI交易管理器...');
        AITradingManager.initialize();
    }, 2000); // 延迟2秒，确保页面状态恢复完成
});
