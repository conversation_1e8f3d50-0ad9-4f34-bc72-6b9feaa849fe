import ccxt
import yfinance as yf
from models import TradingAccount, Trade, db
from datetime import datetime
import pandas as pd

class TradingService:
    def __init__(self):
        self.exchanges = {}
    
    def get_exchange(self, broker, api_key=None, api_secret=None, sandbox=True):
        """获取交易所连接"""
        if broker.lower() == 'binance':
            if broker not in self.exchanges:
                self.exchanges[broker] = ccxt.binance({
                    'apiKey': api_key,
                    'secret': api_secret,
                    'sandbox': sandbox,
                    'enableRateLimit': True,
                })
            return self.exchanges[broker]
        else:
            raise ValueError(f"不支持的交易所: {broker}")
    
    def get_account_info(self, account_id):
        """获取账户信息"""
        account = TradingAccount.query.get(account_id)
        if not account:
            return None
        
        try:
            exchange = self.get_exchange(
                account.broker, 
                account.api_key, 
                account.api_secret,
                sandbox=(account.account_type == 'demo')
            )
            
            balance = exchange.fetch_balance()
            
            # 更新账户信息
            account.balance = balance['total'].get('USDT', 0)
            account.equity = balance['free'].get('USDT', 0) + balance['used'].get('USDT', 0)
            db.session.commit()
            
            return {
                'balance': account.balance,
                'equity': account.equity,
                'free': balance['free'],
                'used': balance['used'],
                'total': balance['total']
            }
        except Exception as e:
            print(f"获取账户信息失败: {e}")
            return None
    
    def get_market_price(self, symbol):
        """获取市场价格"""
        try:
            # 使用yfinance获取实时价格
            ticker = yf.Ticker(symbol)
            data = ticker.history(period='1d', interval='1m')
            if not data.empty:
                return data['Close'].iloc[-1]
            return None
        except Exception as e:
            print(f"获取市场价格失败: {e}")
            return None
    
    def place_order(self, account_id, symbol, order_type, side, amount, price=None, stop_loss=None, take_profit=None):
        """下单"""
        account = TradingAccount.query.get(account_id)
        if not account:
            return {'success': False, 'error': '账户不存在'}
        
        try:
            exchange = self.get_exchange(
                account.broker,
                account.api_key,
                account.api_secret,
                sandbox=(account.account_type == 'demo')
            )
            
            # 下单
            if order_type == 'market':
                order = exchange.create_market_order(symbol, side, amount)
            elif order_type == 'limit':
                order = exchange.create_limit_order(symbol, side, amount, price)
            else:
                return {'success': False, 'error': '不支持的订单类型'}
            
            # 记录交易
            trade = Trade(
                account_id=account_id,
                symbol=symbol,
                trade_type=side,
                volume=amount,
                open_price=order.get('price', price),
                stop_loss=stop_loss,
                take_profit=take_profit,
                status='open'
            )
            db.session.add(trade)
            db.session.commit()
            
            return {
                'success': True,
                'order_id': order['id'],
                'trade_id': trade.id,
                'message': '订单已提交'
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def close_position(self, trade_id):
        """平仓"""
        trade = Trade.query.get(trade_id)
        if not trade or trade.status != 'open':
            return {'success': False, 'error': '交易不存在或已关闭'}
        
        try:
            account = TradingAccount.query.get(trade.account_id)
            exchange = self.get_exchange(
                account.broker,
                account.api_key,
                account.api_secret,
                sandbox=(account.account_type == 'demo')
            )
            
            # 平仓订单
            close_side = 'sell' if trade.trade_type == 'buy' else 'buy'
            order = exchange.create_market_order(trade.symbol, close_side, trade.volume)
            
            # 更新交易记录
            trade.close_price = order.get('price')
            trade.close_time = datetime.utcnow()
            trade.status = 'closed'
            
            # 计算盈亏
            if trade.trade_type == 'buy':
                trade.profit = (trade.close_price - trade.open_price) * trade.volume
            else:
                trade.profit = (trade.open_price - trade.close_price) * trade.volume
            
            db.session.commit()
            
            return {
                'success': True,
                'profit': trade.profit,
                'message': '平仓成功'
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def get_open_positions(self, account_id):
        """获取持仓"""
        trades = Trade.query.filter_by(account_id=account_id, status='open').all()
        positions = []
        
        for trade in trades:
            current_price = self.get_market_price(trade.symbol)
            if current_price:
                if trade.trade_type == 'buy':
                    unrealized_pnl = (current_price - trade.open_price) * trade.volume
                else:
                    unrealized_pnl = (trade.open_price - current_price) * trade.volume
            else:
                unrealized_pnl = 0
            
            positions.append({
                'trade_id': trade.id,
                'symbol': trade.symbol,
                'side': trade.trade_type,
                'volume': trade.volume,
                'open_price': trade.open_price,
                'current_price': current_price,
                'unrealized_pnl': unrealized_pnl,
                'open_time': trade.open_time.isoformat()
            })
        
        return positions
    
    def get_trade_history(self, account_id, limit=50):
        """获取交易历史"""
        trades = Trade.query.filter_by(account_id=account_id).order_by(
            Trade.open_time.desc()
        ).limit(limit).all()
        
        history = []
        for trade in trades:
            history.append({
                'trade_id': trade.id,
                'symbol': trade.symbol,
                'side': trade.trade_type,
                'volume': trade.volume,
                'open_price': trade.open_price,
                'close_price': trade.close_price,
                'profit': trade.profit,
                'status': trade.status,
                'open_time': trade.open_time.isoformat(),
                'close_time': trade.close_time.isoformat() if trade.close_time else None
            })
        
        return history
    
    def calculate_account_performance(self, account_id):
        """计算账户表现"""
        trades = Trade.query.filter_by(account_id=account_id, status='closed').all()
        
        if not trades:
            return {
                'total_trades': 0,
                'winning_trades': 0,
                'losing_trades': 0,
                'win_rate': 0,
                'total_profit': 0,
                'average_profit': 0,
                'max_profit': 0,
                'max_loss': 0
            }
        
        total_trades = len(trades)
        winning_trades = len([t for t in trades if t.profit > 0])
        losing_trades = len([t for t in trades if t.profit < 0])
        win_rate = (winning_trades / total_trades) * 100 if total_trades > 0 else 0
        
        profits = [t.profit for t in trades]
        total_profit = sum(profits)
        average_profit = total_profit / total_trades if total_trades > 0 else 0
        max_profit = max(profits) if profits else 0
        max_loss = min(profits) if profits else 0
        
        return {
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'win_rate': win_rate,
            'total_profit': total_profit,
            'average_profit': average_profit,
            'max_profit': max_profit,
            'max_loss': max_loss
        }
