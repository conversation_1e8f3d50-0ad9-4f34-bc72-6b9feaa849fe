#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断AI推理交易中lot_size参数缺失的问题
"""

def analyze_lot_size_issue():
    """分析lot_size参数问题"""
    print("🔍 AI推理交易lot_size参数问题分析")
    print("=" * 60)
    
    print("📋 问题描述:")
    print("• 错误信息：❌ 交易执行失败: 缺少必需参数: lot_size")
    print("• 发生位置：AI推理交易执行时")
    print("• 影响功能：自动交易无法执行")
    print()
    
    print("🔍 可能的原因:")
    print("1. 前端tradingLotSize输入框值为空")
    print("2. parseFloat()返回NaN")
    print("3. 参数传递过程中丢失")
    print("4. 后端参数验证逻辑过于严格")
    print()
    
    print("🔧 解决方案:")
    print("1. 添加前端参数验证和默认值")
    print("2. 改进后端参数检查逻辑")
    print("3. 增加详细的错误日志")
    print("4. 提供参数修复机制")

def generate_frontend_fix():
    """生成前端修复代码"""
    print("\n🔧 前端修复方案")
    print("=" * 60)
    
    print("📝 修改 getTradingConfig() 函数:")
    print("""
// 修复后的 getTradingConfig 函数
function getTradingConfig() {
    // 获取并验证lot_size
    const lotSizeElement = document.getElementById('tradingLotSize');
    let lotSize = parseFloat(lotSizeElement?.value || '0.01');
    
    // 确保lot_size有效
    if (isNaN(lotSize) || lotSize <= 0) {
        lotSize = 0.01; // 默认值
        console.warn('⚠️ lot_size无效，使用默认值: 0.01');
        if (lotSizeElement) {
            lotSizeElement.value = '0.01';
        }
    }
    
    // 限制lot_size范围
    if (lotSize > 10) {
        lotSize = 10;
        console.warn('⚠️ lot_size超出最大值，调整为: 10');
        if (lotSizeElement) {
            lotSizeElement.value = '10';
        }
    }
    
    const config = {
        lot_size: lotSize,
        max_positions: parseInt(document.getElementById('maxPositions')?.value || '4'),
        stop_loss_pips: parseInt(document.getElementById('stopLossPips')?.value || '50'),
        take_profit_pips: parseInt(document.getElementById('takeProfitPips')?.value || '100'),
        min_confidence: parseFloat(document.getElementById('minConfidence')?.value || '0.8'),
        // ... 其他参数
    };
    
    // 验证配置
    console.log('📊 交易配置:', config);
    return config;
}
""")

def generate_backend_fix():
    """生成后端修复代码"""
    print("\n🔧 后端修复方案")
    print("=" * 60)
    
    print("📝 修改 API 参数验证逻辑:")
    print("""
@app.route('/api/deep-learning/execute-trade', methods=['POST'])
@login_required
def api_execute_ai_trade():
    try:
        data = request.json
        
        # 详细的参数验证和修复
        if not data:
            return jsonify({
                'success': False,
                'error': '请求数据为空'
            })
        
        # 验证和修复关键参数
        symbol = data.get('symbol')
        action = data.get('action')
        lot_size = data.get('lot_size')
        
        # 详细验证
        if not symbol:
            return jsonify({
                'success': False,
                'error': '缺少必需参数: symbol'
            })
        
        if not action or action not in ['BUY', 'SELL']:
            return jsonify({
                'success': False,
                'error': f'无效的交易方向: {action}'
            })
        
        # 修复lot_size参数
        if lot_size is None or lot_size == '':
            lot_size = 0.01  # 默认值
            logger.warning(f"⚠️ lot_size为空，使用默认值: {lot_size}")
        
        try:
            lot_size = float(lot_size)
        except (ValueError, TypeError):
            lot_size = 0.01
            logger.warning(f"⚠️ lot_size转换失败，使用默认值: {lot_size}")
        
        if lot_size <= 0 or lot_size > 10:
            lot_size = min(max(lot_size, 0.01), 10)
            logger.warning(f"⚠️ lot_size超出范围，调整为: {lot_size}")
        
        # 更新数据
        data['lot_size'] = lot_size
        
        logger.info(f"📊 交易参数: symbol={symbol}, action={action}, lot_size={lot_size}")
        
        # 执行交易
        result = deep_learning_service.execute_ai_trade(
            user_id=current_user.id,
            trade_data=data
        )
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"执行AI交易失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })
""")

def generate_validation_function():
    """生成参数验证函数"""
    print("\n🔧 参数验证函数")
    print("=" * 60)
    
    print("📝 前端参数验证函数:")
    print("""
// 验证交易参数
function validateTradeParameters(tradeData) {
    const errors = [];
    
    // 验证symbol
    if (!tradeData.symbol) {
        errors.push('交易品种不能为空');
    }
    
    // 验证action
    if (!tradeData.action || !['BUY', 'SELL'].includes(tradeData.action)) {
        errors.push('交易方向必须是BUY或SELL');
    }
    
    // 验证lot_size
    if (!tradeData.lot_size || isNaN(tradeData.lot_size) || tradeData.lot_size <= 0) {
        errors.push('交易手数必须大于0');
    }
    
    if (tradeData.lot_size > 10) {
        errors.push('交易手数不能超过10');
    }
    
    // 验证止损止盈
    if (tradeData.stop_loss_pips && (isNaN(tradeData.stop_loss_pips) || tradeData.stop_loss_pips < 0)) {
        errors.push('止损点数必须大于等于0');
    }
    
    if (tradeData.take_profit_pips && (isNaN(tradeData.take_profit_pips) || tradeData.take_profit_pips < 0)) {
        errors.push('止盈点数必须大于等于0');
    }
    
    return {
        valid: errors.length === 0,
        errors: errors
    };
}

// 修复交易参数
function fixTradeParameters(tradeData) {
    const fixed = { ...tradeData };
    
    // 修复lot_size
    if (!fixed.lot_size || isNaN(fixed.lot_size) || fixed.lot_size <= 0) {
        fixed.lot_size = 0.01;
        console.warn('⚠️ 修复lot_size为默认值: 0.01');
    }
    
    if (fixed.lot_size > 10) {
        fixed.lot_size = 10;
        console.warn('⚠️ 修复lot_size为最大值: 10');
    }
    
    // 修复其他参数
    fixed.stop_loss_pips = fixed.stop_loss_pips || 50;
    fixed.take_profit_pips = fixed.take_profit_pips || 100;
    
    return fixed;
}
""")

def main():
    """主函数"""
    analyze_lot_size_issue()
    generate_frontend_fix()
    generate_backend_fix()
    generate_validation_function()
    
    print("\n📊 修复总结")
    print("=" * 60)
    print("✅ 前端：添加参数验证和默认值")
    print("✅ 后端：改进参数检查和修复逻辑")
    print("✅ 验证：增加详细的参数验证函数")
    print("✅ 日志：添加详细的错误和警告信息")
    print()
    print("💡 下一步操作:")
    print("1. 应用前端修复代码")
    print("2. 应用后端修复代码")
    print("3. 测试交易参数传递")
    print("4. 验证AI推理交易功能")

if __name__ == "__main__":
    main()
