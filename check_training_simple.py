import sqlite3
import os

print("检查训练状态...")

# 检查数据库文件
if not os.path.exists('trading_system.db'):
    print("❌ 数据库文件不存在")
    exit()

print("✅ 数据库文件存在")

try:
    conn = sqlite3.connect('trading_system.db')
    cursor = conn.cursor()
    
    # 检查表
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = [row[0] for row in cursor.fetchall()]
    print(f"📊 数据库表: {tables}")
    
    if 'training_tasks' not in tables:
        print("❌ training_tasks表不存在")
        conn.close()
        exit()
    
    # 检查任务
    cursor.execute("SELECT COUNT(*) FROM training_tasks")
    count = cursor.fetchone()[0]
    print(f"📋 训练任务数量: {count}")
    
    if count > 0:
        cursor.execute("""
            SELECT id, name, status, progress, updated_at 
            FROM training_tasks 
            ORDER BY updated_at DESC 
            LIMIT 3
        """)
        
        tasks = cursor.fetchall()
        print("最近的任务:")
        for task in tasks:
            print(f"  {task[1]} | {task[2]} | {task[3]}% | {task[4]}")
    
    conn.close()
    print("✅ 检查完成")
    
except Exception as e:
    print(f"❌ 错误: {e}")
