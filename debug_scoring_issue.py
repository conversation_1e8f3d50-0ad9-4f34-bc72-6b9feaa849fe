#!/usr/bin/env python3
"""
调试评分计算问题
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def analyze_scoring_logic():
    """分析评分逻辑"""
    print("🔍 分析评分逻辑问题")
    print("=" * 60)
    
    # 模拟两个回测结果
    result_high_return = {
        'statistics': {
            'total_return': 1.62,  # 1.62%收益率
            'win_rate': 50.0,      # 假设50%胜率
            'max_drawdown': -3.0,  # 假设3%最大回撤
            'sharpe_ratio': 1.2,   # 假设1.2夏普比率
            'total_trades': 7      # 7笔交易
        }
    }
    
    result_low_return = {
        'statistics': {
            'total_return': 0.86,  # 0.86%收益率
            'win_rate': 57.1,      # 57.1%胜率
            'max_drawdown': -1.0,  # 假设1%最大回撤
            'sharpe_ratio': 1.8,   # 假设1.8夏普比率
            'total_trades': 15     # 15笔交易
        }
    }
    
    print("📊 两个回测结果对比:")
    print(f"   高收益组合: 收益率={result_high_return['statistics']['total_return']:.2f}%, 胜率={result_high_return['statistics']['win_rate']:.1f}%")
    print(f"   低收益组合: 收益率={result_low_return['statistics']['total_return']:.2f}%, 胜率={result_low_return['statistics']['win_rate']:.1f}%")
    
    # 手动计算评分
    def calculate_score_manual(stats):
        total_return = stats['total_return']
        win_rate = stats['win_rate']
        max_drawdown = abs(stats['max_drawdown'])
        sharpe_ratio = stats['sharpe_ratio']
        total_trades = stats['total_trades']
        
        score = 0
        details = {}
        
        # 收益率权重 40%
        return_score = min(total_return * 2, 40)  # 20%收益率得满分
        score += return_score
        details['return_score'] = return_score
        
        # 胜率权重 25%
        win_rate_score = (win_rate / 100) * 25
        score += win_rate_score
        details['win_rate_score'] = win_rate_score
        
        # 最大回撤权重 20% (回撤越小分数越高)
        if max_drawdown > 0:
            drawdown_score = max(0, 20 - max_drawdown * 100)
        else:
            drawdown_score = 20
        score += drawdown_score
        details['drawdown_score'] = drawdown_score
        
        # 夏普比率权重 10%
        sharpe_score = min(max(sharpe_ratio, 0) * 5, 10)  # 夏普比率2.0得满分
        score += sharpe_score
        details['sharpe_score'] = sharpe_score
        
        # 交易次数权重 5% (适中的交易次数得分最高)
        if total_trades >= 5 and total_trades <= 20:
            trade_score = 5
        elif total_trades > 0:
            trade_score = max(0, 5 - abs(total_trades - 12) * 0.2)
        else:
            trade_score = 0
        score += trade_score
        details['trade_score'] = trade_score
        
        return round(score, 2), details
    
    # 计算高收益组合评分
    high_score, high_details = calculate_score_manual(result_high_return['statistics'])
    print(f"\n🔢 高收益组合 (1.62%) 评分计算:")
    print(f"   收益率得分: {high_details['return_score']:.2f}/40 (1.62 * 2 = {1.62 * 2:.2f})")
    print(f"   胜率得分: {high_details['win_rate_score']:.2f}/25 (50.0/100 * 25 = {50.0/100 * 25:.2f})")
    print(f"   回撤得分: {high_details['drawdown_score']:.2f}/20 (20 - 3.0 * 100 = {20 - 3.0 * 100:.2f})")
    print(f"   夏普得分: {high_details['sharpe_score']:.2f}/10 (1.2 * 5 = {1.2 * 5:.2f})")
    print(f"   交易得分: {high_details['trade_score']:.2f}/5 (7笔交易)")
    print(f"   总评分: {high_score:.2f}")
    
    # 计算低收益组合评分
    low_score, low_details = calculate_score_manual(result_low_return['statistics'])
    print(f"\n🔢 低收益组合 (0.86%) 评分计算:")
    print(f"   收益率得分: {low_details['return_score']:.2f}/40 (0.86 * 2 = {0.86 * 2:.2f})")
    print(f"   胜率得分: {low_details['win_rate_score']:.2f}/25 (57.1/100 * 25 = {57.1/100 * 25:.2f})")
    print(f"   回撤得分: {low_details['drawdown_score']:.2f}/20 (20 - 1.0 * 100 = {20 - 1.0 * 100:.2f})")
    print(f"   夏普得分: {low_details['sharpe_score']:.2f}/10 (1.8 * 5 = {1.8 * 5:.2f})")
    print(f"   交易得分: {low_details['trade_score']:.2f}/5 (15笔交易)")
    print(f"   总评分: {low_score:.2f}")
    
    print(f"\n❓ 问题分析:")
    if low_score > high_score:
        print(f"   低收益组合评分更高的原因:")
        if low_details['win_rate_score'] > high_details['win_rate_score']:
            print(f"     • 胜率更高: {low_details['win_rate_score']:.2f} vs {high_details['win_rate_score']:.2f}")
        if low_details['drawdown_score'] > high_details['drawdown_score']:
            print(f"     • 回撤更小: {low_details['drawdown_score']:.2f} vs {high_details['drawdown_score']:.2f}")
        if low_details['sharpe_score'] > high_details['sharpe_score']:
            print(f"     • 夏普比率更高: {low_details['sharpe_score']:.2f} vs {high_details['sharpe_score']:.2f}")
        if low_details['trade_score'] > high_details['trade_score']:
            print(f"     • 交易次数更优: {low_details['trade_score']:.2f} vs {high_details['trade_score']:.2f}")
    
    return high_score, low_score

def suggest_scoring_improvements():
    """建议评分改进方案"""
    print("\n💡 评分系统改进建议")
    print("=" * 60)
    
    print("🔧 当前评分系统的问题:")
    print("   1. 收益率权重可能过低 (40%)")
    print("   2. 其他指标可能过度影响总分")
    print("   3. 回撤惩罚可能过重")
    print("   4. 交易次数评分逻辑可能不合理")
    
    print("\n✅ 改进方案:")
    print("   方案1: 提高收益率权重")
    print("     • 收益率权重: 40% → 50%")
    print("     • 胜率权重: 25% → 20%")
    print("     • 回撤权重: 20% → 15%")
    print("     • 夏普权重: 10% → 10%")
    print("     • 交易权重: 5% → 5%")
    
    print("\n   方案2: 调整回撤惩罚")
    print("     • 当前: 20 - max_drawdown * 100")
    print("     • 改进: 20 - max_drawdown * 50 (减少惩罚)")
    
    print("\n   方案3: 优化交易次数评分")
    print("     • 当前: 5-20笔交易得满分")
    print("     • 改进: 根据收益率调整最优交易次数")

def test_improved_scoring():
    """测试改进的评分系统"""
    print("\n🧪 测试改进的评分系统")
    print("=" * 60)
    
    def calculate_improved_score(stats):
        total_return = stats['total_return']
        win_rate = stats['win_rate']
        max_drawdown = abs(stats['max_drawdown'])
        sharpe_ratio = stats['sharpe_ratio']
        total_trades = stats['total_trades']
        
        score = 0
        
        # 收益率权重 50% (提高)
        return_score = min(total_return * 2.5, 50)  # 20%收益率得满分
        score += return_score
        
        # 胜率权重 20% (降低)
        win_rate_score = (win_rate / 100) * 20
        score += win_rate_score
        
        # 最大回撤权重 15% (降低惩罚)
        if max_drawdown > 0:
            drawdown_score = max(0, 15 - max_drawdown * 30)  # 减少惩罚
        else:
            drawdown_score = 15
        score += drawdown_score
        
        # 夏普比率权重 10%
        sharpe_score = min(max(sharpe_ratio, 0) * 5, 10)
        score += sharpe_score
        
        # 交易次数权重 5%
        if total_trades >= 3 and total_trades <= 30:  # 放宽范围
            trade_score = 5
        elif total_trades > 0:
            trade_score = max(0, 5 - abs(total_trades - 15) * 0.1)  # 减少惩罚
        else:
            trade_score = 0
        score += trade_score
        
        return round(score, 2)
    
    # 测试数据
    high_return_stats = {
        'total_return': 1.62,
        'win_rate': 50.0,
        'max_drawdown': -3.0,
        'sharpe_ratio': 1.2,
        'total_trades': 7
    }
    
    low_return_stats = {
        'total_return': 0.86,
        'win_rate': 57.1,
        'max_drawdown': -1.0,
        'sharpe_ratio': 1.8,
        'total_trades': 15
    }
    
    high_improved = calculate_improved_score(high_return_stats)
    low_improved = calculate_improved_score(low_return_stats)
    
    print(f"改进后评分:")
    print(f"   高收益组合 (1.62%): {high_improved:.2f}")
    print(f"   低收益组合 (0.86%): {low_improved:.2f}")
    
    if high_improved > low_improved:
        print(f"✅ 改进成功！高收益组合评分更高")
    else:
        print(f"⚠️ 仍需进一步调整")

def main():
    print("🔍 参数优化评分问题调试")
    print("=" * 80)
    print("❓ 问题: 收益率1.62%的评分(20.77)低于收益率0.86%的评分(30.5)")
    print("=" * 80)
    
    # 分析当前评分逻辑
    high_score, low_score = analyze_scoring_logic()
    
    # 建议改进方案
    suggest_scoring_improvements()
    
    # 测试改进方案
    test_improved_scoring()
    
    print("\n" + "=" * 80)
    print("📋 问题总结")
    print("=" * 80)
    print("🔍 问题根因:")
    print("   当前评分系统过度重视胜率、回撤控制和夏普比率")
    print("   而收益率权重相对较低，导致高收益但其他指标一般的组合评分偏低")
    
    print("\n💡 解决方案:")
    print("   1. 提高收益率在评分中的权重")
    print("   2. 适当降低回撤惩罚的严重程度")
    print("   3. 调整交易次数的评分逻辑")
    print("   4. 考虑引入收益风险比等综合指标")

if __name__ == '__main__':
    main()
