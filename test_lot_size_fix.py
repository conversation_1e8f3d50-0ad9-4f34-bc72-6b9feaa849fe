#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试lot_size参数修复是否有效
"""

import json
import requests
from datetime import datetime

def test_frontend_validation():
    """测试前端参数验证逻辑"""
    print("🔧 前端参数验证测试")
    print("=" * 50)
    
    # 模拟JavaScript的参数验证逻辑
    def validate_trade_parameters(trade_data):
        errors = []
        
        # 验证symbol
        if not trade_data.get('symbol'):
            errors.append('交易品种不能为空')
        
        # 验证action
        if not trade_data.get('action') or trade_data['action'] not in ['BUY', 'SELL']:
            errors.append('交易方向必须是BUY或SELL')
        
        # 验证lot_size
        lot_size = trade_data.get('lot_size')
        if not lot_size or (isinstance(lot_size, (int, float)) and lot_size <= 0):
            errors.append('交易手数必须大于0')
        
        if isinstance(lot_size, (int, float)) and lot_size > 10:
            errors.append('交易手数不能超过10')
        
        return {
            'valid': len(errors) == 0,
            'errors': errors
        }
    
    def fix_trade_parameters(trade_data):
        fixed = trade_data.copy()
        
        # 修复lot_size
        lot_size = fixed.get('lot_size')
        if not lot_size or (isinstance(lot_size, (int, float)) and lot_size <= 0):
            fixed['lot_size'] = 0.01
            print('⚠️ 修复lot_size为默认值: 0.01')
        
        if isinstance(fixed['lot_size'], (int, float)) and fixed['lot_size'] > 10:
            fixed['lot_size'] = 10
            print('⚠️ 修复lot_size为最大值: 10')
        
        # 修复其他参数
        fixed['stop_loss_pips'] = fixed.get('stop_loss_pips') or 50
        fixed['take_profit_pips'] = fixed.get('take_profit_pips') or 100
        
        return fixed
    
    # 测试用例
    test_cases = [
        {
            'name': '正常参数',
            'data': {
                'symbol': 'XAUUSD',
                'action': 'BUY',
                'lot_size': 0.01,
                'stop_loss_pips': 50,
                'take_profit_pips': 100
            }
        },
        {
            'name': 'lot_size为空',
            'data': {
                'symbol': 'XAUUSD',
                'action': 'BUY',
                'lot_size': None,
                'stop_loss_pips': 50,
                'take_profit_pips': 100
            }
        },
        {
            'name': 'lot_size为0',
            'data': {
                'symbol': 'XAUUSD',
                'action': 'BUY',
                'lot_size': 0,
                'stop_loss_pips': 50,
                'take_profit_pips': 100
            }
        },
        {
            'name': 'lot_size超出范围',
            'data': {
                'symbol': 'XAUUSD',
                'action': 'BUY',
                'lot_size': 15,
                'stop_loss_pips': 50,
                'take_profit_pips': 100
            }
        },
        {
            'name': '缺少symbol',
            'data': {
                'action': 'BUY',
                'lot_size': 0.01,
                'stop_loss_pips': 50,
                'take_profit_pips': 100
            }
        }
    ]
    
    for test_case in test_cases:
        print(f"\n📋 测试用例: {test_case['name']}")
        print(f"   原始数据: {test_case['data']}")
        
        # 验证参数
        validation = validate_trade_parameters(test_case['data'])
        print(f"   验证结果: {'✅ 通过' if validation['valid'] else '❌ 失败'}")
        
        if not validation['valid']:
            print(f"   错误信息: {validation['errors']}")
            
            # 尝试修复
            fixed_data = fix_trade_parameters(test_case['data'])
            print(f"   修复后数据: {fixed_data}")
            
            # 重新验证
            revalidation = validate_trade_parameters(fixed_data)
            print(f"   修复后验证: {'✅ 通过' if revalidation['valid'] else '❌ 仍然失败'}")

def test_backend_parameter_handling():
    """测试后端参数处理逻辑"""
    print("\n🔧 后端参数处理测试")
    print("=" * 50)
    
    # 模拟后端的参数处理逻辑
    def process_trade_parameters(data):
        if not data:
            return {'success': False, 'error': '请求数据为空'}
        
        symbol = data.get('symbol')
        action = data.get('action')
        lot_size = data.get('lot_size')
        
        # 详细验证
        if not symbol:
            return {'success': False, 'error': '缺少必需参数: symbol'}
        
        if not action or action not in ['BUY', 'SELL']:
            return {'success': False, 'error': f'无效的交易方向: {action}'}
        
        # 修复lot_size参数
        if lot_size is None or lot_size == '':
            lot_size = 0.01
            print(f"⚠️ lot_size为空，使用默认值: {lot_size}")
        
        try:
            lot_size = float(lot_size)
        except (ValueError, TypeError):
            lot_size = 0.01
            print(f"⚠️ lot_size转换失败，使用默认值: {lot_size}")
        
        if lot_size <= 0 or lot_size > 10:
            lot_size = min(max(lot_size, 0.01), 10)
            print(f"⚠️ lot_size超出范围，调整为: {lot_size}")
        
        # 更新数据
        data['lot_size'] = lot_size
        
        print(f"📊 交易参数: symbol={symbol}, action={action}, lot_size={lot_size}")
        
        return {'success': True, 'data': data}
    
    # 测试用例
    backend_test_cases = [
        {'name': '正常数据', 'data': {'symbol': 'XAUUSD', 'action': 'BUY', 'lot_size': 0.01}},
        {'name': 'lot_size为None', 'data': {'symbol': 'XAUUSD', 'action': 'BUY', 'lot_size': None}},
        {'name': 'lot_size为空字符串', 'data': {'symbol': 'XAUUSD', 'action': 'BUY', 'lot_size': ''}},
        {'name': 'lot_size为字符串', 'data': {'symbol': 'XAUUSD', 'action': 'BUY', 'lot_size': 'invalid'}},
        {'name': 'lot_size为负数', 'data': {'symbol': 'XAUUSD', 'action': 'BUY', 'lot_size': -1}},
        {'name': 'lot_size超出范围', 'data': {'symbol': 'XAUUSD', 'action': 'BUY', 'lot_size': 20}},
        {'name': '缺少数据', 'data': None},
        {'name': '缺少symbol', 'data': {'action': 'BUY', 'lot_size': 0.01}},
        {'name': '无效action', 'data': {'symbol': 'XAUUSD', 'action': 'INVALID', 'lot_size': 0.01}},
    ]
    
    for test_case in backend_test_cases:
        print(f"\n📋 测试用例: {test_case['name']}")
        print(f"   输入数据: {test_case['data']}")
        
        result = process_trade_parameters(test_case['data'])
        
        if result['success']:
            print(f"   处理结果: ✅ 成功")
            print(f"   最终数据: {result['data']}")
        else:
            print(f"   处理结果: ❌ 失败")
            print(f"   错误信息: {result['error']}")

def generate_fix_summary():
    """生成修复总结"""
    print("\n📊 修复总结")
    print("=" * 50)
    
    print("✅ 前端修复:")
    print("• 改进了getTradingConfig()函数，添加参数验证和默认值")
    print("• 添加了validateTradeParameters()函数进行参数验证")
    print("• 添加了fixTradeParameters()函数进行参数修复")
    print("• 在executeTrade()函数中添加了参数验证流程")
    print()
    
    print("✅ 后端修复:")
    print("• 改进了API参数验证逻辑，支持参数修复")
    print("• 添加了详细的错误信息和警告日志")
    print("• 对lot_size参数进行智能修复和范围限制")
    print("• 提供了更好的错误处理和用户反馈")
    print()
    
    print("🎯 预期效果:")
    print("• lot_size参数缺失时自动使用默认值0.01")
    print("• 无效的lot_size值会被自动修复")
    print("• 提供详细的错误信息帮助用户调试")
    print("• AI推理交易可以正常执行")
    print()
    
    print("💡 测试建议:")
    print("1. 重启应用程序")
    print("2. 进入模型推理页面")
    print("3. 检查交易配置中的手数设置")
    print("4. 启用自动交易并测试推理执行")
    print("5. 查看浏览器控制台的日志信息")

def main():
    """主函数"""
    print("🔧 lot_size参数修复验证工具")
    print("=" * 60)
    
    print("📋 问题描述:")
    print("• 错误信息：❌ 交易执行失败: 缺少必需参数: lot_size")
    print("• 原因：前端传递的lot_size参数为空或无效")
    print("• 影响：AI推理交易无法正常执行")
    print()
    
    # 测试前端验证
    test_frontend_validation()
    
    # 测试后端处理
    test_backend_parameter_handling()
    
    # 生成修复总结
    generate_fix_summary()

if __name__ == "__main__":
    main()
