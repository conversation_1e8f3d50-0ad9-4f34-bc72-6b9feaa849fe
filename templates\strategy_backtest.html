{% extends "base.html" %}

{% block title %}策略回测 - AI策略验证{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">
            <i class="fas fa-chart-line text-primary me-2"></i>
            策略回测
            <span class="badge bg-primary ms-2">AI策略验证</span>
        </h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <div class="btn-group me-2">
                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshPage()">
                    <i class="fas fa-sync-alt"></i> 刷新
                </button>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 左侧：回测配置 -->
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cog text-primary me-2"></i>
                        回测配置
                    </h5>
                </div>
                <div class="card-body">
                    <form id="backtestForm">
                        <!-- AI策略选择 -->
                        <div class="mb-3">
                            <label class="form-label">AI策略模型</label>
                            <select class="form-select" id="aiStrategySelect" required>
                                <option value="">选择AI策略模型</option>
                                <!-- 动态加载AI策略 -->
                            </select>
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                选择要回测的AI策略模型
                            </small>
                        </div>

                        <!-- 货币对 -->
                        <div class="mb-3">
                            <label class="form-label">交易品种</label>
                            <select class="form-select" id="symbolSelect" required>
                                <option value="XAUUSD" selected>XAU/USD (黄金/美元)</option>
                            </select>
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                当前仅支持黄金交易
                            </small>
                        </div>

                        <!-- 时间间隔 -->
                        <div class="mb-3">
                            <label class="form-label">交易时间间隔</label>
                            <select class="form-select" id="timeframeSelect" required>
                                <option value="">请先选择AI策略</option>
                            </select>
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                必须与AI策略训练时使用的时间框架保持一致
                            </small>
                        </div>

                        <!-- 回测周期 -->
                        <div class="mb-3">
                            <label class="form-label">回测周期</label>
                            <select class="form-select" id="backtestPeriodSelect" required>
                                <option value="7">7天</option>
                                <option value="14">14天</option>
                                <option value="30" selected>30天</option>
                                <option value="60">60天</option>
                                <option value="90">90天</option>
                            </select>
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                选择回测的历史数据周期
                            </small>
                        </div>

                        <!-- 初始资金 -->
                        <div class="mb-3">
                            <label class="form-label">初始资金</label>
                            <div class="input-group">
                                <span class="input-group-text">$</span>
                                <input type="number" class="form-control" id="initialBalanceInput" 
                                       value="10000" min="1000" max="100000" step="1000">
                            </div>
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                回测使用的初始资金
                            </small>
                        </div>

                        <!-- 交易手数 -->
                        <div class="mb-3">
                            <label class="form-label">单次交易手数</label>
                            <select class="form-select" id="lotSizeSelect" required>
                                <option value="0.01" selected>0.01手</option>
                                <option value="0.02">0.02手</option>
                                <option value="0.05">0.05手</option>
                                <option value="0.1">0.1手</option>
                            </select>
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                回测使用的交易手数
                            </small>
                        </div>

                        <!-- 策略配置类型 -->
                        <div class="mb-3">
                            <label class="form-label">策略配置</label>
                            <select class="form-select" id="configType" onchange="updateConfigDisplay()">
                                <option value="auto">自动选择 (推荐)</option>
                                <option value="trend_following">趋势跟踪</option>
                                <option value="mean_reversion">均值回归</option>
                                <option value="breakout">突破策略</option>
                                <option value="high_frequency">高频策略</option>
                                <option value="conservative">保守策略</option>
                                <option value="aggressive">激进策略</option>
                                <option value="custom">自定义配置</option>
                            </select>
                            <small class="text-muted">
                                <i class="fas fa-cog me-1"></i>
                                系统会根据策略名称自动选择最适合的配置
                            </small>
                        </div>

                        <!-- MT5连接控制 -->
                        <div class="mb-3">
                            <div class="card border-warning bg-light">
                                <div class="card-body p-3">
                                    <h6 class="card-title mb-3">
                                        <i class="fas fa-plug me-2 text-warning"></i>
                                        MT5连接设置
                                    </h6>

                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="allowAutoConnect" value="">
                                        <label class="form-check-label" for="allowAutoConnect">
                                            允许自动连接MT5
                                        </label>
                                    </div>

                                    <div class="mt-2">
                                        <small class="text-muted">
                                            <i class="fas fa-info-circle me-1"></i>
                                            <strong>重要:</strong> 回测需要MT5真实数据。如果MT5未连接，可以选择允许系统自动连接，
                                            或手动启动MT5终端后再运行回测。
                                        </small>
                                    </div>

                                    <div class="mt-2">
                                        <small class="text-warning">
                                            <i class="fas fa-exclamation-triangle me-1"></i>
                                            <strong>安全提示:</strong> 自动连接将使用系统默认的MT5配置。
                                            建议手动连接以确保使用正确的账户和服务器。
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 动态配置参数 -->
                        <div id="configDetails" class="mb-3" style="display: none;">
                            <div class="card border-light bg-light">
                                <div class="card-body p-3">
                                    <h6 class="card-title mb-3">
                                        <i class="fas fa-sliders-h me-2"></i>
                                        策略参数
                                    </h6>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <label class="form-label">止损百分比 (%)</label>
                                            <input type="number" class="form-control form-control-sm"
                                                   id="stopLossPercent" value="0.8" min="0.1" max="5.0" step="0.1">
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">止盈百分比 (%)</label>
                                            <input type="number" class="form-control form-control-sm"
                                                   id="takeProfitPercent" value="1.6" min="0.2" max="10.0" step="0.1">
                                        </div>
                                    </div>

                                    <div class="row mt-2">
                                        <div class="col-md-6">
                                            <label class="form-label">最大持仓数</label>
                                            <input type="number" class="form-control form-control-sm"
                                                   id="maxPositions" value="3" min="1" max="10" step="1">
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">信号阈值</label>
                                            <input type="number" class="form-control form-control-sm"
                                                   id="signalThreshold" value="0.7" min="0.5" max="1.0" step="0.05">
                                        </div>
                                    </div>

                                    <div class="mt-2">
                                        <small class="text-muted" id="configDescription">
                                            趋势跟踪策略 - 适合趋势明显的市场
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 策略模型状态 -->
                        <div class="mb-3">
                            <div class="card border-info bg-light">
                                <div class="card-body p-3">
                                    <h6 class="card-title mb-2">
                                        <i class="fas fa-brain me-2 text-info"></i>
                                        策略模型状态
                                    </h6>
                                    <div id="strategyModelStatus" class="d-flex align-items-center">
                                        <span class="badge bg-secondary me-2">
                                            <i class="fas fa-spinner fa-spin me-1"></i>检查中
                                        </span>
                                        <small class="text-muted">正在检查策略模型加载状态...</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 回测按钮 -->
                        <div class="d-grid gap-2">
                            <button type="button" class="btn btn-primary" id="runBacktestBtn" onclick="runBacktest()" disabled>
                                <i class="fas fa-play"></i> 运行回测
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 右侧：回测结果 -->
        <div class="col-lg-8">
            <!-- 回测状态 -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar text-success me-2"></i>
                        回测结果
                    </h5>
                </div>
                <div class="card-body">
                    <div id="backtestResultsContainer">
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-chart-area fa-2x mb-2"></i>
                            <p>请选择策略并运行回测</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 交易记录 -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list-alt text-info me-2"></i>
                        交易记录
                    </h5>
                </div>
                <div class="card-body">
                    <div id="tradeHistoryContainer" style="max-height: 400px; overflow-y: auto;">
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-file-alt fa-2x mb-2"></i>
                            <p>暂无交易记录</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 样式 -->
<style>
.performance-card {
    background: linear-gradient(135deg, #4e54c8 0%, #8f94fb 100%);
    border-radius: 10px;
    color: white;
    margin-bottom: 10px;
    padding: 15px;
}

.performance-card h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.performance-card .value {
    font-size: 2rem;
    font-weight: bold;
}

.performance-card .subtitle {
    font-size: 0.8rem;
    opacity: 0.8;
}

.trade-item {
    border-left: 3px solid #007bff;
    padding-left: 10px;
    margin-bottom: 10px;
    background: #f8f9fa;
    border-radius: 5px;
    padding: 10px;
}

.trade-item.profit {
    border-left-color: #28a745;
}

.trade-item.loss {
    border-left-color: #dc3545;
}

.metrics-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
    margin-top: 15px;
}

.metric-item {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 10px;
    text-align: center;
}

.metric-item .metric-value {
    font-size: 1.2rem;
    font-weight: bold;
}

.metric-item .metric-label {
    font-size: 0.8rem;
    opacity: 0.8;
}

#equityChart {
    width: 100%;
    height: 250px;
    margin-top: 20px;
}

/* 交易记录样式 */
.trade-item {
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.trade-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1) !important;
}

.trade-item.profit {
    border-left: 4px solid #28a745 !important;
}

.trade-item.loss {
    border-left: 4px solid #dc3545 !important;
}

.trade-item .badge {
    font-size: 0.75em;
}

.trade-item .small {
    font-size: 0.8em;
}

.trade-item .fw-bold {
    font-weight: 600;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .trade-item .row {
        margin-top: 0.5rem;
    }

    .trade-item .col-md-6 {
        margin-bottom: 0.5rem;
    }
}
</style>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// 策略配置默认值
const strategyConfigs = {
    trend_following: {
        stop_loss_percent: 0.8,
        take_profit_percent: 1.6,
        max_positions: 3,
        signal_threshold: 0.7,
        description: '趋势跟踪策略 - 适合趋势明显的市场'
    },
    mean_reversion: {
        stop_loss_percent: 0.6,
        take_profit_percent: 1.2,
        max_positions: 5,
        signal_threshold: 0.75,
        description: '均值回归策略 - 适合震荡市场'
    },
    breakout: {
        stop_loss_percent: 1.0,
        take_profit_percent: 2.0,
        max_positions: 2,
        signal_threshold: 0.8,
        description: '突破策略 - 适合波动较大的市场'
    },
    high_frequency: {
        stop_loss_percent: 0.4,
        take_profit_percent: 0.8,
        max_positions: 8,
        signal_threshold: 0.6,
        description: '高频策略 - 适合短期交易'
    },
    conservative: {
        stop_loss_percent: 0.5,
        take_profit_percent: 1.0,
        max_positions: 2,
        signal_threshold: 0.85,
        description: '保守策略 - 风险较低'
    },
    aggressive: {
        stop_loss_percent: 1.5,
        take_profit_percent: 3.0,
        max_positions: 5,
        signal_threshold: 0.65,
        description: '激进策略 - 高风险高收益'
    }
};

function updateConfigDisplay() {
    const configType = document.getElementById('configType').value;
    const configDetails = document.getElementById('configDetails');

    if (configType === 'auto') {
        configDetails.style.display = 'none';
        return;
    }

    if (configType === 'custom') {
        configDetails.style.display = 'block';
        // 保持当前值不变
        return;
    }

    // 显示配置详情
    configDetails.style.display = 'block';

    // 更新配置值
    if (strategyConfigs[configType]) {
        const config = strategyConfigs[configType];
        document.getElementById('stopLossPercent').value = config.stop_loss_percent;
        document.getElementById('takeProfitPercent').value = config.take_profit_percent;
        document.getElementById('maxPositions').value = config.max_positions;
        document.getElementById('signalThreshold').value = config.signal_threshold;
        document.getElementById('configDescription').textContent = config.description;
    }
}

// 检查策略模型加载状态
async function checkStrategyModelStatus() {
    try {
        const strategyId = document.getElementById('aiStrategySelect').value;
        if (!strategyId) {
            updateModelStatus('warning', '请先选择策略模型');
            return false;
        }

        updateModelStatus('loading', '正在检查策略模型...');

        const response = await fetch(`/api/ai-strategies/${strategyId}/status`);
        const data = await response.json();

        if (data.success) {
            const status = data.status;
            if (status.loaded) {
                updateModelStatus('success', `模型已加载: ${status.name}`, status);
                document.getElementById('runBacktestBtn').disabled = false;
                return true;
            } else {
                updateModelStatus('error', `模型加载失败: ${status.error || '未知错误'}`);
                document.getElementById('runBacktestBtn').disabled = true;
                return false;
            }
        } else {
            updateModelStatus('error', `检查失败: ${data.error}`);
            document.getElementById('runBacktestBtn').disabled = true;
            return false;
        }
    } catch (error) {
        updateModelStatus('error', `检查异常: ${error.message}`);
        document.getElementById('runBacktestBtn').disabled = true;
        return false;
    }
}

// 更新模型状态显示
function updateModelStatus(type, message, details = null) {
    const statusElement = document.getElementById('strategyModelStatus');

    let badgeClass, icon, textClass;
    switch (type) {
        case 'loading':
            badgeClass = 'bg-secondary';
            icon = 'fas fa-spinner fa-spin';
            textClass = 'text-muted';
            break;
        case 'success':
            badgeClass = 'bg-success';
            icon = 'fas fa-check';
            textClass = 'text-success';
            break;
        case 'warning':
            badgeClass = 'bg-warning';
            icon = 'fas fa-exclamation-triangle';
            textClass = 'text-warning';
            break;
        case 'error':
            badgeClass = 'bg-danger';
            icon = 'fas fa-times';
            textClass = 'text-danger';
            break;
        default:
            badgeClass = 'bg-secondary';
            icon = 'fas fa-question';
            textClass = 'text-muted';
    }

    let detailsHtml = '';
    if (details) {
        detailsHtml = `
            <div class="mt-1">
                <small class="text-muted">
                    类型: ${details.type || 'N/A'} |
                    参数: ${details.parameters ? Object.keys(details.parameters).length : 0}个 |
                    最后更新: ${details.last_updated || 'N/A'}
                </small>
            </div>
        `;
    }

    statusElement.innerHTML = `
        <span class="badge ${badgeClass} me-2">
            <i class="${icon} me-1"></i>${type === 'success' ? '已加载' : type === 'loading' ? '检查中' : type === 'warning' ? '警告' : '错误'}
        </span>
        <small class="${textClass}">${message}</small>
        ${detailsHtml}
    `;
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeBacktestPage();
    updateConfigDisplay();

    // 监听策略选择变化
    document.getElementById('aiStrategySelect').addEventListener('change', function() {
        checkStrategyModelStatus();
    });

    // 初始检查
    setTimeout(checkStrategyModelStatus, 1000);
});

function initializeBacktestPage() {
    console.log('🚀 初始化回测页面...');
    
    // 加载AI策略列表
    loadAIStrategies();
}

function loadAIStrategies() {
    console.log('📊 加载AI策略列表...');
    
    fetch('/api/ai-strategies/list')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const select = document.getElementById('aiStrategySelect');
                select.innerHTML = '<option value="">选择AI策略模型</option>';
                
                data.strategies.forEach(strategy => {
                    const option = document.createElement('option');
                    option.value = strategy.id;
                    option.textContent = `${strategy.name} (${strategy.timeframe || '1h'})`;
                    option.dataset.timeframe = strategy.timeframe || '1h';
                    select.appendChild(option);
                });
                
                console.log(`✅ 加载了 ${data.strategies.length} 个AI策略`);
            } else {
                console.error('❌ 加载AI策略失败:', data.error);
                showAlert('加载AI策略失败: ' + data.error, 'danger');
            }
        })
        .catch(error => {
            console.error('❌ 加载AI策略异常:', error);
            showAlert('加载AI策略异常: ' + error.message, 'danger');
        });
}

// AI策略选择变化时更新时间间隔
document.addEventListener('change', function(e) {
    if (e.target.id === 'aiStrategySelect') {
        const selectedOption = e.target.selectedOptions[0];
        const timeframe = selectedOption ? selectedOption.dataset.timeframe : '';
        
        const timeframeSelect = document.getElementById('timeframeSelect');
        if (timeframe) {
            timeframeSelect.innerHTML = `<option value="${timeframe}" selected>${getTimeframeText(timeframe)} (策略训练时间框架)</option>`;
            timeframeSelect.disabled = true;
        } else {
            timeframeSelect.innerHTML = '<option value="">请先选择AI策略</option>';
            timeframeSelect.disabled = false;
        }
    }
});

function getTimeframeText(timeframe) {
    const timeframeMap = {
        '1m': '1分钟',
        '5m': '5分钟',
        '15m': '15分钟',
        '30m': '30分钟',
        '1h': '1小时',
        '4h': '4小时',
        '1d': '1天'
    };
    return timeframeMap[timeframe] || timeframe;
}

function runBacktest() {
    // 验证表单
    const strategyId = document.getElementById('aiStrategySelect').value;
    if (!strategyId) {
        showAlert('请选择AI策略模型', 'warning');
        return;
    }
    
    // 获取回测参数
    const symbol = document.getElementById('symbolSelect').value;
    const timeframe = document.getElementById('timeframeSelect').value;
    const days = parseInt(document.getElementById('backtestPeriodSelect').value);
    const initialBalance = parseFloat(document.getElementById('initialBalanceInput').value);
    const lotSize = parseFloat(document.getElementById('lotSizeSelect').value);

    // 获取MT5连接设置
    const allowAutoConnect = document.getElementById('allowAutoConnect').checked;

    // 获取策略配置
    const configType = document.getElementById('configType').value;
    let customConfig = null;

    if (configType !== 'auto') {
        if (configType === 'custom') {
            // 使用自定义配置
            customConfig = {
                base_type: 'trend_following',
                stop_loss_percent: parseFloat(document.getElementById('stopLossPercent').value) / 100,
                take_profit_percent: parseFloat(document.getElementById('takeProfitPercent').value) / 100,
                max_positions: parseInt(document.getElementById('maxPositions').value),
                signal_threshold: parseFloat(document.getElementById('signalThreshold').value)
            };
        } else {
            // 使用预设配置
            const config = strategyConfigs[configType];
            customConfig = {
                base_type: configType,
                stop_loss_percent: config.stop_loss_percent / 100,
                take_profit_percent: config.take_profit_percent / 100,
                max_positions: config.max_positions,
                signal_threshold: config.signal_threshold
            };
        }

        console.log('使用策略配置:', customConfig);
    }
    
    // 显示加载状态
    document.getElementById('backtestResultsContainer').innerHTML = `
        <div class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">正在运行回测，请稍候...</p>
        </div>
    `;
    
    document.getElementById('tradeHistoryContainer').innerHTML = `
        <div class="text-center text-muted py-4">
            <i class="fas fa-spinner fa-spin fa-2x mb-2"></i>
            <p>正在加载交易记录...</p>
        </div>
    `;
    
    // 发送回测请求
    fetch(`/api/ai-strategies/${strategyId}/backtest`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            symbol: symbol,
            timeframe: timeframe,
            days: days,
            initial_balance: initialBalance,
            lot_size: lotSize,
            custom_config: customConfig,
            allow_auto_connect: allowAutoConnect
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayBacktestResults(data);
        } else {
            showAlert('回测失败: ' + data.error, 'danger');
            document.getElementById('backtestResultsContainer').innerHTML = `
                <div class="text-center text-danger py-4">
                    <i class="fas fa-exclamation-circle fa-2x mb-2"></i>
                    <p>回测失败: ${data.error}</p>
                </div>
            `;
        }
    })
    .catch(error => {
        console.error('回测请求失败:', error);
        showAlert('回测请求失败: ' + error.message, 'danger');
        document.getElementById('backtestResultsContainer').innerHTML = `
            <div class="text-center text-danger py-4">
                <i class="fas fa-exclamation-circle fa-2x mb-2"></i>
                <p>回测请求失败: ${error.message}</p>
            </div>
        `;
    });
}

function displayBacktestResults(data) {
    const performance = data.performance;
    const results = data.results;
    const report = data.report;
    
    // 显示性能指标
    const resultsContainer = document.getElementById('backtestResultsContainer');
    
    const totalReturnClass = performance.total_return >= 0 ? 'text-success' : 'text-danger';
    const totalReturnIcon = performance.total_return >= 0 ? 'fa-arrow-up' : 'fa-arrow-down';
    
    resultsContainer.innerHTML = `
        <div class="performance-card">
            <div class="row">
                <div class="col-md-6">
                    <h3>总收益率</h3>
                    <div class="value ${totalReturnClass}">
                        <i class="fas ${totalReturnIcon}"></i> 
                        ${performance.total_return}%
                    </div>
                    <div class="subtitle">初始资金: $${(results.initial_balance || 0).toFixed(2)} | 最终资金: $${(results.final_balance || 0).toFixed(2)}</div>
                </div>
                <div class="col-md-6 text-md-end">
                    <h3>交易统计</h3>
                    <div class="value">${performance.total_trades} 笔交易</div>
                    <div class="subtitle">胜率: ${performance.win_rate}% | 盈亏比: ${performance.profit_factor}</div>
                </div>
            </div>
            
            <div class="metrics-grid">
                <div class="metric-item">
                    <div class="metric-value">${performance.winning_trades || 0}</div>
                    <div class="metric-label">盈利交易</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value">${performance.losing_trades || 0}</div>
                    <div class="metric-label">亏损交易</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value">${performance.max_drawdown}%</div>
                    <div class="metric-label">最大回撤</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value">$${(performance.average_win || 0).toFixed(2)}</div>
                    <div class="metric-label">平均盈利</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value">$${Math.abs(performance.average_loss || 0).toFixed(2)}</div>
                    <div class="metric-label">平均亏损</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value">${(performance.sharpe_ratio || 0).toFixed(2)}</div>
                    <div class="metric-label">夏普比率</div>
                </div>
            </div>
        </div>
        
        <canvas id="equityChart"></canvas>
    `;
    
    // 绘制权益曲线
    drawEquityChart(results.equity_curve);
    
    // 显示交易记录
    displayTradeHistory(results.trades);
}

function drawEquityChart(equityCurve) {
    const ctx = document.getElementById('equityChart').getContext('2d');
    
    // 创建标签（索引）
    const labels = Array.from({ length: equityCurve.length }, (_, i) => i);
    
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: '账户权益',
                data: equityCurve,
                borderColor: 'rgba(75, 192, 192, 1)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1,
                fill: true
            }]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: '回测权益曲线'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return `权益: $${(context.raw || 0).toFixed(2)}`;
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: false,
                    title: {
                        display: true,
                        text: '账户权益 ($)'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: '交易周期'
                    }
                }
            }
        }
    });
}

function displayTradeHistory(trades) {
    const container = document.getElementById('tradeHistoryContainer');
    
    if (!trades || trades.length === 0) {
        container.innerHTML = `
            <div class="text-center text-muted py-4">
                <i class="fas fa-file-alt fa-2x mb-2"></i>
                <p>回测期间无交易记录</p>
            </div>
        `;
        return;
    }
    
    let html = '';
    
    // 按时间倒序排列
    trades.sort((a, b) => new Date(b.entry_time) - new Date(a.entry_time));
    
    trades.forEach(trade => {
        const isProfitable = trade.pnl > 0;
        const tradeClass = isProfitable ? 'profit' : 'loss';
        const pnlClass = isProfitable ? 'text-success' : 'text-danger';
        const pnlPrefix = isProfitable ? '+' : '';
        
        const entryTime = new Date(trade.entry_time).toLocaleString();
        const exitTime = new Date(trade.exit_time).toLocaleString();
        
        let closeReasonText = '';
        switch(trade.close_reason) {
            case 'take_profit':
                closeReasonText = '<span class="badge bg-success">止盈</span>';
                break;
            case 'stop_loss':
                closeReasonText = '<span class="badge bg-danger">止损</span>';
                break;
            case 'backtest_end':
                closeReasonText = '<span class="badge bg-secondary">回测结束</span>';
                break;
            default:
                closeReasonText = `<span class="badge bg-info">${trade.close_reason}</span>`;
        }
        
        // 构建止盈止损信息
        let riskManagementInfo = '';
        if (trade.take_profit && trade.stop_loss) {
            riskManagementInfo = `
                <div class="small text-muted mt-1">
                    <span class="me-3">
                        <i class="fas fa-arrow-up text-success me-1"></i>
                        止盈: ${(trade.take_profit || 0).toFixed(2)}
                    </span>
                    <span>
                        <i class="fas fa-arrow-down text-danger me-1"></i>
                        止损: ${(trade.stop_loss || 0).toFixed(2)}
                    </span>
                </div>
            `;
        }

        // 构建交易方向图标和颜色（添加安全检查）
        const tradeType = trade.type || 'unknown';
        const tradeDirection = tradeType === 'buy' ? '买入' : tradeType === 'sell' ? '卖出' : '未知';
        const directionIcon = tradeType === 'buy' ? 'fa-arrow-up text-success' : tradeType === 'sell' ? 'fa-arrow-down text-danger' : 'fa-question text-muted';
        const directionBadge = tradeType === 'buy' ? 'bg-success' : tradeType === 'sell' ? 'bg-danger' : 'bg-secondary';

        html += `
            <div class="trade-item ${tradeClass} border rounded p-3 mb-2">
                <div class="d-flex justify-content-between align-items-start">
                    <div class="flex-grow-1">
                        <div class="d-flex align-items-center mb-2">
                            <span class="badge ${directionBadge} me-2">
                                <i class="fas ${directionIcon} me-1"></i>
                                ${tradeDirection}
                            </span>
                            <h6 class="mb-0 me-2">${trade.volume}手</h6>
                            ${closeReasonText}
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="small text-muted">
                                    <i class="fas fa-sign-in-alt me-1"></i>
                                    开仓: ${entryTime}
                                </div>
                                <div class="small fw-bold">
                                    入场价: ${(trade.entry_price || 0).toFixed(2)}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="small text-muted">
                                    <i class="fas fa-sign-out-alt me-1"></i>
                                    平仓: ${exitTime}
                                </div>
                                <div class="small fw-bold">
                                    出场价: ${(trade.exit_price || 0).toFixed(2)}
                                </div>
                            </div>
                        </div>
                        ${riskManagementInfo}
                    </div>
                    <div class="text-end">
                        <div class="${pnlClass} fw-bold fs-5">${pnlPrefix}$${Math.abs(trade.pnl || 0).toFixed(2)}</div>
                        <div class="small text-muted">
                            持仓时长: ${(() => {
                                const duration = new Date(trade.exit_time) - new Date(trade.entry_time);
                                const hours = Math.floor(duration / (1000 * 60 * 60));
                                const minutes = Math.floor((duration % (1000 * 60 * 60)) / (1000 * 60));
                                return hours > 0 ? `${hours}h ${minutes}m` : `${minutes}m`;
                            })()}
                        </div>
                    </div>
                </div>
            </div>
        `;
    });
    
    container.innerHTML = html;
}

function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    
    // 插入到页面顶部
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
    
    // 自动关闭
    setTimeout(() => {
        alertDiv.classList.remove('show');
        setTimeout(() => alertDiv.remove(), 150);
    }, 5000);
}

function refreshPage() {
    location.reload();
}
</script>
{% endblock %}
