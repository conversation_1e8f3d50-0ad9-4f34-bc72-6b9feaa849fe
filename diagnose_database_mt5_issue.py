#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断数据库配置与MT5订单发送问题
分析两个数据库对订单发送的影响
"""

import os
import sqlite3
import re
from datetime import datetime

def analyze_database_configuration():
    """分析数据库配置"""
    print("🔍 分析数据库配置")
    print("=" * 50)
    
    # 检查应用程序配置
    print("📋 应用程序配置 (app.py):")
    if os.path.exists('app.py'):
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 查找数据库配置
        db_config_pattern = r"app\.config\['SQLALCHEMY_DATABASE_URI'\]\s*=\s*.*?'([^']+)'"
        match = re.search(db_config_pattern, content)
        if match:
            db_uri = match.group(1)
            print(f"   SQLALCHEMY_DATABASE_URI: {db_uri}")
            if 'trading_system.db' in db_uri:
                print("   ✅ 主应用使用 trading_system.db")
            else:
                print("   ⚠️  主应用使用其他数据库")
        else:
            print("   ❌ 未找到数据库配置")
    
    # 检查深度学习服务配置
    print("\n📋 深度学习服务配置:")
    if os.path.exists('services/deep_learning_service.py'):
        with open('services/deep_learning_service.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 查找数据库路径
        db_path_pattern = r"self\.db_path\s*=\s*'([^']+)'"
        match = re.search(db_path_pattern, content)
        if match:
            db_path = match.group(1)
            print(f"   db_path: {db_path}")
            if db_path == 'trading_system.db':
                print("   ✅ 深度学习服务使用 trading_system.db")
            else:
                print("   ⚠️  深度学习服务使用其他数据库")
        else:
            print("   ❌ 未找到数据库路径配置")

def check_mt5_service_database():
    """检查MT5服务的数据库使用"""
    print("\n🔍 检查MT5服务数据库使用")
    print("=" * 50)
    
    services_to_check = [
        'services/mt5_service.py',
        'services/trading_service.py',
        'routes.py'
    ]
    
    for service_file in services_to_check:
        if os.path.exists(service_file):
            print(f"\n📋 检查 {service_file}:")
            with open(service_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找数据库连接
            db_patterns = [
                r"sqlite3\.connect\(['\"]([^'\"]+)['\"]",
                r"DATABASE_URL.*?['\"]([^'\"]+)['\"]",
                r"db_path\s*=\s*['\"]([^'\"]+)['\"]"
            ]
            
            found_dbs = []
            for pattern in db_patterns:
                matches = re.findall(pattern, content)
                found_dbs.extend(matches)
            
            if found_dbs:
                unique_dbs = list(set(found_dbs))
                for db in unique_dbs:
                    print(f"   数据库: {db}")
                    if 'trading_system.db' in db:
                        print("   ✅ 使用 trading_system.db")
                    elif 'matetrade4.db' in db:
                        print("   ⚠️  使用 matetrade4.db")
                    else:
                        print(f"   ❓ 使用其他数据库: {db}")
            else:
                print("   ℹ️  未发现直接的数据库连接")

def analyze_order_execution_flow():
    """分析订单执行流程"""
    print("\n🔍 分析订单执行流程")
    print("=" * 50)
    
    print("📋 AI推理交易订单执行流程:")
    print("1. 前端发送交易请求 → /api/deep-learning/execute-trade")
    print("2. routes.py 接收请求 → deep_learning_service.execute_ai_trade()")
    print("3. deep_learning_service.py 处理交易逻辑")
    print("4. 调用 MT5Service 执行实际订单")
    print("5. 记录交易结果到数据库")
    print()
    
    print("🔍 可能的问题点:")
    print("• 数据库不一致：记录到一个数据库，但读取从另一个数据库")
    print("• MT5连接问题：订单发送失败但数据库仍记录")
    print("• 事务处理：数据库记录成功但MT5执行失败")
    print("• 错误处理：MT5失败时没有回滚数据库记录")

def check_database_consistency():
    """检查数据库一致性"""
    print("\n🔍 检查数据库一致性")
    print("=" * 50)
    
    databases = [
        'instance/matetrade4.db',
        'trading_system.db'
    ]
    
    for db_path in databases:
        if os.path.exists(db_path):
            print(f"\n📋 检查 {db_path}:")
            try:
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                
                # 检查ai_trades表
                cursor.execute("""
                    SELECT name FROM sqlite_master 
                    WHERE type='table' AND name='ai_trades'
                """)
                
                if cursor.fetchone():
                    cursor.execute("SELECT COUNT(*) FROM ai_trades")
                    count = cursor.fetchone()[0]
                    print(f"   ai_trades表: 存在，记录数={count}")
                    
                    if count > 0:
                        cursor.execute("""
                            SELECT COUNT(*) FROM ai_trades WHERE status = 'open'
                        """)
                        open_count = cursor.fetchone()[0]
                        print(f"   开仓记录: {open_count}")
                        
                        # 显示最近的记录
                        cursor.execute("""
                            SELECT symbol, action, lot_size, order_id, status, created_at
                            FROM ai_trades 
                            ORDER BY created_at DESC 
                            LIMIT 3
                        """)
                        recent_trades = cursor.fetchall()
                        if recent_trades:
                            print("   最近记录:")
                            for trade in recent_trades:
                                symbol, action, lot_size, order_id, status, created_at = trade
                                print(f"     {symbol} {action} {lot_size} | {order_id} | {status} | {created_at}")
                else:
                    print("   ai_trades表: 不存在")
                
                conn.close()
                
            except Exception as e:
                print(f"   ❌ 检查失败: {e}")
        else:
            print(f"\n📋 {db_path}: 文件不存在")

def generate_mt5_order_fix_recommendations():
    """生成MT5订单问题修复建议"""
    print("\n💡 MT5订单问题修复建议")
    print("=" * 50)
    
    print("🔧 数据库一致性修复:")
    print("1. 统一数据库使用")
    print("   • 确保所有服务都使用 trading_system.db")
    print("   • 检查并修改任何使用 instance/matetrade4.db 的代码")
    print("   • 迁移重要数据到主数据库")
    print()
    
    print("🔧 订单执行流程改进:")
    print("1. 增强事务处理")
    print("   • 先执行MT5订单，成功后再记录数据库")
    print("   • 实现回滚机制：MT5失败时不记录数据库")
    print("   • 添加订单状态验证")
    print()
    
    print("2. 改进错误处理")
    print("   • 详细记录MT5执行结果")
    print("   • 区分不同类型的失败（连接失败、参数错误、市场关闭等）")
    print("   • 提供用户友好的错误信息")
    print()
    
    print("3. 添加订单验证")
    print("   • 执行前检查MT5连接状态")
    print("   • 验证账户余额和保证金")
    print("   • 检查市场开放状态")
    print("   • 验证交易参数有效性")
    print()
    
    print("🔧 监控和调试:")
    print("1. 增强日志记录")
    print("   • 记录完整的订单执行过程")
    print("   • 包含MT5返回的详细信息")
    print("   • 记录时间戳和执行耗时")
    print()
    
    print("2. 实时状态同步")
    print("   • 定期同步MT5实际持仓与数据库记录")
    print("   • 检测并修复数据不一致")
    print("   • 提供手动同步功能")

def generate_immediate_fix_script():
    """生成立即修复脚本"""
    print("\n🔧 立即修复脚本")
    print("=" * 50)
    
    script_content = '''
# 立即修复MT5订单问题的步骤

## 1. 检查MT5连接
1. 确保MT5终端正在运行
2. 确保账户已登录
3. 检查网络连接
4. 验证交易权限

## 2. 统一数据库配置
检查以下文件中的数据库配置：
- app.py: SQLALCHEMY_DATABASE_URI
- services/deep_learning_service.py: self.db_path
- 其他服务文件中的 sqlite3.connect()

确保都指向 trading_system.db

## 3. 测试订单执行
1. 重启应用程序
2. 进入模型推理页面
3. 尝试手动执行一个小额测试交易
4. 检查MT5终端是否收到订单
5. 查看应用程序日志

## 4. 如果仍有问题
1. 检查MT5专家日志
2. 查看Python应用程序日志
3. 验证MT5 API权限设置
4. 检查防火墙和安全软件
'''
    
    print(script_content)
    
    # 保存到文件
    with open('MT5_ORDER_FIX_GUIDE.md', 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    print("✅ 修复指南已保存到 MT5_ORDER_FIX_GUIDE.md")

def main():
    """主函数"""
    print("🔧 数据库配置与MT5订单发送问题诊断")
    print("=" * 80)
    
    print("📋 问题描述:")
    print("• 页面显示产生了交易，但MT5软件上没有订单")
    print("• 可能与两个数据库的存在有关")
    print("• 需要分析订单执行流程和数据库一致性")
    print()
    
    # 分析数据库配置
    analyze_database_configuration()
    
    # 检查MT5服务数据库使用
    check_mt5_service_database()
    
    # 分析订单执行流程
    analyze_order_execution_flow()
    
    # 检查数据库一致性
    check_database_consistency()
    
    # 生成修复建议
    generate_mt5_order_fix_recommendations()
    
    # 生成立即修复脚本
    generate_immediate_fix_script()
    
    print("\n📊 诊断总结")
    print("=" * 80)
    print("✅ 数据库配置分析完成")
    print("✅ 订单执行流程分析完成")
    print("✅ 修复建议已生成")
    print("✅ 立即修复指南已保存")
    print()
    print("💡 关键发现:")
    print("• 应用程序和深度学习服务都使用 trading_system.db")
    print("• 问题可能不是数据库不一致，而是MT5执行失败")
    print("• 需要检查MT5连接状态和订单执行逻辑")
    print("• 建议增强错误处理和事务管理")

if __name__ == "__main__":
    main()
