{% extends "base.html" %}

{% block title %}GPU监控{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0 text-gray-800">
                    <i class="fas fa-microchip text-primary me-2"></i>
                    GPU性能监控
                </h1>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-primary" onclick="refreshMonitor()">
                        <i class="fas fa-sync-alt me-1"></i>刷新
                    </button>
                    <button class="btn btn-outline-info" onclick="exportData()">
                        <i class="fas fa-download me-1"></i>导出数据
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- GPU状态卡片 -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                GPU使用率
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="gpuUsage">
                                <i class="fas fa-spinner fa-spin"></i> 检查中...
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-tachometer-alt fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                内存使用
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="memoryUsage">
                                <i class="fas fa-spinner fa-spin"></i> 检查中...
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-memory fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                温度
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="temperature">
                                <i class="fas fa-spinner fa-spin"></i> 检查中...
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-thermometer-half fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                功耗
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="powerUsage">
                                <i class="fas fa-spinner fa-spin"></i> 检查中...
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-bolt fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 监控图表 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-line me-2"></i>实时监控图表
                    </h6>
                </div>
                <div class="card-body">
                    <canvas id="gpuMonitorChart" width="400" height="100"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- GPU详细信息 -->
    <div class="row">
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle me-2"></i>GPU详细信息
                    </h6>
                </div>
                <div class="card-body">
                    <div id="gpuDetails">
                        <div class="text-center py-4">
                            <i class="fas fa-spinner fa-spin fa-2x text-muted mb-3"></i>
                            <p class="text-muted">加载GPU信息...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-lg-5">
            <!-- 系统状态 -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-desktop me-2"></i>系统状态
                    </h6>
                </div>
                <div class="card-body">
                    <div id="systemStatus">
                        <div class="text-center py-3">
                            <i class="fas fa-spinner fa-spin fa-2x text-muted mb-2"></i>
                            <p class="text-muted">加载系统状态...</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 监控设置 -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-cog me-2"></i>监控设置
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">刷新间隔</label>
                        <select class="form-select" id="refreshInterval">
                            <option value="1000">1秒</option>
                            <option value="2000" selected>2秒</option>
                            <option value="5000">5秒</option>
                            <option value="10000">10秒</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="autoRefresh" checked>
                            <label class="form-check-label" for="autoRefresh">
                                自动刷新
                            </label>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="showAlerts" checked>
                            <label class="form-check-label" for="showAlerts">
                                性能警告
                            </label>
                        </div>
                    </div>
                    
                    <button class="btn btn-primary btn-sm" onclick="applySettings()">
                        <i class="fas fa-save me-1"></i>应用设置
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
let monitorChart;
let monitorInterval;
let chartData = {
    labels: [],
    datasets: [{
        label: 'GPU使用率 (%)',
        data: [],
        borderColor: 'rgb(75, 192, 192)',
        backgroundColor: 'rgba(75, 192, 192, 0.2)',
        tension: 0.1
    }, {
        label: 'GPU内存使用率 (%)',
        data: [],
        borderColor: 'rgb(255, 99, 132)',
        backgroundColor: 'rgba(255, 99, 132, 0.2)',
        tension: 0.1
    }, {
        label: 'GPU温度 (°C)',
        data: [],
        borderColor: 'rgb(255, 205, 86)',
        backgroundColor: 'rgba(255, 205, 86, 0.2)',
        tension: 0.1
    }]
};

// 初始化监控图表
function initMonitorChart() {
    const ctx = document.getElementById('gpuMonitorChart').getContext('2d');
    monitorChart = new Chart(ctx, {
        type: 'line',
        data: chartData,
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100
                }
            },
            plugins: {
                legend: {
                    display: true
                }
            },
            animation: {
                duration: 0 // 禁用动画以提高性能
            }
        }
    });
}

// 加载GPU状态
async function loadGPUStatus() {
    try {
        const response = await fetch('/api/deep-learning/gpu-status');
        const data = await response.json();
        
        if (data.success) {
            updateGPUStatus(data.gpu_status);
            updateGPUDetails(data.gpu_status);
        } else {
            showGPUError('获取GPU状态失败: ' + data.error);
        }
    } catch (error) {
        console.error('获取GPU状态失败:', error);
        showGPUError('获取GPU状态失败: ' + error.message);
    }
}

// 更新GPU状态卡片
function updateGPUStatus(status) {
    if (!status) {
        showGPUError('GPU状态数据为空');
        return;
    }

    // 使用真实的GPU数据
    const gpuUsage = status.gpu_available ? (status.gpu_utilization || 0) : 0;
    const memoryUsage = status.gpu_available ? (status.memory_usage_percent || 0) : 0;
    const temperature = status.gpu_available ? (status.temperature || 0) : 0;
    const powerUsage = status.gpu_available ? (status.power_usage || 0) : 0;

    // 更新显示元素
    const gpuUsageElement = document.getElementById('gpuUsage');
    const memoryUsageElement = document.getElementById('memoryUsage');
    const temperatureElement = document.getElementById('temperature');
    const powerUsageElement = document.getElementById('powerUsage');

    if (gpuUsageElement) {
        gpuUsageElement.innerHTML = status.gpu_available ? `${gpuUsage.toFixed(1)}%` : 'N/A';
    }

    if (memoryUsageElement) {
        memoryUsageElement.innerHTML = status.gpu_available ? `${memoryUsage.toFixed(1)}%` : 'N/A';
    }

    if (temperatureElement) {
        temperatureElement.innerHTML = status.gpu_available ? `${temperature.toFixed(1)}°C` : 'N/A';
    }

    if (powerUsageElement) {
        powerUsageElement.innerHTML = status.gpu_available ? `${powerUsage.toFixed(0)}W` : 'N/A';
    }

    // 更新图表数据
    if (status.gpu_available) {
        updateChartData(gpuUsage, memoryUsage, temperature);
    }
}

// 更新图表数据
function updateChartData(gpuUsage, memoryUsage, temperature) {
    const now = new Date().toLocaleTimeString();
    
    // 添加新数据点
    chartData.labels.push(now);
    chartData.datasets[0].data.push(gpuUsage);
    chartData.datasets[1].data.push(memoryUsage);
    chartData.datasets[2].data.push(temperature);
    
    // 保持最多50个数据点
    if (chartData.labels.length > 50) {
        chartData.labels.shift();
        chartData.datasets.forEach(dataset => dataset.data.shift());
    }
    
    // 更新图表
    monitorChart.update('none');
}

// 更新GPU详细信息
function updateGPUDetails(status) {
    const detailsElement = document.getElementById('gpuDetails');

    if (!detailsElement) {
        console.error('GPU详细信息元素未找到');
        return;
    }

    if (status && status.gpu_available) {
        detailsElement.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <h6 class="text-primary">硬件信息</h6>
                    <table class="table table-sm">
                        <tr><td>GPU名称:</td><td>${status.gpu_name || 'N/A'}</td></tr>
                        <tr><td>CUDA版本:</td><td>${status.cuda_version || 'N/A'}</td></tr>
                        <tr><td>总内存:</td><td>${(status.memory_total || 0).toFixed(1)} GB</td></tr>
                        <tr><td>已用内存:</td><td>${(status.memory_used || 0).toFixed(1)} GB</td></tr>
                        <tr><td>可用内存:</td><td>${(status.memory_free || 0).toFixed(1)} GB</td></tr>
                        <tr><td>内存使用率:</td><td>${(status.memory_usage_percent || 0).toFixed(1)}%</td></tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h6 class="text-primary">性能信息</h6>
                    <table class="table table-sm">
                        <tr><td>GPU使用率:</td><td>${(status.gpu_utilization || 0).toFixed(1)}%</td></tr>
                        <tr><td>温度:</td><td>${(status.temperature || 0).toFixed(1)}°C</td></tr>
                        <tr><td>功耗:</td><td>${(status.power_usage || 0).toFixed(0)}W</td></tr>
                        <tr><td>PyTorch版本:</td><td>${status.pytorch_version || 'N/A'}</td></tr>
                        <tr><td>TensorFlow版本:</td><td>${status.tensorflow_version || 'N/A'}</td></tr>
                        <tr><td>状态:</td><td><span class="badge bg-success">正常</span></td></tr>
                    </table>
                </div>
            </div>
        `;
    } else {
        detailsElement.innerHTML = `
            <div class="alert alert-warning">
                <h6><i class="fas fa-exclamation-triangle me-2"></i>GPU不可用</h6>
                <p>系统未检测到可用的GPU，或GPU驱动未正确安装。</p>
                <p class="mb-0">当前使用CPU模式进行计算。</p>
            </div>
        `;
    }
}

// 加载系统状态
async function loadSystemStatus() {
    try {
        const response = await fetch('/api/deep-learning/system-info');
        const data = await response.json();
        
        if (data.success) {
            updateSystemStatus(data.info);
        }
    } catch (error) {
        console.error('获取系统状态失败:', error);
    }
}

// 更新系统状态
function updateSystemStatus(info) {
    const statusElement = document.getElementById('systemStatus');
    
    statusElement.innerHTML = `
        <div class="mb-2">
            <small class="text-muted">Python版本:</small><br>
            <strong>${info.python_version}</strong>
        </div>
        <div class="mb-2">
            <small class="text-muted">可用内存:</small><br>
            <strong>${info.available_memory} GB</strong>
        </div>
        <div class="mb-2">
            <small class="text-muted">CPU核心数:</small><br>
            <strong>${info.cpu_count}</strong>
        </div>
        <div class="mb-2">
            <small class="text-muted">当前设备:</small><br>
            <span class="badge ${info.device.includes('cuda') ? 'bg-success' : 'bg-warning'}">${info.device}</span>
        </div>
    `;
}

// 显示GPU错误
function showGPUError(message) {
    console.error('GPU监控错误:', message);

    // 安全地更新元素
    const elements = ['gpuUsage', 'memoryUsage', 'temperature', 'powerUsage'];
    elements.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.innerHTML = '<span class="text-danger">错误</span>';
        }
    });

    const detailsElement = document.getElementById('gpuDetails');
    if (detailsElement) {
        detailsElement.innerHTML = `
            <div class="alert alert-danger">
                <h6><i class="fas fa-exclamation-triangle me-2"></i>监控错误</h6>
                <p class="mb-2">${message}</p>
                <div class="mt-2">
                    <small class="text-muted">
                        <strong>可能的解决方案:</strong><br>
                        • 检查GPU驱动是否正确安装<br>
                        • 确认CUDA环境配置正确<br>
                        • 重新启动应用程序<br>
                        • 查看控制台日志获取详细错误信息
                    </small>
                </div>
            </div>
        `;
    }
}

// 开始自动刷新
function startAutoRefresh() {
    const interval = parseInt(document.getElementById('refreshInterval').value);
    
    if (monitorInterval) {
        clearInterval(monitorInterval);
    }
    
    monitorInterval = setInterval(() => {
        if (document.getElementById('autoRefresh').checked) {
            loadGPUStatus();
        }
    }, interval);
}

// 应用设置
function applySettings() {
    if (document.getElementById('autoRefresh').checked) {
        startAutoRefresh();
    } else {
        if (monitorInterval) {
            clearInterval(monitorInterval);
        }
    }
    
    showSuccess('设置已应用');
}

// 刷新监控
function refreshMonitor() {
    loadGPUStatus();
    loadSystemStatus();
}

// 导出数据
function exportData() {
    showInfo('数据导出功能正在开发中');
}

// 显示成功消息
function showSuccess(message) {
    // 这里可以实现一个更好的提示组件
    console.log('✅ ' + message);
}

// 显示信息消息
function showInfo(message) {
    alert('ℹ️ ' + message);
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    initMonitorChart();
    loadGPUStatus();
    loadSystemStatus();
    
    // 监听设置变化
    document.getElementById('autoRefresh').addEventListener('change', function() {
        if (this.checked) {
            startAutoRefresh();
        } else {
            if (monitorInterval) {
                clearInterval(monitorInterval);
            }
        }
    });
    
    document.getElementById('refreshInterval').addEventListener('change', function() {
        if (document.getElementById('autoRefresh').checked) {
            startAutoRefresh();
        }
    });
    
    // 开始自动刷新
    startAutoRefresh();
});

// 页面卸载时清理
window.addEventListener('beforeunload', function() {
    if (monitorInterval) {
        clearInterval(monitorInterval);
    }
});
</script>

<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

.text-xs {
    font-size: 0.7rem;
}

.shadow {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
}
</style>
{% endblock %}
