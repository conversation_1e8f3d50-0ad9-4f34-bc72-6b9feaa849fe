#!/usr/bin/env python3
"""
MT5真实数据形态分析器
从MT5获取真实历史数据并识别各种技术形态
"""

import MetaTrader5 as mt5
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from typing import List, Dict, Optional, Tuple
import talib

logger = logging.getLogger(__name__)

class MT5PatternAnalyzer:
    """MT5真实数据形态分析器"""
    
    def __init__(self):
        self.patterns_found = []
        self.min_pattern_bars = 20  # 形态最小K线数量
        self.max_pattern_bars = 100  # 形态最大K线数量
        
    def connect_mt5(self) -> bool:
        """连接MT5"""
        try:
            if not mt5.initialize():
                error = mt5.last_error()
                logger.error(f"MT5初始化失败，错误代码: {error}")
                return False

            # 检查终端信息
            terminal_info = mt5.terminal_info()
            if not terminal_info:
                logger.error("无法获取MT5终端信息")
                mt5.shutdown()
                return False

            # 检查连接状态
            if not terminal_info.connected:
                logger.error("MT5终端未连接到服务器")
                mt5.shutdown()
                return False

            # 检查DLL权限
            if not terminal_info.dlls_allowed:
                logger.error("MT5未允许DLL导入，请在MT5中启用")
                mt5.shutdown()
                return False

            logger.info(f"MT5连接成功 - 版本: {terminal_info.build}, 公司: {terminal_info.company}")
            return True

        except Exception as e:
            logger.error(f"MT5连接异常: {e}")
            return False
    
    def get_historical_data(self, symbol: str, timeframe: str, bars: int = 1000) -> Optional[pd.DataFrame]:
        """从MT5获取历史数据"""
        try:
            # 时间周期映射
            timeframe_map = {
                '1m': mt5.TIMEFRAME_M1,
                '5m': mt5.TIMEFRAME_M5,
                '15m': mt5.TIMEFRAME_M15,
                '30m': mt5.TIMEFRAME_M30,
                '1h': mt5.TIMEFRAME_H1,
                '4h': mt5.TIMEFRAME_H4,
                '1d': mt5.TIMEFRAME_D1
            }
            
            mt5_timeframe = timeframe_map.get(timeframe, mt5.TIMEFRAME_H1)
            
            # 获取数据
            rates = mt5.copy_rates_from_pos(symbol, mt5_timeframe, 0, bars)
            
            if rates is None or len(rates) == 0:
                logger.error(f"无法获取{symbol}的历史数据")
                return None
            
            # 转换为DataFrame
            df = pd.DataFrame(rates)
            df['time'] = pd.to_datetime(df['time'], unit='s')
            df.set_index('time', inplace=True)
            
            # 重命名列以符合标准格式
            df.rename(columns={
                'open': 'Open',
                'high': 'High', 
                'low': 'Low',
                'close': 'Close',
                'tick_volume': 'Volume'
            }, inplace=True)
            
            logger.info(f"成功获取{symbol} {timeframe}数据: {len(df)}条记录")
            return df
            
        except Exception as e:
            logger.error(f"获取历史数据失败: {e}")
            return None
    
    def identify_head_shoulders_top(self, df: pd.DataFrame) -> List[Dict]:
        """识别头肩顶形态"""
        patterns = []
        highs = df['High'].values
        lows = df['Low'].values
        closes = df['Close'].values
        
        for i in range(50, len(df) - 50):
            # 寻找三个高点
            left_shoulder_idx = self._find_local_peak(highs, i-40, i-20)
            head_idx = self._find_local_peak(highs, i-20, i+20)
            right_shoulder_idx = self._find_local_peak(highs, i+20, i+40)
            
            if left_shoulder_idx and head_idx and right_shoulder_idx:
                left_high = highs[left_shoulder_idx]
                head_high = highs[head_idx]
                right_high = highs[right_shoulder_idx]
                
                # 头肩顶条件：头部最高，两肩相近
                if (head_high > left_high and head_high > right_high and
                    abs(left_high - right_high) / left_high < 0.02):  # 2%误差
                    
                    # 计算颈线
                    left_valley = self._find_local_valley(lows, left_shoulder_idx, head_idx)
                    right_valley = self._find_local_valley(lows, head_idx, right_shoulder_idx)
                    
                    if left_valley and right_valley:
                        neckline = (lows[left_valley] + lows[right_valley]) / 2
                        
                        patterns.append({
                            'pattern_type': 'head_shoulders_top',
                            'pattern_name': '头肩顶',
                            'direction': 'bearish',
                            'start_idx': int(left_shoulder_idx),
                            'end_idx': int(right_shoulder_idx),
                            'confidence': float(self._calculate_hs_confidence(df, left_shoulder_idx, head_idx, right_shoulder_idx)),
                            'entry_point': float(neckline),
                            'stop_loss': float(head_high),
                            'take_profit': float(neckline - (head_high - neckline)),
                            'key_levels': {
                                'left_shoulder': float(left_high),
                                'head': float(head_high),
                                'right_shoulder': float(right_high),
                                'neckline': float(neckline)
                            }
                        })
        
        return patterns
    
    def identify_head_shoulders_bottom(self, df: pd.DataFrame) -> List[Dict]:
        """识别头肩底形态"""
        patterns = []
        highs = df['High'].values
        lows = df['Low'].values
        
        for i in range(50, len(df) - 50):
            # 寻找三个低点
            left_shoulder_idx = self._find_local_valley(lows, i-40, i-20)
            head_idx = self._find_local_valley(lows, i-20, i+20)
            right_shoulder_idx = self._find_local_valley(lows, i+20, i+40)
            
            if left_shoulder_idx and head_idx and right_shoulder_idx:
                left_low = lows[left_shoulder_idx]
                head_low = lows[head_idx]
                right_low = lows[right_shoulder_idx]
                
                # 头肩底条件：头部最低，两肩相近
                if (head_low < left_low and head_low < right_low and
                    abs(left_low - right_low) / left_low < 0.02):
                    
                    # 计算颈线
                    left_peak = self._find_local_peak(highs, left_shoulder_idx, head_idx)
                    right_peak = self._find_local_peak(highs, head_idx, right_shoulder_idx)
                    
                    if left_peak and right_peak:
                        neckline = (highs[left_peak] + highs[right_peak]) / 2
                        
                        patterns.append({
                            'pattern_type': 'head_shoulders_bottom',
                            'pattern_name': '头肩底',
                            'direction': 'bullish',
                            'start_idx': int(left_shoulder_idx),
                            'end_idx': int(right_shoulder_idx),
                            'confidence': float(self._calculate_hs_confidence(df, left_shoulder_idx, head_idx, right_shoulder_idx)),
                            'entry_point': float(neckline),
                            'stop_loss': float(head_low),
                            'take_profit': float(neckline + (neckline - head_low)),
                            'key_levels': {
                                'left_shoulder': float(left_low),
                                'head': float(head_low),
                                'right_shoulder': float(right_low),
                                'neckline': float(neckline)
                            }
                        })
        
        return patterns
    
    def identify_double_top(self, df: pd.DataFrame) -> List[Dict]:
        """识别双顶形态"""
        patterns = []
        highs = df['High'].values
        lows = df['Low'].values
        
        for i in range(30, len(df) - 30):
            # 寻找两个高点
            first_peak_idx = self._find_local_peak(highs, i-25, i-5)
            second_peak_idx = self._find_local_peak(highs, i+5, i+25)
            
            if first_peak_idx and second_peak_idx:
                first_high = highs[first_peak_idx]
                second_high = highs[second_peak_idx]
                
                # 双顶条件：两个高点相近
                if abs(first_high - second_high) / first_high < 0.015:  # 1.5%误差
                    
                    # 找到中间的低点作为颈线
                    valley_idx = self._find_local_valley(lows, first_peak_idx, second_peak_idx)
                    
                    if valley_idx:
                        neckline = lows[valley_idx]
                        
                        patterns.append({
                            'pattern_type': 'double_top',
                            'pattern_name': '双顶',
                            'direction': 'bearish',
                            'start_idx': first_peak_idx,
                            'end_idx': second_peak_idx,
                            'confidence': self._calculate_double_confidence(first_high, second_high, neckline),
                            'entry_point': neckline,
                            'stop_loss': max(first_high, second_high),
                            'take_profit': neckline - (max(first_high, second_high) - neckline),
                            'key_levels': {
                                'first_peak': first_high,
                                'second_peak': second_high,
                                'neckline': neckline
                            }
                        })
        
        return patterns
    
    def identify_double_bottom(self, df: pd.DataFrame) -> List[Dict]:
        """识别双底形态"""
        patterns = []
        highs = df['High'].values
        lows = df['Low'].values
        
        for i in range(30, len(df) - 30):
            # 寻找两个低点
            first_valley_idx = self._find_local_valley(lows, i-25, i-5)
            second_valley_idx = self._find_local_valley(lows, i+5, i+25)
            
            if first_valley_idx and second_valley_idx:
                first_low = lows[first_valley_idx]
                second_low = lows[second_valley_idx]
                
                # 双底条件：两个低点相近
                if abs(first_low - second_low) / first_low < 0.015:
                    
                    # 找到中间的高点作为颈线
                    peak_idx = self._find_local_peak(highs, first_valley_idx, second_valley_idx)
                    
                    if peak_idx:
                        neckline = highs[peak_idx]
                        
                        patterns.append({
                            'pattern_type': 'double_bottom',
                            'pattern_name': '双底',
                            'direction': 'bullish',
                            'start_idx': first_valley_idx,
                            'end_idx': second_valley_idx,
                            'confidence': self._calculate_double_confidence(first_low, second_low, neckline),
                            'entry_point': neckline,
                            'stop_loss': min(first_low, second_low),
                            'take_profit': neckline + (neckline - min(first_low, second_low)),
                            'key_levels': {
                                'first_bottom': first_low,
                                'second_bottom': second_low,
                                'neckline': neckline
                            }
                        })
        
        return patterns
    
    def identify_triangles(self, df: pd.DataFrame) -> List[Dict]:
        """识别三角形形态"""
        patterns = []
        highs = df['High'].values
        lows = df['Low'].values
        
        # 上升三角形
        ascending_triangles = self._find_ascending_triangles(df)
        patterns.extend(ascending_triangles)
        
        # 下降三角形
        descending_triangles = self._find_descending_triangles(df)
        patterns.extend(descending_triangles)
        
        # 对称三角形
        symmetric_triangles = self._find_symmetric_triangles(df)
        patterns.extend(symmetric_triangles)
        
        return patterns
    
    def _find_local_peak(self, data: np.ndarray, start: int, end: int) -> Optional[int]:
        """寻找局部高点"""
        if start < 0 or end >= len(data) or start >= end:
            return None
        
        segment = data[start:end+1]
        if len(segment) < 3:
            return None
        
        max_idx = np.argmax(segment)
        global_idx = start + max_idx
        
        # 确保是真正的局部高点
        if (global_idx > 0 and global_idx < len(data) - 1 and
            data[global_idx] > data[global_idx-1] and 
            data[global_idx] > data[global_idx+1]):
            return global_idx
        
        return None
    
    def _find_local_valley(self, data: np.ndarray, start: int, end: int) -> Optional[int]:
        """寻找局部低点"""
        if start < 0 or end >= len(data) or start >= end:
            return None
        
        segment = data[start:end+1]
        if len(segment) < 3:
            return None
        
        min_idx = np.argmin(segment)
        global_idx = start + min_idx
        
        # 确保是真正的局部低点
        if (global_idx > 0 and global_idx < len(data) - 1 and
            data[global_idx] < data[global_idx-1] and 
            data[global_idx] < data[global_idx+1]):
            return global_idx
        
        return None

    def _calculate_hs_confidence(self, df: pd.DataFrame, left_idx: int, head_idx: int, right_idx: int) -> float:
        """计算头肩形态置信度"""
        try:
            highs = df['High'].values
            volumes = df['Volume'].values if 'Volume' in df.columns else None

            left_high = highs[left_idx]
            head_high = highs[head_idx]
            right_high = highs[right_idx]

            # 基础置信度
            confidence = 0.5

            # 头部明显高于肩部
            head_prominence = min((head_high - left_high) / left_high, (head_high - right_high) / right_high)
            if head_prominence > 0.02:  # 2%以上
                confidence += 0.2

            # 两肩高度相似
            shoulder_similarity = 1 - abs(left_high - right_high) / max(left_high, right_high)
            confidence += shoulder_similarity * 0.2

            # 成交量确认（如果有数据）
            if volumes is not None:
                left_vol = volumes[left_idx]
                head_vol = volumes[head_idx]
                right_vol = volumes[right_idx]

                # 右肩成交量应该较小
                if right_vol < left_vol and right_vol < head_vol:
                    confidence += 0.1

            return min(1.0, confidence)

        except Exception:
            return 0.5

    def _calculate_double_confidence(self, first_level: float, second_level: float, neckline: float) -> float:
        """计算双顶/双底置信度"""
        try:
            # 两个顶/底的相似度
            similarity = 1 - abs(first_level - second_level) / max(abs(first_level), abs(second_level))

            # 与颈线的距离
            distance_ratio = abs(first_level - neckline) / abs(first_level)

            confidence = 0.3 + similarity * 0.4 + min(distance_ratio, 0.1) * 3

            return min(1.0, confidence)

        except Exception:
            return 0.5

    def _find_ascending_triangles(self, df: pd.DataFrame) -> List[Dict]:
        """寻找上升三角形"""
        patterns = []
        highs = df['High'].values
        lows = df['Low'].values

        for i in range(40, len(df) - 20):
            # 寻找水平阻力线（多个相近的高点）
            resistance_points = []
            support_points = []

            # 在一定范围内寻找高点和低点
            for j in range(i-35, i+15):
                if j > 5 and j < len(highs) - 5:
                    # 检查是否为局部高点
                    if (highs[j] > highs[j-1] and highs[j] > highs[j+1] and
                        highs[j] > highs[j-2] and highs[j] > highs[j+2]):
                        resistance_points.append((j, highs[j]))

                    # 检查是否为局部低点
                    if (lows[j] < lows[j-1] and lows[j] < lows[j+1] and
                        lows[j] < lows[j-2] and lows[j] < lows[j+2]):
                        support_points.append((j, lows[j]))

            if len(resistance_points) >= 2 and len(support_points) >= 2:
                # 检查阻力线是否水平
                resistance_levels = [point[1] for point in resistance_points]
                if max(resistance_levels) - min(resistance_levels) < np.mean(resistance_levels) * 0.01:  # 1%误差

                    # 检查支撑线是否上升
                    support_points.sort(key=lambda x: x[0])  # 按时间排序
                    if len(support_points) >= 2:
                        first_support = support_points[0][1]
                        last_support = support_points[-1][1]

                        if last_support > first_support:  # 支撑线上升
                            resistance_level = np.mean(resistance_levels)

                            patterns.append({
                                'pattern_type': 'ascending_triangle',
                                'pattern_name': '上升三角形',
                                'direction': 'bullish',
                                'start_idx': support_points[0][0],
                                'end_idx': resistance_points[-1][0],
                                'confidence': 0.7,
                                'entry_point': resistance_level * 1.001,  # 突破阻力线
                                'stop_loss': last_support,
                                'take_profit': resistance_level + (resistance_level - last_support),
                                'key_levels': {
                                    'resistance': resistance_level,
                                    'support_start': first_support,
                                    'support_end': last_support
                                }
                            })

        return patterns

    def _find_descending_triangles(self, df: pd.DataFrame) -> List[Dict]:
        """寻找下降三角形"""
        patterns = []
        highs = df['High'].values
        lows = df['Low'].values

        for i in range(40, len(df) - 20):
            # 寻找水平支撑线和下降阻力线
            resistance_points = []
            support_points = []

            for j in range(i-35, i+15):
                if j > 5 and j < len(highs) - 5:
                    if (highs[j] > highs[j-1] and highs[j] > highs[j+1]):
                        resistance_points.append((j, highs[j]))

                    if (lows[j] < lows[j-1] and lows[j] < lows[j+1]):
                        support_points.append((j, lows[j]))

            if len(resistance_points) >= 2 and len(support_points) >= 2:
                # 检查支撑线是否水平
                support_levels = [point[1] for point in support_points]
                if max(support_levels) - min(support_levels) < np.mean(support_levels) * 0.01:

                    # 检查阻力线是否下降
                    resistance_points.sort(key=lambda x: x[0])
                    if len(resistance_points) >= 2:
                        first_resistance = resistance_points[0][1]
                        last_resistance = resistance_points[-1][1]

                        if last_resistance < first_resistance:  # 阻力线下降
                            support_level = np.mean(support_levels)

                            patterns.append({
                                'pattern_type': 'descending_triangle',
                                'pattern_name': '下降三角形',
                                'direction': 'bearish',
                                'start_idx': resistance_points[0][0],
                                'end_idx': support_points[-1][0],
                                'confidence': 0.7,
                                'entry_point': support_level * 0.999,  # 跌破支撑线
                                'stop_loss': last_resistance,
                                'take_profit': support_level - (last_resistance - support_level),
                                'key_levels': {
                                    'support': support_level,
                                    'resistance_start': first_resistance,
                                    'resistance_end': last_resistance
                                }
                            })

        return patterns

    def _find_symmetric_triangles(self, df: pd.DataFrame) -> List[Dict]:
        """寻找对称三角形"""
        patterns = []
        highs = df['High'].values
        lows = df['Low'].values

        for i in range(50, len(df) - 25):
            # 寻找收敛的高点和低点
            resistance_points = []
            support_points = []

            for j in range(i-45, i+20):
                if j > 5 and j < len(highs) - 5:
                    if (highs[j] > highs[j-1] and highs[j] > highs[j+1]):
                        resistance_points.append((j, highs[j]))

                    if (lows[j] < lows[j-1] and lows[j] < lows[j+1]):
                        support_points.append((j, lows[j]))

            if len(resistance_points) >= 3 and len(support_points) >= 3:
                resistance_points.sort(key=lambda x: x[0])
                support_points.sort(key=lambda x: x[0])

                # 检查高点是否下降，低点是否上升
                resistance_trend = (resistance_points[-1][1] - resistance_points[0][1]) / resistance_points[0][1]
                support_trend = (support_points[-1][1] - support_points[0][1]) / support_points[0][1]

                if resistance_trend < -0.01 and support_trend > 0.01:  # 收敛趋势
                    # 计算交汇点
                    mid_price = (resistance_points[-1][1] + support_points[-1][1]) / 2

                    patterns.append({
                        'pattern_type': 'symmetric_triangle',
                        'pattern_name': '对称三角形',
                        'direction': 'neutral',  # 方向待定
                        'start_idx': min(resistance_points[0][0], support_points[0][0]),
                        'end_idx': max(resistance_points[-1][0], support_points[-1][0]),
                        'confidence': 0.6,
                        'entry_point': mid_price,
                        'stop_loss': None,  # 需要根据突破方向确定
                        'take_profit': None,
                        'key_levels': {
                            'resistance_start': resistance_points[0][1],
                            'resistance_end': resistance_points[-1][1],
                            'support_start': support_points[0][1],
                            'support_end': support_points[-1][1]
                        }
                    })

        return patterns

    def identify_wedges(self, df: pd.DataFrame) -> List[Dict]:
        """识别楔形形态"""
        patterns = []

        # 上升楔形
        rising_wedges = self._find_rising_wedges(df)
        patterns.extend(rising_wedges)

        # 下降楔形
        falling_wedges = self._find_falling_wedges(df)
        patterns.extend(falling_wedges)

        return patterns

    def _find_rising_wedges(self, df: pd.DataFrame) -> List[Dict]:
        """寻找上升楔形"""
        patterns = []
        highs = df['High'].values
        lows = df['Low'].values

        for i in range(40, len(df) - 20):
            resistance_points = []
            support_points = []

            for j in range(i-35, i+15):
                if j > 5 and j < len(highs) - 5:
                    if (highs[j] > highs[j-1] and highs[j] > highs[j+1]):
                        resistance_points.append((j, highs[j]))

                    if (lows[j] < lows[j-1] and lows[j] < lows[j+1]):
                        support_points.append((j, lows[j]))

            if len(resistance_points) >= 2 and len(support_points) >= 2:
                resistance_points.sort(key=lambda x: x[0])
                support_points.sort(key=lambda x: x[0])

                # 检查两条线都上升，但支撑线上升更陡
                resistance_slope = (resistance_points[-1][1] - resistance_points[0][1]) / (resistance_points[-1][0] - resistance_points[0][0])
                support_slope = (support_points[-1][1] - support_points[0][1]) / (support_points[-1][0] - support_points[0][0])

                if resistance_slope > 0 and support_slope > 0 and support_slope > resistance_slope:
                    patterns.append({
                        'pattern_type': 'rising_wedge',
                        'pattern_name': '上升楔形',
                        'direction': 'bearish',  # 上升楔形通常看跌
                        'start_idx': min(resistance_points[0][0], support_points[0][0]),
                        'end_idx': max(resistance_points[-1][0], support_points[-1][0]),
                        'confidence': 0.65,
                        'entry_point': support_points[-1][1] * 0.999,  # 跌破支撑
                        'stop_loss': resistance_points[-1][1],
                        'take_profit': support_points[-1][1] - (resistance_points[-1][1] - support_points[-1][1]),
                        'key_levels': {
                            'resistance_slope': resistance_slope,
                            'support_slope': support_slope
                        }
                    })

        return patterns

    def _find_falling_wedges(self, df: pd.DataFrame) -> List[Dict]:
        """寻找下降楔形"""
        patterns = []
        highs = df['High'].values
        lows = df['Low'].values

        for i in range(40, len(df) - 20):
            resistance_points = []
            support_points = []

            for j in range(i-35, i+15):
                if j > 5 and j < len(highs) - 5:
                    if (highs[j] > highs[j-1] and highs[j] > highs[j+1]):
                        resistance_points.append((j, highs[j]))

                    if (lows[j] < lows[j-1] and lows[j] < lows[j+1]):
                        support_points.append((j, lows[j]))

            if len(resistance_points) >= 2 and len(support_points) >= 2:
                resistance_points.sort(key=lambda x: x[0])
                support_points.sort(key=lambda x: x[0])

                # 检查两条线都下降，但阻力线下降更陡
                resistance_slope = (resistance_points[-1][1] - resistance_points[0][1]) / (resistance_points[-1][0] - resistance_points[0][0])
                support_slope = (support_points[-1][1] - support_points[0][1]) / (support_points[-1][0] - support_points[0][0])

                if resistance_slope < 0 and support_slope < 0 and resistance_slope < support_slope:
                    patterns.append({
                        'pattern_type': 'falling_wedge',
                        'pattern_name': '下降楔形',
                        'direction': 'bullish',  # 下降楔形通常看涨
                        'start_idx': min(resistance_points[0][0], support_points[0][0]),
                        'end_idx': max(resistance_points[-1][0], support_points[-1][0]),
                        'confidence': 0.65,
                        'entry_point': resistance_points[-1][1] * 1.001,  # 突破阻力
                        'stop_loss': support_points[-1][1],
                        'take_profit': resistance_points[-1][1] + (resistance_points[-1][1] - support_points[-1][1]),
                        'key_levels': {
                            'resistance_slope': resistance_slope,
                            'support_slope': support_slope
                        }
                    })

        return patterns
