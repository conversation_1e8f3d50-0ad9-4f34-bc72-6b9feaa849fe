#!/usr/bin/env python3
"""
检查数据库中保存的模型训练信息
"""

import sqlite3
import json
from datetime import datetime

def check_model_database():
    """检查数据库中的模型信息"""
    
    print("🔍 检查数据库中的AI模型训练信息")
    print("=" * 70)
    
    try:
        # 连接数据库
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 获取所有深度学习模型
        cursor.execute('''
            SELECT id, name, symbol, timeframe, config, created_at, completed_at, status
            FROM deep_learning_models
            ORDER BY created_at DESC
            LIMIT 5
        ''')
        
        models = cursor.fetchall()
        
        print(f"📊 找到 {len(models)} 个模型记录")
        print("-" * 70)
        
        for i, model in enumerate(models, 1):
            model_id, name, symbol, timeframe, config_str, created_at, completed_at, status = model
            
            print(f"\n{i}. 模型: {name} ({model_id[:8]}...)")
            print(f"   状态: {status}")
            print(f"   品种: {symbol} {timeframe}")
            print(f"   创建时间: {created_at}")
            print(f"   完成时间: {completed_at}")
            
            # 解析配置
            try:
                config = json.loads(config_str) if config_str else {}
                
                print(f"\n   📋 配置信息:")
                
                # 检查数据配置
                data_config = config.get('data_config', {})
                if data_config:
                    print(f"      数据配置模式: {data_config.get('mode', 'N/A')}")
                    print(f"      训练天数: {data_config.get('training_days', 'N/A')}")
                    print(f"      开始日期: {data_config.get('start_date', 'N/A')}")
                    print(f"      结束日期: {data_config.get('end_date', 'N/A')}")
                else:
                    print(f"      ⚠️ 没有data_config信息")
                
                # 检查其他日期相关字段
                print(f"      训练天数(旧): {config.get('training_days', 'N/A')}")
                print(f"      开始日期(旧): {config.get('start_date', 'N/A')}")
                print(f"      结束日期(旧): {config.get('end_date', 'N/A')}")
                
                # 检查特征配置
                features = config.get('features', {})
                if features:
                    print(f"      特征配置: {features}")
                
            except Exception as e:
                print(f"      ❌ 配置解析失败: {e}")
        
        # 检查训练任务日志
        print(f"\n📋 检查训练任务日志:")
        print("-" * 50)
        
        cursor.execute('''
            SELECT t.id, t.model_id, t.status, t.logs, m.name
            FROM training_tasks t
            JOIN deep_learning_models m ON t.model_id = m.id
            ORDER BY t.created_at DESC
            LIMIT 3
        ''')
        
        tasks = cursor.fetchall()
        
        for i, task in enumerate(tasks, 1):
            task_id, model_id, status, logs_str, model_name = task
            
            print(f"\n{i}. 任务: {model_name} ({task_id[:8]}...)")
            print(f"   状态: {status}")
            
            # 解析日志
            try:
                logs = json.loads(logs_str) if logs_str else {}
                
                # 查找数据信息
                data_info = logs.get('data_info', {})
                if data_info:
                    print(f"   📊 数据信息:")
                    print(f"      开始日期: {data_info.get('start_date', 'N/A')}")
                    print(f"      结束日期: {data_info.get('end_date', 'N/A')}")
                    print(f"      总样本数: {data_info.get('total_samples', 'N/A')}")
                    print(f"      训练样本: {data_info.get('training_samples', 'N/A')}")
                    print(f"      验证样本: {data_info.get('validation_samples', 'N/A')}")
                else:
                    print(f"   ⚠️ 没有data_info信息")
                
                # 查找训练信息
                training_info = logs.get('training_info', {})
                if training_info:
                    print(f"   🏋️ 训练信息:")
                    for key, value in training_info.items():
                        print(f"      {key}: {value}")
                
            except Exception as e:
                print(f"   ❌ 日志解析失败: {e}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库检查失败: {e}")
        return False

def check_specific_model():
    """检查特定模型的详细信息"""
    
    print(f"\n🔍 检查特定模型的详细信息")
    print("-" * 50)
    
    try:
        # 连接数据库
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 获取最新的已完成模型
        cursor.execute('''
            SELECT id, name, config
            FROM deep_learning_models
            WHERE status = 'completed'
            ORDER BY created_at DESC
            LIMIT 1
        ''')
        
        result = cursor.fetchone()
        
        if result:
            model_id, name, config_str = result
            
            print(f"模型: {name} ({model_id[:8]}...)")
            
            # 解析配置
            config = json.loads(config_str) if config_str else {}
            
            print(f"\n📋 完整配置:")
            print(json.dumps(config, indent=2, ensure_ascii=False))
            
            # 特别检查日期相关配置
            print(f"\n📅 日期相关配置分析:")
            
            data_config = config.get('data_config', {})
            if data_config:
                print(f"✅ 有data_config:")
                print(f"   模式: {data_config.get('mode')}")
                print(f"   训练天数: {data_config.get('training_days')}")
                print(f"   结束日期: {data_config.get('end_date')}")
                print(f"   开始日期: {data_config.get('start_date')}")
            else:
                print(f"❌ 没有data_config")
            
            # 检查旧格式
            if config.get('training_days'):
                print(f"⚠️ 发现旧格式training_days: {config.get('training_days')}")
            
            if config.get('end_date'):
                print(f"⚠️ 发现旧格式end_date: {config.get('end_date')}")
        
        else:
            print("❌ 没有找到已完成的模型")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")

def main():
    """主函数"""
    
    print("🔧 AI模型数据库信息检查")
    print("=" * 80)
    
    # 检查数据库中的模型信息
    success = check_model_database()
    
    if success:
        # 检查特定模型的详细信息
        check_specific_model()
    
    print(f"\n📊 检查结果")
    print("=" * 80)
    
    if success:
        print("✅ 数据库检查完成")
        
        print(f"\n💡 分析要点:")
        print("• 检查data_config中的end_date是否正确")
        print("• 对比配置中的日期和实际显示的日期")
        print("• 确认是前端传递问题还是后端处理问题")
        print("• 查看训练日志中的数据信息")
        
        print(f"\n🔧 如果发现问题:")
        print("• 前端传递错误：修复JavaScript代码")
        print("• 后端处理错误：修复Python日期逻辑")
        print("• 显示错误：修复模板渲染逻辑")
        
    else:
        print("❌ 数据库检查失败")
        print("⚠️ 请确认数据库文件存在且可访问")

if __name__ == '__main__':
    main()
