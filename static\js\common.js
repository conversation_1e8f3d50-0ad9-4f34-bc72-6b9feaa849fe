// MateTrade4 通用JavaScript函数

// 全局变量
window.MateTrade4 = {
    config: {
        apiBaseUrl: '/api',
        refreshInterval: 30000, // 30秒
        chartColors: {
            primary: '#667eea',
            secondary: '#764ba2',
            success: '#28a745',
            danger: '#dc3545',
            warning: '#ffc107',
            info: '#17a2b8'
        }
    },
    utils: {},
    api: {},
    charts: {},
    notifications: {}
};

// 工具函数
MateTrade4.utils = {
    // 格式化数字
    formatNumber: function(num, decimals = 2) {
        if (num === null || num === undefined || isNaN(num)) return '-';
        return parseFloat(num).toFixed(decimals);
    },
    
    // 格式化货币
    formatCurrency: function(amount, currency = '$', decimals = 2) {
        if (amount === null || amount === undefined || isNaN(amount)) return '-';
        return currency + parseFloat(amount).toLocaleString('en-US', {
            minimumFractionDigits: decimals,
            maximumFractionDigits: decimals
        });
    },
    
    // 格式化百分比
    formatPercentage: function(value, decimals = 2) {
        if (value === null || value === undefined || isNaN(value)) return '-';
        return parseFloat(value).toFixed(decimals) + '%';
    },
    
    // 格式化日期时间
    formatDateTime: function(dateString) {
        if (!dateString) return '-';
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN');
    },
    
    // 获取盈亏颜色类
    getProfitClass: function(value) {
        if (value > 0) return 'profit-positive';
        if (value < 0) return 'profit-negative';
        return 'text-muted';
    },
    
    // 防抖函数
    debounce: function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },
    
    // 节流函数
    throttle: function(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },
    
    // 深拷贝
    deepClone: function(obj) {
        return JSON.parse(JSON.stringify(obj));
    },
    
    // 生成随机ID
    generateId: function() {
        return Math.random().toString(36).substr(2, 9);
    }
};

// API调用函数
MateTrade4.api = {
    // 通用GET请求
    get: function(endpoint, params = {}) {
        const url = new URL(MateTrade4.config.apiBaseUrl + endpoint, window.location.origin);
        Object.keys(params).forEach(key => url.searchParams.append(key, params[key]));
        
        return fetch(url, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            },
            credentials: 'same-origin'
        }).then(response => response.json());
    },
    
    // 通用POST请求
    post: function(endpoint, data = {}) {
        return fetch(MateTrade4.config.apiBaseUrl + endpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            credentials: 'same-origin',
            body: JSON.stringify(data)
        }).then(response => response.json());
    },
    
    // 通用PUT请求
    put: function(endpoint, data = {}) {
        return fetch(MateTrade4.config.apiBaseUrl + endpoint, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            credentials: 'same-origin',
            body: JSON.stringify(data)
        }).then(response => response.json());
    },
    
    // 通用DELETE请求
    delete: function(endpoint) {
        return fetch(MateTrade4.config.apiBaseUrl + endpoint, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            },
            credentials: 'same-origin'
        }).then(response => response.json());
    }
};

// 通知系统
MateTrade4.notifications = {
    // 显示成功消息
    success: function(message, duration = 3000) {
        this.show(message, 'success', duration);
    },
    
    // 显示错误消息
    error: function(message, duration = 5000) {
        this.show(message, 'danger', duration);
    },
    
    // 显示警告消息
    warning: function(message, duration = 4000) {
        this.show(message, 'warning', duration);
    },
    
    // 显示信息消息
    info: function(message, duration = 3000) {
        this.show(message, 'info', duration);
    },
    
    // 显示通知
    show: function(message, type = 'info', duration = 3000) {
        const toastId = 'toast-' + MateTrade4.utils.generateId();
        const toastHtml = `
            <div class="toast align-items-center text-white bg-${type} border-0 position-fixed top-0 end-0 m-3" 
                 id="${toastId}" role="alert" style="z-index: 9999;">
                <div class="d-flex">
                    <div class="toast-body">
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" 
                            data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', toastHtml);
        const toastElement = document.getElementById(toastId);
        const toast = new bootstrap.Toast(toastElement, { delay: duration });
        toast.show();
        
        // 自动移除DOM元素
        toastElement.addEventListener('hidden.bs.toast', function() {
            toastElement.remove();
        });
    }
};

// 图表工具
MateTrade4.charts = {
    // 默认图表配置
    defaultConfig: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'top',
            },
            tooltip: {
                mode: 'index',
                intersect: false,
            }
        },
        scales: {
            x: {
                display: true,
                grid: {
                    display: false
                }
            },
            y: {
                display: true,
                grid: {
                    color: 'rgba(0,0,0,0.1)'
                }
            }
        }
    },
    
    // 创建线性图表
    createLineChart: function(ctx, data, options = {}) {
        const config = {
            type: 'line',
            data: data,
            options: { ...this.defaultConfig, ...options }
        };
        return new Chart(ctx, config);
    },
    
    // 创建柱状图表
    createBarChart: function(ctx, data, options = {}) {
        const config = {
            type: 'bar',
            data: data,
            options: { ...this.defaultConfig, ...options }
        };
        return new Chart(ctx, config);
    },
    
    // 创建饼图
    createPieChart: function(ctx, data, options = {}) {
        const config = {
            type: 'pie',
            data: data,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                    }
                },
                ...options
            }
        };
        return new Chart(ctx, config);
    }
};

// 表格工具
MateTrade4.tables = {
    // 更新表格数据
    updateTable: function(tableId, data, columns) {
        const table = document.getElementById(tableId);
        if (!table) return;
        
        const tbody = table.querySelector('tbody');
        tbody.innerHTML = '';
        
        if (data.length === 0) {
            const row = tbody.insertRow();
            const cell = row.insertCell();
            cell.colSpan = columns.length;
            cell.className = 'text-center text-muted';
            cell.textContent = '暂无数据';
            return;
        }
        
        data.forEach(item => {
            const row = tbody.insertRow();
            columns.forEach(column => {
                const cell = row.insertCell();
                if (column.render) {
                    cell.innerHTML = column.render(item[column.key], item);
                } else {
                    cell.textContent = item[column.key] || '-';
                }
                if (column.className) {
                    cell.className = column.className;
                }
            });
        });
    }
};

// 加载状态管理
MateTrade4.loading = {
    // 显示加载状态
    show: function(element, text = '加载中...') {
        if (typeof element === 'string') {
            element = document.getElementById(element);
        }
        if (!element) return;
        
        element.innerHTML = `
            <div class="text-center p-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">${text}</span>
                </div>
                <p class="mt-2 text-muted">${text}</p>
            </div>
        `;
    },
    
    // 隐藏加载状态
    hide: function(element) {
        if (typeof element === 'string') {
            element = document.getElementById(element);
        }
        if (!element) return;
        
        element.innerHTML = '';
    }
};

// 表单验证
MateTrade4.validation = {
    // 验证必填字段
    required: function(value) {
        return value !== null && value !== undefined && value.toString().trim() !== '';
    },
    
    // 验证邮箱
    email: function(value) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(value);
    },
    
    // 验证数字
    number: function(value) {
        return !isNaN(value) && isFinite(value);
    },
    
    // 验证正数
    positive: function(value) {
        return this.number(value) && parseFloat(value) > 0;
    },
    
    // 验证表单
    validateForm: function(formId, rules) {
        const form = document.getElementById(formId);
        if (!form) return false;
        
        let isValid = true;
        const errors = {};
        
        Object.keys(rules).forEach(fieldName => {
            const field = form.querySelector(`[name="${fieldName}"]`);
            if (!field) return;
            
            const value = field.value;
            const fieldRules = rules[fieldName];
            
            fieldRules.forEach(rule => {
                if (!rule.validator(value)) {
                    isValid = false;
                    errors[fieldName] = rule.message;
                    field.classList.add('is-invalid');
                } else {
                    field.classList.remove('is-invalid');
                }
            });
        });
        
        return { isValid, errors };
    }
};

// 初始化函数
MateTrade4.init = function() {
    // 初始化工具提示
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // 初始化弹出框
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
    
    // 添加淡入动画
    document.querySelectorAll('.card').forEach((card, index) => {
        card.style.animationDelay = (index * 0.1) + 's';
        card.classList.add('fade-in-up');
    });
    
    console.log('MateTrade4 系统已初始化');
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    MateTrade4.init();
});

// 导出到全局
window.MateTrade4 = MateTrade4;
