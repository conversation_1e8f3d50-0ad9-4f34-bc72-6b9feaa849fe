#!/usr/bin/env python3
"""
回测诊断工具 - 分析回测结果异常的原因
"""

import requests
import json
import time

def diagnose_backtest_issue():
    """诊断回测问题"""
    print("🔍 回测问题诊断工具")
    print("=" * 50)
    
    # 测试不同的配置
    test_configs = [
        {
            'name': '宽松配置 (更多交易)',
            'params': {
                'days': 7,
                'timeframe': '15m',
                'lot_size': 0.01,
                'stop_loss_percent': 0.6,    # 降低止损
                'take_profit_percent': 1.8,  # 降低止盈
                'daily_limit': 3,            # 增加每日限制
                'min_signals': 2             # 降低信号要求
            }
        },
        {
            'name': '您当前配置',
            'params': {
                'days': 7,
                'timeframe': '15m',
                'lot_size': 0.01,
                'stop_loss_percent': 0.8,
                'take_profit_percent': 2.4,
                'daily_limit': 2,
                'min_signals': 3
            }
        },
        {
            'name': '高频配置 (5分钟)',
            'params': {
                'days': 7,
                'timeframe': '5m',
                'lot_size': 0.01,
                'stop_loss_percent': 0.4,
                'take_profit_percent': 1.2,
                'daily_limit': 5,
                'min_signals': 2
            }
        }
    ]
    
    results = []
    
    for config in test_configs:
        print(f"\n🧪 测试配置: {config['name']}")
        print(f"   参数: {config['params']}")
        
        try:
            url = "http://127.0.0.1:5000/api/low-risk-trading/backtest"
            response = requests.post(url, json=config['params'], timeout=60)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    result = data.get('results', {})
                    summary = result.get('summary', {})
                    
                    print(f"✅ 回测成功:")
                    print(f"   总交易数: {summary.get('total_trades', 0)}")
                    print(f"   盈利次数: {summary.get('win_trades', 0)}")
                    print(f"   亏损次数: {summary.get('loss_trades', 0)}")
                    print(f"   胜率: {summary.get('win_rate', 0):.1f}%")
                    print(f"   总盈亏: ${summary.get('total_pnl', 0):.2f}")
                    
                    # 检查数据一致性
                    total_trades = summary.get('total_trades', 0)
                    win_trades = summary.get('win_trades', 0)
                    loss_trades = summary.get('loss_trades', 0)
                    win_rate = summary.get('win_rate', 0)
                    
                    if total_trades != (win_trades + loss_trades):
                        print(f"⚠️ 数据不一致: 总交易数{total_trades} ≠ 盈利{win_trades} + 亏损{loss_trades}")
                    
                    expected_win_rate = (win_trades / total_trades * 100) if total_trades > 0 else 0
                    if abs(win_rate - expected_win_rate) > 0.1:
                        print(f"⚠️ 胜率计算错误: 显示{win_rate:.1f}%, 应为{expected_win_rate:.1f}%")
                    
                    results.append({
                        'config': config['name'],
                        'summary': summary,
                        'success': True
                    })
                    
                else:
                    print(f"❌ 回测失败: {data.get('error')}")
                    results.append({
                        'config': config['name'],
                        'error': data.get('error'),
                        'success': False
                    })
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                results.append({
                    'config': config['name'],
                    'error': f'HTTP {response.status_code}',
                    'success': False
                })
                
        except Exception as e:
            print(f"❌ 请求失败: {e}")
            results.append({
                'config': config['name'],
                'error': str(e),
                'success': False
            })
        
        time.sleep(2)  # 避免请求过频
    
    # 分析结果
    print(f"\n" + "=" * 60)
    print("📊 诊断结果分析")
    print("=" * 60)
    
    successful_tests = [r for r in results if r['success']]
    failed_tests = [r for r in results if not r['success']]
    
    print(f"\n✅ 成功测试: {len(successful_tests)}/{len(results)}")
    print(f"❌ 失败测试: {len(failed_tests)}/{len(results)}")
    
    if successful_tests:
        print(f"\n📈 成功测试结果对比:")
        print("-" * 80)
        print(f"{'配置':<20} {'交易数':<8} {'胜率%':<8} {'总盈亏$':<12} {'问题':<20}")
        print("-" * 80)
        
        for result in successful_tests:
            summary = result['summary']
            config_name = result['config']
            trades = summary.get('total_trades', 0)
            win_rate = summary.get('win_rate', 0)
            total_pnl = summary.get('total_pnl', 0)
            
            # 识别问题
            issues = []
            if trades < 3:
                issues.append("交易过少")
            if win_rate == 0 and trades > 0:
                issues.append("胜率异常")
            if total_pnl < -50:
                issues.append("亏损严重")
            
            issue_text = ", ".join(issues) if issues else "正常"
            
            print(f"{config_name:<20} {trades:<8} {win_rate:<8.1f} {total_pnl:<12.2f} {issue_text:<20}")
    
    if failed_tests:
        print(f"\n❌ 失败测试详情:")
        for result in failed_tests:
            print(f"   {result['config']}: {result['error']}")
    
    # 给出建议
    print(f"\n💡 诊断建议:")
    print("-" * 40)
    
    if len(successful_tests) == 0:
        print("🚨 所有测试都失败，可能的原因:")
        print("   1. MT5服务未启动或连接失败")
        print("   2. 回测API存在问题")
        print("   3. 网络连接问题")
        print("   建议: 检查MT5连接状态和服务器日志")
    
    elif any(r['summary'].get('total_trades', 0) < 3 for r in successful_tests):
        print("📉 交易数量过少，可能的原因:")
        print("   1. 信号生成阈值过高")
        print("   2. 确认信号要求过多")
        print("   3. 市场数据不足")
        print("   建议: 降低信号阈值或减少确认信号数量")
    
    elif any(r['summary'].get('win_rate', 0) == 0 and r['summary'].get('total_trades', 0) > 0 for r in successful_tests):
        print("🎯 胜率异常，可能的原因:")
        print("   1. 止损设置过小")
        print("   2. 信号质量差")
        print("   3. 市场环境不适合当前策略")
        print("   建议: 调整止损止盈比例或优化信号生成逻辑")
    
    else:
        print("✅ 回测基本正常，建议:")
        print("   1. 选择交易数量适中的配置")
        print("   2. 关注胜率和盈亏比的平衡")
        print("   3. 进行更长时间的回测验证")
    
    return results

def main():
    """主函数"""
    try:
        results = diagnose_backtest_issue()
        
        print(f"\n🎯 诊断完成！")
        print(f"建议根据诊断结果调整策略参数。")
        
    except KeyboardInterrupt:
        print(f"\n⏹️ 用户中断诊断")
    except Exception as e:
        print(f"\n❌ 诊断过程出错: {e}")

if __name__ == "__main__":
    main()
