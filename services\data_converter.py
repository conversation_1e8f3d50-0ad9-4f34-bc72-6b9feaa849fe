#!/usr/bin/env python3
"""
数据类型转换工具
处理numpy类型到Python原生类型的转换，解决JSON序列化问题
"""

import numpy as np
import pandas as pd
from typing import Any, Dict, List, Union
import json

class DataConverter:
    """数据类型转换器"""
    
    @staticmethod
    def convert_numpy_types(obj: Any) -> Any:
        """转换numpy类型为Python原生类型"""
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, np.bool_):
            return bool(obj)
        elif hasattr(obj, 'item'):
            return obj.item()
        elif hasattr(obj, 'tolist'):
            return obj.tolist()
        return obj
    
    @staticmethod
    def clean_for_json(data: Any) -> Any:
        """递归清理数据，确保可以JSON序列化"""
        if isinstance(data, dict):
            return {k: DataConverter.clean_for_json(v) for k, v in data.items()}
        elif isinstance(data, list):
            return [DataConverter.clean_for_json(item) for item in data]
        elif isinstance(data, tuple):
            return tuple(DataConverter.clean_for_json(item) for item in data)
        elif isinstance(data, (np.integer, np.floating, np.ndarray, np.bool_)):
            return DataConverter.convert_numpy_types(data)
        elif pd.isna(data):
            return None
        else:
            return DataConverter.convert_numpy_types(data)
    
    @staticmethod
    def safe_json_dumps(data: Any, **kwargs) -> str:
        """安全的JSON序列化"""
        cleaned_data = DataConverter.clean_for_json(data)
        return json.dumps(cleaned_data, **kwargs)
    
    @staticmethod
    def convert_pattern_data(pattern: Dict) -> Dict:
        """专门转换形态数据"""
        converted = {}
        
        for key, value in pattern.items():
            if key in ['start_idx', 'end_idx']:
                converted[key] = int(value) if value is not None else None
            elif key in ['confidence', 'entry_point', 'stop_loss', 'take_profit']:
                converted[key] = float(value) if value is not None else None
            elif key == 'key_levels' and isinstance(value, dict):
                converted[key] = {k: float(v) if v is not None else None for k, v in value.items()}
            else:
                converted[key] = DataConverter.clean_for_json(value)
        
        return converted
    
    @staticmethod
    def convert_analysis_results(results: Dict) -> Dict:
        """转换分析结果数据"""
        converted = {}
        
        for key, value in results.items():
            if key == 'timeframes' and isinstance(value, dict):
                converted[key] = {}
                for tf, tf_data in value.items():
                    converted[key][tf] = DataConverter._convert_timeframe_data(tf_data)
            elif key == 'best_opportunities' and isinstance(value, list):
                converted[key] = [DataConverter.convert_pattern_data(opp) for opp in value]
            elif key == 'summary' and isinstance(value, dict):
                converted[key] = DataConverter._convert_summary_data(value)
            else:
                converted[key] = DataConverter.clean_for_json(value)
        
        return converted
    
    @staticmethod
    def _convert_timeframe_data(tf_data: Dict) -> Dict:
        """转换时间周期数据"""
        converted = {}
        
        for key, value in tf_data.items():
            if key == 'patterns' and isinstance(value, list):
                converted[key] = [DataConverter.convert_pattern_data(pattern) for pattern in value]
            elif key in ['data_points', 'total_patterns', 'high_confidence_patterns', 
                        'bullish_count', 'bearish_count']:
                converted[key] = int(value) if value is not None else 0
            else:
                converted[key] = DataConverter.clean_for_json(value)
        
        return converted
    
    @staticmethod
    def _convert_summary_data(summary: Dict) -> Dict:
        """转换摘要数据"""
        converted = {}
        
        for key, value in summary.items():
            if key in ['total_patterns', 'total_bullish', 'total_bearish']:
                converted[key] = int(value) if value is not None else 0
            elif isinstance(value, dict):
                converted[key] = {k: int(v) if isinstance(v, (np.integer, int)) else 
                                DataConverter.clean_for_json(v) for k, v in value.items()}
            else:
                converted[key] = DataConverter.clean_for_json(value)
        
        return converted

def test_converter():
    """测试转换器"""
    # 测试数据
    test_data = {
        'numpy_int': np.int64(123),
        'numpy_float': np.float64(123.456),
        'numpy_array': np.array([1, 2, 3]),
        'normal_data': 'test',
        'nested': {
            'numpy_bool': np.bool_(True),
            'list_with_numpy': [np.int32(1), np.float32(2.5), 'normal']
        }
    }
    
    print("原始数据类型:")
    for key, value in test_data.items():
        print(f"  {key}: {type(value)} = {value}")
    
    # 转换数据
    converted = DataConverter.clean_for_json(test_data)
    
    print("\n转换后数据类型:")
    for key, value in converted.items():
        print(f"  {key}: {type(value)} = {value}")
    
    # 测试JSON序列化
    try:
        json_str = json.dumps(converted, indent=2)
        print("\n✅ JSON序列化成功!")
        print("JSON长度:", len(json_str))
    except Exception as e:
        print(f"\n❌ JSON序列化失败: {e}")

if __name__ == "__main__":
    test_converter()
