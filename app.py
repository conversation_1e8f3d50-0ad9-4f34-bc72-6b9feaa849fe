from flask import Flask, render_template, request, jsonify, redirect, url_for, flash
from flask_login import LoginManager, UserMixin, login_user, logout_user, login_required, current_user
from datetime import datetime, timedelta
import os
from dotenv import load_dotenv
import json

# 加载环境变量
load_dotenv()

app = Flask(__name__)
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'your-secret-key-here')
app.config['SQLALCHEMY_DATABASE_URI'] = os.environ.get('DATABASE_URL', 'sqlite:///trading_system.db')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# 导入并初始化数据库
from models import db
db.init_app(app)

# 初始化登录管理器
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

# 导入模型
from models import User, AIModelConfig, TradingAccount, Trade, Strategy, BacktestResult, MarketData, LowRiskTrade, LowRiskTradingConfig, EntrySignal

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# 导入路由
from routes import *

if __name__ == '__main__':
    with app.app_context():
        # 检查数据库表结构（使用原生SQL避免SQLAlchemy缓存问题）
        print("正在检查数据库表结构...")
        try:
            import sqlite3
            conn = sqlite3.connect('trading_system.db')
            cursor = conn.cursor()

            # 检查用户表
            cursor.execute("SELECT COUNT(*) FROM user")
            user_count = cursor.fetchone()[0]

            # 检查策略表
            cursor.execute("SELECT COUNT(*) FROM strategy")
            strategy_count = cursor.fetchone()[0]

            conn.close()
            print(f"✅ 数据库表结构正常 (用户: {user_count}, 策略: {strategy_count})")
        except Exception as e:
            print(f"⚠️  数据库表结构检查失败: {e}")
            print("💡 如果是新安装，请运行: python init_database.py")
            print("💡 如果是升级，请运行: python migrate_database.py")
            print("🚀 继续启动应用程序...")

        # 启动止盈止损监控服务
        print("正在启动止盈止损监控服务...")
        try:
            from services.stop_loss_take_profit_service import stop_loss_take_profit_service
            stop_loss_take_profit_service.start_monitoring()
            print("✅ 止盈止损监控服务已启动")
        except Exception as e:
            print(f"⚠️ 止盈止损监控服务启动失败: {e}")
            print("💡 止盈止损功能可能无法正常工作")




    app.run(debug=True, host='0.0.0.0', port=5000)
