#!/usr/bin/env python3
"""
使用Flask SQLAlchemy创建低风险交易相关表
"""

import os
import sys

def create_tables():
    """使用Flask SQLAlchemy创建表"""
    try:
        # 导入Flask应用和数据库
        from app import app, db
        from models import LowRiskTrade, LowRiskTradingConfig
        
        with app.app_context():
            print("🔄 开始创建低风险交易相关表...")
            
            # 创建所有表（包括新添加的LowRiskTrade和LowRiskTradingConfig）
            db.create_all()
            
            print("✅ 数据库表创建完成")
            
            # 验证表是否创建成功
            from sqlalchemy import inspect
            inspector = inspect(db.engine)
            tables = inspector.get_table_names()
            
            print(f"📊 数据库中的所有表: {tables}")
            
            # 检查关键表是否存在
            required_tables = ['low_risk_trades', 'low_risk_trading_configs']
            missing_tables = []
            
            for table in required_tables:
                if table in tables:
                    print(f"✅ 表 {table} 创建成功")
                else:
                    missing_tables.append(table)
                    print(f"❌ 表 {table} 创建失败")
            
            if missing_tables:
                print(f"\n⚠️  以下表创建失败: {', '.join(missing_tables)}")
                return False
            
            # 检查表结构
            print("\n🔍 检查表结构...")
            
            # 检查low_risk_trades表结构
            columns = inspector.get_columns('low_risk_trades')
            print(f"📋 low_risk_trades表字段数: {len(columns)}")
            
            required_fields = ['id', 'user_id', 'symbol', 'action', 'volume', 'entry_price', 'operation_type']
            for field in required_fields:
                field_exists = any(col['name'] == field for col in columns)
                if field_exists:
                    print(f"   ✅ {field} 字段存在")
                else:
                    print(f"   ❌ {field} 字段缺失")
            
            # 插入示例数据（如果表为空）
            print("\n📝 检查是否需要插入示例数据...")
            
            from models import User
            
            # 获取第一个用户（通常是admin）
            first_user = User.query.first()
            if not first_user:
                print("⚠️  没有找到用户，无法插入示例数据")
                return True
            
            # 检查是否已有数据
            existing_trades = LowRiskTrade.query.filter_by(user_id=first_user.id).count()
            
            if existing_trades == 0:
                print("📊 插入示例数据...")
                
                from datetime import datetime
                
                # 创建示例交易记录
                sample_trades = [
                    LowRiskTrade(
                        user_id=first_user.id,
                        symbol='XAUUSD',
                        action='buy',
                        volume=0.01,
                        entry_price=2650.45,
                        stop_loss=2640.00,
                        take_profit=2670.00,
                        mt5_order_id=12345,
                        comment='Manual_BUY_Test',
                        status='closed',
                        close_price=2665.20,
                        close_time=datetime(2024, 1, 15, 14, 30, 0),
                        pnl=14.75,
                        signal_type='TREND_BUY',
                        confidence=0.75,
                        market_analysis='{"signal_reason": "TREND_BUY"}',
                        operation_type='manual',
                        created_at=datetime(2024, 1, 15, 10, 30, 0)
                    ),
                    LowRiskTrade(
                        user_id=first_user.id,
                        symbol='XAUUSD',
                        action='sell',
                        volume=0.01,
                        entry_price=2645.80,
                        stop_loss=2655.00,
                        take_profit=2630.00,
                        mt5_order_id=12346,
                        comment='Auto_SELL_850',
                        status='closed',
                        close_price=2632.15,
                        close_time=datetime(2024, 1, 14, 16, 45, 0),
                        pnl=13.65,
                        signal_type='MEAN_REVERSION_SELL',
                        confidence=0.85,
                        market_analysis='{"signal_reason": "MEAN_REVERSION_SELL"}',
                        operation_type='auto',
                        created_at=datetime(2024, 1, 14, 15, 45, 0)
                    ),
                    LowRiskTrade(
                        user_id=first_user.id,
                        symbol='XAUUSD',
                        action='buy',
                        volume=0.01,
                        entry_price=2638.90,
                        stop_loss=2628.00,
                        take_profit=2655.00,
                        mt5_order_id=12347,
                        comment='Manual_BUY_Reversal',
                        status='closed',
                        close_price=2625.45,
                        close_time=datetime(2024, 1, 13, 11, 15, 0),
                        pnl=-13.45,
                        signal_type='FORCE_RSI_OVERSOLD',
                        confidence=0.65,
                        market_analysis='{"signal_reason": "FORCE_RSI_OVERSOLD"}',
                        operation_type='manual',
                        created_at=datetime(2024, 1, 13, 9, 15, 0)
                    )
                ]
                
                # 添加到数据库
                for trade in sample_trades:
                    db.session.add(trade)
                
                db.session.commit()
                
                print(f"✅ 插入了 {len(sample_trades)} 条示例交易记录")
            else:
                print(f"📊 表中已有 {existing_trades} 条记录，跳过示例数据插入")
            
            # 最终验证
            print("\n🎯 最终验证...")
            total_trades = LowRiskTrade.query.count()
            user_trades = LowRiskTrade.query.filter_by(user_id=first_user.id).count()
            auto_trades = LowRiskTrade.query.filter_by(user_id=first_user.id, operation_type='auto').count()
            manual_trades = LowRiskTrade.query.filter_by(user_id=first_user.id, operation_type='manual').count()
            
            print(f"📊 数据统计:")
            print(f"   总交易记录: {total_trades}")
            print(f"   用户交易记录: {user_trades}")
            print(f"   自动交易: {auto_trades}")
            print(f"   手动交易: {manual_trades}")
            
            print("\n🎉 低风险交易表创建和初始化完成！")
            print("现在可以正常使用历史记录查询功能了。")
            
            return True
            
    except Exception as e:
        print(f"❌ 创建表失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("低风险交易系统 - 数据库表创建")
    print("=" * 60)
    
    if create_tables():
        print("\n✅ 数据库表创建成功！")
        print("现在可以正常使用低风险交易历史记录查询功能了。")
    else:
        print("\n❌ 数据库表创建失败！")
        print("请检查错误信息并重试。")

if __name__ == '__main__':
    main()
