"""
智能体交易-真实服务
实现由外部AI驱动的真实交易闭环系统
"""

import json
import re
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

@dataclass
class TradingSignal:
    """交易信号"""
    action: str  # 'buy', 'sell', 'close'
    symbol: str
    volume: float
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    reason: str = ""
    confidence: float = 0.0
    # 平仓相关参数
    close_type: Optional[str] = None  # 'all', 'symbol', 'ticket'
    ticket: Optional[int] = None  # 指定订单号

@dataclass
class AgentState:
    """智能体状态"""
    user_id: int
    status: str  # 'running', 'stopped', 'paused'
    config: Dict[str, Any]
    start_time: datetime
    last_analysis_time: Optional[datetime] = None
    total_trades: int = 0
    total_profit: float = 0.0
    today_profit: float = 0.0

class AgentTradingRealService:
    """智能体交易-真实服务"""
    
    def __init__(self):
        self.active_agents: Dict[int, AgentState] = {}
        self.analysis_threads: Dict[int, threading.Thread] = {}
        self.stop_flags: Dict[int, threading.Event] = {}
    
    def start_agent(self, user_id: int, config: Dict[str, Any]) -> Dict[str, Any]:
        """启动智能体"""
        try:
            # 检查MT5连接
            if not self._check_mt5_connection():
                return {'success': False, 'error': 'MT5未连接，请先连接MT5'}
            
            # 验证AI模型和策略
            validation_result = self._validate_ai_config(config)
            if not validation_result:
                ai_model = config.get('ai_model', 'Unknown')
                ai_strategies = config.get('ai_strategies', [])
                ai_strategy = config.get('ai_strategy', 'Unknown')
                strategies_info = ai_strategies if ai_strategies else [ai_strategy]
                error_msg = f'AI配置验证失败 - 模型: {ai_model}, 策略: {strategies_info}'
                print(f"❌ {error_msg}")
                return {'success': False, 'error': error_msg}
            
            # 停止现有的智能体（如果有）
            if user_id in self.active_agents:
                self.stop_agent(user_id)
            
            # 创建智能体状态
            agent_state = AgentState(
                user_id=user_id,
                status='running',
                config=config,
                start_time=datetime.now()
            )
            
            self.active_agents[user_id] = agent_state
            
            # 创建停止标志
            self.stop_flags[user_id] = threading.Event()
            
            # 启动分析线程（不设置为daemon，确保持久运行）
            analysis_thread = threading.Thread(
                target=self._analysis_loop,
                args=(user_id,),
                daemon=False,  # 改为False，确保线程持久运行
                name=f"AgentTrading-{user_id}"
            )
            analysis_thread.start()
            self.analysis_threads[user_id] = analysis_thread
            
            print(f"🤖 智能体已启动 - 用户: {user_id}")
            
            return {
                'success': True,
                'agent_id': f"agent_{user_id}_{int(time.time())}"
            }
            
        except Exception as e:
            print(f"❌ 启动智能体失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def stop_agent(self, user_id: int) -> Dict[str, Any]:
        """停止智能体"""
        try:
            if user_id not in self.active_agents:
                return {'success': False, 'error': '智能体未运行'}
            
            # 设置停止标志
            if user_id in self.stop_flags:
                self.stop_flags[user_id].set()
            
            # 等待线程结束
            if user_id in self.analysis_threads:
                thread = self.analysis_threads[user_id]
                print(f"🔄 等待智能体线程结束 - 用户: {user_id}")
                thread.join(timeout=10)  # 最多等待10秒

                # 如果线程仍然活跃，强制标记为停止
                if thread.is_alive():
                    print(f"⚠️ 智能体线程未能正常结束，强制标记为停止 - 用户: {user_id}")
                    # 线程会在下次检查时自动退出

                del self.analysis_threads[user_id]
            
            # 更新状态
            self.active_agents[user_id].status = 'stopped'
            
            # 清理资源
            if user_id in self.stop_flags:
                del self.stop_flags[user_id]
            
            print(f"🛑 智能体已停止 - 用户: {user_id}")
            
            return {'success': True}
            
        except Exception as e:
            print(f"❌ 停止智能体失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def pause_agent(self, user_id: int) -> Dict[str, Any]:
        """暂停智能体"""
        try:
            if user_id not in self.active_agents:
                return {'success': False, 'error': '智能体未运行'}
            
            self.active_agents[user_id].status = 'paused'
            
            print(f"⏸️ 智能体已暂停 - 用户: {user_id}")
            
            return {'success': True}
            
        except Exception as e:
            print(f"❌ 暂停智能体失败: {e}")
            return {'success': False, 'error': str(e)}

    def force_stop_all_agents(self) -> Dict[str, Any]:
        """强制停止所有智能体（紧急情况使用）"""
        try:
            stopped_count = 0
            for user_id in list(self.active_agents.keys()):
                try:
                    self.stop_agent(user_id)
                    stopped_count += 1
                    print(f"🛑 强制停止智能体 - 用户: {user_id}")
                except Exception as e:
                    print(f"❌ 强制停止智能体失败 - 用户: {user_id}, 错误: {e}")

            # 清理所有状态
            self.active_agents.clear()
            self.analysis_threads.clear()
            self.stop_flags.clear()

            print(f"🧹 已清理所有智能体状态，停止了 {stopped_count} 个智能体")

            return {
                'success': True,
                'stopped_count': stopped_count,
                'message': f'已强制停止 {stopped_count} 个智能体'
            }

        except Exception as e:
            print(f"❌ 强制停止所有智能体失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def perform_ai_analysis(self, user_id: int, config: Dict[str, Any]) -> Dict[str, Any]:
        """执行AI市场分析"""
        try:
            # 首先检查交易时间段
            if not self._is_trading_time_allowed(config):
                return {
                    'success': False,
                    'error': '当前时间不在允许的交易时段内',
                    'analysis': '系统检测到当前时间不在配置的交易时段内，已停止分析。'
                }

            # 获取市场数据
            market_data = self._get_market_data(config['trading_symbols'])

            # 获取用户的AI策略（支持新旧格式）
            ai_strategies = config.get('ai_strategies', [])
            ai_strategy_id = config.get('ai_strategy')

            # 兼容性处理
            if ai_strategy_id and not ai_strategies:
                ai_strategies = [ai_strategy_id]
            elif ai_strategies and not ai_strategy_id:
                ai_strategy_id = ai_strategies[0]

            # 获取第一个策略的详情（用于分析）
            ai_strategy = self._get_ai_strategy(ai_strategy_id) if ai_strategy_id else {}
            
            # 获取当前持仓
            current_positions = self._get_current_positions()
            
            # 构建AI分析提示
            analysis_prompt = self._build_analysis_prompt(
                market_data=market_data,
                ai_strategy=ai_strategy,
                user_requirements=config['user_requirements'],
                current_positions=current_positions,
                risk_settings=config.get('risk_settings', {})
            )
            
            # 调用外部AI模型
            ai_response = self._call_external_ai(config['ai_model'], analysis_prompt)
            
            # 解析AI响应，提取交易信号
            trading_signal = self._parse_ai_response(ai_response)
            
            # 更新最后分析时间
            if user_id in self.active_agents:
                self.active_agents[user_id].last_analysis_time = datetime.now()
            
            result = {
                'success': True,
                'analysis': ai_response,
                'market_data': market_data,
                'timestamp': datetime.now().isoformat()
            }
            
            if trading_signal:
                result['trading_signal'] = trading_signal.__dict__
            
            return result
            
        except Exception as e:
            print(f"❌ AI分析失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def execute_trade(self, user_id: int, signal: Dict[str, Any]) -> Dict[str, Any]:
        """执行交易"""
        try:
            from services.mt5_service import mt5_service

            print(f"📊 收到交易信号: {signal['action']} {signal.get('symbol', '')} {signal.get('volume', 0)}手")

            # 验证交易信号
            if not self._validate_trading_signal(signal):
                return {'success': False, 'error': '交易信号验证失败'}

            # 风险检查
            if not self._risk_check(user_id, signal):
                return {'success': False, 'error': '风险检查未通过'}

            # 清理货币对名称
            if 'symbol' in signal and signal['symbol']:
                original_symbol = signal['symbol']
                signal['symbol'] = self._clean_symbol_name(signal['symbol'])
                if original_symbol != signal['symbol']:
                    print(f"🔧 货币对名称清理: {original_symbol} -> {signal['symbol']}")

            # 执行MT5交易
            print(f"🔄 准备执行MT5交易: {signal['action']} {signal['symbol']} {signal['volume']}手")

            # 预验证止损止盈价格
            sl = signal.get('stop_loss')
            tp = signal.get('take_profit')

            if sl or tp:
                # 获取当前价格进行预验证
                from services.mt5_service import mt5_service
                tick = mt5_service.get_symbol_tick(signal['symbol'])
                if tick:
                    current_price = tick['ask'] if signal['action'] == 'buy' else tick['bid']
                    print(f"📊 当前价格: {current_price}")

                    # 基本验证
                    if signal['action'] == 'buy':
                        if sl and sl >= current_price:
                            print(f"⚠️ 买单止损价格异常: {sl} >= {current_price}")
                        if tp and tp <= current_price:
                            print(f"⚠️ 买单止盈价格异常: {tp} <= {current_price}")
                    else:  # sell
                        if sl and sl <= current_price:
                            print(f"⚠️ 卖单止损价格异常: {sl} <= {current_price}")
                        if tp and tp >= current_price:
                            print(f"⚠️ 卖单止盈价格异常: {tp} >= {current_price}")

                    if sl and tp and sl == tp:
                        print(f"⚠️ 止损止盈价格相同: {sl}")

            if signal['action'] == 'buy':
                print(f"📊 买入参数: symbol={signal['symbol']}, volume={signal['volume']}, sl={sl}, tp={tp}")
                result = mt5_service.place_order(
                    symbol=signal['symbol'],
                    order_type='buy',
                    volume=signal['volume'],
                    sl=sl,
                    tp=tp
                )
            elif signal['action'] == 'sell':
                print(f"📊 卖出参数: symbol={signal['symbol']}, volume={signal['volume']}, sl={sl}, tp={tp}")
                result = mt5_service.place_order(
                    symbol=signal['symbol'],
                    order_type='sell',
                    volume=signal['volume'],
                    sl=sl,
                    tp=tp
                )
            elif signal['action'] == 'close':
                # 平仓操作，传递完整的信号参数
                close_signal = {
                    'symbol': signal.get('symbol'),
                    'close_type': signal.get('close_type', 'all'),
                    'ticket': signal.get('ticket')
                }
                return self._execute_close_positions(user_id, close_signal)
            else:
                return {'success': False, 'error': '不支持的交易类型'}
            
            print(f"📋 MT5交易结果: {result}")
            print(f"📋 结果类型: {type(result)}")

            if result:
                print(f"📋 结果属性: {dir(result) if hasattr(result, '__dict__') else 'No attributes'}")
                if hasattr(result, 'retcode'):
                    print(f"📋 返回码: {result.retcode}")
                if hasattr(result, 'comment'):
                    print(f"📋 注释: {result.comment}")
                if hasattr(result, 'order'):
                    print(f"📋 订单号: {result.order}")

            if result and hasattr(result, 'retcode') and result.retcode == 10009:  # TRADE_RETCODE_DONE
                # 更新统计
                if user_id in self.active_agents:
                    self.active_agents[user_id].total_trades += 1

                order_id = getattr(result, 'order', 'Unknown')
                print(f"✅ 交易执行成功 - 用户: {user_id}, 订单: {order_id}")

                return {
                    'success': True,
                    'ticket': order_id,
                    'message': '交易执行成功'
                }
            else:
                # 详细的错误信息
                if result:
                    error_code = getattr(result, 'retcode', 'Unknown')
                    error_msg = getattr(result, 'comment', '未知错误')
                    error_details = f"错误码: {error_code}, 错误信息: {error_msg}"

                    # 常见错误码解释
                    error_explanations = {
                        10004: "重新报价",
                        10006: "请求被拒绝",
                        10007: "请求被取消",
                        10008: "订单已放置",
                        10009: "请求已完成",
                        10010: "仅部分请求已完成",
                        10011: "请求处理错误",
                        10012: "请求被取消（超时）",
                        10013: "无效请求",
                        10014: "无效数量",
                        10015: "无效价格",
                        10016: "无效止损",
                        10017: "无效止盈",
                        10018: "市场关闭",
                        10019: "交易被禁用",
                        10020: "资金不足",
                        10021: "价格已更改",
                        10022: "经纪商繁忙",
                        10023: "重新报价",
                        10024: "订单锁定",
                        10025: "买单数量过多",
                        10026: "卖单数量过多",
                        10027: "处理中",
                        10028: "仅限平仓",
                        10029: "仅限多头",
                        10030: "仅限空头"
                    }

                    explanation = error_explanations.get(error_code, "未知错误类型")
                    error_details += f" ({explanation})"
                else:
                    error_details = "MT5返回空结果"

                print(f"❌ 交易执行失败 - 用户: {user_id}, {error_details}")

                return {'success': False, 'error': f'交易执行失败: {error_details}'}
            
        except Exception as e:
            print(f"❌ 执行交易异常: {e}")
            return {'success': False, 'error': str(e)}
    
    def get_trading_stats(self, user_id: int) -> Dict[str, Any]:
        """获取交易统计"""
        try:
            from services.mt5_service import mt5_service
            from datetime import datetime, timedelta
            
            # 获取今日交易历史
            today = datetime.now().date()
            start_time = datetime.combine(today, datetime.min.time())
            end_time = datetime.combine(today, datetime.max.time())
            
            trades = mt5_service.get_trade_history(start_time, end_time)
            
            total_trades = len(trades) if trades else 0
            winning_trades = len([t for t in trades if t['profit'] > 0]) if trades else 0
            win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0
            
            total_profit = sum(t['profit'] for t in trades) if trades else 0.0
            today_profit = total_profit  # 今日就是总计（因为只查询今日）
            
            return {
                'success': True,
                'total_trades': total_trades,
                'win_rate': round(win_rate, 1),
                'total_profit': round(total_profit, 2),
                'today_profit': round(today_profit, 2)
            }
            
        except Exception as e:
            print(f"❌ 获取统计失败: {e}")
            return {
                'success': False,
                'total_trades': 0,
                'win_rate': 0,
                'total_profit': 0.0,
                'today_profit': 0.0
            }
    
    def _analysis_loop(self, user_id: int):
        """新架构分析循环：智能体负责宏观决策，AI策略负责具体交易"""
        try:
            print(f"🔄 智能体分析循环开始 (新架构) - 用户: {user_id}")

            # 获取Flask应用实例
            from app import app

            agent = self.active_agents[user_id]
            stop_flag = self.stop_flags[user_id]

            interval = agent.config.get('analysis_interval', 15) * 60  # 转换为秒
            consecutive_errors = 0  # 连续错误计数
            max_consecutive_errors = 5  # 最大连续错误次数

            # 新架构：交易轮次管理
            current_round = 1
            round_start_time = datetime.now()
            round_orders_count = 0
            max_orders_per_round = agent.config.get('risk_settings', {}).get('order_limit_per_round', 20)
            auto_stop_rounds = agent.config.get('risk_settings', {}).get('auto_stop_rounds', 0)  # 0表示不自动停止

            while not stop_flag.is_set():
                try:
                    if agent.status == 'running':
                        # 检查是否达到自动停止轮次
                        if auto_stop_rounds > 0 and current_round > auto_stop_rounds:
                            print(f"🛑 智能体达到自动停止轮次({auto_stop_rounds}轮)，停止新增订单 - 用户{user_id}")
                            agent.status = 'auto_stopping'  # 设置为自动停止状态
                            self._handle_auto_stop(user_id, current_round - 1)
                            break

                        # 检查交易时间段
                        if not self._is_trading_time_allowed(agent.config):
                            # 进入休眠模式，避免频繁检查
                            sleep_duration = self._calculate_sleep_duration(agent.config)
                            print(f"⏰ 当前时间不在允许的交易时段内，智能体进入休眠模式 - 用户{user_id}")
                            print(f"😴 预计休眠 {sleep_duration//60} 分钟，等待交易时段开始")

                            # 更新智能体状态为休眠
                            agent.status = 'sleeping'

                            # 长时间休眠，但每5分钟检查一次停止信号
                            sleep_start = time.time()
                            while time.time() - sleep_start < sleep_duration:
                                if stop_flag.is_set():
                                    print(f"🛑 智能体休眠期间收到停止信号 - 用户{user_id}")
                                    return
                                time.sleep(300)  # 每5分钟检查一次

                            # 休眠结束，恢复运行状态
                            agent.status = 'running'
                            print(f"⏰ 智能体休眠结束，重新检查交易时段 - 用户{user_id}")
                            continue

                        print(f"🤖 智能体第{current_round}轮分析 - 用户{user_id}")

                        # 在Flask应用上下文中执行
                        with app.app_context():
                            # 第一阶段：智能体进行交易开始分析
                            agent_analysis = self._perform_agent_analysis(user_id, agent.config, current_round)

                            if agent_analysis['success']:
                                # 获取用户的风险事件过滤设置
                                risk_analysis = self._get_filtered_risk_analysis(user_id, agent.config)

                                # 检查是否需要停止交易（风险事件）
                                if agent_analysis.get('should_stop_trading') or risk_analysis.get('should_stop_trading'):
                                    risk_reason = agent_analysis.get('risk_reason') or risk_analysis.get('risk_reason', '未知风险')
                                    print(f"🚨 智能体检测到风险，停止新增订单 - 用户{user_id}: {risk_reason}")
                                    self._send_risk_alert(user_id, risk_reason)
                                    # 暂停智能体，但不完全停止
                                    agent.status = 'paused'
                                    continue

                                # 第二阶段：如果智能体认为可以交易，让AI策略执行具体交易
                                if agent_analysis.get('should_trade', False):
                                    selected_strategy_id = agent_analysis.get('selected_strategy_id')
                                    trading_result = self._execute_ai_strategy_trading(
                                        user_id, agent.config, current_round,
                                        max_orders_per_round - round_orders_count,
                                        selected_strategy_id
                                    )

                                    if trading_result['success']:
                                        round_orders_count += trading_result.get('orders_executed', 0)
                                        agent.total_trades += trading_result.get('orders_executed', 0)

                                        print(f"✅ AI策略执行完成 - 用户{user_id}, 本轮订单: {round_orders_count}/{max_orders_per_round}")

                                # 检查是否达到本轮订单限制
                                if round_orders_count >= max_orders_per_round:
                                    print(f"📊 第{current_round}轮交易完成，开始反思阶段 - 用户{user_id}")

                                    # 第三阶段：智能体进行一轮结束反思
                                    reflection_result = self._perform_round_reflection(
                                        user_id, agent.config, current_round, round_start_time, round_orders_count
                                    )

                                    # 开始新一轮
                                    current_round += 1
                                    round_start_time = datetime.now()
                                    round_orders_count = 0

                                    print(f"🔄 开始第{current_round}轮交易 - 用户{user_id}")

                        # 更新最后分析时间
                        agent.last_analysis_time = datetime.now()
                        consecutive_errors = 0  # 重置错误计数

                    # 等待下一次分析（分段等待，避免长时间阻塞）
                    print(f"⏰ 等待下次分析 - 用户{user_id}, 间隔: {interval}秒")

                    # 分段等待，每10秒检查一次停止标志
                    remaining_time = interval
                    while remaining_time > 0 and not stop_flag.is_set():
                        wait_time = min(10, remaining_time)  # 最多等待10秒
                        if stop_flag.wait(timeout=wait_time):
                            break  # 收到停止信号
                        remaining_time -= wait_time

                except Exception as e:
                    consecutive_errors += 1
                    print(f"❌ 智能体分析异常 - 用户{user_id} (第{consecutive_errors}次): {e}")

                    # 如果连续错误次数过多，停止智能体
                    if consecutive_errors >= max_consecutive_errors:
                        print(f"🚨 连续错误过多，停止智能体 - 用户{user_id}")
                        agent.status = 'stopped'
                        break

                    # 发生异常时等待较短时间后重试（分段等待）
                    remaining_time = 60
                    while remaining_time > 0 and not stop_flag.is_set():
                        wait_time = min(5, remaining_time)  # 最多等待5秒
                        if stop_flag.wait(timeout=wait_time):
                            break  # 收到停止信号
                        remaining_time -= wait_time

            print(f"🛑 智能体分析循环结束 - 用户: {user_id}")

        except Exception as e:
            print(f"❌ 智能体分析循环严重异常 - 用户{user_id}: {e}")
            # 发生严重异常时，标记智能体为停止状态
            if user_id in self.active_agents:
                self.active_agents[user_id].status = 'stopped'
    
    def _check_mt5_connection(self) -> bool:
        """检查MT5连接"""
        try:
            from services.mt5_service import mt5_service
            return mt5_service.connected
        except:
            return False
    
    def _validate_ai_config(self, config: Dict[str, Any]) -> bool:
        """验证AI配置"""
        try:
            print(f"🔍 验证AI配置: {config}")

            # 获取Flask应用实例
            from app import app

            with app.app_context():
                # 检查AI模型是否存在
                ai_model_id = config.get('ai_model')
                if not ai_model_id:
                    print("❌ AI模型ID为空")
                    return False

                # 尝试多种方式查找AI模型
                from models import AIModelConfig
            ai_model = None

            print(f"🔍 查找AI模型: {ai_model_id}")

            # 方法1: 按name查找（优先，因为前端现在传递模型名称）
            try:
                ai_model = AIModelConfig.query.filter_by(name=ai_model_id).first()
                if ai_model:
                    print(f"✅ 找到AI模型(按name): {ai_model.name}")
                    print(f"   - ID: {ai_model.id}")
                    print(f"   - 状态: {'活跃' if ai_model.is_active else '非活跃'}")
                    print(f"   - API密钥: {'已配置' if ai_model.api_key else '未配置'}")
            except Exception as e:
                print(f"⚠️ 按name查询AI模型失败: {e}")

            # 方法2: 按ID查找
            if not ai_model:
                try:
                    if str(ai_model_id).isdigit():
                        ai_model = AIModelConfig.query.get(int(ai_model_id))
                        if ai_model:
                            print(f"✅ 找到AI模型(按ID): {ai_model.name}")
                except Exception as e:
                    print(f"⚠️ 按ID查询AI模型失败: {e}")

            # 方法3: 模糊匹配（处理可能的名称变体）
            if not ai_model:
                try:
                    # 尝试模糊匹配
                    fuzzy_matches = AIModelConfig.query.filter(
                        AIModelConfig.name.ilike(f'%{ai_model_id}%')
                    ).all()

                    if fuzzy_matches:
                        ai_model = fuzzy_matches[0]  # 取第一个匹配
                        print(f"✅ 找到AI模型(模糊匹配): {ai_model.name}")
                        if len(fuzzy_matches) > 1:
                            print(f"⚠️ 找到多个匹配，使用第一个: {[m.name for m in fuzzy_matches]}")
                except Exception as e:
                    print(f"⚠️ 模糊匹配失败: {e}")

            # 方法4: 检查是否是系统默认模型
            if not ai_model:
                system_models = ['deepseek_v3', 'openai_gpt4', 'claude_3', 'qwen_max', 'gemini_pro']
                if ai_model_id in system_models:
                    print(f"✅ 系统默认AI模型: {ai_model_id}")
                    ai_model = True  # 标记为有效
                else:
                    print(f"❌ AI模型不存在: {ai_model_id}")

                    # 提供调试信息
                    try:
                        all_models = AIModelConfig.query.all()
                        print(f"💡 数据库中共有 {len(all_models)} 个AI模型:")
                        for model in all_models[:5]:  # 只显示前5个
                            print(f"   - ID: {model.id}, 名称: '{model.name}', 状态: {model.is_active}")
                    except Exception as e:
                        print(f"⚠️ 获取模型列表失败: {e}")

                    return False

            # 检查AI策略是否存在（支持新旧格式）
            ai_strategies = config.get('ai_strategies', [])
            ai_strategy_id = config.get('ai_strategy')

            # 兼容性处理
            if ai_strategy_id and not ai_strategies:
                ai_strategies = [ai_strategy_id]
            elif ai_strategies and not ai_strategy_id:
                ai_strategy_id = ai_strategies[0]

            if not ai_strategies:
                print("❌ AI策略列表为空")
                return False

            print(f"🔍 验证AI策略列表: {ai_strategies}")

            # 验证每个策略
            for strategy_id in ai_strategies:
                print(f"🔍 查找AI策略: {strategy_id}")

                # 检查是否是演示策略
                if str(strategy_id).startswith('demo_'):
                    print(f"✅ 演示AI策略: {strategy_id}")
                    continue

                # 检查真实策略
                from models import AIStrategy
                try:
                    # 尝试按ID查找
                    ai_strategy = None
                    if str(strategy_id).isdigit():
                        ai_strategy = AIStrategy.query.get(int(strategy_id))

                    if ai_strategy:
                        print(f"✅ 找到AI策略: {ai_strategy.name} (ID: {ai_strategy.id})")
                        print(f"   - 状态: {ai_strategy.status}")
                        print(f"   - AI模型: {ai_strategy.ai_model}")

                        # 检查策略状态
                        if ai_strategy.status != 'completed':
                            print(f"⚠️ AI策略状态不是completed: {ai_strategy.status}")
                            # 但仍然允许使用，只是给出警告
                    else:
                        print(f"❌ AI策略不存在: {strategy_id}")

                        # 提供一些调试信息
                        all_strategies = AIStrategy.query.all()
                        print(f"💡 数据库中共有 {len(all_strategies)} 个AI策略:")
                        for strategy in all_strategies[:5]:  # 只显示前5个
                            print(f"   - ID: {strategy.id}, 名称: {strategy.name}, 状态: {strategy.status}")

                        return False

                except Exception as e:
                    print(f"❌ 查询AI策略失败: {e}")
                    return False

            # 所有策略验证通过
            print(f"✅ 所有AI策略验证通过: {len(ai_strategies)}个策略")
            return True

        except Exception as e:
            print(f"❌ AI配置验证异常: {e}")
            return False
    
    def _get_market_data(self, symbols: List[str]) -> Dict[str, Any]:
        """获取市场数据"""
        try:
            from services.mt5_service import mt5_service
            import MetaTrader5 as mt5
            
            market_data = {}
            
            for symbol in symbols:
                # 获取当前价格
                tick = mt5.symbol_info_tick(symbol)
                if tick:
                    market_data[symbol] = {
                        'bid': tick.bid,
                        'ask': tick.ask,
                        'spread': tick.ask - tick.bid,
                        'time': tick.time
                    }
                    
                    # 获取简单的技术指标（最近几根K线）
                    rates = mt5.copy_rates_from_pos(symbol, mt5.TIMEFRAME_M15, 0, 20)
                    if rates is not None and len(rates) > 0:
                        market_data[symbol]['recent_high'] = max(rates['high'][-5:])
                        market_data[symbol]['recent_low'] = min(rates['low'][-5:])
                        market_data[symbol]['trend'] = 'up' if rates['close'][-1] > rates['close'][-5] else 'down'
            
            return market_data
            
        except Exception as e:
            print(f"❌ 获取市场数据失败: {e}")
            return {}
    
    def _get_ai_strategy(self, strategy_id: str) -> Dict[str, Any]:
        """获取AI策略"""
        try:
            # 获取Flask应用实例
            from app import app

            with app.app_context():
                # 使用原生SQL查询避免SQLAlchemy缓存问题
                import sqlite3
                conn = sqlite3.connect('trading_system.db')
                cursor = conn.cursor()

                cursor.execute("""
                    SELECT id, name, description, ai_model, training_data
                    FROM strategy
                    WHERE id = ? AND strategy_type = ?
                """, (strategy_id, 'ai'))

                row = cursor.fetchone()
                conn.close()

                if row:
                    import json
                    training_data = json.loads(row[4]) if row[4] else {}

                    return {
                        'name': row[1],
                        'description': row[2],
                        'ai_model': row[3],
                        'parameters': training_data
                    }

                return {}

        except Exception as e:
            print(f"❌ 获取AI策略失败: {e}")
            return {}
    
    def _get_current_positions(self) -> List[Dict[str, Any]]:
        """获取当前持仓"""
        try:
            from services.mt5_service import mt5_service
            
            positions = mt5_service.get_positions()
            return positions if positions else []
            
        except Exception as e:
            print(f"❌ 获取持仓失败: {e}")
            return []
    
    def _build_analysis_prompt(self, market_data: Dict, ai_strategy: Dict, 
                             user_requirements: str, current_positions: List,
                             risk_settings: Dict) -> str:
        """构建AI分析提示"""
        
        # 检查是否考虑风险事件
        consider_risk_events = risk_settings.get('consider_risk_events', True)

        prompt = f"""
你是一个专业的外汇交易AI分析师。请基于以下信息进行市场分析并给出交易建议：

## 市场数据
{json.dumps(market_data, indent=2, ensure_ascii=False)}
"""

        # 根据配置决定是否包含风险事件
        if consider_risk_events:
            prompt += f"""
## 当前风险事件
{self._get_risk_events_for_analysis()}
"""
        else:
            prompt += """
## 风险事件分析
用户已选择不考虑风险事件，请专注于技术分析和市场数据进行决策。
"""

        prompt += f"""
## AI策略信息
策略名称: {ai_strategy.get('name', '未知')}
策略描述: {ai_strategy.get('description', '无描述')}

## 用户要求
{user_requirements}

## 当前持仓（包含止盈止损设置）
{self._format_positions_with_stop_loss_take_profit(current_positions)}

## 风险设置
最大风险: {risk_settings.get('max_risk_percent', 2)}%
单笔手数: {risk_settings.get('max_lot_size', 0.1)}
止损点数: {risk_settings.get('stop_loss_points', 50)}
考虑风险事件: {'是' if consider_risk_events else '否'}

请分析当前市场情况，结合AI策略和用户要求，给出具体的交易建议。

## 交易信号格式

### 开仓信号：
TRADE_SIGNAL: {{{{
    "action": "buy/sell",
    "symbol": "货币对",
    "volume": 手数,
    "stop_loss": 止损价格(买单必须低于当前价格，卖单必须高于当前价格，具体数值如2634.50),
    "take_profit": 止盈价格(买单必须高于当前价格，卖单必须低于当前价格，具体数值如2650.00),
    "reason": "交易理由",
    "confidence": 信心度(0-1)
}}}}

## 止损止盈设置规则：
- **买单(buy)**：止损 < 当前价格 < 止盈
- **卖单(sell)**：止盈 < 当前价格 < 止损
- 止损止盈距离当前价格至少10个点
- 止损和止盈价格不能相同

### 平仓信号：
TRADE_SIGNAL: {{{{
    "action": "close",
    "close_type": "all/symbol/ticket",
    "symbol": "货币对(当close_type为symbol时)",
    "ticket": 订单号(当close_type为ticket时),
    "reason": "平仓理由",
    "confidence": 信心度(0-1)
}}}}

## 平仓类型说明：
- "all": 平掉所有持仓
- "symbol": 平掉指定货币对的所有持仓
- "ticket": 平掉指定订单号的持仓

## 决策建议（考虑止盈止损设置）：

### 平仓决策原则：
1. **尊重用户止盈止损设置**：
   - 如果持仓已设置止盈止损，优先考虑是否需要调整而非直接平仓
   - 如果价格接近止盈止损，评估是否让自动止盈止损生效
   - 只在有明确理由时才建议提前平仓

2. **智能平仓场景（谨慎使用）**：
   - 市场出现极端反转信号且确认度很高时，才建议提前止损
   - 达到显著的止盈机会（盈利超过预期50%以上）时，才建议提前止盈
   - 市场风险急剧增加且有明确危险信号时，才建议保护性平仓
   - 持仓未设置止盈止损且面临重大风险事件时，才建议平仓
   - **重要**：优先建议调整止盈止损，而不是直接平仓

3. **平仓类型选择**：
   - 如果所有持仓都面临相同风险，使用 "close_type": "all"
   - 如果只有特定货币对有问题，使用 "close_type": "symbol"
   - 如果只需要平掉特定订单，使用 "close_type": "ticket"

4. **风险事件处理**：
"""

        # 根据配置调整风险事件处理说明
        if consider_risk_events:
            prompt += """
   - 高风险事件（风险等级3-4）：建议立即保护性平仓
   - 中风险事件（风险等级2）：评估是否需要调整止盈止损
   - 低风险事件（风险等级1）：继续观察，保持当前策略
   - 经济数据发布前后：考虑降低仓位或设置更严格的止损"""
        else:
            prompt += """
   - 用户已选择不考虑风险事件，请专注于技术分析和价格行为
   - 基于图表形态、技术指标和市场结构进行决策
   - 不需要考虑经济数据发布或地缘政治事件的影响"""

        prompt += """

5. **保守决策原则**：
   - **优先持有**：除非有明确的危险信号，否则倾向于持有现有持仓
   - **调整优于平仓**：优先建议调整止盈止损，而不是直接平仓
   - **高标准平仓**：只有在极端情况下才建议平仓
   - **尊重用户设置**：用户已设置的止盈止损通常是合理的，不要轻易覆盖

6. **平仓理由要求**：
   - 必须说明为什么要覆盖用户的止盈止损设置
   - 必须说明当前市场条件的影响
   - 必须说明平仓的紧急性和必要性
   - 必须提供具体的技术或基本面依据

### 示例平仓理由："""

        # 根据配置提供不同的示例
        if consider_risk_events:
            prompt += """
- "市场出现突发事件，建议立即平仓保护资金，不等待止损触发"
- "重要经济数据即将发布，市场波动加剧，建议降低风险敞口"
- "价格已接近止盈位，但技术指标显示可能反转，建议提前止盈" """
        else:
            prompt += """
- "技术指标显示强烈反转信号，建议提前止损保护资金"
- "价格突破关键支撑/阻力位，建议调整持仓"
- "价格已接近止盈位，但图表形态显示可能反转，建议提前止盈"
- "该货币对基本面发生重大变化，建议平仓重新评估"

如果不建议任何操作，请说明原因并评估当前止盈止损设置的合理性。
"""
        
        return prompt
    
    def _call_external_ai(self, ai_model: str, prompt: str) -> str:
        """调用外部AI模型"""
        try:
            from services.ai_service import AIService

            ai_service = AIService()

            print(f"🤖 准备调用AI模型: {ai_model}")

            # 调用配置的AI模型
            response = ai_service.call_ai_model(
                model_name=ai_model,
                prompt=prompt,
                system_prompt="你是一个专业的外汇交易分析师，具有丰富的市场分析经验。"
            )

            print(f"✅ AI模型调用成功，响应长度: {len(response)} 字符")
            return response

        except Exception as e:
            print(f"❌ 调用外部AI失败: {e}")
            return f"AI分析失败: {str(e)}"
    
    def _parse_ai_response(self, ai_response: str) -> Optional[TradingSignal]:
        """解析AI响应，提取交易信号"""
        try:
            print(f"🔍 开始解析AI响应...")

            # 查找交易信号
            if "TRADE_SIGNAL:" in ai_response:
                signal_start = ai_response.find("TRADE_SIGNAL:") + len("TRADE_SIGNAL:")

                # 更智能的JSON提取
                signal_json = self._extract_json_from_text(ai_response[signal_start:])

                if not signal_json:
                    print("❌ 未找到有效的JSON格式")
                    return None

                print(f"📋 提取的JSON: {signal_json}")

                signal_data = json.loads(signal_json)
                
                # 根据动作类型创建不同的交易信号
                if signal_data['action'] in ['buy', 'sell']:
                    return TradingSignal(
                        action=signal_data['action'],
                        symbol=signal_data['symbol'],
                        volume=float(signal_data['volume']),
                        stop_loss=signal_data.get('stop_loss'),
                        take_profit=signal_data.get('take_profit'),
                        reason=signal_data.get('reason', ''),
                        confidence=float(signal_data.get('confidence', 0.0))
                    )
                elif signal_data['action'] == 'close':
                    # 平仓信号，创建特殊的交易信号
                    return TradingSignal(
                        action='close',
                        symbol=signal_data.get('symbol', ''),
                        volume=0,  # 平仓不需要手数
                        stop_loss=None,
                        take_profit=None,
                        reason=signal_data.get('reason', ''),
                        confidence=float(signal_data.get('confidence', 0.0)),
                        close_type=signal_data.get('close_type', 'all'),
                        ticket=signal_data.get('ticket')
                    )
            
            return None

        except json.JSONDecodeError as e:
            print(f"❌ JSON解析失败: {e}")
            print(f"📋 错误位置: 第{e.lineno}行, 第{e.colno}列")
            if hasattr(e, 'doc') and hasattr(e, 'pos'):
                print(f"📋 错误字符: {repr(e.doc[max(0, e.pos-10):e.pos+10])}")
            print(f"📋 尝试备用解析方法...")

            # 尝试手动修复JSON格式问题
            fixed_signal = self._try_fix_json_format(signal_json)
            if fixed_signal:
                return fixed_signal

            # 尝试备用解析方法
            backup_signal = self._parse_ai_response_backup(ai_response)
            if backup_signal:
                return backup_signal

            print(f"📋 原始AI响应: {ai_response[:500]}...")  # 只显示前500字符
            return None
        except Exception as e:
            print(f"❌ 解析AI响应失败: {e}")
            return None

    def _extract_json_from_text(self, text: str) -> str:
        """从文本中提取JSON字符串"""
        try:
            text = text.strip()

            # 查找JSON开始位置
            start_pos = -1
            for i, char in enumerate(text):
                if char == '{':
                    start_pos = i
                    break

            if start_pos == -1:
                return ""

            # 使用括号匹配找到JSON结束位置
            brace_count = 0
            end_pos = -1

            for i in range(start_pos, len(text)):
                char = text[i]
                if char == '{':
                    brace_count += 1
                elif char == '}':
                    brace_count -= 1
                    if brace_count == 0:
                        end_pos = i
                        break

            if end_pos == -1:
                return ""

            json_str = text[start_pos:end_pos + 1]

            # 尝试清理常见的JSON格式问题
            json_str = self._clean_json_string(json_str)

            return json_str

        except Exception as e:
            print(f"❌ 提取JSON失败: {e}")
            return ""

    def _try_fix_json_format(self, json_str: str) -> Optional[TradingSignal]:
        """尝试修复JSON格式问题"""
        try:
            print(f"🔧 尝试修复JSON格式...")

            # 常见修复方法
            fixes = [
                # 1. 移除外层双重大括号
                lambda s: s[1:-1] if s.startswith('{{') and s.endswith('}}') else s,
                # 2. 移除多余的换行和空格
                lambda s: ' '.join(s.split()),
                # 3. 修复引号问题
                lambda s: s.replace("'", '"'),
                # 4. 移除尾随逗号
                lambda s: re.sub(r',(\s*[}\]])', r'\1', s),
            ]

            for i, fix in enumerate(fixes):
                try:
                    fixed_json = fix(json_str)
                    print(f"🔧 尝试修复方法 {i+1}: {fixed_json[:100]}...")

                    signal_data = json.loads(fixed_json)
                    print(f"✅ 修复方法 {i+1} 成功!")

                    # 创建交易信号
                    if signal_data.get('action') in ['buy', 'sell']:
                        return TradingSignal(
                            action=signal_data['action'],
                            symbol=signal_data['symbol'],
                            volume=float(signal_data['volume']),
                            stop_loss=signal_data.get('stop_loss'),
                            take_profit=signal_data.get('take_profit'),
                            reason=signal_data.get('reason', ''),
                            confidence=float(signal_data.get('confidence', 0.0))
                        )
                except Exception as fix_error:
                    print(f"🔧 修复方法 {i+1} 失败: {fix_error}")
                    continue

            print(f"❌ 所有修复方法都失败了")
            return None

        except Exception as e:
            print(f"❌ JSON修复过程失败: {e}")
            return None

    def _clean_json_string(self, json_str: str) -> str:
        """清理JSON字符串中的常见问题"""
        try:
            # 处理双重大括号问题 {{...}} -> {...}
            json_str = json_str.strip()
            if json_str.startswith('{{') and json_str.endswith('}}'):
                print("🔧 检测到双重大括号，正在修复...")
                json_str = json_str[1:-1]  # 移除外层大括号
                print(f"🔧 修复后的JSON: {json_str[:100]}...")

            # 移除注释
            lines = json_str.split('\n')
            cleaned_lines = []

            for line in lines:
                # 移除行注释
                if '//' in line:
                    line = line[:line.find('//')]
                cleaned_lines.append(line)

            json_str = '\n'.join(cleaned_lines)

            # 修复常见的JSON问题
            # 1. 移除尾随逗号
            json_str = re.sub(r',(\s*[}\]])', r'\1', json_str)

            # 2. 替换中文标点符号为英文标点符号
            json_str = json_str.replace('，', ',')  # 中文逗号 -> 英文逗号
            json_str = json_str.replace('：', ':')  # 中文冒号 -> 英文冒号
            json_str = json_str.replace('；', ';')  # 中文分号 -> 英文分号
            json_str = json_str.replace('"', '"')  # 中文左引号 -> 英文引号
            json_str = json_str.replace('"', '"')  # 中文右引号 -> 英文引号
            json_str = json_str.replace(''', "'")  # 中文左单引号 -> 英文单引号
            json_str = json_str.replace(''', "'")  # 中文右单引号 -> 英文单引号

            # 3. 处理数学表达式
            json_str = self._evaluate_math_expressions(json_str)

            # 4. 确保字符串值被正确引用
            # 这里可以添加更多的清理规则

            return json_str

        except Exception as e:
            print(f"❌ 清理JSON字符串失败: {e}")
            return json_str

    def _evaluate_math_expressions(self, json_str: str) -> str:
        """计算JSON字符串中的数学表达式"""
        try:
            # 查找数学表达式模式
            # 匹配类似 "stop_loss": 3344.41 - 50 * (3355.04 - 3354.88) 的模式
            math_pattern = r'("(?:stop_loss|take_profit)":\s*)([0-9.+\-*/() ]+)(?=\s*[,}])'

            def calculate_expression(match):
                field_name = match.group(1)  # "stop_loss": 或 "take_profit":
                expression = match.group(2).strip()  # 数学表达式

                try:
                    # 安全地计算数学表达式
                    # 只允许数字、基本运算符和括号
                    allowed_chars = set('0123456789.+-*/() ')
                    if all(c in allowed_chars for c in expression):
                        result = eval(expression)
                        print(f"🧮 计算表达式: {expression} = {result}")
                        return f"{field_name}{result:.2f}"
                    else:
                        print(f"⚠️ 表达式包含不安全字符: {expression}")
                        return match.group(0)  # 返回原始内容
                except Exception as e:
                    print(f"❌ 计算表达式失败: {expression}, 错误: {e}")
                    return match.group(0)  # 返回原始内容

            # 替换所有数学表达式
            cleaned_json = re.sub(math_pattern, calculate_expression, json_str)

            if cleaned_json != json_str:
                print(f"🔧 已处理数学表达式")
                print(f"📋 处理前: {json_str}")
                print(f"📋 处理后: {cleaned_json}")

            return cleaned_json

        except Exception as e:
            print(f"❌ 处理数学表达式失败: {e}")
            return json_str

    def _parse_ai_response_backup(self, ai_response: str) -> Optional[TradingSignal]:
        """备用AI响应解析方法，使用正则表达式"""
        try:
            print(f"🔄 使用备用解析方法...")

            # 使用正则表达式提取关键信息
            action_match = re.search(r'"action":\s*"(buy|sell|close)"', ai_response, re.IGNORECASE)
            if not action_match:
                print("❌ 未找到action字段")
                return None

            action = action_match.group(1).lower()

            if action in ['buy', 'sell']:
                # 提取买入/卖出信号的字段
                symbol_match = re.search(r'"symbol":\s*"([^"]+)"', ai_response, re.IGNORECASE)
                volume_match = re.search(r'"volume":\s*([0-9.]+)', ai_response, re.IGNORECASE)

                if not symbol_match or not volume_match:
                    print("❌ 缺少必要字段: symbol或volume")
                    return None

                symbol = symbol_match.group(1)
                volume = float(volume_match.group(1))

                # 可选字段
                stop_loss_match = re.search(r'"stop_loss":\s*([0-9.]+)', ai_response, re.IGNORECASE)
                take_profit_match = re.search(r'"take_profit":\s*([0-9.]+)', ai_response, re.IGNORECASE)
                reason_match = re.search(r'"reason":\s*"([^"]*)"', ai_response, re.IGNORECASE)
                confidence_match = re.search(r'"confidence":\s*([0-9.]+)', ai_response, re.IGNORECASE)

                stop_loss = float(stop_loss_match.group(1)) if stop_loss_match else None
                take_profit = float(take_profit_match.group(1)) if take_profit_match else None
                reason = reason_match.group(1) if reason_match else ""
                confidence = float(confidence_match.group(1)) if confidence_match else 0.0

                print(f"✅ 备用解析成功: {action} {symbol} {volume}手")

                return TradingSignal(
                    action=action,
                    symbol=symbol,
                    volume=volume,
                    stop_loss=stop_loss,
                    take_profit=take_profit,
                    reason=reason,
                    confidence=confidence
                )

            elif action == 'close':
                # 提取平仓信号的字段
                close_type_match = re.search(r'"close_type":\s*"([^"]+)"', ai_response, re.IGNORECASE)
                symbol_match = re.search(r'"symbol":\s*"([^"]*)"', ai_response, re.IGNORECASE)
                reason_match = re.search(r'"reason":\s*"([^"]*)"', ai_response, re.IGNORECASE)
                confidence_match = re.search(r'"confidence":\s*([0-9.]+)', ai_response, re.IGNORECASE)

                close_type = close_type_match.group(1) if close_type_match else 'all'
                symbol = symbol_match.group(1) if symbol_match else ''
                reason = reason_match.group(1) if reason_match else ""
                confidence = float(confidence_match.group(1)) if confidence_match else 0.0

                print(f"✅ 备用解析成功: close {close_type}")

                return TradingSignal(
                    action='close',
                    symbol=symbol,
                    volume=0,
                    stop_loss=None,
                    take_profit=None,
                    reason=reason,
                    confidence=confidence,
                    close_type=close_type,
                    ticket=None
                )

            return None

        except Exception as e:
            print(f"❌ 备用解析方法失败: {e}")
            return None

    def _validate_trading_signal(self, signal: Dict[str, Any]) -> bool:
        """验证交易信号"""
        try:
            # 检查必需的action字段
            if 'action' not in signal:
                print(f"❌ 信号验证失败: 缺少action字段")
                return False

            if signal['action'] not in ['buy', 'sell', 'close']:
                print(f"❌ 信号验证失败: 不支持的action类型: {signal['action']}")
                return False

            # 对于买入和卖出信号，需要symbol和volume
            if signal['action'] in ['buy', 'sell']:
                if 'symbol' not in signal:
                    print(f"❌ 信号验证失败: 缺少symbol字段")
                    return False

                if 'volume' not in signal:
                    print(f"❌ 信号验证失败: 缺少volume字段")
                    return False

                if signal['volume'] <= 0:
                    print(f"❌ 信号验证失败: volume必须大于0, 当前: {signal['volume']}")
                    return False

            # 对于平仓信号，检查平仓类型
            elif signal['action'] == 'close':
                close_type = signal.get('close_type', 'all')

                if close_type == 'symbol' and 'symbol' not in signal:
                    print(f"❌ 信号验证失败: 平仓指定货币对时缺少symbol字段")
                    return False

                if close_type == 'ticket' and 'ticket' not in signal:
                    print(f"❌ 信号验证失败: 平仓指定订单时缺少ticket字段")
                    return False

            print(f"✅ 信号验证通过: {signal['action']} {signal.get('symbol', '')} {signal.get('volume', 'N/A')}手")
            return True

        except Exception as e:
            print(f"❌ 信号验证异常: {e}")
            return False
    
    def _risk_check(self, user_id: int, signal: Dict[str, Any]) -> bool:
        """风险检查"""
        try:
            if user_id not in self.active_agents:
                print(f"❌ 风险检查失败: 用户{user_id}的智能体未运行")
                return False

            # 平仓信号不需要风险检查
            if signal['action'] == 'close':
                print(f"✅ 平仓信号跳过风险检查")
                return True

            config = self.active_agents[user_id].config
            risk_settings = config.get('risk_settings', {})

            # 检查手数限制（仅对买入和卖出信号）
            if signal['action'] in ['buy', 'sell']:
                max_lot_size = risk_settings.get('max_lot_size', 0.1)
                if signal['volume'] > max_lot_size:
                    print(f"⚠️ 手数超限: {signal['volume']} > {max_lot_size}")
                    return False

                # 检查最大风险（简化版本）
                max_risk_percent = risk_settings.get('max_risk_percent', 2)
                # 这里可以添加更复杂的风险计算逻辑

                print(f"✅ 风险检查通过: {signal['action']} {signal['symbol']} {signal['volume']}手")

            return True

        except Exception as e:
            print(f"❌ 风险检查失败: {e}")
            return False

    def _get_filtered_risk_analysis(self, user_id: int, config: Dict[str, Any]) -> Dict[str, Any]:
        """获取过滤后的风险分析"""
        try:
            # 获取用户的风险过滤设置
            risk_settings = config.get('risk_settings', {})
            consider_risk_events = risk_settings.get('consider_risk_events', True)

            if not consider_risk_events:
                return {'should_stop_trading': False, 'risk_reason': ''}

            # 获取用户关注的交易品种
            trading_symbols = config.get('trading_symbols', ['XAUUSD'])
            analysis_mode = risk_settings.get('analysis_mode', 'directional')

            # 获取风险事件服务
            from services.risk_event_service import RiskEventService
            risk_service = RiskEventService()
            all_events = risk_service.get_current_risk_events()

            # 过滤与用户关注品种相关的风险事件
            relevant_events = self._filter_risk_events_for_symbols(all_events, trading_symbols)

            if not relevant_events:
                return {'should_stop_trading': False, 'risk_reason': ''}

            # 分析风险事件对交易的影响
            risk_analysis = self._analyze_risk_events_impact(relevant_events, trading_symbols, analysis_mode)

            return risk_analysis

        except Exception as e:
            print(f"❌ 获取风险分析失败: {e}")
            return {'should_stop_trading': False, 'risk_reason': ''}

    def _filter_risk_events_for_symbols(self, events: List[Dict], symbols: List[str]) -> List[Dict]:
        """过滤与指定品种相关的风险事件"""
        if not symbols:
            return []

        relevant_events = []

        for event in events:
            event_text = (event.get('title', '') + ' ' + event.get('description', '') + ' ' +
                         event.get('currency', '') + ' ' + event.get('symbol', '')).lower()

            is_relevant = False
            for symbol in symbols:
                symbol_lower = symbol.lower()

                # 品种关键词映射
                symbol_keywords = {
                    'xauusd': ['gold', 'xau', '黄金', '贵金属', 'precious metal', 'fed', 'inflation', 'dollar'],
                    'eurusd': ['eur', 'euro', '欧元', 'european', 'ecb', 'eurozone'],
                    'gbpusd': ['gbp', 'pound', '英镑', 'sterling', 'uk', 'britain', 'boe'],
                    'usdjpy': ['jpy', 'yen', '日元', 'japan', 'boj', 'intervention'],
                    'crude_oil': ['oil', 'crude', '原油', '石油', 'wti', 'brent', 'opec']
                }

                # 检查直接匹配或关键词匹配
                if symbol_lower in event_text:
                    is_relevant = True
                    break

                keywords = symbol_keywords.get(symbol_lower, [])
                if any(keyword in event_text for keyword in keywords):
                    is_relevant = True
                    break

            if is_relevant:
                relevant_events.append(event)

        return relevant_events

    def _analyze_risk_events_impact(self, events: List[Dict], symbols: List[str], analysis_mode: str) -> Dict[str, Any]:
        """分析风险事件对交易的影响"""
        try:
            if not events:
                return {'should_stop_trading': False, 'risk_reason': ''}

            # 统计高风险事件
            critical_events = [e for e in events if e.get('risk_level', 1) >= 4]
            high_risk_events = [e for e in events if e.get('risk_level', 1) >= 3]

            # 极高风险事件：无论什么模式都建议停止交易
            if critical_events:
                event_titles = [e.get('title', '未知事件') for e in critical_events[:2]]
                return {
                    'should_stop_trading': True,
                    'risk_reason': f'检测到极高风险事件: {", ".join(event_titles)}',
                    'analysis_mode': analysis_mode,
                    'affected_symbols': symbols
                }

            # 保守模式：高风险事件也建议停止交易
            if analysis_mode == 'conservative' and high_risk_events:
                event_titles = [e.get('title', '未知事件') for e in high_risk_events[:2]]
                return {
                    'should_stop_trading': True,
                    'risk_reason': f'保守模式检测到高风险事件: {", ".join(event_titles)}',
                    'analysis_mode': analysis_mode,
                    'affected_symbols': symbols
                }

            # 方向性分析模式：分析风险事件的方向性影响
            if analysis_mode == 'directional':
                directional_analysis = self._get_directional_risk_analysis(events, symbols)
                return {
                    'should_stop_trading': False,
                    'risk_reason': '',
                    'directional_analysis': directional_analysis,
                    'analysis_mode': analysis_mode,
                    'affected_symbols': symbols
                }

            return {'should_stop_trading': False, 'risk_reason': ''}

        except Exception as e:
            print(f"❌ 分析风险事件影响失败: {e}")
            return {'should_stop_trading': False, 'risk_reason': ''}

    def _get_directional_risk_analysis(self, events: List[Dict], symbols: List[str]) -> Dict[str, Any]:
        """获取方向性风险分析"""
        analysis = {}

        for symbol in symbols:
            symbol_analysis = {
                'bullish_factors': [],
                'bearish_factors': [],
                'overall_direction': 'neutral',
                'confidence': 0.5
            }

            for event in events:
                impact = self._analyze_single_event_impact(event, symbol)

                if impact['direction'] == 'bullish':
                    symbol_analysis['bullish_factors'].append({
                        'event': event.get('title', ''),
                        'reasoning': impact['reasoning'],
                        'confidence': impact['confidence']
                    })
                elif impact['direction'] == 'bearish':
                    symbol_analysis['bearish_factors'].append({
                        'event': event.get('title', ''),
                        'reasoning': impact['reasoning'],
                        'confidence': impact['confidence']
                    })

            # 计算整体方向
            bullish_score = sum(f['confidence'] for f in symbol_analysis['bullish_factors'])
            bearish_score = sum(f['confidence'] for f in symbol_analysis['bearish_factors'])

            if bullish_score > bearish_score + 0.2:
                symbol_analysis['overall_direction'] = 'bullish'
                symbol_analysis['confidence'] = min(bullish_score / len(events), 0.9)
            elif bearish_score > bullish_score + 0.2:
                symbol_analysis['overall_direction'] = 'bearish'
                symbol_analysis['confidence'] = min(bearish_score / len(events), 0.9)

            analysis[symbol] = symbol_analysis

        return analysis

    def _analyze_single_event_impact(self, event: Dict, symbol: str) -> Dict[str, Any]:
        """分析单个事件对品种的影响"""
        event_text = (event.get('title', '') + ' ' + event.get('description', '')).lower()
        symbol_lower = symbol.lower()
        risk_level = event.get('risk_level', 1)

        impact = {
            'direction': 'neutral',
            'confidence': 0.5,
            'reasoning': ''
        }

        # 黄金特定分析
        if symbol_lower == 'xauusd':
            # 利好因素
            if any(keyword in event_text for keyword in ['inflation', '通胀', 'uncertainty', '不确定', 'crisis', '危机']):
                impact['direction'] = 'bullish'
                impact['confidence'] = 0.7
                impact['reasoning'] = '避险情绪和通胀担忧利好黄金'
            # 利空因素
            elif any(keyword in event_text for keyword in ['rate hike', '加息', 'strong dollar', '美元走强']):
                impact['direction'] = 'bearish'
                impact['confidence'] = 0.6
                impact['reasoning'] = '加息预期或美元走强压制黄金'

            # 高风险事件通常利好黄金
            if risk_level >= 3 and impact['direction'] == 'neutral':
                impact['direction'] = 'bullish'
                impact['confidence'] = 0.6
                impact['reasoning'] = '高风险事件推动避险需求'

        # 其他品种的分析逻辑...
        # 这里可以添加EURUSD、GBPUSD、USDJPY等的分析

        return impact

    def _execute_close_positions(self, user_id: int, signal: Dict[str, Any]) -> Dict[str, Any]:
        """执行平仓操作"""
        try:
            from services.mt5_service import mt5_service

            symbol = signal.get('symbol')
            close_type = signal.get('close_type', 'all')  # 'all', 'symbol', 'ticket'

            # 清理货币对名称
            if symbol:
                original_symbol = symbol
                symbol = self._clean_symbol_name(symbol)
                if original_symbol != symbol:
                    print(f"🔧 平仓货币对名称清理: {original_symbol} -> {symbol}")

            print(f"🔄 智能体执行平仓 - 用户: {user_id}, 货币对: {symbol}, 类型: {close_type}")

            if close_type == 'all':
                # 平掉所有持仓
                return self._close_all_positions(user_id)
            elif close_type == 'symbol' and symbol:
                # 平掉指定货币对的所有持仓
                return self._close_positions_by_symbol(user_id, symbol)
            elif close_type == 'ticket' and signal.get('ticket'):
                # 平掉指定订单
                return self._close_position_by_ticket(user_id, signal['ticket'])
            else:
                return {'success': False, 'error': '无效的平仓参数'}

        except Exception as e:
            print(f"❌ 执行平仓异常: {e}")
            return {'success': False, 'error': str(e)}

    def _close_all_positions(self, user_id: int) -> Dict[str, Any]:
        """平掉所有持仓"""
        try:
            from services.mt5_service import mt5_service
            import time
            import threading

            print(f"🔄 开始获取所有持仓 - 用户: {user_id}")

            # 使用线程安全的超时机制
            positions = None
            timeout_occurred = threading.Event()

            def get_positions_with_timeout():
                nonlocal positions
                try:
                    positions = mt5_service.get_positions()
                except Exception as e:
                    print(f"❌ 获取持仓异常: {e}")
                    positions = None

            # 启动获取持仓的线程
            thread = threading.Thread(target=get_positions_with_timeout)
            thread.daemon = True
            thread.start()
            thread.join(timeout=10)  # 10秒超时

            if thread.is_alive():
                print(f"❌ 获取持仓超时 - 用户: {user_id}")
                return {'success': False, 'error': '获取持仓超时'}

            if not positions:
                print(f"ℹ️ 用户 {user_id} 没有持仓需要平仓")
                return {
                    'success': True,
                    'message': '没有持仓需要平仓',
                    'closed_count': 0
                }

            print(f"📊 找到 {len(positions)} 个持仓需要平仓")
            closed_count = 0
            failed_count = 0
            max_positions = 10  # 限制最大平仓数量，防止无限循环

            for i, position in enumerate(positions[:max_positions]):
                try:
                    print(f"🔄 平仓进度: {i+1}/{min(len(positions), max_positions)} - 订单: {position['ticket']}")

                    # 使用线程超时机制进行平仓
                    result = None
                    close_completed = threading.Event()

                    def close_position_with_timeout():
                        nonlocal result
                        try:
                            result = mt5_service.close_position(position['ticket'])
                        except Exception as e:
                            print(f"❌ 平仓操作异常: {e}")
                            result = None
                        finally:
                            close_completed.set()

                    # 启动平仓线程
                    close_thread = threading.Thread(target=close_position_with_timeout)
                    close_thread.daemon = True
                    close_thread.start()

                    # 等待完成或超时
                    if close_completed.wait(timeout=5):  # 5秒超时
                        if result and hasattr(result, 'retcode') and result.retcode == 10009:
                            closed_count += 1
                            print(f"✅ 智能体平仓成功 - 订单: {position['ticket']}")
                        else:
                            failed_count += 1
                            error_code = getattr(result, 'retcode', 'Unknown') if result else 'No result'
                            print(f"❌ 智能体平仓失败 - 订单: {position['ticket']}, 错误码: {error_code}")
                    else:
                        failed_count += 1
                        print(f"❌ 智能体平仓超时 - 订单: {position['ticket']}")

                except Exception as e:
                    failed_count += 1
                    print(f"❌ 智能体平仓异常 - 订单: {position['ticket']}, 错误: {e}")

                # 添加小延迟，避免过快操作
                time.sleep(0.1)

            # 更新统计
            if user_id in self.active_agents:
                self.active_agents[user_id].total_trades += closed_count

            print(f"📊 平仓操作完成 - 成功: {closed_count}, 失败: {failed_count}")

            return {
                'success': True,
                'message': f'智能体平仓完成: 成功 {closed_count} 个，失败 {failed_count} 个',
                'closed_count': closed_count,
                'failed_count': failed_count
            }

        except Exception as e:
            print(f"❌ 智能体平掉所有持仓异常: {e}")
            return {'success': False, 'error': str(e)}

    def _close_positions_by_symbol(self, user_id: int, symbol: str) -> Dict[str, Any]:
        """平掉指定货币对的所有持仓"""
        try:
            from services.mt5_service import mt5_service
            import time
            import threading

            print(f"🔄 开始获取 {symbol} 持仓 - 用户: {user_id}")

            # 使用线程安全的超时机制
            positions = None

            def get_positions_with_timeout():
                nonlocal positions
                try:
                    positions = mt5_service.get_positions()
                except Exception as e:
                    print(f"❌ 获取持仓异常: {e}")
                    positions = None

            # 启动获取持仓的线程
            thread = threading.Thread(target=get_positions_with_timeout)
            thread.daemon = True
            thread.start()
            thread.join(timeout=10)  # 10秒超时

            if thread.is_alive():
                print(f"❌ 获取持仓超时 - 用户: {user_id}")
                return {'success': False, 'error': '获取持仓超时'}

            if not positions:
                return {
                    'success': True,
                    'message': f'没有 {symbol} 的持仓需要平仓',
                    'closed_count': 0
                }

            # 筛选指定货币对的持仓
            symbol_positions = [pos for pos in positions if pos['symbol'] == symbol]

            if not symbol_positions:
                return {
                    'success': True,
                    'message': f'没有 {symbol} 的持仓需要平仓',
                    'closed_count': 0
                }

            print(f"📊 找到 {len(symbol_positions)} 个 {symbol} 持仓需要平仓")
            closed_count = 0
            failed_count = 0

            for i, position in enumerate(symbol_positions):
                try:
                    print(f"🔄 平仓 {symbol} 进度: {i+1}/{len(symbol_positions)} - 订单: {position['ticket']}")

                    # 使用线程超时机制进行平仓
                    result = None
                    close_completed = threading.Event()

                    def close_position_with_timeout():
                        nonlocal result
                        try:
                            result = mt5_service.close_position(position['ticket'])
                        except Exception as e:
                            print(f"❌ 平仓操作异常: {e}")
                            result = None
                        finally:
                            close_completed.set()

                    # 启动平仓线程
                    close_thread = threading.Thread(target=close_position_with_timeout)
                    close_thread.daemon = True
                    close_thread.start()

                    # 等待完成或超时
                    if close_completed.wait(timeout=5):  # 5秒超时
                        if result and hasattr(result, 'retcode') and result.retcode == 10009:
                            closed_count += 1
                            print(f"✅ 智能体平仓 {symbol} 成功 - 订单: {position['ticket']}")
                        else:
                            failed_count += 1
                            error_code = getattr(result, 'retcode', 'Unknown') if result else 'No result'
                            print(f"❌ 智能体平仓 {symbol} 失败 - 订单: {position['ticket']}, 错误码: {error_code}")
                    else:
                        failed_count += 1
                        print(f"❌ 智能体平仓 {symbol} 超时 - 订单: {position['ticket']}")

                except Exception as e:
                    failed_count += 1
                    print(f"❌ 智能体平仓 {symbol} 异常 - 订单: {position['ticket']}, 错误: {e}")

                # 添加小延迟，避免过快操作
                time.sleep(0.1)

            # 更新统计
            if user_id in self.active_agents:
                self.active_agents[user_id].total_trades += closed_count

            return {
                'success': True,
                'message': f'智能体平仓 {symbol} 完成: 成功 {closed_count} 个，失败 {failed_count} 个',
                'closed_count': closed_count,
                'failed_count': failed_count
            }

        except Exception as e:
            print(f"❌ 智能体平仓 {symbol} 异常: {e}")
            return {'success': False, 'error': str(e)}

    def _close_position_by_ticket(self, user_id: int, ticket: int) -> Dict[str, Any]:
        """平掉指定订单"""
        try:
            from services.mt5_service import mt5_service
            import threading

            print(f"🔄 开始平仓指定订单 - 用户: {user_id}, 订单: {ticket}")

            # 使用线程超时机制
            result = None
            close_completed = threading.Event()

            def close_position_with_timeout():
                nonlocal result
                try:
                    result = mt5_service.close_position(ticket)
                except Exception as e:
                    print(f"❌ 平仓操作异常: {e}")
                    result = None
                finally:
                    close_completed.set()

            # 启动平仓线程
            close_thread = threading.Thread(target=close_position_with_timeout)
            close_thread.daemon = True
            close_thread.start()

            # 等待完成或超时
            if not close_completed.wait(timeout=5):  # 5秒超时
                print(f"❌ 智能体平仓超时 - 用户: {user_id}, 订单: {ticket}")
                return {'success': False, 'error': '平仓操作超时'}

            if result and hasattr(result, 'retcode') and result.retcode == 10009:
                # 更新统计
                if user_id in self.active_agents:
                    self.active_agents[user_id].total_trades += 1

                print(f"✅ 智能体平仓成功 - 用户: {user_id}, 订单: {ticket}")

                return {
                    'success': True,
                    'ticket': getattr(result, 'order', ticket),
                    'message': '智能体平仓成功'
                }
            else:
                error_code = getattr(result, 'retcode', 'Unknown') if result else 'No result'
                error_msg = getattr(result, 'comment', '未知错误') if result else '未知错误'
                print(f"❌ 智能体平仓失败 - 用户: {user_id}, 订单: {ticket}, 错误码: {error_code}, 错误: {error_msg}")

                return {'success': False, 'error': f'智能体平仓失败: {error_msg} (错误码: {error_code})'}

        except Exception as e:
            print(f"❌ 智能体平仓异常: {e}")
            return {'success': False, 'error': str(e)}

    def _format_positions_with_stop_loss_take_profit(self, positions: List[Dict]) -> str:
        """格式化持仓信息，包含止盈止损设置"""
        if not positions:
            return "当前无持仓"

        formatted_positions = []

        for position in positions:
            # 基本信息
            symbol = position.get('symbol', 'Unknown')
            ticket = position.get('ticket', 'Unknown')
            position_type = 'BUY' if position.get('type') == 0 else 'SELL'
            volume = position.get('volume', 0)
            open_price = position.get('price_open', 0)
            current_price = position.get('price_current', 0)
            profit = position.get('profit', 0)

            # 止盈止损信息
            stop_loss = position.get('sl', 0)
            take_profit = position.get('tp', 0)

            # 计算距离止盈止损的点数
            sl_distance = ""
            tp_distance = ""

            if stop_loss > 0:
                if position_type == 'BUY':
                    sl_distance = f" (距离: {(current_price - stop_loss) * 10000:.1f} 点)"
                else:
                    sl_distance = f" (距离: {(stop_loss - current_price) * 10000:.1f} 点)"

            if take_profit > 0:
                if position_type == 'BUY':
                    tp_distance = f" (距离: {(take_profit - current_price) * 10000:.1f} 点)"
                else:
                    tp_distance = f" (距离: {(current_price - take_profit) * 10000:.1f} 点)"

            # 风险评估
            risk_status = self._assess_position_risk(position)

            formatted_position = {
                "订单号": ticket,
                "货币对": symbol,
                "方向": position_type,
                "手数": volume,
                "开仓价": open_price,
                "当前价": current_price,
                "盈亏": f"${profit:.2f}",
                "止损价": f"{stop_loss}{sl_distance}" if stop_loss > 0 else "未设置",
                "止盈价": f"{take_profit}{tp_distance}" if take_profit > 0 else "未设置",
                "风险状态": risk_status
            }

            formatted_positions.append(formatted_position)

        return json.dumps(formatted_positions, indent=2, ensure_ascii=False)

    def _assess_position_risk(self, position: Dict) -> str:
        """评估持仓风险状态"""
        try:
            current_price = position.get('price_current', 0)
            stop_loss = position.get('sl', 0)
            take_profit = position.get('tp', 0)
            profit = position.get('profit', 0)
            position_type = position.get('type', 0)

            risk_factors = []

            # 检查是否接近止损
            if stop_loss > 0:
                if position_type == 0:  # BUY
                    distance_to_sl = (current_price - stop_loss) * 10000
                    if distance_to_sl < 10:
                        risk_factors.append("接近止损")
                else:  # SELL
                    distance_to_sl = (stop_loss - current_price) * 10000
                    if distance_to_sl < 10:
                        risk_factors.append("接近止损")

            # 检查是否接近止盈
            if take_profit > 0:
                if position_type == 0:  # BUY
                    distance_to_tp = (take_profit - current_price) * 10000
                    if distance_to_tp < 10:
                        risk_factors.append("接近止盈")
                else:  # SELL
                    distance_to_tp = (current_price - take_profit) * 10000
                    if distance_to_tp < 10:
                        risk_factors.append("接近止盈")

            # 检查盈亏状态
            if profit > 50:
                risk_factors.append("盈利较好")
            elif profit < -50:
                risk_factors.append("亏损较大")

            # 检查是否未设置止盈止损
            if stop_loss == 0 and take_profit == 0:
                risk_factors.append("未设置止盈止损")
            elif stop_loss == 0:
                risk_factors.append("未设置止损")
            elif take_profit == 0:
                risk_factors.append("未设置止盈")

            return ", ".join(risk_factors) if risk_factors else "正常"

        except Exception as e:
            return f"评估异常: {e}"

    def _get_risk_events_for_analysis(self) -> str:
        """获取风险事件用于AI分析"""
        try:
            from services.risk_event_service import risk_event_service
            return risk_event_service.format_risk_events_for_ai()
        except Exception as e:
            print(f"❌ 获取风险事件失败: {e}")
            return "风险事件获取失败"

    def get_agent_status(self, user_id: int) -> Dict[str, Any]:
        """获取智能体状态"""
        try:
            if user_id not in self.active_agents:
                return {
                    'success': True,
                    'running': False,
                    'status': 'stopped',
                    'start_time': None,
                    'last_analysis': None,
                    'total_trades': 0,
                    'total_profit': 0.0
                }

            agent = self.active_agents[user_id]

            # 检查线程是否还在运行
            thread_alive = False
            if user_id in self.analysis_threads:
                thread_alive = self.analysis_threads[user_id].is_alive()

            # 如果线程已死但状态还是running，更新状态
            if agent.status == 'running' and not thread_alive:
                agent.status = 'stopped'
                print(f"⚠️ 检测到智能体线程已停止，更新状态: 用户{user_id}")

            # 判断是否正在运行（包括自动停止状态和休眠状态）
            is_running = agent.status in ['running', 'auto_stopping', 'sleeping'] and thread_alive

            return {
                'success': True,
                'running': is_running,
                'status': agent.status,
                'start_time': agent.start_time.isoformat() if agent.start_time else None,
                'last_analysis': agent.last_analysis_time.isoformat() if agent.last_analysis_time else None,
                'total_trades': agent.total_trades,
                'total_profit': agent.total_profit,
                'thread_alive': thread_alive,
                'auto_stopping': agent.status == 'auto_stopping',
                'sleeping': agent.status == 'sleeping'
            }

        except Exception as e:
            print(f"❌ 获取智能体状态失败: {e}")
            return {'success': False, 'error': str(e)}

    def _clean_symbol_name(self, symbol: str) -> str:
        """清理货币对名称，移除可能的后缀"""
        try:
            if not symbol:
                return symbol

            # 移除常见的后缀
            suffixes_to_remove = ['=X', '.', '_', '-']

            cleaned_symbol = symbol.strip().upper()

            for suffix in suffixes_to_remove:
                if suffix in cleaned_symbol:
                    cleaned_symbol = cleaned_symbol.split(suffix)[0]

            # 确保是有效的货币对格式（6个字符）
            if len(cleaned_symbol) == 6 and cleaned_symbol.isalpha():
                return cleaned_symbol

            # 如果不是标准格式，返回原始符号（可能是其他类型的交易品种）
            return symbol.strip()

        except Exception as e:
            print(f"❌ 清理货币对名称失败: {e}")
            return symbol

    # ==================== 新架构核心方法 ====================

    def _perform_agent_analysis(self, user_id: int, config: Dict[str, Any], round_number: int) -> Dict[str, Any]:
        """智能体进行交易开始分析（宏观决策）"""
        try:
            print(f"🧠 智能体分析阶段 - 用户{user_id}, 第{round_number}轮")

            # 获取市场数据
            market_data = self._get_market_data(config['trading_symbols'])
            current_positions = self._get_current_positions()

            # 获取可用的AI策略详情
            available_strategies = self._get_strategy_details(config.get('ai_strategies', []))

            # 构建智能体分析提示（专注于宏观决策和策略选择）
            agent_prompt = self._build_agent_analysis_prompt(
                market_data, config, round_number, current_positions, available_strategies
            )

            # 调用AI模型进行分析
            ai_response = self._call_ai_model(config['ai_model'], agent_prompt)

            if ai_response and ai_response.get('success'):
                analysis_text = ai_response.get('content', '')

                # 解析智能体决策
                decision = self._parse_agent_decision(analysis_text)

                return {
                    'success': True,
                    'analysis': analysis_text,
                    'should_trade': decision.get('should_trade', False),
                    'should_stop_trading': decision.get('should_stop_trading', False),
                    'risk_reason': decision.get('risk_reason', ''),
                    'market_sentiment': decision.get('market_sentiment', 'neutral'),
                    'recommended_strategy': decision.get('recommended_strategy', '')
                }
            else:
                return {'success': False, 'error': '智能体分析失败'}

        except Exception as e:
            print(f"❌ 智能体分析异常: {e}")
            return {'success': False, 'error': str(e)}

    def _execute_ai_strategy_trading(self, user_id: int, config: Dict[str, Any],
                                   round_number: int, max_orders: int, selected_strategy_id: str = None) -> Dict[str, Any]:
        """AI策略执行具体交易"""
        try:
            print(f"🎯 AI策略交易执行 - 用户{user_id}, 第{round_number}轮, 最大订单数: {max_orders}")
            print(f"📋 选定策略ID: {selected_strategy_id}")

            # 再次检查交易时间段（双重保险）
            if not self._is_trading_time_allowed(config):
                print(f"🛑 交易执行阶段检测到非交易时段，停止执行")
                return {'success': False, 'error': '当前时间不在允许的交易时段内', 'orders_executed': 0}

            if max_orders <= 0:
                return {'success': True, 'orders_executed': 0, 'message': '已达到本轮订单限制'}

            # 获取选定的AI策略配置
            if selected_strategy_id:
                ai_strategy = self._get_strategy_by_id(selected_strategy_id)
            else:
                # 如果没有指定策略，使用配置中的第一个策略
                strategies = config.get('ai_strategies', [])
                if strategies:
                    ai_strategy = self._get_strategy_by_id(strategies[0])
                else:
                    return {'success': False, 'error': '没有可用的AI策略'}

            # 获取市场数据
            market_data = self._get_market_data(config['trading_symbols'])
            current_positions = self._get_current_positions()

            # 构建AI策略交易提示（专注于具体交易信号）
            strategy_prompt = self._build_strategy_trading_prompt(
                market_data, ai_strategy, config, current_positions, max_orders
            )

            # 调用AI策略模型
            strategy_response = self._call_ai_strategy(ai_strategy, strategy_prompt)

            if strategy_response and strategy_response.get('success'):
                # 解析交易信号
                trading_signals = self._parse_strategy_signals(strategy_response.get('content', ''))

                # 执行交易信号（限制数量）
                executed_orders = 0
                for signal in trading_signals[:max_orders]:
                    trade_result = self.execute_trade(user_id, signal)
                    if trade_result and trade_result.get('success'):
                        executed_orders += 1

                return {
                    'success': True,
                    'orders_executed': executed_orders,
                    'total_signals': len(trading_signals),
                    'strategy_analysis': strategy_response.get('content', '')
                }
            else:
                return {'success': False, 'error': 'AI策略执行失败'}

        except Exception as e:
            print(f"❌ AI策略交易执行异常: {e}")
            return {'success': False, 'error': str(e)}

    def _perform_round_reflection(self, user_id: int, config: Dict[str, Any],
                                round_number: int, round_start_time: datetime,
                                orders_count: int) -> Dict[str, Any]:
        """智能体进行一轮结束反思"""
        try:
            print(f"🤔 智能体反思阶段 - 用户{user_id}, 第{round_number}轮完成")

            # 计算本轮交易统计
            round_duration = datetime.now() - round_start_time
            round_stats = self._calculate_round_stats(user_id, round_start_time)

            # 构建反思提示
            reflection_prompt = self._build_reflection_prompt(
                config, round_number, round_duration, orders_count, round_stats
            )

            # 调用AI模型进行反思
            reflection_response = self._call_ai_model(config['ai_model'], reflection_prompt)

            if reflection_response and reflection_response.get('success'):
                reflection_text = reflection_response.get('content', '')

                # 解析反思结果
                insights = self._parse_reflection_insights(reflection_text)

                # 保存反思记录
                self._save_reflection_record(user_id, round_number, {
                    'reflection': reflection_text,
                    'insights': insights,
                    'stats': round_stats,
                    'duration': round_duration.total_seconds()
                })

                return {
                    'success': True,
                    'reflection': reflection_text,
                    'insights': insights,
                    'adjustments': insights.get('adjustments', [])
                }
            else:
                return {'success': False, 'error': '反思分析失败'}

        except Exception as e:
            print(f"❌ 智能体反思异常: {e}")
            return {'success': False, 'error': str(e)}

    # ==================== 新架构辅助方法 ====================

    def _build_agent_analysis_prompt(self, market_data: Dict, config: Dict,
                                   round_number: int, positions: List, available_strategies: List) -> str:
        """构建智能体分析提示（专注于宏观决策和策略选择）"""

        risk_events = ""
        if config.get('risk_settings', {}).get('consider_risk_events', True):
            risk_events = self._get_risk_events_for_analysis()

        # 格式化可用策略信息
        strategies_info = self._format_strategies_for_prompt(available_strategies)

        # 获取交易时间段信息
        trading_time_info = self._get_trading_time_info(config)

        prompt = f"""
你是一个专业的交易智能体，负责宏观交易决策、风险控制和AI策略选择。

当前状态：
- 交易轮次：第{round_number}轮
- 当前持仓：{len(positions)}个
- 持仓详情：{self._format_positions_for_prompt(positions)}

市场数据：
{self._format_market_data_for_prompt(market_data)}

交易时间段配置：
{trading_time_info}

风险事件：
{risk_events}

可用AI策略：
{strategies_info}

用户要求：
{config.get('user_requirements', '无特殊要求')}

你的职责：
1. 分析当前市场环境和风险状况
2. 考虑交易时间段的特点和适用性
3. 决定是否应该进行交易
4. 检测是否存在重大风险需要停止交易
5. 从可用策略中选择最适合当前市场和时间段的AI策略
6. 为选中的AI策略提供宏观指导

请分析并回答：
1. 当前市场情况如何？
2. 当前交易时间段的特点是什么？
3. 是否存在重大风险需要停止交易？
4. 从可用策略中选择最适合当前市场和时间段的策略（如果只有一个策略则必须选择它）
5. 选择该策略的理由是什么？
6. 对本轮交易的整体建议？

请以JSON格式回复：
{{
    "should_trade": true/false,
    "should_stop_trading": true/false,
    "risk_reason": "风险原因（如果需要停止）",
    "market_sentiment": "bullish/bearish/neutral",
    "selected_strategy_id": "选中的策略ID",
    "strategy_selection_reason": "选择该策略的理由",
    "analysis": "详细分析"
}}
"""
        return prompt

    def _build_strategy_trading_prompt(self, market_data: Dict, ai_strategy: Dict,
                                     config: Dict, positions: List, max_orders: int) -> str:
        """构建AI策略交易提示（专注于具体交易信号）"""

        # 获取交易时间段信息
        trading_time_info = self._get_trading_time_info(config)

        prompt = f"""
你是一个专业的AI交易策略执行器，负责生成具体的交易信号。

策略配置：
{ai_strategy}

当前市场数据：
{self._format_market_data_for_prompt(market_data)}

交易时间段信息：
{trading_time_info}

当前持仓：
{self._format_positions_for_prompt(positions)}

交易限制：
- 最大订单数：{max_orders}单
- 手数区间：{config.get('risk_settings', {}).get('min_lot_size', 0.01)} - {config.get('risk_settings', {}).get('max_lot_size', 0.02)}手
- 止损点数：{config.get('risk_settings', {}).get('stop_loss_points', 50)}
- 止盈点数：{config.get('risk_settings', {}).get('take_profit_points', 100)}

你的任务：
1. 根据策略配置分析市场
2. 考虑当前交易时间段的特点和适用性
3. 根据时间段特点调整交易策略和手数选择
4. 生成具体的交易信号
5. 设置合理的止损({config.get('risk_settings', {}).get('stop_loss_points', 50)}点)和止盈({config.get('risk_settings', {}).get('take_profit_points', 100)}点)
6. 控制交易数量在限制范围内

请生成交易信号，格式如下：
[
    {{
        "action": "buy/sell/close",
        "symbol": "交易品种",
        "volume": 手数,
        "stop_loss": 止损价格,
        "take_profit": 止盈价格,
        "reason": "交易理由",
        "confidence": 0.0-1.0
    }}
]

如果不建议交易，请返回空数组 []
"""
        return prompt

    def _build_reflection_prompt(self, config: Dict, round_number: int,
                               duration: timedelta, orders_count: int, stats: Dict) -> str:
        """构建反思提示"""

        # 获取交易时间段信息
        trading_time_info = self._get_trading_time_info(config)

        prompt = f"""
你是一个专业的交易智能体，现在需要对刚完成的交易轮次进行反思。

轮次信息：
- 轮次编号：第{round_number}轮
- 持续时间：{duration.total_seconds()/60:.1f}分钟
- 执行订单：{orders_count}单
- 盈亏情况：{stats.get('profit', 0):.2f}美元
- 成功率：{stats.get('win_rate', 0):.1%}

交易时间段信息：
{trading_time_info}

配置信息：
- AI模型：{config.get('ai_model', {}).get('name', '未知')}
- 可用AI策略：{', '.join(config.get('ai_strategies', []))}
- 用户要求：{config.get('user_requirements', '无')}

请进行深度反思并回答：
1. 本轮交易的整体表现如何？
2. 当前交易时间段的特点是否得到充分利用？
3. 哪些决策是正确的？
4. 哪些地方需要改进？
5. 对下一轮有什么建议？
6. 下一轮应该选择哪个AI策略？为什么？
7. 是否需要根据时间段特点调整策略参数？

请以JSON格式回复：
{{
    "performance_rating": 1-10,
    "strengths": ["优点1", "优点2"],
    "weaknesses": ["不足1", "不足2"],
    "adjustments": ["调整建议1", "调整建议2"],
    "next_round_strategy_id": "下轮推荐的策略ID",
    "strategy_selection_reason": "选择该策略的理由",
    "reflection": "详细反思"
}}
"""
        return prompt

    def _parse_agent_decision(self, analysis_text: str) -> Dict[str, Any]:
        """解析智能体决策"""
        try:
            # 尝试解析JSON格式
            import json

            # 查找JSON部分
            json_match = re.search(r'\{.*\}', analysis_text, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                decision = json.loads(json_str)
                return decision

            # 如果没有JSON，使用关键词解析
            decision = {
                'should_trade': False,
                'should_stop_trading': False,
                'market_sentiment': 'neutral',
                'recommended_strategy': '',
                'risk_reason': ''
            }

            # 关键词检测
            text_lower = analysis_text.lower()

            if any(word in text_lower for word in ['建议交易', '可以交易', 'should trade', 'recommend trading']):
                decision['should_trade'] = True

            if any(word in text_lower for word in ['停止交易', '风险过高', 'stop trading', 'high risk']):
                decision['should_stop_trading'] = True

            if any(word in text_lower for word in ['看涨', '上涨', 'bullish', 'uptrend']):
                decision['market_sentiment'] = 'bullish'
            elif any(word in text_lower for word in ['看跌', '下跌', 'bearish', 'downtrend']):
                decision['market_sentiment'] = 'bearish'

            return decision

        except Exception as e:
            print(f"❌ 解析智能体决策失败: {e}")
            return {
                'should_trade': False,
                'should_stop_trading': False,
                'market_sentiment': 'neutral',
                'recommended_strategy': '',
                'risk_reason': ''
            }

    def _parse_strategy_signals(self, strategy_text: str) -> List[Dict[str, Any]]:
        """解析AI策略交易信号"""
        try:
            import json

            # 查找JSON数组
            json_match = re.search(r'\[.*\]', strategy_text, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                signals = json.loads(json_str)

                # 验证信号格式并过滤平仓信号
                valid_signals = []
                for signal in signals:
                    if isinstance(signal, dict) and 'action' in signal and 'symbol' in signal:
                        # 对平仓信号进行严格过滤
                        if signal.get('action') == 'close':
                            # 检查平仓理由的强度
                            reason = signal.get('reason', '').lower()
                            confidence = signal.get('confidence', 0)

                            # 只有在以下情况下才允许平仓：
                            # 1. 置信度超过80%
                            # 2. 有明确的危险信号关键词
                            # 3. 有具体的技术分析依据
                            danger_keywords = ['极端', '重大', '急剧', '暴跌', '暴涨', '危险', '紧急', 'extreme', 'critical', 'urgent']
                            has_danger_signal = any(keyword in reason for keyword in danger_keywords)

                            if confidence >= 0.8 and has_danger_signal:
                                print(f"⚠️ 允许高置信度平仓信号: {signal}")
                                valid_signals.append(signal)
                            else:
                                print(f"🛑 过滤低置信度平仓信号: 置信度{confidence}, 理由: {reason}")
                        else:
                            # 非平仓信号正常添加
                            valid_signals.append(signal)

                return valid_signals

            return []

        except Exception as e:
            print(f"❌ 解析AI策略信号失败: {e}")
            return []

    def _parse_reflection_insights(self, reflection_text: str) -> Dict[str, Any]:
        """解析反思洞察"""
        try:
            import json

            # 查找JSON部分
            json_match = re.search(r'\{.*\}', reflection_text, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                insights = json.loads(json_str)
                return insights

            # 默认返回
            return {
                'performance_rating': 5,
                'strengths': [],
                'weaknesses': [],
                'adjustments': [],
                'next_round_strategy': '',
                'reflection': reflection_text
            }

        except Exception as e:
            print(f"❌ 解析反思洞察失败: {e}")
            return {
                'performance_rating': 5,
                'strengths': [],
                'weaknesses': [],
                'adjustments': [],
                'next_round_strategy': '',
                'reflection': reflection_text
            }

    def _call_ai_strategy(self, ai_strategy: Dict, prompt: str) -> Dict[str, Any]:
        """调用AI策略（可以是用户训练的策略或系统策略）"""
        try:
            # 如果是用户训练的AI策略，调用策略服务
            if ai_strategy.get('type') == 'user_trained':
                from services.ai_strategy_service import ai_strategy_service
                return ai_strategy_service.execute_strategy(ai_strategy.get('id'), prompt)

            # 否则使用默认的AI模型调用
            return self._call_ai_model(ai_strategy, prompt)

        except Exception as e:
            print(f"❌ 调用AI策略失败: {e}")
            return {'success': False, 'error': str(e)}

    def _calculate_round_stats(self, user_id: int, round_start_time: datetime) -> Dict[str, Any]:
        """计算本轮交易统计"""
        try:
            from services.mt5_service import mt5_service

            # 获取本轮交易历史
            trades = mt5_service.get_trading_history(
                start_time=round_start_time,
                end_time=datetime.now()
            )

            if not trades or not trades.get('success'):
                return {'profit': 0, 'win_rate': 0, 'total_trades': 0}

            trade_list = trades.get('trades', [])
            total_profit = sum(trade.get('profit', 0) for trade in trade_list)
            winning_trades = sum(1 for trade in trade_list if trade.get('profit', 0) > 0)
            total_trades = len(trade_list)
            win_rate = winning_trades / total_trades if total_trades > 0 else 0

            return {
                'profit': total_profit,
                'win_rate': win_rate,
                'total_trades': total_trades,
                'winning_trades': winning_trades
            }

        except Exception as e:
            print(f"❌ 计算轮次统计失败: {e}")
            return {'profit': 0, 'win_rate': 0, 'total_trades': 0}

    def _send_risk_alert(self, user_id: int, risk_reason: str):
        """发送风险告警"""
        try:
            print(f"🚨 风险告警 - 用户{user_id}: {risk_reason}")

            # 这里可以添加更多告警方式：
            # - 发送邮件
            # - 推送通知
            # - 记录到数据库
            # - 发送到监控系统

            # 暂时只记录日志
            alert_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            print(f"📝 风险告警记录: [{alert_time}] 用户{user_id} - {risk_reason}")

        except Exception as e:
            print(f"❌ 发送风险告警失败: {e}")

    def _save_reflection_record(self, user_id: int, round_number: int, reflection_data: Dict):
        """保存反思记录"""
        try:
            # 这里可以保存到数据库或文件
            print(f"💾 保存反思记录 - 用户{user_id}, 第{round_number}轮")
            print(f"📊 反思数据: {reflection_data}")

            # 暂时只记录日志，后续可以扩展到数据库

        except Exception as e:
            print(f"❌ 保存反思记录失败: {e}")

    def _get_strategy_details(self, strategy_ids: List[str]) -> List[Dict]:
        """获取AI策略的详细信息"""
        try:
            import requests

            strategies = []
            for strategy_id in strategy_ids:
                try:
                    # 这里应该调用策略服务获取详情
                    # 暂时使用模拟数据
                    strategy_detail = {
                        'id': strategy_id,
                        'name': f'策略_{strategy_id}',
                        'training_details': {
                            'analysis_dimensions': ['技术指标分析', '价格趋势分析', '成交量分析'],
                            'time_frame': 'M15',
                            'data_period': '30天',
                            'strategy_type': '趋势跟踪',
                            'risk_level': '中等',
                            'market_conditions': ['趋势市场', '震荡市场'],
                            'performance_summary': {
                                'win_rate': 0.65,
                                'avg_profit': 15.5,
                                'max_drawdown': 8.2,
                                'sharpe_ratio': 1.2
                            }
                        }
                    }
                    strategies.append(strategy_detail)
                except Exception as e:
                    print(f"❌ 获取策略{strategy_id}详情失败: {e}")

            return strategies

        except Exception as e:
            print(f"❌ 获取策略详情失败: {e}")
            return []

    def _format_strategies_for_prompt(self, strategies: List[Dict]) -> str:
        """格式化策略信息用于提示"""
        if not strategies:
            return "无可用策略"

        formatted = []
        for strategy in strategies:
            details = strategy.get('training_details', {})
            performance = details.get('performance_summary', {})

            strategy_text = f"""
策略ID: {strategy['id']}
策略名称: {strategy['name']}
分析维度: {', '.join(details.get('analysis_dimensions', []))}
时间框架: {details.get('time_frame', '未知')}
数据周期: {details.get('data_period', '未知')}
策略类型: {details.get('strategy_type', '未知')}
风险等级: {details.get('risk_level', '未知')}
适用市场: {', '.join(details.get('market_conditions', []))}
历史表现:
  - 胜率: {performance.get('win_rate', 0):.1%}
  - 平均盈利: {performance.get('avg_profit', 0):.1f}%
  - 最大回撤: {performance.get('max_drawdown', 0):.1f}%
  - 夏普比率: {performance.get('sharpe_ratio', 0):.2f}
"""
            formatted.append(strategy_text)

        return '\n'.join(formatted)

    def _get_strategy_by_id(self, strategy_id: str) -> Dict:
        """根据ID获取策略配置"""
        try:
            # 这里应该从数据库或策略服务获取策略详情
            # 暂时返回模拟数据
            return {
                'id': strategy_id,
                'name': f'策略_{strategy_id}',
                'type': 'user_trained',
                'config': {
                    'analysis_type': 'technical',
                    'risk_level': 'medium',
                    'time_frame': 'M15'
                }
            }
        except Exception as e:
            print(f"❌ 获取策略{strategy_id}失败: {e}")
            return {}

    def _is_trading_time_allowed(self, config: Dict) -> bool:
        """检查当前时间是否在允许的交易时段内"""
        try:
            trading_time_slot = config.get('trading_time_slot', 'asia')

            # 如果是全时段，直接返回True
            if trading_time_slot == 'full':
                return True

            # 获取当前北京时间
            from datetime import datetime, timezone, timedelta
            beijing_tz = timezone(timedelta(hours=8))
            now = datetime.now(beijing_tz)
            current_hour = now.hour
            current_weekday = now.weekday()  # 0=周一, 6=周日

            # 检查是否是工作日（周一到周五）
            weekday_names = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
            current_weekday_name = weekday_names[current_weekday]

            print(f"🕐 时间检查 - 当前时间: {now.strftime('%Y-%m-%d %H:%M:%S')} ({current_weekday_name})")
            print(f"🕐 配置的交易时段: {trading_time_slot}")

            if current_weekday >= 5:  # 周六、周日
                print(f"🛑 当前是{current_weekday_name}，周末不进行交易")
                return False

            # 定义各时段的交易时间（北京时间）
            time_slots = {
                'asia': (6, 16),      # 06:00-16:00
                'europe': (14, 24),   # 14:00-24:00 (跨天处理)
                'america': (20, 28)   # 20:00-04:00+1 (跨天处理，用28表示次日04:00)
            }

            if trading_time_slot not in time_slots:
                print(f"⚠️ 未知的交易时段: {trading_time_slot}，默认允许交易")
                return True

            start_hour, end_hour = time_slots[trading_time_slot]

            # 处理跨天的情况
            if end_hour > 24:
                # 跨天时段（如美洲时段 20:00-04:00）
                if current_hour >= start_hour or current_hour < (end_hour - 24):
                    print(f"✅ 当前时间 {current_hour:02d}:00 在交易时段内 ({trading_time_slot})")
                    return True
                else:
                    print(f"⏰ 当前时间 {current_hour:02d}:00 不在交易时段内 ({trading_time_slot})")
                    return False
            else:
                # 同一天的时段
                if start_hour <= current_hour < end_hour:
                    print(f"✅ 当前时间 {current_hour:02d}:00 在交易时段内 ({trading_time_slot})")
                    return True
                else:
                    print(f"⏰ 当前时间 {current_hour:02d}:00 不在交易时段内 ({trading_time_slot})")
                    return False

        except Exception as e:
            print(f"❌ 检查交易时间段失败: {e}")
            print(f"🛑 为安全起见，禁止交易")
            return False  # 出错时默认禁止交易，确保安全

    def _calculate_sleep_duration(self, config: Dict) -> int:
        """计算智能体应该休眠多长时间（秒）"""
        try:
            trading_time_slot = config.get('trading_time_slot', 'asia')

            # 获取当前北京时间
            from datetime import datetime, timezone, timedelta
            beijing_tz = timezone(timedelta(hours=8))
            now = datetime.now(beijing_tz)
            current_hour = now.hour
            current_weekday = now.weekday()  # 0=周一, 6=周日

            # 如果是周末，计算到下周一的时间
            if current_weekday >= 5:  # 周六、周日
                if current_weekday == 5:  # 周六
                    days_to_monday = 2
                else:  # 周日
                    days_to_monday = 1

                # 计算到下周一早上6点的时间
                next_monday_6am = now.replace(hour=6, minute=0, second=0, microsecond=0) + timedelta(days=days_to_monday)
                sleep_seconds = int((next_monday_6am - now).total_seconds())
                print(f"📅 周末休眠：将在 {next_monday_6am.strftime('%Y-%m-%d %H:%M')} 恢复")
                return max(sleep_seconds, 300)  # 最少休眠5分钟

            # 工作日但不在交易时段
            time_slots = {
                'asia': (6, 16),      # 06:00-16:00
                'europe': (14, 24),   # 14:00-24:00
                'america': (20, 28)   # 20:00-04:00+1
            }

            if trading_time_slot not in time_slots:
                return 3600  # 未知时段，休眠1小时

            start_hour, end_hour = time_slots[trading_time_slot]

            # 计算到下一个交易时段开始的时间
            if end_hour > 24:  # 跨天时段
                if current_hour < (end_hour - 24):  # 还在当天的后半段
                    # 等待到今天的开始时间
                    next_start = now.replace(hour=start_hour, minute=0, second=0, microsecond=0)
                    if next_start <= now:
                        next_start += timedelta(days=1)
                else:  # 在前半段，等待到今天的开始时间
                    next_start = now.replace(hour=start_hour, minute=0, second=0, microsecond=0)
            else:  # 同一天时段
                if current_hour < start_hour:  # 还没到开始时间
                    next_start = now.replace(hour=start_hour, minute=0, second=0, microsecond=0)
                else:  # 已经过了结束时间，等待明天
                    next_start = now.replace(hour=start_hour, minute=0, second=0, microsecond=0) + timedelta(days=1)

            sleep_seconds = int((next_start - now).total_seconds())
            print(f"⏰ 工作日休眠：将在 {next_start.strftime('%Y-%m-%d %H:%M')} 恢复")
            return max(sleep_seconds, 300)  # 最少休眠5分钟

        except Exception as e:
            print(f"❌ 计算休眠时间失败: {e}")
            return 3600  # 出错时休眠1小时

    def _get_trading_time_info(self, config: Dict) -> str:
        """获取交易时间段信息用于智能体分析"""
        try:
            trading_time_slot = config.get('trading_time_slot', 'asia')

            # 获取当前北京时间
            from datetime import datetime, timezone, timedelta
            beijing_tz = timezone(timedelta(hours=8))
            now = datetime.now(beijing_tz)
            current_hour = now.hour
            current_weekday = now.weekday()  # 0=周一, 6=周日

            # 时间段定义和特点
            time_slot_info = {
                'asia': {
                    'name': '亚洲时段',
                    'time_range': '06:00-16:00 (北京时间)',
                    'characteristics': '波动相对平稳，适合稳健交易策略',
                    'major_markets': '东京、香港、新加坡',
                    'currency_pairs': 'USDJPY、AUDUSD等亚洲货币对较活跃',
                    'volatility': '低到中等',
                    'liquidity': '中等',
                    'trading_style': '适合趋势跟踪和区间交易'
                },
                'europe': {
                    'name': '欧洲时段',
                    'time_range': '14:00-24:00 (北京时间)',
                    'characteristics': '市场活跃度提升，波动增加',
                    'major_markets': '伦敦、法兰克福、苏黎世',
                    'currency_pairs': 'EURUSD、GBPUSD等欧洲货币对最活跃',
                    'volatility': '中等到高',
                    'liquidity': '高',
                    'trading_style': '适合突破交易和趋势跟踪'
                },
                'america': {
                    'name': '美洲时段',
                    'time_range': '20:00-04:00+1 (北京时间)',
                    'characteristics': '波动最剧烈，风险和机会并存',
                    'major_markets': '纽约、芝加哥',
                    'currency_pairs': '所有主要货币对都非常活跃',
                    'volatility': '高到极高',
                    'liquidity': '最高',
                    'trading_style': '适合短线交易和高频策略'
                },
                'full': {
                    'name': '全时段',
                    'time_range': '24小时 (周一至周五)',
                    'characteristics': '覆盖所有主要交易时段',
                    'major_markets': '全球所有主要市场',
                    'currency_pairs': '所有货币对',
                    'volatility': '根据时段变化',
                    'liquidity': '根据时段变化',
                    'trading_style': '需要根据不同时段调整策略'
                }
            }

            current_slot_info = time_slot_info.get(trading_time_slot, time_slot_info['asia'])

            # 判断当前是否在交易时段内
            is_in_trading_time = self._is_trading_time_allowed(config)

            # 获取当前时段状态
            current_time_status = "交易时段内" if is_in_trading_time else "非交易时段"

            # 工作日检查
            weekday_status = "工作日" if current_weekday < 5 else "周末"

            time_info = f"""
配置的交易时段：{current_slot_info['name']} ({current_slot_info['time_range']})
时段特点：{current_slot_info['characteristics']}
主要市场：{current_slot_info['major_markets']}
活跃货币对：{current_slot_info['currency_pairs']}
波动性：{current_slot_info['volatility']}
流动性：{current_slot_info['liquidity']}
适合策略：{current_slot_info['trading_style']}

当前时间状态：
- 北京时间：{now.strftime('%Y-%m-%d %H:%M:%S')} ({weekday_status})
- 当前小时：{current_hour:02d}:00
- 交易状态：{current_time_status}

时段交易建议：
根据当前时段特点，建议选择适合{current_slot_info['volatility']}波动性和{current_slot_info['liquidity']}流动性的交易策略。
{current_slot_info['trading_style']}在此时段通常表现较好。
"""

            return time_info.strip()

        except Exception as e:
            print(f"❌ 获取交易时间段信息失败: {e}")
            return "交易时间段信息获取失败，使用默认设置"

    def _handle_auto_stop(self, user_id: int, completed_rounds: int):
        """处理自动停止"""
        try:
            print(f"🔄 智能体自动停止处理 - 用户{user_id}, 已完成{completed_rounds}轮")

            # 发送自动停止通知
            self._send_auto_stop_notification(user_id, completed_rounds)

            # 开始监控所有订单平仓
            self._start_position_monitoring(user_id)

        except Exception as e:
            print(f"❌ 自动停止处理失败: {e}")

    def _send_auto_stop_notification(self, user_id: int, completed_rounds: int):
        """发送自动停止通知"""
        try:
            print(f"📢 自动停止通知 - 用户{user_id}: 智能体已完成{completed_rounds}轮交易，停止新增订单")
            print(f"🔍 等待AI策略平仓所有订单后完全停止...")

            # 这里可以添加更多通知方式：
            # - 发送邮件
            # - 推送通知
            # - 记录到数据库

        except Exception as e:
            print(f"❌ 发送自动停止通知失败: {e}")

    def _start_position_monitoring(self, user_id: int):
        """开始监控持仓，等待AI策略平仓"""
        try:
            print(f"👁️ 开始监控持仓 - 用户{user_id}")

            # 这里可以启动一个监控线程
            # 定期检查是否还有持仓
            # 如果没有持仓了，就完全停止智能体

            # 暂时只记录日志

        except Exception as e:
            print(f"❌ 启动持仓监控失败: {e}")

    def _call_ai_model(self, model_config: Dict, prompt: str) -> Dict[str, Any]:
        """调用外部AI模型"""
        try:
            from services.ai_service import AIService
            ai_service = AIService()

            # 获取模型名称
            model_name = model_config.get('model_name', 'Deepseek-V3')

            print(f"🤖 智能体调用AI模型: {model_name}")

            # 调用AI服务（已包含重试机制）
            response = ai_service.call_ai_model(model_name, prompt)

            # 检查响应是否为错误信息
            if response and (response.startswith("AI分析失败:") or
                           response.startswith("AI分析超时:") or
                           response.startswith("AI分析连接失败:")):
                return {
                    'success': False,
                    'error': response
                }

            return {
                'success': True,
                'content': response
            }

        except Exception as e:
            print(f"❌ 调用AI模型失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

# 创建全局实例
agent_trading_real_service = AgentTradingRealService()
