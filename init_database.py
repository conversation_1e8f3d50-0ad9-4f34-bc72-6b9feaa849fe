#!/usr/bin/env python3
"""
数据库初始化脚本
创建所有必要的表和初始数据
"""

import sqlite3
import os
from datetime import datetime
import secrets
from werkzeug.security import generate_password_hash

def create_database():
    """创建数据库和所有表"""
    conn = sqlite3.connect('trading_system.db')
    cursor = conn.cursor()
    
    try:
        # 1. 创建User表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS user (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username VARCHAR(80) UNIQUE NOT NULL,
                email VARCHAR(120) UNIQUE NOT NULL,
                phone VARCHAR(20),
                real_name VARCHAR(100),
                password_hash VARCHAR(120) NOT NULL,
                password_salt VARCHAR(32) NOT NULL,
                user_type VARCHAR(20) DEFAULT 'normal',
                is_approved BOOLEAN DEFAULT 0,
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                approved_at DATETIME,
                approved_by INTEGER,
                FOREI<PERSON><PERSON> KEY (approved_by) REFERENCES user (id)
            )
        """)
        print("✅ User表创建完成")
        
        # 2. 创建AIModelConfig表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS ai_model_config (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(100) NOT NULL,
                api_key VARCHAR(255) NOT NULL,
                api_url VARCHAR(255) NOT NULL,
                model_type VARCHAR(50) NOT NULL,
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        print("✅ AIModelConfig表创建完成")
        
        # 3. 创建Strategy表（包含权限管理字段）
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS strategy (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                name VARCHAR(100) NOT NULL,
                description TEXT,
                strategy_type VARCHAR(50) NOT NULL,
                parameters TEXT,
                is_active BOOLEAN DEFAULT 0,
                is_shared BOOLEAN DEFAULT 0,
                shared_by INTEGER,
                shared_at DATETIME,
                status VARCHAR(20) DEFAULT 'training',
                training_results TEXT,
                performance_metrics TEXT,
                training_data TEXT,
                ai_model VARCHAR(100),
                timeframe VARCHAR(10),
                symbols TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES user (id),
                FOREIGN KEY (shared_by) REFERENCES user (id)
            )
        """)
        print("✅ Strategy表创建完成")
        
        # 4. 创建MarketData表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS market_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol VARCHAR(20) NOT NULL,
                timestamp DATETIME NOT NULL,
                timeframe VARCHAR(10) NOT NULL,
                open_price DECIMAL(10, 5) NOT NULL,
                high_price DECIMAL(10, 5) NOT NULL,
                low_price DECIMAL(10, 5) NOT NULL,
                close_price DECIMAL(10, 5) NOT NULL,
                volume DECIMAL(15, 2),
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(symbol, timestamp, timeframe)
            )
        """)
        print("✅ MarketData表创建完成")
        
        # 5. 创建SystemSettings表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS system_settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                setting_key VARCHAR(100) UNIQUE NOT NULL,
                setting_value TEXT,
                setting_type VARCHAR(20) DEFAULT 'string',
                description VARCHAR(255),
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        print("✅ SystemSettings表创建完成")
        
        conn.commit()
        
    except Exception as e:
        print(f"❌ 创建表时出错: {e}")
        conn.rollback()
        raise
    finally:
        conn.close()

def create_default_admin():
    """创建默认管理员账户"""
    conn = sqlite3.connect('trading_system.db')
    cursor = conn.cursor()
    
    try:
        # 检查是否已有管理员
        cursor.execute("SELECT id FROM user WHERE user_type = 'admin'")
        if cursor.fetchone():
            print("管理员账户已存在")
            return
        
        # 创建默认管理员
        salt = secrets.token_hex(16)
        password = "admin123"  # 默认密码
        password_hash = generate_password_hash(password + salt)
        
        cursor.execute("""
            INSERT INTO user (
                username, email, password_hash, password_salt, 
                user_type, is_approved, is_active, real_name
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            'admin',
            '<EMAIL>',
            password_hash,
            salt,
            'admin',
            1,  # 已审核
            1,  # 已激活
            '系统管理员'
        ))
        
        print("✅ 默认管理员账户创建完成")
        print("   用户名: admin")
        print("   密码: admin123")
        print("   邮箱: <EMAIL>")

        # 创建示例VIP用户
        vip_salt = secrets.token_hex(16)
        vip_password = "vip123"
        vip_password_hash = generate_password_hash(vip_password + vip_salt)

        cursor.execute("""
            INSERT INTO user (
                username, email, password_hash, password_salt,
                user_type, is_approved, is_active, real_name
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            'vip_user',
            '<EMAIL>',
            vip_password_hash,
            vip_salt,
            'vip',
            1,  # 已审核
            1,  # 已激活
            'VIP用户示例'
        ))

        print("✅ 示例VIP用户创建完成")
        print("   用户名: vip_user")
        print("   密码: vip123")
        print("   邮箱: <EMAIL>")

        # 创建示例普通用户
        normal_salt = secrets.token_hex(16)
        normal_password = "user123"
        normal_password_hash = generate_password_hash(normal_password + normal_salt)

        cursor.execute("""
            INSERT INTO user (
                username, email, password_hash, password_salt,
                user_type, is_approved, is_active, real_name
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            'normal_user',
            '<EMAIL>',
            normal_password_hash,
            normal_salt,
            'normal',
            1,  # 已审核
            1,  # 已激活
            '普通用户示例'
        ))

        print("✅ 示例普通用户创建完成")
        print("   用户名: normal_user")
        print("   密码: user123")
        print("   邮箱: <EMAIL>")

        conn.commit()
        
    except Exception as e:
        print(f"❌ 创建管理员账户时出错: {e}")
        conn.rollback()
        raise
    finally:
        conn.close()

def create_default_settings():
    """创建默认系统设置"""
    conn = sqlite3.connect('trading_system.db')
    cursor = conn.cursor()
    
    try:
        # 默认设置
        default_settings = [
            ('enable_user_registration', 'true', 'boolean', '是否开启用户注册功能'),
            ('require_user_approval', 'true', 'boolean', '新用户是否需要管理员审核'),
            ('system_name', 'MateTrade4', 'string', '系统名称'),
            ('max_users', '1000', 'integer', '最大用户数量'),
            ('default_user_type', 'normal', 'string', '默认用户类型'),
            ('session_timeout', '3600', 'integer', '会话超时时间（秒）'),
            ('enable_demo_trading', 'true', 'boolean', '是否开启模拟交易'),
            ('enable_real_trading', 'true', 'boolean', '是否开启真实交易'),
            ('max_ai_strategies_per_user', '10', 'integer', '每个用户最大AI策略数量')
        ]
        
        for setting_key, setting_value, setting_type, description in default_settings:
            cursor.execute("""
                INSERT OR IGNORE INTO system_settings 
                (setting_key, setting_value, setting_type, description)
                VALUES (?, ?, ?, ?)
            """, (setting_key, setting_value, setting_type, description))
        
        print("✅ 默认系统设置创建完成")
        conn.commit()
        
    except Exception as e:
        print(f"❌ 创建默认设置时出错: {e}")
        conn.rollback()
        raise
    finally:
        conn.close()

def create_sample_ai_configs():
    """创建示例AI模型配置"""
    conn = sqlite3.connect('trading_system.db')
    cursor = conn.cursor()
    
    try:
        # 示例AI模型配置
        sample_configs = [
            ('DeepSeek-V3', 'your-deepseek-api-key', 'https://api.deepseek.com/v1/chat/completions', 'deepseek'),
            ('GPT-4', 'your-openai-api-key', 'https://api.openai.com/v1/chat/completions', 'openai'),
            ('Claude-3', 'your-anthropic-api-key', 'https://api.anthropic.com/v1/messages', 'anthropic'),
            ('Gemini-Pro', 'your-google-api-key', 'https://generativelanguage.googleapis.com/v1/models', 'google'),
            ('Qwen-Max', 'your-qwen-api-key', 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation', 'qwen')
        ]
        
        for name, api_key, api_url, model_type in sample_configs:
            cursor.execute("""
                INSERT OR IGNORE INTO ai_model_config 
                (name, api_key, api_url, model_type, is_active)
                VALUES (?, ?, ?, ?, ?)
            """, (name, api_key, api_url, model_type, 0))  # 默认不激活，需要用户配置真实API密钥
        
        print("✅ 示例AI模型配置创建完成")
        print("   注意: 请在系统设置中配置真实的API密钥")
        conn.commit()
        
    except Exception as e:
        print(f"❌ 创建AI模型配置时出错: {e}")
        conn.rollback()
        raise
    finally:
        conn.close()

def verify_database():
    """验证数据库创建结果"""
    conn = sqlite3.connect('trading_system.db')
    cursor = conn.cursor()
    
    try:
        # 检查所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [table[0] for table in cursor.fetchall()]
        print(f"数据库表: {tables}")
        
        # 检查用户数据
        cursor.execute("SELECT id, username, user_type, is_approved FROM user")
        users = cursor.fetchall()
        print(f"用户数据:")
        for user in users:
            print(f"  ID: {user[0]}, 用户名: {user[1]}, 类型: {user[2]}, 已审核: {user[3]}")
        
        # 检查系统设置
        cursor.execute("SELECT COUNT(*) FROM system_settings")
        settings_count = cursor.fetchone()[0]
        print(f"系统设置数量: {settings_count}")
        
        # 检查AI模型配置
        cursor.execute("SELECT COUNT(*) FROM ai_model_config")
        ai_configs_count = cursor.fetchone()[0]
        print(f"AI模型配置数量: {ai_configs_count}")
        
        print("✅ 数据库验证完成")
        
    except Exception as e:
        print(f"❌ 验证过程中出错: {e}")
    finally:
        conn.close()

def main():
    """主函数"""
    print("🚀 开始数据库初始化...")
    print("=" * 50)
    
    # 备份现有数据库（如果存在）
    if os.path.exists('trading_system.db'):
        backup_name = f'trading_system_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.db'
        try:
            import shutil
            shutil.copy2('trading_system.db', backup_name)
            print(f"现有数据库已备份为: {backup_name}")
        except Exception as e:
            print(f"备份失败: {e}")
    
    # 1. 创建数据库表
    print("\n1. 创建数据库表...")
    create_database()
    
    # 2. 创建默认管理员
    print("\n2. 创建默认管理员账户...")
    create_default_admin()
    
    # 3. 创建默认设置
    print("\n3. 创建默认系统设置...")
    create_default_settings()
    
    # 4. 创建示例AI配置
    print("\n4. 创建示例AI模型配置...")
    create_sample_ai_configs()
    
    # 5. 验证结果
    print("\n5. 验证数据库...")
    verify_database()
    
    print("\n" + "=" * 50)
    print("🎉 数据库初始化完成！")
    print("\n📋 重要信息:")
    print("   • 默认管理员账户: admin / admin123")
    print("   • 请在首次登录后修改管理员密码")
    print("   • 请在系统设置中配置真实的AI模型API密钥")
    print("   • 现在可以启动应用程序了")

if __name__ == "__main__":
    main()
