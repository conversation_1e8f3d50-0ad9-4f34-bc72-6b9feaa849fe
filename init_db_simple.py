#!/usr/bin/env python3
"""
简化版数据库初始化脚本
"""

import sqlite3
import os
from datetime import datetime

def init_database():
    """初始化数据库"""
    db_path = 'trading_system.db'
    
    try:
        print("🔄 开始初始化数据库...")
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 创建low_risk_trades表
        print("📊 创建low_risk_trades表...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS low_risk_trades (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                symbol VARCHAR(20) NOT NULL,
                action VARCHAR(10) NOT NULL,
                volume FLOAT NOT NULL,
                entry_price FLOAT NOT NULL,
                stop_loss FLOAT,
                take_profit FLOAT,
                mt5_order_id BIGINT,
                comment VARCHAR(200),
                status VARCHAR(20) DEFAULT 'open',
                close_price FLOAT,
                close_time DATETIME,
                pnl FLOAT DEFAULT 0.0,
                signal_type VARCHAR(50),
                confidence FLOAT,
                market_analysis TEXT,
                operation_type VARCHAR(20) DEFAULT 'manual',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 创建索引
        print("🚀 创建索引...")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_low_risk_trades_user_id ON low_risk_trades (user_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_low_risk_trades_created_at ON low_risk_trades (created_at)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_low_risk_trades_status ON low_risk_trades (status)")
        
        # 创建配置表
        print("⚙️ 创建配置表...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS low_risk_trading_configs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                daily_limit INTEGER DEFAULT 2,
                lot_size FLOAT DEFAULT 0.01,
                stop_loss_percent FLOAT DEFAULT 0.5,
                take_profit_percent FLOAT DEFAULT 1.5,
                auto_trade BOOLEAN DEFAULT 1,
                trend_detection BOOLEAN DEFAULT 1,
                min_signals INTEGER DEFAULT 2,
                trading_start_time VARCHAR(10) DEFAULT '08:00',
                trading_end_time VARCHAR(10) DEFAULT '22:00',
                yearly_period INTEGER DEFAULT 365,
                weekly_days INTEGER DEFAULT 7,
                overnight_start VARCHAR(10) DEFAULT '21:00',
                enabled BOOLEAN DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 检查是否已有数据
        cursor.execute("SELECT COUNT(*) FROM low_risk_trades")
        count = cursor.fetchone()[0]
        
        if count == 0:
            print("📝 插入示例数据...")
            # 插入示例数据
            sample_data = [
                (1, 'XAUUSD', 'buy', 0.01, 2650.45, 2640.00, 2670.00, 12345, 
                 'Manual_BUY_Test', 'closed', 2665.20, '2024-01-15 10:30:00', 14.75, 
                 'TREND_BUY', 0.75, '{"signal_reason": "TREND_BUY"}', 'manual', 
                 '2024-01-15 10:30:00', '2024-01-15 14:30:00'),
                
                (1, 'XAUUSD', 'sell', 0.01, 2645.80, 2655.00, 2630.00, 12346, 
                 'Auto_SELL_850', 'closed', 2632.15, '2024-01-14 15:45:00', 13.65, 
                 'MEAN_REVERSION_SELL', 0.85, '{"signal_reason": "MEAN_REVERSION_SELL"}', 'auto',
                 '2024-01-14 15:45:00', '2024-01-14 16:45:00'),
                
                (1, 'XAUUSD', 'buy', 0.01, 2638.90, 2628.00, 2655.00, 12347, 
                 'Manual_BUY_Reversal', 'closed', 2625.45, '2024-01-13 09:15:00', -13.45, 
                 'FORCE_RSI_OVERSOLD', 0.65, '{"signal_reason": "FORCE_RSI_OVERSOLD"}', 'manual',
                 '2024-01-13 09:15:00', '2024-01-13 11:15:00')
            ]
            
            cursor.executemany("""
                INSERT INTO low_risk_trades (
                    user_id, symbol, action, volume, entry_price, stop_loss, take_profit,
                    mt5_order_id, comment, status, close_price, close_time, pnl,
                    signal_type, confidence, market_analysis, operation_type,
                    created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, sample_data)
            
            print(f"✅ 插入了 {len(sample_data)} 条示例记录")
        else:
            print(f"📊 表中已有 {count} 条记录，跳过示例数据插入")
        
        # 提交更改
        conn.commit()
        
        # 验证表结构
        print("🔍 验证表结构...")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='low_risk_trades'")
        if cursor.fetchone():
            print("✅ low_risk_trades表创建成功")
            
            cursor.execute("PRAGMA table_info(low_risk_trades)")
            columns = cursor.fetchall()
            print(f"   表字段数: {len(columns)}")
            
            # 检查关键字段
            column_names = [col[1] for col in columns]
            required_fields = ['id', 'user_id', 'symbol', 'action', 'volume', 'entry_price', 'operation_type']
            
            for field in required_fields:
                if field in column_names:
                    print(f"   ✅ {field} 字段存在")
                else:
                    print(f"   ❌ {field} 字段缺失")
        
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='low_risk_trading_configs'")
        if cursor.fetchone():
            print("✅ low_risk_trading_configs表创建成功")
        
        # 统计数据
        cursor.execute("SELECT COUNT(*) FROM low_risk_trades")
        total_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM low_risk_trades WHERE operation_type = 'auto'")
        auto_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM low_risk_trades WHERE operation_type = 'manual'")
        manual_count = cursor.fetchone()[0]
        
        print(f"\n📊 数据统计:")
        print(f"   总记录数: {total_count}")
        print(f"   自动交易: {auto_count}")
        print(f"   手动交易: {manual_count}")
        
        conn.close()
        
        print("\n🎉 数据库初始化完成！")
        print("现在可以正常使用低风险交易历史记录查询功能了。")
        return True
        
    except Exception as e:
        print(f"❌ 数据库初始化失败: {str(e)}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

if __name__ == '__main__':
    print("=" * 60)
    print("低风险交易系统 - 数据库初始化")
    print("=" * 60)
    init_database()
