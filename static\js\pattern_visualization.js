/**
 * K线形态可视化组件
 * 用于在K线图上绘制识别到的形态
 */

class PatternVisualization {
    constructor(chartContainer) {
        this.chartContainer = chartContainer;
        this.patterns = [];
        this.chartData = null;
        this.layout = null;
    }

    /**
     * 将UTC时间转换为本地时间
     */
    convertToLocalTime(timestamp) {
        // 如果是ISO字符串，转换为Date对象
        if (typeof timestamp === 'string') {
            const date = new Date(timestamp);
            // 检查是否为有效日期
            if (isNaN(date.getTime())) {
                console.warn('无效的时间戳:', timestamp);
                return new Date();
            }
            return date;
        }
        // 如果已经是Date对象，直接返回
        if (timestamp instanceof Date) {
            return timestamp;
        }
        // 如果是数字时间戳，转换为Date对象
        if (typeof timestamp === 'number') {
            return new Date(timestamp * 1000); // 假设是秒级时间戳
        }

        console.warn('未知的时间戳格式:', timestamp);
        return new Date();
    }

    /**
     * 初始化图表
     */
    initChart(symbol, timeframe) {
        this.layout = {
            title: `${symbol} - ${timeframe} 形态识别`,
            xaxis: {
                title: '时间',
                type: 'date',
                rangeslider: { visible: false },
                tickangle: -45,  // 倾斜显示时间标签
                tickfont: { size: 10 }
            },
            yaxis: {
                title: '价格',
                side: 'right'
            },
            showlegend: true,
            height: 550,  // 增加高度
            margin: { t: 60, b: 80, l: 60, r: 100 },  // 增加底部和右侧边距
            plot_bgcolor: '#f8f9fa',
            paper_bgcolor: 'white'
        };

        Plotly.newPlot(this.chartContainer, [], this.layout);
    }

    /**
     * 更新K线数据
     */
    updateCandlestickData(data) {
        this.chartData = data;
        this.renderChart();
    }

    /**
     * 添加形态标记
     */
    addPatternMarkers(patterns) {
        this.patterns = patterns;
        this.renderChart();
    }

    /**
     * 渲染完整图表
     */
    renderChart() {
        if (!this.chartData || this.chartData.length === 0) {
            return;
        }

        const traces = [];

        // K线图 - 转换时间戳为本地时间
        const candlestickTrace = {
            x: this.chartData.map(d => this.convertToLocalTime(d.timestamp)),
            open: this.chartData.map(d => d.open),
            high: this.chartData.map(d => d.high),
            low: this.chartData.map(d => d.low),
            close: this.chartData.map(d => d.close),
            type: 'candlestick',
            name: 'K线',
            increasing: { line: { color: '#26a69a' } },
            decreasing: { line: { color: '#ef5350' } },
            showlegend: false
        };
        traces.push(candlestickTrace);

        // 成交量图（副图）- 转换时间戳为本地时间
        const volumeTrace = {
            x: this.chartData.map(d => this.convertToLocalTime(d.timestamp)),
            y: this.chartData.map(d => d.volume),
            type: 'bar',
            name: '成交量',
            yaxis: 'y2',
            marker: {
                color: this.chartData.map((d, i) => {
                    if (i === 0) return '#26a69a';
                    return d.close >= this.chartData[i-1].close ? '#26a69a' : '#ef5350';
                })
            },
            opacity: 0.6
        };
        traces.push(volumeTrace);

        // 添加形态标记
        this.patterns.forEach((pattern, index) => {
            const patternTrace = this.createPatternTrace(pattern, index);
            if (patternTrace) {
                traces.push(patternTrace);
            }
        });

        // 更新布局以包含成交量副图
        const updatedLayout = {
            ...this.layout,
            yaxis2: {
                title: '成交量',
                overlaying: 'y',
                side: 'left',
                position: 0.05,
                range: [0, Math.max(...this.chartData.map(d => d.volume)) * 4],
                tickfont: { size: 9 }
            },
            // 确保x轴标签完整显示
            xaxis: {
                ...this.layout.xaxis,
                tickmode: 'auto',
                nticks: 10,  // 限制标签数量避免重叠
                tickformat: '%H:%M<br>%m-%d',  // 时间格式：时:分 换行 月-日
                timezone: 'Asia/Shanghai'  // 设置为中国时区
            }
        };

        Plotly.react(this.chartContainer, traces, updatedLayout);
    }

    /**
     * 创建形态标记轨迹
     */
    createPatternTrace(pattern, index) {
        const color = pattern.pattern_type === 'bullish' ? '#28a745' : '#dc3545';
        const symbol = pattern.pattern_type === 'bullish' ? 'triangle-up' : 'triangle-down';

        // 基本标记点
        const markerTrace = {
            x: [pattern.timestamp],
            y: [pattern.entry_point],
            mode: 'markers+text',
            marker: {
                size: 15,
                color: color,
                symbol: symbol,
                line: { width: 2, color: 'white' }
            },
            text: [pattern.pattern_name],
            textposition: 'top center',
            textfont: {
                size: 10,
                color: color,
                family: 'Arial, sans-serif'
            },
            name: `${pattern.pattern_name || 'Unknown'} (${pattern.confidence ? (pattern.confidence * 100).toFixed(1) : 'N/A'}%)`,
            hovertemplate:
                `<b>${pattern.pattern_name || 'Unknown Pattern'}</b><br>` +
                `类型: ${pattern.pattern_type === 'bullish' ? '看涨' : '看跌'}<br>` +
                `置信度: ${pattern.confidence ? (pattern.confidence * 100).toFixed(1) : 'N/A'}%<br>` +
                `入场价: ${pattern.entry_point ? pattern.entry_point.toFixed(4) : 'N/A'}<br>` +
                `止损价: ${pattern.stop_loss ? pattern.stop_loss.toFixed(4) : 'N/A'}<br>` +
                `止盈价: ${pattern.take_profit ? pattern.take_profit.toFixed(4) : 'N/A'}<br>` +
                `<extra></extra>`
        };

        return markerTrace;
    }

    /**
     * 绘制形态轮廓线
     */
    drawPatternOutline(pattern) {
        // 根据不同形态类型绘制特征线条
        switch (pattern.pattern_name) {
            case '双底':
                return this.drawDoubleBottom(pattern);
            case '双顶':
                return this.drawDoubleTop(pattern);
            case '头肩底':
                return this.drawHeadAndShoulders(pattern, 'bottom');
            case '头肩顶':
                return this.drawHeadAndShoulders(pattern, 'top');
            case '上升三角形':
                return this.drawTriangle(pattern, 'ascending');
            case '下降三角形':
                return this.drawTriangle(pattern, 'descending');
            default:
                return null;
        }
    }

    /**
     * 绘制双底形态线条
     */
    drawDoubleBottom(pattern) {
        // 这里需要根据实际的形态数据点来绘制
        // 简化版本，实际应用中需要更复杂的逻辑
        const color = '#28a745';
        return {
            type: 'scatter',
            mode: 'lines',
            line: { color: color, width: 2, dash: 'dash' },
            name: '双底支撑线',
            showlegend: false
        };
    }

    /**
     * 绘制双顶形态线条
     */
    drawDoubleTop(pattern) {
        const color = '#dc3545';
        return {
            type: 'scatter',
            mode: 'lines',
            line: { color: color, width: 2, dash: 'dash' },
            name: '双顶阻力线',
            showlegend: false
        };
    }

    /**
     * 绘制头肩形态线条
     */
    drawHeadAndShoulders(pattern, type) {
        const color = type === 'bottom' ? '#28a745' : '#dc3545';
        return {
            type: 'scatter',
            mode: 'lines',
            line: { color: color, width: 2, dash: 'dot' },
            name: `头肩${type === 'bottom' ? '底' : '顶'}颈线`,
            showlegend: false
        };
    }

    /**
     * 绘制三角形形态线条
     */
    drawTriangle(pattern, direction) {
        const color = direction === 'ascending' ? '#28a745' : '#dc3545';
        return {
            type: 'scatter',
            mode: 'lines',
            line: { color: color, width: 2, dash: 'dashdot' },
            name: `${direction === 'ascending' ? '上升' : '下降'}三角形`,
            showlegend: false
        };
    }

    /**
     * 添加技术指标
     */
    addTechnicalIndicators(indicators) {
        if (!this.chartData) return;

        const traces = [];

        // 移动平均线
        if (indicators.ma5) {
            traces.push({
                x: this.chartData.map(d => d.timestamp),
                y: indicators.ma5,
                type: 'scatter',
                mode: 'lines',
                name: 'MA5',
                line: { color: '#ff9800', width: 1 }
            });
        }

        if (indicators.ma20) {
            traces.push({
                x: this.chartData.map(d => d.timestamp),
                y: indicators.ma20,
                type: 'scatter',
                mode: 'lines',
                name: 'MA20',
                line: { color: '#2196f3', width: 1 }
            });
        }

        // 布林带
        if (indicators.bb_upper && indicators.bb_lower) {
            traces.push({
                x: this.chartData.map(d => d.timestamp),
                y: indicators.bb_upper,
                type: 'scatter',
                mode: 'lines',
                name: '布林上轨',
                line: { color: '#9c27b0', width: 1, dash: 'dot' },
                showlegend: false
            });

            traces.push({
                x: this.chartData.map(d => d.timestamp),
                y: indicators.bb_lower,
                type: 'scatter',
                mode: 'lines',
                name: '布林下轨',
                line: { color: '#9c27b0', width: 1, dash: 'dot' },
                fill: 'tonexty',
                fillcolor: 'rgba(156, 39, 176, 0.1)',
                showlegend: false
            });
        }

        return traces;
    }

    /**
     * 高亮显示特定形态
     */
    highlightPattern(patternName) {
        const pattern = this.patterns.find(p => p.pattern_name === patternName);
        if (!pattern) return;

        // 添加高亮效果
        const highlightTrace = {
            x: [pattern.timestamp],
            y: [pattern.entry_point],
            mode: 'markers',
            marker: {
                size: 25,
                color: 'rgba(255, 255, 0, 0.3)',
                symbol: 'circle'
            },
            name: '高亮',
            showlegend: false
        };

        Plotly.addTraces(this.chartContainer, highlightTrace);

        // 3秒后移除高亮
        setTimeout(() => {
            const data = this.chartContainer.data;
            const traceIndex = data.length - 1;
            Plotly.deleteTraces(this.chartContainer, traceIndex);
        }, 3000);
    }

    /**
     * 导出图表为图片
     */
    exportChart(filename = 'pattern_chart.png') {
        Plotly.toImage(this.chartContainer, {
            format: 'png',
            width: 1200,
            height: 600
        }).then(dataURL => {
            const link = document.createElement('a');
            link.download = filename;
            link.href = dataURL;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        });
    }

    /**
     * 清除所有形态标记
     */
    clearPatterns() {
        this.patterns = [];
        this.renderChart();
    }

    /**
     * 获取图表当前状态
     */
    getChartState() {
        return {
            patterns: this.patterns,
            chartData: this.chartData,
            layout: this.layout
        };
    }

    /**
     * 恢复图表状态
     */
    restoreChartState(state) {
        this.patterns = state.patterns || [];
        this.chartData = state.chartData || null;
        this.layout = state.layout || this.layout;
        this.renderChart();
    }
}

// 导出类供其他模块使用
window.PatternVisualization = PatternVisualization;
