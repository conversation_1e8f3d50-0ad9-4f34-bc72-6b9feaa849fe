#!/usr/bin/env python3
"""
训练监控脚本 - 自动检测和处理卡住的训练
"""

import sqlite3
import time
import psutil
import torch
from datetime import datetime, timedelta

def monitor_training():
    """监控训练状态"""
    while True:
        try:
            # 检查数据库中的训练任务
            conn = sqlite3.connect('trading_system.db')
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT id, updated_at FROM training_tasks
                WHERE status = 'running'
            """)
            
            running_tasks = cursor.fetchall()
            
            for task_id, updated_at in running_tasks:
                if updated_at:
                    try:
                        last_update = datetime.fromisoformat(updated_at.replace('Z', '+00:00'))
                        now = datetime.now()
                        time_since_update = now - last_update
                        
                        # 如果超过15分钟没更新，重置任务
                        if time_since_update > timedelta(minutes=15):
                            print(f"⚠️ 检测到卡住任务: {task_id[:8]}... (已 {time_since_update} 没更新)")
                            
                            cursor.execute("""
                                UPDATE training_tasks
                                SET status = 'failed',
                                    logs = COALESCE(logs, '') || char(10) || '[' || datetime('now') || '] 监控检测到任务卡住，已自动重置',
                                    updated_at = datetime('now')
                                WHERE id = ?
                            """, (task_id,))
                            
                            conn.commit()
                            print(f"✅ 任务 {task_id[:8]}... 已自动重置")
                    except:
                        pass
            
            conn.close()
            
            # 清理GPU内存
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            
            # 等待60秒后再次检查
            time.sleep(60)
            
        except Exception as e:
            print(f"❌ 监控异常: {e}")
            time.sleep(60)

if __name__ == '__main__':
    print("🔍 启动训练监控...")
    monitor_training()
