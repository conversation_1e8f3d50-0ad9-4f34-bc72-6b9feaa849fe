{% extends "base.html" %}

{% block page_title %}AI策略分析过程{% endblock %}

{% block content %}
<div class="row">
    <!-- 分析配置 -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-brain"></i>
                    AI分析配置
                </h5>
            </div>
            <div class="card-body">
                <form id="analysisForm">
                    <div class="mb-3">
                        <label class="form-label">选择货币对</label>
                        <select class="form-select" id="symbolSelect" required>
                            <option value="">选择货币对</option>
                            <option value="EURUSD=X">EUR/USD (欧元/美元)</option>
                            <option value="GBPUSD=X">GBP/USD (英镑/美元)</option>
                            <option value="USDJPY=X">USD/JPY (美元/日元)</option>
                            <option value="AUDUSD=X">AUD/USD (澳元/美元)</option>
                            <option value="USDCAD=X">USD/CAD (美元/加元)</option>
                            <option value="USDCHF=X">USD/CHF (美元/瑞郎)</option>
                            <option value="NZDUSD=X">NZD/USD (纽元/美元)</option>
                            <option value="EURJPY=X">EUR/JPY (欧元/日元)</option>
                            <option value="GBPJPY=X">GBP/JPY (英镑/日元)</option>
                            <option value="XAUUSD=X">XAU/USD (黄金/美元)</option>
                            <option value="XAGUSD=X">XAG/USD (白银/美元)</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">分析时间范围</label>
                        <select class="form-select" id="timeRange">
                            <option value="1mo">1个月</option>
                            <option value="3mo" selected>3个月</option>
                            <option value="6mo">6个月</option>
                            <option value="1y">1年</option>
                            <option value="2y">2年</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">分析深度</label>
                        <select class="form-select" id="analysisDepth">
                            <option value="basic">基础分析</option>
                            <option value="detailed" selected>详细分析</option>
                            <option value="comprehensive">全面分析</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">AI策略模型</label>
                        <select class="form-select" id="aiStrategyModel" required>
                            <option value="">正在加载AI策略...</option>
                        </select>
                        <small class="text-muted">选择已训练完成的AI策略进行分析</small>
                    </div>

                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-play"></i>
                        开始AI分析
                    </button>
                </form>
            </div>
        </div>

        <!-- 分析进度 -->
        <div class="card mt-4" id="progressCard" style="display: none;">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-tasks"></i>
                    分析进度
                </h5>
            </div>
            <div class="card-body">
                <div class="progress mb-3">
                    <div class="progress-bar progress-bar-striped progress-bar-animated"
                         id="progressBar" role="progressbar" style="width: 0%"></div>
                </div>
                <div id="progressSteps">
                    <div class="step" id="step1">
                        <i class="fas fa-download text-muted"></i>
                        <span class="text-muted">获取市场数据</span>
                    </div>
                    <div class="step" id="step2">
                        <i class="fas fa-calculator text-muted"></i>
                        <span class="text-muted">计算技术指标</span>
                    </div>
                    <div class="step" id="step3">
                        <i class="fas fa-brain text-muted"></i>
                        <span class="text-muted">AI策略分析</span>
                    </div>
                    <div class="step" id="step4">
                        <i class="fas fa-chart-line text-muted"></i>
                        <span class="text-muted">生成策略建议</span>
                    </div>
                    <div class="step" id="step5">
                        <i class="fas fa-check text-muted"></i>
                        <span class="text-muted">完成分析</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 分析结果 -->
    <div class="col-lg-8">
        <div class="card" id="resultsCard" style="display: none;">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar"></i>
                    AI分析结果
                </h5>
            </div>
            <div class="card-body">
                <!-- 市场概况 -->
                <div class="mb-4" id="marketOverview">
                    <h6><i class="fas fa-globe"></i> 市场概况</h6>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <h6 class="text-muted">当前价格</h6>
                                <h4 id="currentPrice">-</h4>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h6 class="text-muted">24h变化</h6>
                                <h4 id="priceChange">-</h4>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h6 class="text-muted">成交量</h6>
                                <h5 id="volume">-</h5>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h6 class="text-muted">波动率</h6>
                                <h5 id="volatility">-</h5>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 技术指标分析 -->
                <div class="mb-4" id="technicalAnalysis">
                    <h6><i class="fas fa-chart-line"></i> 技术指标分析</h6>
                    <div class="row" id="indicatorsGrid">
                        <!-- 技术指标将在这里动态生成 -->
                    </div>
                </div>

                <!-- AI分析过程 -->
                <div class="mb-4" id="aiAnalysisProcess">
                    <h6><i class="fas fa-brain"></i> AI分析过程</h6>
                    <div class="card bg-light">
                        <div class="card-body">
                            <div id="analysisSteps">
                                <!-- AI分析步骤将在这里显示 -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 策略建议 -->
                <div class="mb-4" id="strategyRecommendation">
                    <h6><i class="fas fa-lightbulb"></i> 策略建议</h6>
                    <div class="card border-primary">
                        <div class="card-body">
                            <div id="recommendationContent">
                                <!-- 策略建议内容 -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 风险评估 -->
                <div class="mb-4" id="riskAssessment">
                    <h6><i class="fas fa-exclamation-triangle"></i> 风险评估</h6>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="text-center">
                                <h6 class="text-muted">风险等级</h6>
                                <span class="badge bg-warning" id="riskLevel">中等</span>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <h6 class="text-muted">建议仓位</h6>
                                <h5 id="positionSize">-</h5>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <h6 class="text-muted">止损建议</h6>
                                <h5 id="stopLoss">-</h5>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 分析历史 -->
        <div class="card mt-4">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-history"></i>
                        分析历史
                    </h5>
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="testAnalysisDetail()" title="测试分析详情功能">
                        <i class="fas fa-bug"></i>
                        测试详情
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="analysisHistoryTable">
                        <thead>
                            <tr>
                                <th>时间</th>
                                <th>货币对</th>
                                <th>AI策略</th>
                                <th>分析深度</th>
                                <th>建议操作</th>
                                <th>风险等级</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="7" class="text-center text-muted">暂无分析记录</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 分析详情模态框 -->
<div class="modal fade" id="analysisDetailModal" tabindex="-1" aria-labelledby="analysisDetailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="analysisDetailModalLabel">
                    <i class="fas fa-chart-bar"></i>
                    AI策略分析详情
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" onclick="closeAnalysisDetailModal()"></button>
            </div>
            <div class="modal-body">
                <div id="analysisDetailContent">
                    <!-- 分析基本信息 -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">
                                        <i class="fas fa-info-circle"></i>
                                        分析基本信息
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-sm">
                                        <tr><td><strong>分析ID:</strong></td><td id="detailAnalysisId">-</td></tr>
                                        <tr><td><strong>AI策略:</strong></td><td id="detailStrategyName">-</td></tr>
                                        <tr><td><strong>交易品种:</strong></td><td id="detailSymbol">-</td></tr>
                                        <tr><td><strong>分析时间范围:</strong></td><td id="detailTimeRange">-</td></tr>
                                        <tr><td><strong>分析深度:</strong></td><td id="detailAnalysisDepth">-</td></tr>
                                        <tr><td><strong>分析时间:</strong></td><td id="detailAnalysisTime">-</td></tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">
                                        <i class="fas fa-chart-line"></i>
                                        市场数据概览
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-sm">
                                        <tr><td><strong>当前价格:</strong></td><td id="detailCurrentPrice" class="profit-positive">-</td></tr>
                                        <tr><td><strong>价格变化:</strong></td><td id="detailPriceChange">-</td></tr>
                                        <tr><td><strong>成交量:</strong></td><td id="detailVolume">-</td></tr>
                                        <tr><td><strong>波动率:</strong></td><td id="detailVolatility">-</td></tr>
                                        <tr><td><strong>趋势方向:</strong></td><td id="detailTrend">-</td></tr>
                                        <tr><td><strong>市场情绪:</strong></td><td id="detailSentiment">-</td></tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 技术指标分析 -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">
                                        <i class="fas fa-calculator"></i>
                                        技术指标分析
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row" id="detailIndicatorsGrid">
                                        <!-- 技术指标将在这里动态生成 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- AI策略分析过程 -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">
                                        <i class="fas fa-brain"></i>
                                        AI策略分析过程
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div id="detailAnalysisSteps">
                                        <!-- 分析步骤将在这里显示 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 策略建议详情 -->
                    <div class="row mb-4">
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">
                                        <i class="fas fa-lightbulb"></i>
                                        策略建议详情
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div id="detailRecommendationContent">
                                        <!-- 策略建议内容 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">
                                        <i class="fas fa-shield-alt"></i>
                                        风险评估
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="text-center mb-3">
                                        <h6 class="text-muted">风险等级</h6>
                                        <h4 id="detailRiskLevel" class="badge bg-warning">-</h4>
                                    </div>
                                    <div class="text-center mb-3">
                                        <h6 class="text-muted">建议仓位</h6>
                                        <h5 id="detailPositionSize">-</h5>
                                    </div>
                                    <div class="text-center">
                                        <h6 class="text-muted">止损建议</h6>
                                        <h5 id="detailStopLoss">-</h5>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- AI策略性能指标 -->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">
                                        <i class="fas fa-trophy"></i>
                                        AI策略性能指标
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <div class="text-center">
                                                <h6 class="text-muted">策略胜率</h6>
                                                <h4 id="detailStrategyWinRate" class="profit-positive">-</h4>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="text-center">
                                                <h6 class="text-muted">平均收益</h6>
                                                <h4 id="detailAvgReturn">-</h4>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="text-center">
                                                <h6 class="text-muted">最大回撤</h6>
                                                <h4 id="detailMaxDrawdown" class="profit-negative">-</h4>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="text-center">
                                                <h6 class="text-muted">夏普比率</h6>
                                                <h4 id="detailSharpeRatio">-</h4>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" onclick="closeAnalysisDetailModal()">关闭</button>
                <button type="button" class="btn btn-success" onclick="useAnalysisForTrading()">
                    <i class="fas fa-chart-line"></i>
                    应用到交易
                </button>
                <button type="button" class="btn btn-primary" onclick="exportAnalysisReport()">
                    <i class="fas fa-download"></i>
                    导出报告
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
/* 分析详情模态框样式 */
#analysisDetailModal .modal-dialog {
    max-width: 90%;
}

#analysisDetailModal .card {
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

#analysisDetailModal .card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    padding: 0.75rem 1rem;
}

#analysisDetailModal .card-title {
    color: #495057;
    font-size: 0.9rem;
    font-weight: 600;
}

#analysisDetailModal .table td {
    padding: 0.5rem;
    border-top: 1px solid #f1f3f4;
}

#analysisDetailModal .table td:first-child {
    width: 40%;
    color: #6c757d;
    font-weight: 500;
}

/* 盈亏颜色样式 */
.profit-positive {
    color: #198754 !important;
    font-weight: 600;
}

.profit-negative {
    color: #dc3545 !important;
    font-weight: 600;
}

/* 技术指标卡片样式 */
#detailIndicatorsGrid .card {
    transition: transform 0.2s ease;
}

#detailIndicatorsGrid .card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

/* 分析步骤样式 */
#detailAnalysisSteps .badge {
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
}

/* 策略建议样式 */
#detailRecommendationContent h5 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

/* 风险等级徽章样式 */
#detailRiskLevel {
    font-size: 1.1rem;
    padding: 0.5rem 1rem;
}

/* 性能指标样式 */
.text-center h4 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}
.step {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    padding: 8px;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.step.active {
    background-color: rgba(102, 126, 234, 0.1);
    color: #667eea;
}

.step.completed {
    background-color: rgba(40, 167, 69, 0.1);
    color: #28a745;
}

.step i {
    margin-right: 10px;
    width: 20px;
}

.step.active i {
    color: #667eea;
}

.step.completed i {
    color: #28a745;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
let analysisInProgress = false;

// 分析详情数据存储
let analysisDetailsStorage = new Map();

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    loadAIStrategies();
});

// 加载AI策略列表
function loadAIStrategies() {
    console.log('开始加载AI策略列表...');

    fetch('/api/ai-strategies/list')
    .then(response => response.json())
    .then(data => {
        console.log('AI策略API响应:', data);

        const select = document.getElementById('aiStrategyModel');
        select.innerHTML = '<option value="">选择AI策略</option>';

        if (data.success && data.strategies.length > 0) {
            let hasCompletedStrategy = false;

            data.strategies.forEach(strategy => {
                if (strategy.status === 'completed') {
                    hasCompletedStrategy = true;

                    const option = document.createElement('option');
                    option.value = strategy.id;

                    // 显示策略名称和状态
                    const statusText = strategy.is_active ? '✅' : '⏸️';
                    let displayText = `${statusText} ${strategy.name}`;

                    // 添加性能指标
                    if (strategy.performance_metrics && strategy.performance_metrics.win_rate) {
                        displayText += ` (胜率${(strategy.performance_metrics.win_rate * 100).toFixed(1)}%)`;
                    }

                    // 添加训练时间范围
                    if (strategy.training_data && strategy.training_data.time_span) {
                        displayText += ` [${strategy.training_data.time_span}]`;
                    }

                    option.textContent = displayText;
                    select.appendChild(option);
                }
            });

            if (!hasCompletedStrategy) {
                select.innerHTML = '<option value="">暂无已完成的AI策略，请先完成策略训练</option>';
            } else {
                // 添加提示信息
                const helpOption = document.createElement('option');
                helpOption.disabled = true;
                helpOption.textContent = '─────────────────────';
                select.appendChild(helpOption);

                const infoOption = document.createElement('option');
                infoOption.disabled = true;
                infoOption.textContent = '💡 如需更多策略，请前往AI策略训练页面';
                select.appendChild(infoOption);
            }
        } else {
            select.innerHTML = '<option value="">暂无可用的AI策略，请先训练AI策略</option>';
        }
    })
    .catch(error => {
        console.error('加载AI策略失败:', error);
        document.getElementById('aiStrategyModel').innerHTML = '<option value="">加载失败，请刷新页面重试</option>';
    });
}

// 提交分析表单
document.getElementById('analysisForm').addEventListener('submit', function(e) {
    e.preventDefault();

    if (analysisInProgress) {
        alert('分析正在进行中，请稍候...');
        return;
    }

    const analysisData = {
        symbol: document.getElementById('symbolSelect').value,
        time_range: document.getElementById('timeRange').value,
        analysis_depth: document.getElementById('analysisDepth').value,
        ai_strategy_id: document.getElementById('aiStrategyModel').value
    };

    // 验证必填字段
    if (!analysisData.symbol) {
        alert('请选择货币对');
        return;
    }

    if (!analysisData.ai_strategy_id) {
        alert('请选择AI策略模型');
        return;
    }

    startAnalysis(analysisData);
});

// 开始分析
function startAnalysis(data) {
    analysisInProgress = true;

    // 显示进度卡片
    document.getElementById('progressCard').style.display = 'block';
    document.getElementById('resultsCard').style.display = 'none';

    // 重置进度
    resetProgress();

    // 获取AI策略详细信息
    if (data.ai_strategy_id) {
        fetch(`/api/ai-strategies/${data.ai_strategy_id}`)
        .then(response => response.json())
        .then(strategyData => {
            if (strategyData.success) {
                data.strategy_name = strategyData.strategy.name;
                data.strategy_info = strategyData.strategy;
                console.log('获取到AI策略信息:', strategyData.strategy);
            } else {
                data.strategy_name = 'AI策略';
                console.error('获取AI策略信息失败:', strategyData.error);
            }

            // 开始模拟分析过程
            simulateAnalysisProcess(data);
        })
        .catch(error => {
            console.error('获取AI策略信息请求失败:', error);
            data.strategy_name = 'AI策略';

            // 继续分析过程
            simulateAnalysisProcess(data);
        });
    } else {
        data.strategy_name = 'AI策略';
        simulateAnalysisProcess(data);
    }
}

// 重置进度
function resetProgress() {
    document.getElementById('progressBar').style.width = '0%';
    document.querySelectorAll('.step').forEach(step => {
        step.classList.remove('active', 'completed');
        const icon = step.querySelector('i');
        const text = step.querySelector('span');
        icon.className = 'fas fa-circle text-muted';
        text.className = 'text-muted';
    });
}

// 模拟分析过程
function simulateAnalysisProcess(data) {
    const steps = [
        { id: 'step1', icon: 'fas fa-download', text: '获取市场数据', duration: 2000 },
        { id: 'step2', icon: 'fas fa-calculator', text: '计算技术指标', duration: 1500 },
        { id: 'step3', icon: 'fas fa-brain', text: 'AI策略分析', duration: 3000 },
        { id: 'step4', icon: 'fas fa-chart-line', text: '生成策略建议', duration: 2000 },
        { id: 'step5', icon: 'fas fa-check', text: '完成分析', duration: 500 }
    ];

    let currentStep = 0;

    function executeStep() {
        if (currentStep < steps.length) {
            const step = steps[currentStep];
            const stepElement = document.getElementById(step.id);

            // 设置当前步骤为活跃状态
            stepElement.classList.add('active');
            stepElement.querySelector('i').className = step.icon + ' text-primary';
            stepElement.querySelector('span').className = 'text-primary';

            // 更新进度条
            const progress = ((currentStep + 1) / steps.length) * 100;
            document.getElementById('progressBar').style.width = progress + '%';

            setTimeout(() => {
                // 完成当前步骤
                stepElement.classList.remove('active');
                stepElement.classList.add('completed');
                stepElement.querySelector('i').className = 'fas fa-check text-success';
                stepElement.querySelector('span').className = 'text-success';

                currentStep++;

                if (currentStep < steps.length) {
                    executeStep();
                } else {
                    // 分析完成
                    setTimeout(() => {
                        completeAnalysis(data);
                    }, 500);
                }
            }, step.duration);
        }
    }

    executeStep();
}

// 完成分析
function completeAnalysis(data) {
    analysisInProgress = false;

    // 隐藏进度卡片，显示结果
    document.getElementById('progressCard').style.display = 'none';
    document.getElementById('resultsCard').style.display = 'block';

    // 模拟分析结果
    displayAnalysisResults(data);
}

// 显示分析结果
function displayAnalysisResults(data) {
    // 模拟市场数据
    document.getElementById('currentPrice').textContent = '$45,234.56';
    document.getElementById('priceChange').textContent = '*****%';
    document.getElementById('priceChange').className = 'profit-positive';
    document.getElementById('volume').textContent = '1.2B';
    document.getElementById('volatility').textContent = '15.6%';

    // 模拟技术指标
    const indicators = [
        { name: 'RSI', value: '65.4', status: 'neutral' },
        { name: 'MACD', value: '0.023', status: 'bullish' },
        { name: 'SMA20', value: '$44,890', status: 'bullish' },
        { name: 'SMA50', value: '$43,210', status: 'bullish' },
        { name: '布林带', value: '中轨附近', status: 'neutral' },
        { name: '成交量', value: '高于平均', status: 'bullish' }
    ];

    const indicatorsGrid = document.getElementById('indicatorsGrid');
    indicatorsGrid.innerHTML = '';

    indicators.forEach(indicator => {
        const col = document.createElement('div');
        col.className = 'col-md-4 mb-3';

        let statusClass = 'text-muted';
        let statusIcon = 'fas fa-minus';

        if (indicator.status === 'bullish') {
            statusClass = 'profit-positive';
            statusIcon = 'fas fa-arrow-up';
        } else if (indicator.status === 'bearish') {
            statusClass = 'profit-negative';
            statusIcon = 'fas fa-arrow-down';
        }

        col.innerHTML = `
            <div class="text-center">
                <h6 class="text-muted">${indicator.name}</h6>
                <div class="${statusClass}">
                    <i class="${statusIcon}"></i>
                    ${indicator.value}
                </div>
            </div>
        `;
        indicatorsGrid.appendChild(col);
    });

    // AI分析过程
    const analysisSteps = document.getElementById('analysisSteps');
    analysisSteps.innerHTML = `
        <div class="mb-3">
            <strong>数据收集阶段：</strong>
            <p class="mb-2">成功获取${data.symbol}过去${data.time_range}的历史数据，包括价格、成交量等关键指标。</p>
        </div>
        <div class="mb-3">
            <strong>技术分析阶段：</strong>
            <p class="mb-2">计算了多项技术指标，发现当前趋势偏向看涨，RSI处于中性区域，MACD显示买入信号。</p>
        </div>
        <div class="mb-3">
            <strong>AI策略推理：</strong>
            <p class="mb-2">使用训练好的AI策略"${data.strategy_name}"进行深度分析，该策略基于历史数据训练，综合考虑技术面、市场情绪和历史模式。</p>
        </div>
        <div class="mb-3">
            <strong>风险评估：</strong>
            <p class="mb-0">当前市场波动性适中，建议采用中等仓位进行操作，设置合理的止损位。</p>
        </div>
    `;

    // 策略建议
    document.getElementById('recommendationContent').innerHTML = `
        <h5 class="text-primary">建议操作：适量买入</h5>
        <p><strong>入场时机：</strong>当前价格附近可以考虑分批建仓</p>
        <p><strong>目标价位：</strong>$48,000 - $50,000</p>
        <p><strong>止损位：</strong>$42,500</p>
        <p><strong>持仓周期：</strong>中短期（2-4周）</p>
        <p><strong>理由：</strong>技术指标显示上涨趋势，成交量配合良好，但需注意市场整体风险。</p>
    `;

    // 风险评估
    document.getElementById('riskLevel').textContent = '中等';
    document.getElementById('riskLevel').className = 'badge bg-warning';
    document.getElementById('positionSize').textContent = '30-50%';
    document.getElementById('stopLoss').textContent = '$42,500';

    // 添加到历史记录
    addToAnalysisHistory(data);
}

// 添加到分析历史
function addToAnalysisHistory(data) {
    const tbody = document.querySelector('#analysisHistoryTable tbody');

    // 如果是第一条记录，清除占位符
    if (tbody.children.length === 1 && tbody.children[0].children.length === 1) {
        tbody.innerHTML = '';
    }

    // 生成分析ID
    const analysisId = `analysis_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // 保存完整的分析数据到存储中
    const analysisDetailData = {
        analysisId: analysisId,
        basicInfo: {
            strategyName: data.strategy_name || 'AI策略',
            symbol: data.symbol,
            timeRange: data.time_range,
            analysisDepth: data.analysis_depth,
            analysisTime: new Date().toISOString()
        },
        marketData: {
            currentPrice: '$45,234.56',
            priceChange: '*****%',
            volume: '1.2B',
            volatility: '15.6%',
            trend: '上涨趋势',
            sentiment: '乐观'
        },
        technicalIndicators: [
            { name: 'RSI', value: '65.4', status: 'neutral', description: 'RSI指标显示市场处于中性区域，既不超买也不超卖' },
            { name: 'MACD', value: '0.023', status: 'bullish', description: 'MACD线上穿信号线，显示买入信号' },
            { name: 'SMA20', value: '$44,890', status: 'bullish', description: '价格位于20日均线上方，短期趋势向上' },
            { name: 'SMA50', value: '$43,210', status: 'bullish', description: '价格位于50日均线上方，中期趋势向上' },
            { name: '布林带', value: '中轨附近', status: 'neutral', description: '价格在布林带中轨附近，波动性正常' },
            { name: '成交量', value: '高于平均', status: 'bullish', description: '成交量高于平均水平，支持价格上涨' }
        ],
        analysisSteps: [
            {
                title: '数据收集阶段',
                content: `成功获取${data.symbol}过去${data.time_range}的历史数据，包括价格、成交量等关键指标。数据质量良好，覆盖了足够的市场周期。`,
                status: 'completed'
            },
            {
                title: '技术分析阶段',
                content: '计算了多项技术指标，发现当前趋势偏向看涨，RSI处于中性区域，MACD显示买入信号。移动平均线呈多头排列。',
                status: 'completed'
            },
            {
                title: 'AI策略推理',
                content: `使用训练好的AI策略"${data.strategy_name}"进行深度分析，该策略基于历史数据训练，综合考虑技术面、市场情绪和历史模式。模型置信度较高。`,
                status: 'completed'
            },
            {
                title: '风险评估',
                content: '当前市场波动性适中，建议采用中等仓位进行操作，设置合理的止损位。整体风险可控，适合中短期投资。',
                status: 'completed'
            }
        ],
        recommendation: {
            action: '适量买入',
            entryTiming: '当前价格附近可以考虑分批建仓',
            targetPrice: '$48,000 - $50,000',
            stopLoss: '$42,500',
            holdingPeriod: '中短期（2-4周）',
            reason: '技术指标显示上涨趋势，成交量配合良好，但需注意市场整体风险。AI策略模型给出积极信号。',
            confidence: '85%'
        },
        riskAssessment: {
            riskLevel: '中等',
            positionSize: '30-50%',
            stopLoss: '$42,500',
            riskReward: '1:2.5'
        },
        strategyPerformance: data.strategy_info ? {
            winRate: data.strategy_info.performance_metrics?.win_rate || 0.65,
            avgReturn: data.strategy_info.performance_metrics?.avg_return || 0.12,
            maxDrawdown: data.strategy_info.performance_metrics?.max_drawdown || -0.08,
            sharpeRatio: data.strategy_info.performance_metrics?.sharpe_ratio || 1.85
        } : {
            winRate: 0.65,
            avgReturn: 0.12,
            maxDrawdown: -0.08,
            sharpeRatio: 1.85
        }
    };

    // 保存到存储中
    analysisDetailsStorage.set(analysisId, analysisDetailData);
    console.log('保存分析详情数据:', analysisId, analysisDetailData);

    const row = document.createElement('tr');
    row.innerHTML = `
        <td>${new Date().toLocaleString()}</td>
        <td>${data.symbol}</td>
        <td><span class="badge bg-primary">${data.strategy_name || 'AI策略'}</span></td>
        <td><span class="badge bg-info">${data.analysis_depth}</span></td>
        <td class="profit-positive">适量买入</td>
        <td><span class="badge bg-warning">中等</span></td>
        <td>
            <button class="btn btn-sm btn-outline-info" onclick="viewAnalysisDetail('${analysisId}')" title="查看详细分析报告">
                <i class="fas fa-eye"></i>
                详情
            </button>
        </td>
    `;

    // 插入到表格顶部
    tbody.insertBefore(row, tbody.firstChild);

    // 限制历史记录数量
    if (tbody.children.length > 10) {
        tbody.removeChild(tbody.lastChild);
    }
}

// 查看分析详情
function viewAnalysisDetail(analysisId) {
    console.log('=== 查看分析详情 ===');
    console.log('分析ID:', analysisId);
    console.log('当前存储的分析数据:', analysisDetailsStorage);

    // 从存储中获取分析数据
    const analysisData = analysisDetailsStorage.get(analysisId);
    console.log('获取到的分析数据:', analysisData);

    if (!analysisData) {
        console.error('分析详情数据不存在，analysisId:', analysisId);
        alert('分析详情数据不存在，可能是页面刷新后丢失\n\n调试信息:\n- 请求的ID: ' + analysisId + '\n- 可用的ID: ' + Array.from(analysisDetailsStorage.keys()).join(', '));
        return;
    }

    console.log('准备显示分析详情模态框...');
    // 显示分析详情模态框
    showAnalysisDetailModal(analysisData);
}

// 显示分析详情模态框
function showAnalysisDetailModal(analysisData) {
    console.log('=== 显示分析详情模态框 ===');
    console.log('分析数据:', analysisData);

    const { basicInfo, marketData, technicalIndicators, analysisSteps, recommendation, riskAssessment, strategyPerformance } = analysisData;

    // 填充基本信息
    document.getElementById('detailAnalysisId').textContent = analysisData.analysisId;
    document.getElementById('detailStrategyName').textContent = basicInfo.strategyName;
    document.getElementById('detailSymbol').textContent = basicInfo.symbol;
    document.getElementById('detailTimeRange').textContent = basicInfo.timeRange;
    document.getElementById('detailAnalysisDepth').textContent = basicInfo.analysisDepth;
    document.getElementById('detailAnalysisTime').textContent = new Date(basicInfo.analysisTime).toLocaleString('zh-CN', {timeZone: 'Asia/Shanghai'});

    // 填充市场数据
    document.getElementById('detailCurrentPrice').textContent = marketData.currentPrice;
    document.getElementById('detailPriceChange').textContent = marketData.priceChange;
    document.getElementById('detailPriceChange').className = marketData.priceChange.startsWith('+') ? 'profit-positive' : 'profit-negative';
    document.getElementById('detailVolume').textContent = marketData.volume;
    document.getElementById('detailVolatility').textContent = marketData.volatility;
    document.getElementById('detailTrend').textContent = marketData.trend;
    document.getElementById('detailSentiment').textContent = marketData.sentiment;

    // 填充技术指标
    const indicatorsGrid = document.getElementById('detailIndicatorsGrid');
    indicatorsGrid.innerHTML = '';

    technicalIndicators.forEach(indicator => {
        const col = document.createElement('div');
        col.className = 'col-md-4 mb-3';

        let statusClass = 'text-muted';
        let statusIcon = 'fas fa-minus';

        if (indicator.status === 'bullish') {
            statusClass = 'profit-positive';
            statusIcon = 'fas fa-arrow-up';
        } else if (indicator.status === 'bearish') {
            statusClass = 'profit-negative';
            statusIcon = 'fas fa-arrow-down';
        }

        col.innerHTML = `
            <div class="card h-100">
                <div class="card-body text-center">
                    <h6 class="card-title text-muted">${indicator.name}</h6>
                    <div class="${statusClass} mb-2">
                        <i class="${statusIcon}"></i>
                        <strong>${indicator.value}</strong>
                    </div>
                    <small class="text-muted">${indicator.description}</small>
                </div>
            </div>
        `;
        indicatorsGrid.appendChild(col);
    });

    // 填充分析步骤
    const analysisStepsContainer = document.getElementById('detailAnalysisSteps');
    analysisStepsContainer.innerHTML = '';

    analysisSteps.forEach((step, index) => {
        const stepDiv = document.createElement('div');
        stepDiv.className = 'mb-3';
        stepDiv.innerHTML = `
            <div class="d-flex align-items-start">
                <div class="flex-shrink-0">
                    <div class="badge bg-success rounded-pill me-3">${index + 1}</div>
                </div>
                <div class="flex-grow-1">
                    <h6 class="mb-1">${step.title}</h6>
                    <p class="mb-0 text-muted">${step.content}</p>
                </div>
            </div>
        `;
        analysisStepsContainer.appendChild(stepDiv);
    });

    // 填充策略建议
    document.getElementById('detailRecommendationContent').innerHTML = `
        <div class="mb-3">
            <h5 class="text-primary">${recommendation.action}</h5>
            <div class="badge bg-info mb-3">置信度: ${recommendation.confidence}</div>
        </div>
        <div class="row">
            <div class="col-md-6">
                <p><strong>入场时机：</strong>${recommendation.entryTiming}</p>
                <p><strong>目标价位：</strong>${recommendation.targetPrice}</p>
                <p><strong>止损位：</strong>${recommendation.stopLoss}</p>
            </div>
            <div class="col-md-6">
                <p><strong>持仓周期：</strong>${recommendation.holdingPeriod}</p>
                <p><strong>分析理由：</strong>${recommendation.reason}</p>
            </div>
        </div>
    `;

    // 填充风险评估
    document.getElementById('detailRiskLevel').textContent = riskAssessment.riskLevel;
    document.getElementById('detailRiskLevel').className = `badge ${riskAssessment.riskLevel === '低' ? 'bg-success' : riskAssessment.riskLevel === '高' ? 'bg-danger' : 'bg-warning'}`;
    document.getElementById('detailPositionSize').textContent = riskAssessment.positionSize;
    document.getElementById('detailStopLoss').textContent = riskAssessment.stopLoss;

    // 填充策略性能指标
    document.getElementById('detailStrategyWinRate').textContent = (strategyPerformance.winRate * 100).toFixed(1) + '%';
    document.getElementById('detailAvgReturn').textContent = (strategyPerformance.avgReturn * 100).toFixed(1) + '%';
    document.getElementById('detailMaxDrawdown').textContent = (strategyPerformance.maxDrawdown * 100).toFixed(1) + '%';
    document.getElementById('detailSharpeRatio').textContent = strategyPerformance.sharpeRatio.toFixed(2);

    // 显示模态框
    try {
        const modalElement = document.getElementById('analysisDetailModal');
        if (modalElement) {
            // 尝试使用Bootstrap 5的方式
            if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
                const modal = new bootstrap.Modal(modalElement);
                modal.show();
            } else {
                // 如果Bootstrap不可用，使用jQuery方式（Bootstrap 4兼容）
                if (typeof $ !== 'undefined') {
                    $(modalElement).modal('show');
                } else {
                    // 最后的备选方案：直接显示
                    modalElement.style.display = 'block';
                    modalElement.classList.add('show');
                    document.body.classList.add('modal-open');

                    // 添加背景遮罩
                    const backdrop = document.createElement('div');
                    backdrop.className = 'modal-backdrop fade show';
                    backdrop.id = 'analysisDetailBackdrop';
                    document.body.appendChild(backdrop);
                }
            }
        } else {
            console.error('分析详情模态框元素未找到');
            alert('无法显示分析详情，请刷新页面重试');
        }
    } catch (error) {
        console.error('显示模态框时出错:', error);
        alert('显示分析详情时出错: ' + error.message);
    }
}

// 关闭分析详情模态框（备选方案）
function closeAnalysisDetailModal() {
    const modalElement = document.getElementById('analysisDetailModal');
    const backdrop = document.getElementById('analysisDetailBackdrop');

    if (modalElement) {
        modalElement.style.display = 'none';
        modalElement.classList.remove('show');
        document.body.classList.remove('modal-open');
    }

    if (backdrop) {
        document.body.removeChild(backdrop);
    }
}

// 应用分析结果到交易
function useAnalysisForTrading() {
    if (confirm('是否要将此分析结果应用到交易页面？')) {
        // 这里可以跳转到交易页面并预填充分析建议
        const analysisData = Array.from(analysisDetailsStorage.values())[0]; // 获取最新的分析数据
        if (analysisData) {
            const params = new URLSearchParams({
                symbol: analysisData.basicInfo.symbol,
                action: analysisData.recommendation.action,
                target: analysisData.recommendation.targetPrice,
                stop_loss: analysisData.recommendation.stopLoss
            });

            // 跳转到模拟交易页面
            window.open(`/trading/demo?${params.toString()}`, '_blank');
        }
    }
}

// 导出分析报告
function exportAnalysisReport() {
    alert('导出分析报告功能待实现\n\n将包含：\n- 完整的分析过程\n- 技术指标详情\n- AI策略建议\n- 风险评估报告');
}

// 测试函数 - 验证分析详情功能
function testAnalysisDetail() {
    console.log('测试分析详情功能');
    console.log('当前存储的分析数据:', analysisDetailsStorage);

    if (analysisDetailsStorage.size === 0) {
        alert('没有可用的分析数据，请先执行一次AI分析');
        return;
    }

    // 获取第一个分析数据进行测试
    const firstKey = analysisDetailsStorage.keys().next().value;
    console.log('使用第一个分析数据进行测试:', firstKey);
    viewAnalysisDetail(firstKey);
}
</script>
{% endblock %}
