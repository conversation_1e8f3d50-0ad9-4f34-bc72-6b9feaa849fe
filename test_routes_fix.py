#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试路由修复是否成功
"""

import sys
import traceback

def test_routes_import():
    """测试routes.py导入"""
    try:
        print("🔄 测试routes.py导入...")
        
        # 导入Flask应用
        from app import app
        
        print("✅ Flask应用导入成功")
        
        # 检查路由是否正确注册
        routes = []
        for rule in app.url_map.iter_rules():
            routes.append({
                'endpoint': rule.endpoint,
                'methods': list(rule.methods),
                'rule': rule.rule
            })
        
        # 查找AI交易相关的路由
        ai_routes = [r for r in routes if 'deep-learning' in r['rule']]
        
        print(f"\n📋 找到 {len(ai_routes)} 个AI交易相关路由:")
        for route in ai_routes:
            print(f"  - {route['rule']} [{', '.join(route['methods'])}] -> {route['endpoint']}")
        
        # 检查是否有重复的endpoint
        endpoints = [r['endpoint'] for r in routes]
        duplicates = []
        seen = set()
        for endpoint in endpoints:
            if endpoint in seen:
                duplicates.append(endpoint)
            seen.add(endpoint)
        
        if duplicates:
            print(f"\n❌ 发现重复的endpoint: {duplicates}")
            return False
        else:
            print(f"\n✅ 没有重复的endpoint，共 {len(routes)} 个路由")
        
        # 特别检查我们修复的路由
        close_position_routes = [r for r in routes if 'close-position' in r['rule']]
        print(f"\n📋 平仓相关路由 ({len(close_position_routes)} 个):")
        for route in close_position_routes:
            print(f"  - {route['rule']} -> {route['endpoint']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        traceback.print_exc()
        return False

def test_app_startup():
    """测试应用启动"""
    try:
        print("\n🔄 测试应用启动...")
        
        from app import app
        
        # 检查应用配置
        print(f"✅ 应用名称: {app.name}")
        print(f"✅ 调试模式: {app.debug}")
        print(f"✅ 路由数量: {len(list(app.url_map.iter_rules()))}")
        
        return True
        
    except Exception as e:
        print(f"❌ 应用启动测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 路由修复验证工具")
    print("=" * 50)
    
    print("📋 问题描述:")
    print("• AssertionError: View function mapping is overwriting an existing endpoint function: api_close_position")
    print("• 两个同名函数导致Flask路由冲突")
    print()
    
    # 测试路由导入
    routes_ok = test_routes_import()
    
    # 测试应用启动
    app_ok = test_app_startup()
    
    print("\n📊 测试结果")
    print("=" * 50)
    
    if routes_ok and app_ok:
        print("✅ 所有测试通过！")
        print("✅ 路由冲突已修复")
        print("✅ 应用可以正常启动")
        print()
        print("🎉 修复总结:")
        print("• 将 /api/deep-learning/close-position 的函数名从 api_close_position 改为 api_close_ai_position")
        print("• 保留了 /api/demo-trading/close-position 的原始函数名 api_close_position")
        print("• 两个路由现在有不同的函数名，避免了冲突")
        print()
        print("💡 现在可以正常启动应用程序:")
        print("   python app.py")
    else:
        print("❌ 测试失败，需要进一步检查")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
