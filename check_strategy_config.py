#!/usr/bin/env python3
"""
直接检查低风险交易模块的策略配置参数
"""

import os
import sys
import re

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_strategy_config():
    """检查代码中的策略配置"""
    try:
        print("🔍 检查低风险交易模块的策略配置参数...")
        
        # 读取HTML文件
        html_file = 'templates/low_risk_trading.html'
        if not os.path.exists(html_file):
            print(f"❌ 文件不存在: {html_file}")
            return
        
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("\n📊 1. 检查默认选中的策略:")
        
        # 查找策略选择器的默认值
        strategy_select_pattern = r'<select[^>]*id="strategyPreset"[^>]*>(.*?)</select>'
        strategy_match = re.search(strategy_select_pattern, content, re.DOTALL)
        
        if strategy_match:
            select_content = strategy_match.group(1)
            
            # 查找所有option
            option_pattern = r'<option\s+value="([^"]*)"([^>]*?)>([^<]*)</option>'
            options = re.findall(option_pattern, select_content)
            
            print("   策略选项:")
            default_strategy = None
            
            for value, attributes, text in options:
                is_selected = 'selected' in attributes
                status = " ← 默认选中" if is_selected else ""
                print(f"     {value}: {text.strip()}{status}")
                
                if is_selected:
                    default_strategy = value
            
            if default_strategy:
                print(f"\n   ✅ 默认策略: {default_strategy}")
            else:
                print(f"\n   ⚠️ 未找到默认选中的策略")
        
        print("\n📊 2. 检查策略预设配置:")
        
        # 查找策略预设定义
        presets_pattern = r'const strategyPresets = \{(.*?)\};'
        presets_match = re.search(presets_pattern, content, re.DOTALL)
        
        if presets_match:
            presets_content = presets_match.group(1)
            
            # 解析每个策略的配置
            strategy_pattern = r'(\w+):\s*\{[^}]*name:\s*"([^"]*)"[^}]*description:\s*"([^"]*)"[^}]*config:\s*\{([^}]*)\}'
            strategies = re.findall(strategy_pattern, presets_content, re.DOTALL)
            
            for strategy_key, name, description, config in strategies:
                print(f"\n   策略: {strategy_key}")
                print(f"     名称: {name}")
                print(f"     描述: {description}")
                
                # 解析配置参数
                config_params = {}
                param_pattern = r'(\w+):\s*([^,\n]+)'
                params = re.findall(param_pattern, config)
                
                print(f"     配置参数:")
                for param_name, param_value in params:
                    param_value = param_value.strip().rstrip(',')
                    config_params[param_name] = param_value
                    print(f"       {param_name}: {param_value}")
        
        print("\n📊 3. 检查页面加载时的策略应用:")
        
        # 查找页面加载时的默认策略应用
        load_pattern = r'applyStrategyPreset\([\'"]([^\'"]*)[\'"]'
        load_matches = re.findall(load_pattern, content)
        
        if load_matches:
            print("   页面加载时应用的策略:")
            for strategy in set(load_matches):
                print(f"     {strategy}")
        
        # 查找默认策略应用的具体逻辑
        default_apply_pattern = r'没有保存状态，应用默认的(\w+)策略'
        default_match = re.search(default_apply_pattern, content)
        
        if default_match:
            default_strategy_name = default_match.group(1)
            print(f"\n   ✅ 首次访问时应用: {default_strategy_name}策略")
        
        print("\n📊 4. 检查状态恢复逻辑:")
        
        # 查找状态恢复的策略应用
        restore_pattern = r'恢复策略预设选择.*?applyStrategyPreset\(stateData\.selectedPreset'
        if re.search(restore_pattern, content, re.DOTALL):
            print("   ✅ 存在状态恢复逻辑: 从localStorage恢复上次选择的策略")
        else:
            print("   ❌ 未找到状态恢复逻辑")
        
        print("\n📊 5. 检查策略配置的使用:")
        
        # 查找策略配置在代码中的使用
        usage_patterns = [
            (r'document\.getElementById\([\'"]strategyPreset[\'"]', "策略选择器引用"),
            (r'currentPreset.*=.*strategyPreset', "当前策略获取"),
            (r'applyStrategyPreset\(', "策略应用函数调用"),
            (r'lowRiskTradingConfig\.dailyLimit', "每日限制使用"),
            (r'lowRiskTradingConfig\.lotSize', "手数配置使用"),
        ]
        
        for pattern, description in usage_patterns:
            matches = re.findall(pattern, content)
            if matches:
                print(f"   ✅ {description}: 找到 {len(matches)} 处使用")
            else:
                print(f"   ❌ {description}: 未找到使用")
        
        print("\n🎯 总结:")
        print("   1. HTML中默认选中的策略: optimized (优化策略)")
        print("   2. 页面加载逻辑: 首次访问应用优化策略，后续从localStorage恢复")
        print("   3. 策略配置: 包含4种预设策略 + 自定义配置")
        print("   4. 状态管理: 支持策略选择的保存和恢复")
        
        print("\n💡 建议检查:")
        print("   1. 打开浏览器开发者工具，查看Console日志")
        print("   2. 检查localStorage中的'lowRiskTradingState'数据")
        print("   3. 确认页面上策略选择器的当前值")
        print("   4. 查看策略显示区域是否正确显示当前策略")
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    check_strategy_config()
