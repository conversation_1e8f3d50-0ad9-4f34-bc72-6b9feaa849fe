{% extends "base.html" %}

{% block page_title %}风险管理{% endblock %}

{% block content %}
<div class="row">
    <!-- 仓位计算器 -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calculator"></i>
                    仓位计算器
                </h5>
            </div>
            <div class="card-body">
                <form id="positionCalculatorForm">
                    <div class="mb-3">
                        <label class="form-label">账户余额</label>
                        <div class="input-group">
                            <span class="input-group-text">$</span>
                            <input type="number" class="form-control" id="accountBalance" 
                                   value="10000" step="0.01" required>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">风险比例</label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="riskPercentage" 
                                   value="2" min="0.1" max="10" step="0.1" required>
                            <span class="input-group-text">%</span>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">入场价格</label>
                        <input type="number" class="form-control" id="entryPrice" 
                               value="1.0850" step="0.00001" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">止损价格</label>
                        <input type="number" class="form-control" id="stopLossPrice" 
                               value="1.0800" step="0.00001" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">交易品种</label>
                        <select class="form-select" id="tradingSymbol">
                            <option value="EURUSD">EUR/USD</option>
                            <option value="GBPUSD">GBP/USD</option>
                            <option value="USDJPY">USD/JPY</option>
                            <option value="AUDUSD">AUD/USD</option>
                            <option value="XAUUSD">XAU/USD</option>
                        </select>
                    </div>
                    
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-calculator"></i>
                        计算仓位
                    </button>
                </form>
                
                <!-- 计算结果 -->
                <div id="calculationResult" class="mt-4" style="display: none;">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle"></i> 计算结果</h6>
                        <div class="row">
                            <div class="col-6">
                                <small class="text-muted">建议仓位:</small>
                                <div class="fw-bold" id="recommendedSize">-</div>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">风险金额:</small>
                                <div class="fw-bold" id="riskAmount">-</div>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">止损点数:</small>
                                <div class="fw-bold" id="stopLossPoints">-</div>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">风险回报比:</small>
                                <div class="fw-bold" id="riskRewardRatio">-</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 风险指标 -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie"></i>
                    风险指标
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6 mb-3">
                        <div class="text-center">
                            <h6 class="text-muted">胜率</h6>
                            <h4 class="profit-positive" id="winRate">65.2%</h4>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="text-center">
                            <h6 class="text-muted">盈亏比</h6>
                            <h4 id="profitFactor">1.85</h4>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="text-center">
                            <h6 class="text-muted">最大回撤</h6>
                            <h4 class="profit-negative" id="maxDrawdown">-8.5%</h4>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="text-center">
                            <h6 class="text-muted">夏普比率</h6>
                            <h4 id="sharpeRatio">1.42</h4>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="text-center">
                            <h6 class="text-muted">VaR (95%)</h6>
                            <h4 class="profit-negative" id="var95">-2.1%</h4>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="text-center">
                            <h6 class="text-muted">凯利比例</h6>
                            <h4 id="kellyFraction">12.5%</h4>
                        </div>
                    </div>
                </div>
                
                <button class="btn btn-outline-primary w-100" onclick="refreshRiskMetrics()">
                    <i class="fas fa-sync"></i>
                    刷新指标
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 风险分析图表 -->
<div class="row">
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line"></i>
                    回撤分析
                </h5>
            </div>
            <div class="card-body">
                <div id="drawdownChart" style="height: 400px;"></div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-exclamation-triangle"></i>
                    风险警告
                </h5>
            </div>
            <div class="card-body">
                <div id="riskWarnings">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>保证金警告</strong><br>
                        当前保证金水平较低，建议减少持仓
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>相关性提醒</strong><br>
                        EUR/USD 和 GBP/USD 相关性较高
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 持仓相关性分析 -->
<div class="row">
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-project-diagram"></i>
                    持仓相关性
                </h5>
            </div>
            <div class="card-body">
                <div id="correlationMatrix" style="height: 400px;"></div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar"></i>
                    收益分布
                </h5>
            </div>
            <div class="card-body">
                <div id="returnsDistribution" style="height: 400px;"></div>
            </div>
        </div>
    </div>
</div>

<!-- 风险管理设置 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-cog"></i>
                    风险管理设置
                </h5>
            </div>
            <div class="card-body">
                <form id="riskSettingsForm" class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">单笔最大风险</label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="maxRiskPerTrade" 
                                   value="2" min="0.1" max="10" step="0.1">
                            <span class="input-group-text">%</span>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <label class="form-label">日最大亏损</label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="maxDailyLoss" 
                                   value="5" min="1" max="20" step="0.5">
                            <span class="input-group-text">%</span>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <label class="form-label">最大回撤</label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="maxDrawdown" 
                                   value="20" min="5" max="50" step="1">
                            <span class="input-group-text">%</span>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <label class="form-label">最大相关性</label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="maxCorrelation" 
                                   value="70" min="10" max="95" step="5">
                            <span class="input-group-text">%</span>
                        </div>
                    </div>
                    
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            保存设置
                        </button>
                        <button type="button" class="btn btn-outline-info ms-2" onclick="generateRiskReport()">
                            <i class="fas fa-file-alt"></i>
                            生成风险报告
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeCharts();
    loadRiskMetrics();
});

// 仓位计算器
document.getElementById('positionCalculatorForm').addEventListener('submit', function(e) {
    e.preventDefault();
    calculatePosition();
});

// 风险设置表单
document.getElementById('riskSettingsForm').addEventListener('submit', function(e) {
    e.preventDefault();
    saveRiskSettings();
});

// 计算仓位
function calculatePosition() {
    const accountBalance = parseFloat(document.getElementById('accountBalance').value);
    const riskPercentage = parseFloat(document.getElementById('riskPercentage').value) / 100;
    const entryPrice = parseFloat(document.getElementById('entryPrice').value);
    const stopLossPrice = parseFloat(document.getElementById('stopLossPrice').value);
    const symbol = document.getElementById('tradingSymbol').value;
    
    // 计算风险金额
    const riskAmount = accountBalance * riskPercentage;
    
    // 计算止损点数
    const stopLossPoints = Math.abs(entryPrice - stopLossPrice);
    
    // 计算仓位大小 (简化计算)
    const contractSize = getContractSize(symbol);
    const positionSize = riskAmount / (stopLossPoints * contractSize);
    
    // 计算风险回报比 (假设止盈是止损的2倍)
    const riskRewardRatio = 2.0;
    
    // 显示结果
    document.getElementById('recommendedSize').textContent = positionSize.toFixed(2) + ' 手';
    document.getElementById('riskAmount').textContent = '$' + riskAmount.toFixed(2);
    document.getElementById('stopLossPoints').textContent = stopLossPoints.toFixed(5);
    document.getElementById('riskRewardRatio').textContent = '1:' + riskRewardRatio.toFixed(1);
    
    document.getElementById('calculationResult').style.display = 'block';
}

// 获取合约大小
function getContractSize(symbol) {
    const contractSizes = {
        'EURUSD': 100000,
        'GBPUSD': 100000,
        'USDJPY': 100000,
        'AUDUSD': 100000,
        'XAUUSD': 100
    };
    return contractSizes[symbol] || 100000;
}

// 初始化图表
function initializeCharts() {
    createDrawdownChart();
    createCorrelationMatrix();
    createReturnsDistribution();
}

// 创建回撤图表
function createDrawdownChart() {
    // 生成模拟回撤数据
    const dates = [];
    const drawdown = [];
    const startDate = new Date();
    startDate.setMonth(startDate.getMonth() - 6);
    
    let currentDrawdown = 0;
    for (let i = 0; i < 180; i++) {
        const date = new Date(startDate);
        date.setDate(date.getDate() + i);
        dates.push(date);
        
        // 模拟回撤变化
        const change = (Math.random() - 0.7) * 0.02;
        currentDrawdown = Math.max(currentDrawdown + change, -0.25);
        drawdown.push(currentDrawdown * 100);
    }
    
    const trace = {
        x: dates,
        y: drawdown,
        type: 'scatter',
        mode: 'lines',
        fill: 'tozeroy',
        fillcolor: 'rgba(255,0,0,0.3)',
        line: { color: 'red' },
        name: '回撤'
    };
    
    const layout = {
        title: '历史回撤分析',
        xaxis: { title: '日期' },
        yaxis: { title: '回撤 (%)' },
        margin: { t: 50, r: 50, b: 50, l: 80 }
    };
    
    Plotly.newPlot('drawdownChart', [trace], layout, { responsive: true });
}

// 创建相关性矩阵
function createCorrelationMatrix() {
    const symbols = ['EURUSD', 'GBPUSD', 'USDJPY', 'AUDUSD', 'USDCAD'];
    const correlationData = [
        [1.00, 0.85, -0.45, 0.72, -0.68],
        [0.85, 1.00, -0.38, 0.65, -0.72],
        [-0.45, -0.38, 1.00, -0.25, 0.42],
        [0.72, 0.65, -0.25, 1.00, -0.58],
        [-0.68, -0.72, 0.42, -0.58, 1.00]
    ];
    
    const trace = {
        z: correlationData,
        x: symbols,
        y: symbols,
        type: 'heatmap',
        colorscale: 'RdBu',
        zmid: 0,
        text: correlationData.map(row => row.map(val => val.toFixed(2))),
        texttemplate: '%{text}',
        textfont: { size: 12 }
    };
    
    const layout = {
        title: '货币对相关性矩阵',
        margin: { t: 50, r: 50, b: 80, l: 80 }
    };
    
    Plotly.newPlot('correlationMatrix', [trace], layout, { responsive: true });
}

// 创建收益分布图
function createReturnsDistribution() {
    // 生成模拟收益数据
    const returns = [];
    for (let i = 0; i < 1000; i++) {
        // 正态分布收益
        const u1 = Math.random();
        const u2 = Math.random();
        const z0 = Math.sqrt(-2 * Math.log(u1)) * Math.cos(2 * Math.PI * u2);
        returns.push(z0 * 2 + 0.5); // 均值0.5%，标准差2%
    }
    
    const trace = {
        x: returns,
        type: 'histogram',
        nbinsx: 50,
        name: '收益分布',
        marker: { color: 'lightblue', opacity: 0.7 }
    };
    
    const layout = {
        title: '日收益率分布',
        xaxis: { title: '收益率 (%)' },
        yaxis: { title: '频次' },
        margin: { t: 50, r: 50, b: 50, l: 80 }
    };
    
    Plotly.newPlot('returnsDistribution', [trace], layout, { responsive: true });
}

// 加载风险指标
function loadRiskMetrics() {
    // 这里可以从API获取真实数据
    // 目前使用模拟数据
    updateRiskMetrics({
        winRate: 65.2,
        profitFactor: 1.85,
        maxDrawdown: -8.5,
        sharpeRatio: 1.42,
        var95: -2.1,
        kellyFraction: 12.5
    });
}

// 更新风险指标显示
function updateRiskMetrics(metrics) {
    document.getElementById('winRate').textContent = metrics.winRate.toFixed(1) + '%';
    document.getElementById('profitFactor').textContent = metrics.profitFactor.toFixed(2);
    document.getElementById('maxDrawdown').textContent = metrics.maxDrawdown.toFixed(1) + '%';
    document.getElementById('sharpeRatio').textContent = metrics.sharpeRatio.toFixed(2);
    document.getElementById('var95').textContent = metrics.var95.toFixed(1) + '%';
    document.getElementById('kellyFraction').textContent = metrics.kellyFraction.toFixed(1) + '%';
}

// 刷新风险指标
function refreshRiskMetrics() {
    MateTrade4.notifications.info('正在刷新风险指标...');
    
    // 模拟API调用
    setTimeout(() => {
        loadRiskMetrics();
        MateTrade4.notifications.success('风险指标已更新');
    }, 1000);
}

// 保存风险设置
function saveRiskSettings() {
    const settings = {
        maxRiskPerTrade: document.getElementById('maxRiskPerTrade').value,
        maxDailyLoss: document.getElementById('maxDailyLoss').value,
        maxDrawdown: document.getElementById('maxDrawdown').value,
        maxCorrelation: document.getElementById('maxCorrelation').value
    };
    
    // 这里可以发送到API保存
    console.log('保存风险设置:', settings);
    MateTrade4.notifications.success('风险管理设置已保存');
}

// 生成风险报告
function generateRiskReport() {
    MateTrade4.notifications.info('正在生成风险报告...');
    
    // 模拟生成报告
    setTimeout(() => {
        const reportData = {
            timestamp: new Date().toISOString(),
            accountSummary: {
                balance: 10000,
                equity: 10250,
                margin: 500,
                freeMargin: 9750
            },
            riskMetrics: {
                winRate: 65.2,
                profitFactor: 1.85,
                maxDrawdown: -8.5,
                sharpeRatio: 1.42
            },
            recommendations: [
                '建议降低单笔交易风险至1.5%',
                '考虑增加EUR/USD和GBP/USD的对冲',
                '当前保证金水平健康'
            ]
        };
        
        // 这里可以下载报告或显示在新窗口
        console.log('风险报告:', reportData);
        MateTrade4.notifications.success('风险报告生成完成');
    }, 2000);
}
</script>
{% endblock %}
