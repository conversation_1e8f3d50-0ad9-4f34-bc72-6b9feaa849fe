#!/usr/bin/env python3
"""
停止卡住的训练任务
"""

import requests
import json

def login_session():
    """登录并返回session"""
    session = requests.Session()
    login_data = {'username': 'admin', 'password': 'admin123'}
    
    try:
        response = session.post('http://127.0.0.1:5000/login', data=login_data)
        if response.status_code == 200:
            print("✅ 登录成功")
            return session
        else:
            print(f"❌ 登录失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return None

def stop_training(session, task_id):
    """停止训练任务"""
    print(f"\n🛑 停止训练任务: {task_id}")
    print("=" * 50)
    
    try:
        response = session.post(f'http://127.0.0.1:5000/api/deep-learning/training/{task_id}/stop')
        
        print(f"   HTTP状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"   停止响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            if result.get('success'):
                print(f"   ✅ 训练任务停止成功")
                return True
            else:
                print(f"   ❌ 停止失败: {result.get('error')}")
                return False
        else:
            print(f"   ❌ HTTP请求失败")
            print(f"   响应内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 停止训练异常: {e}")
        return False

def check_final_status(session, task_id):
    """检查最终状态"""
    print(f"\n🔍 检查最终状态")
    print("=" * 50)
    
    try:
        response = session.get(f'http://127.0.0.1:5000/api/deep-learning/training-progress/{task_id}')
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                progress_data = result['progress']
                status = progress_data.get('status')
                progress = progress_data.get('progress')
                
                print(f"   最终状态: {status}")
                print(f"   最终进度: {progress}%")
                
                if status in ['stopped', 'failed']:
                    print(f"   ✅ 训练已成功停止")
                    return True
                else:
                    print(f"   ⚠️ 训练可能仍在运行")
                    return False
            else:
                print(f"   ❌ 获取状态失败: {result.get('error')}")
                return False
        else:
            print(f"   ❌ 状态检查失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 状态检查异常: {e}")
        return False

def main():
    """主函数"""
    task_id = "3f0e9a54-2111-4ec0-849b-8b4640a6f268"
    
    print("🛑 停止卡住的训练任务")
    print("=" * 80)
    print(f"任务ID: {task_id}")
    
    # 1. 登录
    session = login_session()
    if not session:
        return
    
    # 2. 停止训练
    success = stop_training(session, task_id)
    
    if success:
        # 3. 等待一下，然后检查最终状态
        import time
        print(f"\n⏳ 等待3秒后检查最终状态...")
        time.sleep(3)
        
        check_final_status(session, task_id)
        
        print(f"\n✅ 训练任务处理完成")
        print(f"💡 下一步建议:")
        print(f"   1. 检查训练配置（批次大小、序列长度等）")
        print(f"   2. 减少数据量或简化模型")
        print(f"   3. 重新启动训练")
        print(f"   4. 监控系统资源使用情况")
    else:
        print(f"\n❌ 停止训练失败")
        print(f"💡 手动解决方案:")
        print(f"   1. 重启应用程序")
        print(f"   2. 检查数据库状态")
        print(f"   3. 清理卡住的进程")

if __name__ == '__main__':
    main()
