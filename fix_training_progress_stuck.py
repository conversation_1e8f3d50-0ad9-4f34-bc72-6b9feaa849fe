#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复AI推理模型训练进度卡住问题
"""

import sqlite3
import sys
import os
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def cleanup_stuck_training():
    """清理卡住的训练任务"""
    print("🧹 清理卡住的训练任务...")
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 查找卡住的任务（超过10分钟没更新的running任务）
        cursor.execute('''
            SELECT id, model_id, updated_at
            FROM training_tasks
            WHERE status = 'running'
        ''')
        
        stuck_tasks = []
        for task_id, model_id, updated_at in cursor.fetchall():
            try:
                updated_time = datetime.fromisoformat(updated_at)
                time_diff = datetime.now() - updated_time
                if time_diff.total_seconds() > 600:  # 10分钟
                    stuck_tasks.append((task_id, model_id, time_diff.total_seconds() / 60))
            except:
                stuck_tasks.append((task_id, model_id, 999))  # 时间解析失败，认为卡住
        
        if stuck_tasks:
            print(f"🔍 发现 {len(stuck_tasks)} 个卡住的训练任务:")
            for task_id, model_id, minutes in stuck_tasks:
                print(f"  任务: {task_id}")
                print(f"  模型: {model_id}")
                print(f"  卡住时间: {minutes:.1f}分钟")
                
                # 更新任务状态为失败
                cursor.execute('''
                    UPDATE training_tasks
                    SET status = 'failed', 
                        logs = '{"stage": "cleanup", "message": "训练任务卡住，自动清理"}'
                    WHERE id = ?
                ''', (task_id,))
                
                print(f"  ✅ 已标记为失败")
                print()
            
            conn.commit()
            print(f"✅ 清理完成，共处理 {len(stuck_tasks)} 个卡住的任务")
        else:
            print("📋 没有发现卡住的训练任务")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 清理卡住任务失败: {e}")
        return False

def fix_training_loop_issue():
    """修复训练循环问题"""
    print("\n🔧 修复训练循环问题...")
    
    try:
        # 检查深度学习服务中的训练循环
        with open('services/deep_learning_service.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否有死循环或阻塞问题
        issues_found = []
        
        # 检查1: 数据获取是否有超时
        if 'while time.time() - start_time < timeout' in content:
            print("✅ 数据获取有超时机制")
        else:
            issues_found.append("数据获取缺少超时机制")
        
        # 检查2: 训练循环是否有心跳更新
        if 'heartbeat_interval' in content:
            print("✅ 训练循环有心跳机制")
        else:
            issues_found.append("训练循环缺少心跳机制")
        
        # 检查3: 是否有异常处理
        if 'except Exception as e:' in content:
            print("✅ 有异常处理机制")
        else:
            issues_found.append("缺少异常处理")
        
        if issues_found:
            print("⚠️ 发现以下问题:")
            for issue in issues_found:
                print(f"  - {issue}")
        else:
            print("✅ 训练循环代码检查通过")
        
        return len(issues_found) == 0
        
    except Exception as e:
        print(f"❌ 检查训练循环失败: {e}")
        return False

def add_training_timeout_mechanism():
    """添加训练超时机制"""
    print("\n🔧 添加训练超时机制...")
    
    try:
        # 读取深度学习服务文件
        with open('services/deep_learning_service.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找训练函数开始位置
        train_function_start = content.find('def _train_model_async(self, model_id: str, task_id: str, config: Dict[str, Any]):')
        
        if train_function_start == -1:
            print("❌ 未找到训练函数")
            return False
        
        # 添加超时检查机制
        timeout_check_code = '''
        # 添加训练总超时检查
        training_start_time = time.time()
        max_training_time = 3600 * 6  # 6小时最大训练时间
        
        def check_training_timeout():
            if time.time() - training_start_time > max_training_time:
                logger.error("❌ 训练超时，自动停止")
                raise TimeoutError("训练超时")
        '''
        
        # 在训练函数开始后插入超时检查
        function_body_start = content.find('try:', train_function_start)
        if function_body_start != -1:
            content = content[:function_body_start + 4] + timeout_check_code + content[function_body_start + 4:]
            
            # 在训练循环中添加超时检查
            epoch_loop_pattern = 'for epoch in range(start_epoch, epochs):'
            epoch_loop_pos = content.find(epoch_loop_pattern)
            if epoch_loop_pos != -1:
                # 在epoch循环开始后添加超时检查
                loop_body_start = content.find('try:', epoch_loop_pos)
                if loop_body_start != -1:
                    timeout_call = '\n                check_training_timeout()  # 检查训练超时\n'
                    content = content[:loop_body_start + 4] + timeout_call + content[loop_body_start + 4:]
            
            # 写回文件
            with open('services/deep_learning_service.py', 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✅ 已添加训练超时机制")
            return True
        else:
            print("❌ 未找到合适的插入位置")
            return False
        
    except Exception as e:
        print(f"❌ 添加超时机制失败: {e}")
        return False

def restart_training_service():
    """重启训练服务"""
    print("\n🔄 重启训练服务...")
    
    try:
        from services.deep_learning_service import deep_learning_service
        
        # 清理训练控制状态
        deep_learning_service.training_control.clear()
        deep_learning_service.training_tasks.clear()
        
        print("✅ 训练服务状态已清理")
        
        # 检查是否有活跃的训练任务需要重启
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, model_id FROM training_tasks
            WHERE status = 'running'
        ''')
        
        running_tasks = cursor.fetchall()
        conn.close()
        
        if running_tasks:
            print(f"⚠️ 发现 {len(running_tasks)} 个运行中的任务，建议手动重新启动")
            for task_id, model_id in running_tasks:
                print(f"  任务: {task_id}")
                print(f"  模型: {model_id}")
        else:
            print("📋 没有运行中的任务")
        
        return True
        
    except Exception as e:
        print(f"❌ 重启训练服务失败: {e}")
        return False

def test_training_progress_update():
    """测试训练进度更新"""
    print("\n🧪 测试训练进度更新...")
    
    try:
        from services.deep_learning_service import deep_learning_service
        
        # 创建一个测试任务
        test_task_id = "test-progress-update"
        
        # 测试进度更新
        print("🔄 测试进度更新功能...")
        deep_learning_service._update_task_progress(test_task_id, 50.0, 10, 0.5, 0.3)
        
        # 验证更新
        progress_result = deep_learning_service.get_training_progress(test_task_id)
        
        if progress_result.get('success'):
            print("✅ 进度更新功能正常")
            return True
        else:
            print(f"❌ 进度更新功能异常: {progress_result.get('error')}")
            return False
        
    except Exception as e:
        print(f"❌ 测试进度更新失败: {e}")
        return False

def main():
    """主修复函数"""
    print("🔧 修复AI推理模型训练进度卡住问题")
    print("=" * 60)
    
    success_count = 0
    total_steps = 5
    
    # 步骤1: 清理卡住的训练任务
    if cleanup_stuck_training():
        success_count += 1
    
    # 步骤2: 检查训练循环问题
    if fix_training_loop_issue():
        success_count += 1
    
    # 步骤3: 添加超时机制
    if add_training_timeout_mechanism():
        success_count += 1
    
    # 步骤4: 重启训练服务
    if restart_training_service():
        success_count += 1
    
    # 步骤5: 测试进度更新
    if test_training_progress_update():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"🎯 修复完成! 成功: {success_count}/{total_steps}")
    
    if success_count >= 4:
        print("✅ 主要修复步骤完成")
        print("\n💡 建议:")
        print("1. 重新启动应用程序")
        print("2. 重新开始AI模型训练")
        print("3. 监控训练进度是否正常更新")
        print("4. 如果仍有问题，检查MT5数据获取是否正常")
    else:
        print("⚠️ 修复未完全成功，请检查错误信息")

if __name__ == "__main__":
    main()
