<!DOCTYPE html>
<html>
<head>
    <title>悬崖勒马配置测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
    </style>
</head>
<body>
    <h1>悬崖勒马配置测试</h1>
    
    <div class="test-section">
        <h3>模拟AI推理交易区域</h3>
        <div class="form-check form-switch mb-2">
            <input class="form-check-input" type="checkbox" id="enableCliffBrake">
            <label class="form-check-label" for="enableCliffBrake">
                <strong>悬崖勒马</strong>
                <small class="text-muted d-block">连续2单亏损时，根据价格趋势反转交易方向</small>
            </label>
        </div>
        
        <select id="tradingPreset" onchange="applyTradingPreset()">
            <option value="custom">自定义配置</option>
            <option value="conservative">保守型</option>
            <option value="balanced" selected>平衡型</option>
            <option value="aggressive">激进型</option>
        </select>
        
        <button onclick="applyTradingPreset()">应用配置</button>
        <button onclick="testConfiguration()">测试当前配置</button>
    </div>
    
    <div class="test-section">
        <h3>模拟回测区域</h3>
        <div class="form-check mb-2">
            <input class="form-check-input" type="checkbox" id="backtestCliffBrake">
            <label class="form-check-label" for="backtestCliffBrake">
                <strong>悬崖勒马</strong>
                <small class="text-muted d-block">连续2单亏损时，根据价格趋势反转交易方向</small>
            </label>
        </div>
        
        <button onclick="testBacktestConfiguration()">测试回测配置</button>
    </div>
    
    <div class="test-section">
        <h3>测试结果</h3>
        <div id="testResults"></div>
    </div>

    <script>
        function showResult(message, type = 'info') {
            const results = document.getElementById('testResults');
            const div = document.createElement('div');
            div.className = type;
            div.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            results.appendChild(div);
        }

        function applyTradingPreset() {
            const preset = document.getElementById('tradingPreset').value;
            
            switch(preset) {
                case 'conservative':
                    document.getElementById('enableCliffBrake').checked = false;
                    showResult('保守型配置：悬崖勒马关闭', 'info');
                    break;
                    
                case 'balanced':
                    document.getElementById('enableCliffBrake').checked = true;
                    showResult('平衡型配置：悬崖勒马开启', 'success');
                    break;
                    
                case 'aggressive':
                    document.getElementById('enableCliffBrake').checked = true;
                    showResult('激进型配置：悬崖勒马开启', 'success');
                    break;
                    
                case 'custom':
                    showResult('自定义配置：请手动设置', 'info');
                    break;
            }
        }

        function testConfiguration() {
            const cliffBrakeEnabled = document.getElementById('enableCliffBrake').checked;
            const preset = document.getElementById('tradingPreset').value;
            
            showResult(`AI推理交易配置测试：`, 'info');
            showResult(`  - 当前预设：${preset}`, 'info');
            showResult(`  - 悬崖勒马状态：${cliffBrakeEnabled ? '开启' : '关闭'}`, cliffBrakeEnabled ? 'success' : 'error');
            
            // 验证平衡型配置
            if (preset === 'balanced' && cliffBrakeEnabled) {
                showResult('✅ 平衡型配置正确：悬崖勒马已开启', 'success');
            } else if (preset === 'balanced' && !cliffBrakeEnabled) {
                showResult('❌ 平衡型配置错误：悬崖勒马应该开启', 'error');
            }
        }

        function testBacktestConfiguration() {
            const backtestCliffBrake = document.getElementById('backtestCliffBrake').checked;
            
            showResult(`回测配置测试：`, 'info');
            showResult(`  - 悬崖勒马状态：${backtestCliffBrake ? '开启' : '关闭'}`, backtestCliffBrake ? 'success' : 'info');
            showResult('✅ 回测区域使用独立的ID：backtestCliffBrake', 'success');
        }

        // 页面加载时自动应用平衡型配置
        document.addEventListener('DOMContentLoaded', function() {
            showResult('页面加载完成，自动应用平衡型配置...', 'info');
            applyTradingPreset();
            
            // 验证ID唯一性
            const aiCliffBrake = document.getElementById('enableCliffBrake');
            const backtestCliffBrake = document.getElementById('backtestCliffBrake');
            
            if (aiCliffBrake && backtestCliffBrake) {
                showResult('✅ ID唯一性检查通过：两个悬崖勒马复选框使用不同ID', 'success');
            } else {
                showResult('❌ ID唯一性检查失败：缺少必要的元素', 'error');
            }
        });
    </script>
</body>
</html>
