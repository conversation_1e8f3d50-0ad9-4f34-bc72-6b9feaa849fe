#!/usr/bin/env python3
"""
诊断深度学习训练进度停滞问题
"""

import sqlite3
import requests
import json
import time
from datetime import datetime, timed<PERSON><PERSON>

def login_session():
    """创建登录会话"""
    session = requests.Session()
    
    try:
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post('http://127.0.0.1:5000/login', data=login_data)
        return session if response.status_code == 200 else None
            
    except Exception as e:
        print(f"❌ 登录失败: {e}")
        return None

def check_database_training_tasks():
    """检查数据库中的训练任务"""
    
    print("🗄️ 检查数据库中的训练任务")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 查找所有训练任务，按创建时间排序
        cursor.execute("""
            SELECT id, model_id, status, progress, current_epoch, total_epochs,
                   train_loss, val_loss, created_at, updated_at, started_at
            FROM training_tasks 
            ORDER BY created_at DESC
            LIMIT 10
        """)
        
        tasks = cursor.fetchall()
        
        if tasks:
            print(f"📊 发现 {len(tasks)} 个训练任务:")
            
            current_time = datetime.now()
            active_tasks = []
            
            for i, task in enumerate(tasks):
                (task_id, model_id, status, progress, current_epoch, total_epochs,
                 train_loss, val_loss, created_at, updated_at, started_at) = task
                
                print(f"\n🔹 任务 {i+1}: {task_id[:8]}...")
                print(f"   状态: {status}")
                print(f"   进度: {progress}%")
                print(f"   轮次: {current_epoch}/{total_epochs}")
                print(f"   训练损失: {train_loss}")
                print(f"   验证损失: {val_loss}")
                print(f"   创建时间: {created_at}")
                print(f"   更新时间: {updated_at}")
                print(f"   开始时间: {started_at}")
                
                # 检查是否为活跃任务
                if status in ['running', 'pending', 'paused']:
                    active_tasks.append(task_id)
                    
                    # 检查更新时间
                    if updated_at:
                        try:
                            last_update = datetime.fromisoformat(updated_at.replace('Z', '+00:00').replace('+00:00', ''))
                            time_since_update = current_time - last_update
                            
                            print(f"   距离上次更新: {time_since_update}")
                            
                            if time_since_update > timedelta(minutes=5):
                                print(f"   ⚠️ 可能已卡住 (超过5分钟无更新)")
                            elif time_since_update > timedelta(minutes=2):
                                print(f"   ⚠️ 更新较慢 (超过2分钟)")
                            else:
                                print(f"   ✅ 更新正常")
                                
                        except Exception as e:
                            print(f"   ❌ 时间解析错误: {e}")
                    else:
                        print(f"   ⚠️ 没有更新时间记录")
            
            conn.close()
            return active_tasks
            
        else:
            print("❌ 没有发现训练任务")
            conn.close()
            return []
            
    except Exception as e:
        print(f"❌ 检查数据库失败: {e}")
        return []

def check_training_progress_api(task_ids):
    """检查训练进度API"""
    
    print(f"\n📡 检查训练进度API")
    print("=" * 60)
    
    session = login_session()
    if not session:
        print("❌ 无法登录")
        return
    
    for task_id in task_ids:
        print(f"\n🔍 检查任务: {task_id[:8]}...")
        
        try:
            response = session.get(f'http://127.0.0.1:5000/api/deep-learning/training-progress/{task_id}')
            
            print(f"   API响应状态: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get('success'):
                    progress_data = result['progress']
                    
                    print(f"   ✅ API响应成功")
                    print(f"   状态: {progress_data.get('status')}")
                    print(f"   进度: {progress_data.get('progress')}%")
                    print(f"   轮次: {progress_data.get('epoch')}/{progress_data.get('total_epochs')}")
                    print(f"   训练损失: {progress_data.get('train_loss')}")
                    print(f"   验证损失: {progress_data.get('val_loss')}")
                    
                    # 检查控制状态
                    control_response = session.get(f'http://127.0.0.1:5000/api/deep-learning/training/{task_id}/control-status')
                    
                    if control_response.status_code == 200:
                        control_result = control_response.json()
                        
                        if control_result.get('success'):
                            control_data = control_result['control']
                            print(f"   控制状态:")
                            print(f"     可控制: {control_data.get('can_control')}")
                            print(f"     已暂停: {control_data.get('is_paused')}")
                            print(f"     已停止: {control_data.get('is_stopped')}")
                            print(f"     任务状态: {control_data.get('task_status')}")
                        else:
                            print(f"   ❌ 控制状态API错误: {control_result.get('error')}")
                    else:
                        print(f"   ❌ 控制状态API请求失败: {control_response.status_code}")
                        
                else:
                    print(f"   ❌ API返回错误: {result.get('error')}")
            else:
                print(f"   ❌ API请求失败: {response.status_code}")
                print(f"   响应内容: {response.text[:200]}...")
                
        except Exception as e:
            print(f"   ❌ 检查API失败: {e}")

def monitor_progress_changes(task_id, duration=60):
    """监控进度变化"""
    
    print(f"\n📊 监控进度变化 (任务: {task_id[:8]}..., 时长: {duration}秒)")
    print("=" * 60)
    
    session = login_session()
    if not session:
        print("❌ 无法登录")
        return False
    
    progress_history = []
    
    for i in range(duration // 5):  # 每5秒检查一次
        try:
            response = session.get(f'http://127.0.0.1:5000/api/deep-learning/training-progress/{task_id}')
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get('success'):
                    progress_data = result['progress']
                    
                    current_progress = progress_data.get('progress', 0)
                    current_epoch = progress_data.get('epoch', 0)
                    current_status = progress_data.get('status', 'unknown')
                    train_loss = progress_data.get('train_loss')
                    val_loss = progress_data.get('val_loss')
                    
                    timestamp = datetime.now().strftime("%H:%M:%S")
                    
                    progress_entry = {
                        'time': timestamp,
                        'progress': current_progress,
                        'epoch': current_epoch,
                        'status': current_status,
                        'train_loss': train_loss,
                        'val_loss': val_loss
                    }
                    
                    progress_history.append(progress_entry)
                    
                    print(f"   [{timestamp}] 进度: {current_progress}%, 轮次: {current_epoch}, 状态: {current_status}")
                    
                    if train_loss is not None:
                        print(f"   [{timestamp}] 损失: 训练={train_loss:.4f}, 验证={val_loss:.4f if val_loss else 'N/A'}")
                    
                    # 检查训练是否完成
                    if current_status in ['completed', 'failed', 'stopped']:
                        print(f"   [{timestamp}] 🏁 训练已结束: {current_status}")
                        break
                        
                else:
                    print(f"   [{datetime.now().strftime('%H:%M:%S')}] ❌ API错误: {result.get('error')}")
            else:
                print(f"   [{datetime.now().strftime('%H:%M:%S')}] ❌ HTTP错误: {response.status_code}")
            
            time.sleep(5)
            
        except Exception as e:
            print(f"   [{datetime.now().strftime('%H:%M:%S')}] ❌ 监控异常: {e}")
            time.sleep(5)
    
    # 分析进度变化
    print(f"\n📈 进度变化分析:")
    
    if len(progress_history) >= 2:
        first_progress = progress_history[0]['progress']
        last_progress = progress_history[-1]['progress']
        progress_change = last_progress - first_progress
        
        first_epoch = progress_history[0]['epoch']
        last_epoch = progress_history[-1]['epoch']
        epoch_change = last_epoch - first_epoch
        
        print(f"   监控时长: {len(progress_history) * 5}秒")
        print(f"   进度变化: {first_progress}% -> {last_progress}% (变化: {progress_change}%)")
        print(f"   轮次变化: {first_epoch} -> {last_epoch} (变化: {epoch_change}轮)")
        
        if progress_change > 0:
            print(f"   ✅ 训练正在进行，进度正常更新")
            return True
        elif epoch_change > 0:
            print(f"   ✅ 训练正在进行，轮次正常更新")
            return True
        else:
            print(f"   ❌ 训练可能卡住，进度和轮次都没有变化")
            return False
    else:
        print(f"   ⚠️ 监控数据不足，无法判断")
        return False

def check_system_resources():
    """检查系统资源"""
    
    print(f"\n🖥️ 检查系统资源")
    print("=" * 60)
    
    session = login_session()
    if not session:
        print("❌ 无法登录")
        return
    
    try:
        # 检查GPU状态
        response = session.get('http://127.0.0.1:5000/api/deep-learning/gpu-status')
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                gpu_status = result.get('gpu_status', {})
                
                print(f"📊 GPU状态:")
                print(f"   GPU可用: {gpu_status.get('gpu_available')}")
                print(f"   GPU名称: {gpu_status.get('gpu_name')}")
                print(f"   总内存: {gpu_status.get('memory_total', 0):.1f} GB")
                print(f"   已用内存: {gpu_status.get('memory_used', 0):.1f} GB")
                print(f"   内存使用率: {gpu_status.get('memory_usage_percent', 0):.1f}%")
                print(f"   GPU使用率: {gpu_status.get('gpu_utilization', 0):.1f}%")
                print(f"   温度: {gpu_status.get('temperature', 0):.1f}°C")
                print(f"   功耗: {gpu_status.get('power_usage', 0):.0f}W")
                
                # 检查是否有资源问题
                memory_usage = gpu_status.get('memory_usage_percent', 0)
                gpu_utilization = gpu_status.get('gpu_utilization', 0)
                temperature = gpu_status.get('temperature', 0)
                
                if memory_usage > 90:
                    print(f"   ⚠️ GPU内存使用率过高: {memory_usage}%")
                elif memory_usage < 10:
                    print(f"   ⚠️ GPU内存使用率过低: {memory_usage}% (可能训练未运行)")
                else:
                    print(f"   ✅ GPU内存使用正常")
                
                if gpu_utilization < 10:
                    print(f"   ⚠️ GPU使用率过低: {gpu_utilization}% (可能训练卡住)")
                else:
                    print(f"   ✅ GPU使用率正常")
                
                if temperature > 80:
                    print(f"   ⚠️ GPU温度过高: {temperature}°C")
                else:
                    print(f"   ✅ GPU温度正常")
                    
            else:
                print(f"❌ GPU状态API错误: {result.get('error')}")
        else:
            print(f"❌ GPU状态API请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 检查系统资源失败: {e}")

def main():
    """主函数"""
    
    print("🔧 深度学习训练进度停滞问题诊断")
    print("=" * 80)
    
    # 1. 检查数据库中的训练任务
    active_tasks = check_database_training_tasks()
    
    if not active_tasks:
        print(f"\n❌ 没有发现活跃的训练任务")
        print(f"💡 建议:")
        print(f"• 启动一个新的训练任务")
        print(f"• 检查之前的训练是否已完成")
        return
    
    # 2. 检查训练进度API
    check_training_progress_api(active_tasks)
    
    # 3. 监控第一个活跃任务的进度变化
    if active_tasks:
        first_task = active_tasks[0]
        progress_changing = monitor_progress_changes(first_task, duration=60)
        
        if not progress_changing:
            print(f"\n⚠️ 检测到训练进度停滞")
            
            # 4. 检查系统资源
            check_system_resources()
            
            print(f"\n💡 可能的解决方案:")
            print(f"• 重启训练任务")
            print(f"• 检查GPU资源使用情况")
            print(f"• 减少批次大小或序列长度")
            print(f"• 检查数据加载是否正常")
            print(f"• 重启应用程序")
        else:
            print(f"\n✅ 训练进度正常更新")
    
    print(f"\n📋 诊断总结")
    print("=" * 80)
    
    print(f"🔍 检查项目:")
    print(f"• 数据库训练任务状态")
    print(f"• 训练进度API响应")
    print(f"• 实时进度变化监控")
    print(f"• 系统资源使用情况")
    
    print(f"\n🎯 下一步建议:")
    print(f"• 如果进度停滞，尝试重启训练")
    print(f"• 关注GPU使用率和内存使用")
    print(f"• 检查训练日志获取详细信息")
    print(f"• 考虑调整训练参数")

if __name__ == '__main__':
    main()
