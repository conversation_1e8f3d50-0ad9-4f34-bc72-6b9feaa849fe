{% extends "base.html" %}

{% block title %}智能体交易-真实{% endblock %}

{% block extra_css %}
<style>
/* 风险控制设置区域样式 */
.risk-control-left {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    border: 1px solid #e9ecef;
    height: 100%;
}

.risk-control-right {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    border: 1px solid #e9ecef;
    height: 100%;
}

.profit-loss-control {
    background: linear-gradient(135deg, #e0f7fa 0%, #ffffff 100%);
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    border: 1px solid #b2ebf2;
    height: 100%;
}

/* 区域标题样式 */
.area-title {
    font-size: 1rem;
    font-weight: 700;
    color: #495057;
    margin-bottom: 1rem;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    text-align: center;
    letter-spacing: 0.5px;
}

.area-title.left {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
}

.area-title.right {
    background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
    color: white;
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}

.area-title.profit-loss {
    background: linear-gradient(135deg, #00bcd4 0%, #26c6da 100%);
    color: white;
    box-shadow: 0 2px 4px rgba(0, 188, 212, 0.3);
}

/* 表单控件样式优化 */
.risk-control-left .form-control,
.risk-control-right .form-control,
.risk-control-left .form-select,
.risk-control-right .form-select,
.profit-loss-control .form-control,
.profit-loss-control .form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
    font-weight: 500;
}

.risk-control-left .form-control:focus,
.risk-control-right .form-control:focus,
.risk-control-left .form-select:focus,
.risk-control-right .form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.15);
    transform: translateY(-1px);
}

.profit-loss-control .form-control:focus,
.profit-loss-control .form-select:focus {
    border-color: #00bcd4;
    box-shadow: 0 0 0 0.2rem rgba(0, 188, 212, 0.15);
    transform: translateY(-1px);
}

.risk-control-left .input-group-text {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 0;
    font-weight: 600;
    color: #6c757d;
}

/* 标签样式 */
.risk-control-left .form-label,
.risk-control-right .form-label,
.profit-loss-control .form-label {
    color: #495057;
    margin-bottom: 0.5rem;
}

/* 小文本样式 */
.risk-control-left small,
.risk-control-right small,
.profit-loss-control small {
    color: #6c757d;
    font-size: 0.8rem;
}

/* 卡片悬停效果 */
.risk-control-left:hover,
.risk-control-right:hover,
.profit-loss-control:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0,0,0,0.12);
}

/* 输入组样式 */
.input-group-lg .input-group-text {
    padding: 0.75rem 1rem;
    font-size: 1rem;
}

/* 响应式调整 */
@media (max-width: 767px) {
    .risk-control-left,
    .risk-control-right,
    .profit-loss-control {
        margin-bottom: 1.5rem;
        height: auto;
    }
}

@media (max-width: 576px) {
    .risk-control-left,
    .risk-control-right,
    .profit-loss-control {
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .area-title {
        font-size: 0.9rem;
        padding: 0.5rem 0.75rem;
    }

    .input-group-lg .form-control,
    .input-group-lg .form-select {
        font-size: 0.9rem;
        padding: 0.5rem 0.75rem;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-brain text-warning"></i> 智能体交易-真实</h2>
                <div class="d-flex align-items-center">
                    <span class="badge bg-warning text-dark me-2">真实交易</span>
                    <span class="badge bg-info me-2" id="connectionStatus">MT5检查中...</span>
                    <span class="badge bg-secondary me-2" id="accountName" style="display: none;">账户: 未知</span>
                    <button id="connectBtn" class="btn btn-sm btn-outline-primary me-1" onclick="connectMT5()" style="display: none;">
                        <i class="fas fa-plug"></i> 连接
                    </button>
                    <button id="reconnectBtn" class="btn btn-sm btn-outline-warning me-1" onclick="reconnectMT5()" style="display: none;">
                        <i class="fas fa-sync"></i> 重连
                    </button>
                    <div class="form-check form-switch ms-2">
                        <input class="form-check-input" type="checkbox" id="autoConnectSwitch" checked>
                        <label class="form-check-label small" for="autoConnectSwitch">自动连接</label>
                    </div>
                    <div class="ms-2" id="connectionDetails" style="display: none;">
                        <small class="text-muted" id="connectionInfo"></small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 警告提示 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-danger">
                <h5><i class="fas fa-exclamation-triangle"></i> 重要提醒</h5>
                <p><strong>这是真实交易模式，将使用真实资金进行交易！</strong></p>
                <ul class="mb-0">
                    <li>所有交易都会在您的MT5真实账户中执行</li>
                    <li>请确保您已充分了解风险并做好资金管理</li>
                    <li>建议先在模拟环境中测试策略</li>
                    <li>系统会根据AI分析和策略进行自动交易</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- 智能体状态面板 -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h6><i class="fas fa-robot"></i> 智能体状态</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-success" id="startAgentBtn" onclick="startAgent()">
                            <i class="fas fa-play"></i> 启动智能体
                        </button>
                        <button class="btn btn-danger" id="stopAgentBtn" onclick="stopAgent()" disabled>
                            <i class="fas fa-stop"></i> 停止智能体
                        </button>
                        <button class="btn btn-warning" id="pauseAgentBtn" onclick="pauseAgent()" disabled>
                            <i class="fas fa-pause"></i> 暂停交易
                        </button>
                        <button class="btn btn-dark" id="forceStopBtn" onclick="forceStopAllAgents()">
                            <i class="fas fa-exclamation-triangle"></i> 紧急停止
                        </button>
                    </div>
                    <div class="mt-3">
                        <small class="text-muted">状态：</small>
                        <span class="badge bg-secondary" id="agentStatus">未启动</span>
                    </div>
                    <div class="mt-2">
                        <small class="text-muted">运行时间：</small>
                        <span id="runningTime">00:00:00</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h6><i class="fas fa-chart-line"></i> 交易统计</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h5 class="text-success mb-0" id="totalTrades">0</h5>
                                <small class="text-muted">总交易</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h5 class="text-primary mb-0" id="winRate">0%</h5>
                            <small class="text-muted">胜率</small>
                        </div>
                    </div>
                    <hr>
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h6 class="text-success mb-0" id="totalProfit">$0.00</h6>
                                <small class="text-muted">总盈亏</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h6 class="text-warning mb-0" id="todayProfit">$0.00</h6>
                            <small class="text-muted">今日盈亏</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h6><i class="fas fa-brain"></i> AI状态</h6>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <small class="text-muted">当前AI模型：</small>
                        <div id="currentAIModel" class="fw-bold">未选择</div>
                    </div>
                    <div class="mb-2">
                        <small class="text-muted">AI策略：</small>
                        <div id="currentAIStrategy" class="fw-bold">未选择</div>
                    </div>
                    <div class="mb-2">
                        <small class="text-muted">最后分析：</small>
                        <div id="lastAnalysis" class="small">等待中...</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h6><i class="fas fa-cog"></i> 风险控制</h6>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <small class="text-muted">最大风险：</small>
                        <div id="maxRisk" class="fw-bold">2%</div>
                    </div>
                    <div class="mb-2">
                        <small class="text-muted">单笔限额：</small>
                        <div id="maxTradeSize" class="fw-bold">0.1手</div>
                    </div>
                    <div class="mb-2">
                        <small class="text-muted">止损设置：</small>
                        <div id="stopLoss" class="fw-bold">50点</div>
                    </div>
                    <div class="mb-2">
                        <small class="text-muted">止盈设置：</small>
                        <div id="takeProfit" class="fw-bold">100点</div>
                    </div>
                    <div class="mb-2">
                        <small class="text-muted">订单限制：</small>
                        <div id="orderLimitDisplay" class="fw-bold">20单/轮</div>
                    </div>
                    <div class="mb-2">
                        <small class="text-muted">自动停止：</small>
                        <div id="autoStopDisplay" class="fw-bold">5轮后</div>
                    </div>
                    <div class="mb-2">
                        <small class="text-muted">交易时段：</small>
                        <div id="tradingTimeDisplay" class="fw-bold">亚洲时段</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 配置面板 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-cogs"></i> 智能体配置</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">AI模型选择</label>
                                <select class="form-select" id="aiModelSelect">
                                    <option value="">请选择AI模型</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">AI策略选择</label>

                                <!-- 选中的AI策略显示区域 -->
                                <div id="selectedStrategiesDisplay" class="border rounded p-2 mb-2" style="min-height: 38px; background-color: #f8f9fa; display: none;">
                                    <div class="d-flex flex-wrap gap-1" id="selectedStrategiesTags">
                                        <!-- 选中的AI策略标签将在这里显示 -->
                                    </div>
                                    <button type="button" class="btn btn-sm btn-outline-primary mt-1" onclick="toggleStrategySelector()">
                                        <i class="fas fa-edit"></i> 修改选择
                                    </button>
                                </div>

                                <!-- AI策略选择器 -->
                                <div id="strategySelectorContainer">
                                    <div class="border rounded p-2" style="background-color: #f8f9fa;">
                                        <div class="mb-2">
                                            <small class="text-muted fw-bold">
                                                <i class="fas fa-mouse-pointer"></i>
                                                点击策略名称进行多选（无需按Ctrl）
                                            </small>
                                        </div>
                                        <div id="strategyCheckboxContainer">
                                            <!-- 策略复选框将通过JavaScript动态加载 -->
                                        </div>
                                    </div>
                                    <div class="mt-2">
                                        <button type="button" class="btn btn-sm btn-success" onclick="confirmStrategySelection()">
                                            <i class="fas fa-check"></i> 确认选择
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-secondary ms-1" onclick="selectAllStrategies()">
                                            <i class="fas fa-check-double"></i> 全选
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-secondary ms-1" onclick="clearAllStrategies()">
                                            <i class="fas fa-times"></i> 清空
                                        </button>
                                    </div>

                                    <!-- 保留原始select作为数据存储，但隐藏 -->
                                    <select class="form-select d-none" id="aiStrategySelect" multiple>
                                        <!-- 策略选项将通过JavaScript动态加载 -->
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">交易货币对</label>

                                <!-- 选中的货币对显示区域 -->
                                <div id="selectedSymbolsDisplay" class="border rounded p-2 mb-2" style="min-height: 38px; background-color: #f8f9fa; display: none;">
                                    <div class="d-flex flex-wrap gap-1" id="selectedSymbolsTags">
                                        <!-- 选中的货币对标签将在这里显示 -->
                                    </div>
                                    <button type="button" class="btn btn-sm btn-outline-primary mt-1" onclick="toggleSymbolSelector()">
                                        <i class="fas fa-edit"></i> 修改选择
                                    </button>
                                </div>

                                <!-- 货币对选择器 -->
                                <div id="symbolSelectorContainer">
                                    <select class="form-select" id="tradingSymbols" multiple onchange="updateSelectedSymbols()">
                                        <option value="EURUSD">EURUSD</option>
                                        <option value="GBPUSD">GBPUSD</option>
                                        <option value="USDJPY">USDJPY</option>
                                        <option value="AUDUSD">AUDUSD</option>
                                        <option value="USDCAD">USDCAD</option>
                                        <option value="XAUUSD">XAUUSD</option>
                                        <option value="XAGUSD">XAGUSD (白银)</option>
                                        <option value="USOIL">USOIL (原油)</option>
                                        <option value="US30">US30 (道琼斯)</option>
                                        <option value="NAS100">NAS100 (纳斯达克)</option>
                                    </select>
                                    <small class="text-muted">按住Ctrl可多选</small>
                                    <button type="button" class="btn btn-sm btn-success mt-1" onclick="confirmSymbolSelection()">
                                        <i class="fas fa-check"></i> 确认选择
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">分析周期(分钟)</label>
                                <select class="form-select" id="analysisInterval">
                                    <option value="5">5分钟</option>
                                    <option value="15" selected>15分钟</option>
                                    <option value="30">30分钟</option>
                                    <option value="60">1小时</option>
                                </select>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">交易时间段</label>
                                <select class="form-select" id="tradingTimeSlot">
                                    <option value="asia" selected>亚洲时段 (06:00-16:00) - 波动平稳</option>
                                    <option value="europe">欧洲时段 (14:00-24:00) - 活跃度提升</option>
                                    <option value="america">美洲时段 (20:00-04:00) - 波动剧烈</option>
                                    <option value="full">全时段 (24小时) - 周一至周五</option>
                                </select>
                                <small class="text-muted">超出时间段时智能体将暂停交易</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">风险事件考虑</label>
                                <div class="form-check mt-2">
                                    <input class="form-check-input" type="checkbox" id="considerRiskEvents" checked>
                                    <label class="form-check-label" for="considerRiskEvents">
                                        <i class="fas fa-exclamation-triangle text-warning"></i>
                                        是否考虑风险事件
                                    </label>
                                </div>
                                <small class="text-muted">启用后，AI将根据重要经济事件和市场风险调整交易策略</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">用户交易要求</label>
                                <textarea class="form-control" id="userRequirements" rows="3"
                                    placeholder="请描述您的交易偏好、风险承受能力、目标收益等..."></textarea>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="profit-loss-control">
                                    <div class="area-title profit-loss mb-3">
                                        <i class="fas fa-chart-line"></i> 止盈止损设置
                                    </div>
                                    <div class="row g-4 align-items-end">
                                        <div class="col-6">
                                            <label class="form-label small fw-semibold">止损点数</label>
                                            <input type="number" class="form-control form-control-lg" id="stopLossPoints" value="50" min="10" max="200" step="5"
                                                   title="止损点数说明：EURUSD/GBPUSD等主要货币对1点=0.0001，USDJPY等1点=0.01，XAUUSD等1点=0.1。50点约等于EURUSD的5个pip。">
                                            <small class="text-muted mt-1 d-block">1点=最小价格变动单位</small>
                                        </div>
                                        <div class="col-6">
                                            <label class="form-label small fw-semibold">止盈点数</label>
                                            <input type="number" class="form-control form-control-lg" id="takeProfitPoints" value="100" min="20" max="500" step="5"
                                                   title="止盈点数说明：EURUSD/GBPUSD等主要货币对1点=0.0001，USDJPY等1点=0.01，XAUUSD等1点=0.1。100点约等于EURUSD的10个pip。">
                                            <small class="text-muted mt-1 d-block">建议设为止损的1.5-2倍</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 智能建议说明 -->
                    <div class="row mb-3">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <h6 class="alert-heading mb-2">💡 智能建议</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <strong>止盈设置建议：</strong>
                                        <ul class="mb-2 small">
                                            <li><strong>保守策略：</strong>止盈 = 止损 × 1.5（如：止损50点，止盈75点）</li>
                                            <li><strong>平衡策略：</strong>止盈 = 止损 × 2.0（如：止损50点，止盈100点）← 默认</li>
                                            <li><strong>激进策略：</strong>止盈 = 止损 × 3.0（如：止损50点，止盈150点）</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <strong>根据时间段调整：</strong>
                                        <ul class="mb-0 small">
                                            <li><strong>亚洲时段：</strong>较小的止盈目标（1.5-2倍止损）</li>
                                            <li><strong>欧洲时段：</strong>中等的止盈目标（2-2.5倍止损）</li>
                                            <li><strong>美洲时段：</strong>较大的止盈目标（2.5-3倍止损）</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 风险控制设置行 -->
                    <div class="row">
                        <!-- 左侧区域：基础风险设置 -->
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="risk-control-left">
                                    <div class="area-title left mb-3">
                                        <i class="fas fa-shield-alt"></i> 基础风险设置
                                    </div>
                                    <div class="row g-4 align-items-end">
                                        <div class="col-6">
                                            <label class="form-label small fw-semibold">最大风险(%)</label>
                                            <input type="number" class="form-control form-control-lg" id="maxRiskPercent" value="2" min="0.1" max="10" step="0.1">
                                            <small class="text-muted mt-1 d-block">控制整体风险暴露</small>
                                        </div>
                                        <div class="col-6">
                                            <label class="form-label small fw-semibold">单笔手数区间</label>
                                            <div class="input-group input-group-lg">
                                                <input type="number" class="form-control text-center"
                                                       id="minLotSize" value="0.01" min="0.01" max="0.1" step="0.01"
                                                       onchange="validateLotSizeInput()" onblur="validateLotSizeInput()"
                                                       placeholder="0.01">
                                                <span class="input-group-text bg-light">
                                                    <i class="fas fa-minus text-muted"></i>
                                                </span>
                                                <input type="number" class="form-control text-center"
                                                       id="maxLotSize" value="0.02" min="0.01" max="0.1" step="0.01"
                                                       onchange="validateLotSizeInput()" onblur="validateLotSizeInput()"
                                                       placeholder="0.02">
                                            </div>
                                            <small class="text-muted mt-1 d-block">范围：0.01-0.1，必须是0.01的整数倍</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 右侧区域：交易控制设置 -->
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="risk-control-right">
                                    <div class="area-title right mb-3">
                                        <i class="fas fa-cogs"></i> 交易控制设置
                                    </div>
                                    <div class="row g-4 align-items-end">
                                        <div class="col-6">
                                            <label class="form-label small fw-semibold">一轮交易订单数量限制</label>
                                            <div class="input-group input-group-lg">
                                                <select class="form-select" id="orderLimitSelect" onchange="handleOrderLimitChange()">
                                                    <option value="10">10单</option>
                                                    <option value="20" selected>20单</option>
                                                    <option value="30">30单</option>
                                                    <option value="custom">自定义</option>
                                                </select>
                                                <input type="number" class="form-control" id="customOrderLimit"
                                                       value="20" min="1" max="100" step="1"
                                                       style="display: none;" placeholder="输入数量">
                                            </div>
                                            <small class="text-muted mt-1 d-block">限制每轮交易的最大订单数量</small>
                                        </div>
                                        <div class="col-6">
                                            <label class="form-label small fw-semibold">自动停止轮次</label>
                                            <div class="input-group input-group-lg">
                                                <select class="form-select" id="autoStopRounds" onchange="handleAutoStopChange()">
                                                    <option value="0">不自动停止</option>
                                                    <option value="1">1轮后停止</option>
                                                    <option value="2">2轮后停止</option>
                                                    <option value="3">3轮后停止</option>
                                                    <option value="5" selected>5轮后停止</option>
                                                    <option value="10">10轮后停止</option>
                                                    <option value="custom">自定义轮次</option>
                                                </select>
                                                <input type="number" class="form-control" id="customAutoStopRounds"
                                                       value="5" min="1" max="50" step="1"
                                                       style="display: none;" placeholder="输入轮次">
                                            </div>
                                            <small class="text-muted mt-1 d-block">执行指定轮次后自动停止新增订单</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="input-group">
                                <input type="text" class="form-control" id="configName" placeholder="配置名称（如：稳健策略、激进策略）">
                                <button class="btn btn-primary" onclick="saveConfiguration()">
                                    <i class="fas fa-save"></i> 保存配置
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="input-group">
                                <select class="form-select" id="savedConfigs">
                                    <option value="">选择已保存的配置</option>
                                </select>
                                <button class="btn btn-outline-info" onclick="previewSelectedConfiguration()">
                                    <i class="fas fa-eye"></i> 预览
                                </button>
                                <button class="btn btn-success" onclick="loadSelectedConfiguration()">
                                    <i class="fas fa-download"></i> 加载
                                </button>
                                <button class="btn btn-danger" onclick="deleteSelectedConfiguration()">
                                    <i class="fas fa-trash"></i> 删除
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 当前配置状态提示 -->
                    <div class="row mt-2">
                        <div class="col-12">
                            <div class="alert alert-info py-2" id="currentConfigStatus" style="display: none;">
                                <i class="fas fa-info-circle"></i>
                                <span id="currentConfigText">当前配置: 无</span>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-2">
                        <div class="col-md-8">
                            <small class="text-muted">
                                💡 提示：可以保存多个不同的交易配置，如"稳健策略"、"激进策略"、"黄金专用"等
                            </small>
                        </div>
                        <div class="col-md-4 text-end">
                            <button class="btn btn-outline-info btn-sm" onclick="loadPresetConfigurations()">
                                <i class="fas fa-magic"></i> 加载预设模板
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 持仓和交易历史 -->
    <div class="row mt-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="fas fa-wallet text-warning"></i> 当前持仓</h5>
                    <div>
                        <button class="btn btn-sm btn-outline-primary" onclick="refreshPositions()">
                            <i class="fas fa-sync"></i> 刷新
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="closeAllPositions()">
                            <i class="fas fa-times-circle"></i> 一键平仓
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div id="positionsContainer" style="max-height: 300px; overflow-y: auto;">
                        <div class="text-center text-muted py-3">
                            <i class="fas fa-spinner fa-spin"></i> 加载持仓中...
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="fas fa-history text-info"></i> 当天交易历史</h5>
                    <button class="btn btn-sm btn-outline-primary" onclick="refreshTradingHistory()">
                        <i class="fas fa-sync"></i> 刷新
                    </button>
                </div>
                <div class="card-body">
                    <div id="tradingHistoryContainer" style="max-height: 300px; overflow-y: auto;">
                        <div class="text-center text-muted py-3">
                            <i class="fas fa-spinner fa-spin"></i> 加载交易历史中...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- AI分析和交易日志 -->
    <div class="row mt-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-brain"></i> AI分析日志</h5>
                </div>
                <div class="card-body">
                    <div id="aiAnalysisLog" style="height: 400px; overflow-y: auto; background-color: #f8f9fa; padding: 10px; border-radius: 5px;">
                        <div class="text-muted text-center">等待AI分析...</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-list"></i> 交易执行日志</h5>
                </div>
                <div class="card-body">
                    <div id="tradingLog" style="height: 400px; overflow-y: auto; background-color: #f8f9fa; padding: 10px; border-radius: 5px;">
                        <div class="text-muted text-center">等待交易执行...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 全局变量
let agentRunning = false;
let agentStartTime = null;
let runningTimer = null;
let analysisTimer = null;

// 智能体状态持久化键名
const AGENT_STATUS_KEY = 'agentTradingRealStatus';

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 页面开始初始化...');

    // 先加载AI模型和策略选项
    loadAIModels();
    loadAIStrategies();

    // 等待一段时间确保下拉框选项加载完成
    setTimeout(() => {
        console.log('📋 开始处理配置...');

        // 检查是否有配置，如果没有则创建默认配置
        const savedConfigs = getSavedConfigurations();
        if (Object.keys(savedConfigs).length === 0) {
            console.log('🔧 没有找到配置，创建默认配置...');
            createDefaultConfiguration();
        }

        // 加载配置列表并自动加载第一个配置
        loadSavedConfigurationsList();

        // 验证手数输入
        validateLotSizeInput();

    }, 500); // 等待500ms确保选项加载完成

    // 初始化MT5连接
    initializeMT5Connection();

    // 初始化持仓和交易历史
    refreshPositions();
    refreshTradingHistory();

    updateDisplay();

    // 恢复智能体状态
    setTimeout(() => {
        restoreAgentStatus();
    }, 1000); // 延迟1秒确保页面完全加载

    console.log('✅ 页面初始化完成');
});

// 初始化MT5连接
function initializeMT5Connection() {
    console.log('🚀 初始化MT5连接...');

    // 首先检查连接状态
    checkMT5Connection();

    // 设置定期检查（每30秒）
    setInterval(checkMT5Connection, 30000);

    // 监听自动连接开关变化
    document.getElementById('autoConnectSwitch').addEventListener('change', function() {
        const autoConnect = this.checked;
        console.log(`🔧 自动连接设置: ${autoConnect ? '开启' : '关闭'}`);

        // 保存设置到本地存储
        localStorage.setItem('mt5AutoConnect', autoConnect);

        if (autoConnect) {
            // 如果开启自动连接且当前未连接，立即尝试连接
            const statusElement = document.getElementById('connectionStatus');
            if (statusElement.textContent !== 'MT5已连接') {
                console.log('🔄 开启自动连接，立即尝试连接...');
                setTimeout(() => {
                    connectMT5(true);
                }, 1000);
            }
        }
    });

    // 从本地存储恢复自动连接设置
    const savedAutoConnect = localStorage.getItem('mt5AutoConnect');
    if (savedAutoConnect !== null) {
        document.getElementById('autoConnectSwitch').checked = savedAutoConnect === 'true';
    }
}

// 加载AI模型
function loadAIModels() {
    console.log('🔄 开始加载AI模型...');

    fetch('/api/ai-models/list')
    .then(response => {
        console.log('📡 AI模型API响应状态:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('📊 AI模型API返回数据:', data);

        const select = document.getElementById('aiModelSelect');
        select.innerHTML = '<option value="">请选择AI模型</option>';

        if (data.success) {
            if (data.models && data.models.length > 0) {
                console.log(`✅ 找到 ${data.models.length} 个AI模型`);

                // 按来源分组显示
                const systemModels = data.models.filter(m => m.source === 'system_default');
                const userModels = data.models.filter(m => m.source === 'user_config');

                // 添加系统默认模型
                if (systemModels.length > 0) {
                    const systemGroup = document.createElement('optgroup');
                    systemGroup.label = '系统配置模型';

                    systemModels.forEach(model => {
                        const option = document.createElement('option');
                        option.value = model.name; // 使用模型名称作为value
                        option.setAttribute('data-model-id', model.id); // 保存ID作为属性
                        option.setAttribute('data-model-name', model.name);
                        option.textContent = `${model.name} (${model.provider})`;
                        if (model.status === 'active') {
                            option.textContent += ' ✓';
                        }
                        systemGroup.appendChild(option);

                        console.log(`  - 系统模型: ${model.name} (value: ${model.name})`);
                    });

                    select.appendChild(systemGroup);
                }

                // 添加用户配置模型
                if (userModels.length > 0) {
                    const userGroup = document.createElement('optgroup');
                    userGroup.label = '用户配置模型';

                    userModels.forEach(model => {
                        const option = document.createElement('option');
                        option.value = model.name; // 使用模型名称作为value
                        option.setAttribute('data-model-id', model.id); // 保存ID作为属性
                        option.setAttribute('data-model-name', model.name);
                        option.textContent = `${model.name} (${model.provider})`;
                        if (model.status === 'active') {
                            option.textContent += ' ✓';
                        }
                        userGroup.appendChild(option);

                        console.log(`  - 用户模型: ${model.name} (value: ${model.name})`);
                    });

                    select.appendChild(userGroup);
                }
            } else {
                console.log('⚠️ 没有找到AI模型配置');

                // 添加提示选项
                const option = document.createElement('option');
                option.value = '';
                option.textContent = '暂无AI模型配置，请到系统设置中配置';
                option.disabled = true;
                select.appendChild(option);
            }
        } else {
            console.error('❌ AI模型API返回失败:', data.error);

            // 添加错误提示选项
            const option = document.createElement('option');
            option.value = '';
            option.textContent = `加载失败: ${data.error}`;
            option.disabled = true;
            select.appendChild(option);
        }
    })
    .catch(error => {
        console.error('❌ 加载AI模型失败:', error);

        // 添加网络错误提示
        const select = document.getElementById('aiModelSelect');
        const option = document.createElement('option');
        option.value = '';
        option.textContent = '网络错误，无法加载AI模型';
        option.disabled = true;
        select.appendChild(option);
    });
}

// 加载AI策略
function loadAIStrategies() {
    console.log('🔄 开始加载AI策略...');

    fetch('/api/ai-strategies/trained')
    .then(response => {
        console.log('📡 AI策略API响应状态:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('📊 AI策略API返回数据:', data);

        const select = document.getElementById('aiStrategySelect');
        const checkboxContainer = document.getElementById('strategyCheckboxContainer');

        // 清空选择器和复选框容器
        select.innerHTML = '';
        checkboxContainer.innerHTML = '';

        if (data.success) {
            if (data.strategies && data.strategies.length > 0) {
                console.log(`✅ 找到 ${data.strategies.length} 个AI策略`);

                data.strategies.forEach(strategy => {
                    // 创建隐藏的option元素（用于数据存储）
                    const option = document.createElement('option');
                    option.value = strategy.id;
                    option.textContent = `${strategy.name} (${strategy.ai_model || 'Unknown'})`;
                    option.title = strategy.description || '';
                    select.appendChild(option);

                    // 创建可见的复选框界面
                    const checkboxDiv = document.createElement('div');
                    checkboxDiv.className = 'form-check mb-2';
                    checkboxDiv.innerHTML = `
                        <input class="form-check-input" type="checkbox" value="${strategy.id}"
                               id="strategy_${strategy.id}" onchange="handleStrategyCheckboxChange()">
                        <label class="form-check-label" for="strategy_${strategy.id}">
                            <strong>${strategy.name}</strong>
                            <small class="text-muted d-block">${strategy.ai_model || 'Unknown'} | ${strategy.description || '无描述'}</small>
                        </label>
                    `;
                    checkboxContainer.appendChild(checkboxDiv);

                    console.log(`  - ${strategy.name} (ID: ${strategy.id})`);
                });
            } else {
                console.log('⚠️ 没有找到已训练的AI策略');

                // 添加提示选项
                const option = document.createElement('option');
                option.value = '';
                option.textContent = '暂无已训练的AI策略，请先到AI训练页面训练策略';
                option.disabled = true;
                select.appendChild(option);
            }
        } else {
            console.error('❌ AI策略API返回失败:', data.error);

            // 添加错误提示选项
            const option = document.createElement('option');
            option.value = '';
            option.textContent = `加载失败: ${data.error}`;
            option.disabled = true;
            select.appendChild(option);
        }
    })
    .catch(error => {
        console.error('❌ 加载AI策略失败:', error);

        // 添加网络错误提示
        const select = document.getElementById('aiStrategySelect');
        const option = document.createElement('option');
        option.value = '';
        option.textContent = '网络错误，无法加载AI策略';
        option.disabled = true;
        select.appendChild(option);
    });
}

// 检查MT5连接状态
function checkMT5Connection() {
    console.log('🔄 检查MT5连接状态...');

    fetch('/api/mt5/status')
    .then(response => {
        console.log('📡 MT5状态API响应:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('📊 MT5状态数据:', data);

        const statusElement = document.getElementById('connectionStatus');
        const connectBtn = document.getElementById('connectBtn');
        const reconnectBtn = document.getElementById('reconnectBtn');

        if (data.success && data.connected) {
            statusElement.textContent = 'MT5已连接';
            statusElement.className = 'badge bg-success';
            connectBtn.style.display = 'none';
            reconnectBtn.style.display = 'inline-block';

            // 显示账户名称
            updateAccountName(data.account_info);

            console.log('✅ MT5连接正常');
        } else {
            statusElement.textContent = 'MT5未连接';
            statusElement.className = 'badge bg-danger';
            connectBtn.style.display = 'inline-block';
            reconnectBtn.style.display = 'none';

            // 隐藏账户名称
            hideAccountName();

            console.log('❌ MT5未连接');

            // 如果开启了自动连接，尝试自动连接
            const autoConnect = document.getElementById('autoConnectSwitch').checked;
            if (autoConnect) {
                console.log('🔄 自动连接MT5...');
                setTimeout(() => {
                    connectMT5(true); // 静默连接
                }, 2000); // 延迟2秒后尝试连接
            }
        }
    })
    .catch(error => {
        console.error('❌ 检查MT5连接失败:', error);
        const statusElement = document.getElementById('connectionStatus');
        const connectBtn = document.getElementById('connectBtn');
        const reconnectBtn = document.getElementById('reconnectBtn');

        statusElement.textContent = 'MT5连接异常';
        statusElement.className = 'badge bg-warning';
        connectBtn.style.display = 'inline-block';
        reconnectBtn.style.display = 'none';

        // 如果开启了自动连接，尝试自动连接
        const autoConnect = document.getElementById('autoConnectSwitch').checked;
        if (autoConnect) {
            console.log('🔄 连接异常，尝试自动连接MT5...');
            setTimeout(() => {
                connectMT5(true); // 静默连接
            }, 5000); // 延迟5秒后尝试连接
        }
    });
}

// 连接MT5
function connectMT5(silent = false) {
    if (!silent) {
        console.log('🔌 手动连接MT5...');
    }

    const statusElement = document.getElementById('connectionStatus');
    const connectBtn = document.getElementById('connectBtn');

    // 显示连接中状态
    statusElement.textContent = 'MT5连接中...';
    statusElement.className = 'badge bg-warning';
    connectBtn.disabled = true;

    fetch('/api/mt5/connect', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            auto_connect: true
        })
    })
    .then(response => response.json())
    .then(data => {
        console.log('📊 MT5连接结果:', data);

        if (data.success) {
            statusElement.textContent = 'MT5已连接';
            statusElement.className = 'badge bg-success';
            connectBtn.style.display = 'none';
            document.getElementById('reconnectBtn').style.display = 'inline-block';

            if (!silent) {
                logAI('MT5连接成功');
            }
            console.log('✅ MT5连接成功');
        } else {
            statusElement.textContent = 'MT5连接失败';
            statusElement.className = 'badge bg-danger';

            if (!silent) {
                logAI(`MT5连接失败: ${data.error}`);
                alert(`MT5连接失败: ${data.error}`);
            }
            console.log(`❌ MT5连接失败: ${data.error}`);
        }
    })
    .catch(error => {
        console.error('❌ MT5连接异常:', error);
        statusElement.textContent = 'MT5连接异常';
        statusElement.className = 'badge bg-warning';

        if (!silent) {
            logAI(`MT5连接异常: ${error.message}`);
            alert(`MT5连接异常: ${error.message}`);
        }
    })
    .finally(() => {
        connectBtn.disabled = false;
    });
}

// 重连MT5
function reconnectMT5() {
    console.log('🔄 重连MT5...');

    const statusElement = document.getElementById('connectionStatus');
    const reconnectBtn = document.getElementById('reconnectBtn');

    // 显示重连中状态
    statusElement.textContent = 'MT5重连中...';
    statusElement.className = 'badge bg-warning';
    reconnectBtn.disabled = true;

    // 先断开连接
    fetch('/api/mt5/disconnect', {
        method: 'POST'
    })
    .then(() => {
        // 延迟1秒后重新连接
        setTimeout(() => {
            connectMT5();
        }, 1000);
    })
    .catch(error => {
        console.error('❌ MT5断开连接失败:', error);
        // 即使断开失败，也尝试重新连接
        setTimeout(() => {
            connectMT5();
        }, 1000);
    })
    .finally(() => {
        reconnectBtn.disabled = false;
    });
}

// 启动智能体
function startAgent() {
    console.log('🚀 开始启动智能体...');

    // 确保复选框状态同步到select元素
    handleStrategyCheckboxChange();

    if (!validateConfiguration()) {
        console.log('❌ 配置验证失败');
        return;
    }

    const config = getConfiguration();
    console.log('📋 智能体配置:', config);

    fetch('/api/agent-trading-real/start', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(config)
    })
    .then(response => {
        console.log('📡 启动API响应状态:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('📊 启动API返回数据:', data);

        if (data.success) {
            agentRunning = true;
            agentStartTime = new Date();
            updateAgentStatus('运行中', 'bg-success');
            startTimers();
            saveAgentStatus(); // 保存状态
            logAI('智能体已启动，开始AI分析和交易执行');
            logTrading('智能体交易系统启动成功');
            console.log('✅ 智能体启动成功');
        } else {
            console.error('❌ 启动失败:', data.error);
            alert('启动失败: ' + data.error);
            logAI('智能体启动失败: ' + data.error);
        }
    })
    .catch(error => {
        console.error('❌ 启动智能体异常:', error);
        alert('启动失败: ' + error.message);
        logAI('智能体启动异常: ' + error.message);
    });
}

// 停止智能体
function stopAgent() {
    fetch('/api/agent-trading-real/stop', {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            agentRunning = false;
            agentStartTime = null;
            updateAgentStatus('已停止', 'bg-secondary');
            stopTimers();
            saveAgentStatus(); // 保存状态
            logAI('智能体已停止');
            logTrading('智能体交易系统已停止');
        } else {
            alert('停止失败: ' + data.error);
        }
    })
    .catch(error => {
        console.error('停止智能体失败:', error);
        alert('停止失败: ' + error.message);
    });
}

// 暂停智能体
function pauseAgent() {
    fetch('/api/agent-trading-real/pause', {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateAgentStatus('已暂停', 'bg-warning');
            logAI('智能体已暂停交易');
            logTrading('交易执行已暂停');
        } else {
            alert('暂停失败: ' + data.error);
        }
    })
    .catch(error => {
        console.error('暂停智能体失败:', error);
        alert('暂停失败: ' + error.message);
    });
}

// 紧急停止所有智能体
function forceStopAllAgents() {
    if (!confirm('⚠️ 紧急停止将强制终止所有智能体！\n这是紧急情况下使用的功能，确定要继续吗？')) {
        return;
    }

    const forceStopBtn = document.getElementById('forceStopBtn');
    const originalText = forceStopBtn.innerHTML;
    forceStopBtn.disabled = true;
    forceStopBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 强制停止中...';

    fetch('/api/agent-trading-real/force-stop-all', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateAgentStatus('已强制停止', 'bg-dark');
            logAI(`🚨 紧急停止成功：已停止 ${data.stopped_count} 个智能体`);
            logTrading('所有智能体已被强制停止');

            // 重置所有按钮状态
            document.getElementById('startAgentBtn').disabled = false;
            document.getElementById('stopAgentBtn').disabled = true;
            document.getElementById('pauseAgentBtn').disabled = true;

            // 2秒后刷新页面
            setTimeout(() => {
                location.reload();
            }, 2000);
        } else {
            alert('紧急停止失败: ' + data.error);
        }
    })
    .catch(error => {
        console.error('紧急停止失败:', error);
        alert('紧急停止失败: ' + error.message);
    })
    .finally(() => {
        forceStopBtn.disabled = false;
        forceStopBtn.innerHTML = originalText;
    });
}

// 验证配置
function validateConfiguration() {
    const aiModel = document.getElementById('aiModelSelect').value;

    // 检查AI策略选择（优先检查复选框，然后检查select）
    const checkboxes = document.querySelectorAll('#strategyCheckboxContainer input[type="checkbox"]:checked');
    const strategySelect = document.getElementById('aiStrategySelect');
    const selectedStrategies = Array.from(strategySelect.selectedOptions).filter(option => option.value);

    // 使用复选框数量或select数量（取较大值）
    const strategyCount = Math.max(checkboxes.length, selectedStrategies.length);

    const symbols = document.getElementById('tradingSymbols').selectedOptions;
    const requirements = document.getElementById('userRequirements').value.trim();

    console.log(`🔍 配置验证 - AI策略数量: 复选框${checkboxes.length}个, Select${selectedStrategies.length}个`);

    if (!aiModel) {
        alert('请选择AI模型');
        return false;
    }

    if (strategyCount === 0) {
        alert('请至少选择一个AI策略');
        return false;
    }
    
    if (symbols.length === 0) {
        alert('请选择至少一个交易货币对');
        return false;
    }
    
    if (!requirements) {
        alert('请输入用户交易要求');
        return false;
    }
    
    return true;
}

// 获取配置
function getConfiguration() {
    const symbols = Array.from(document.getElementById('tradingSymbols').selectedOptions).map(option => option.value);

    // 获取AI模型名称（现在value就是模型名称）
    const aiModelSelect = document.getElementById('aiModelSelect');
    const aiModelName = aiModelSelect.value;

    console.log(`🔍 获取配置 - AI模型: ${aiModelName}`);

    // 获取选中的AI策略（多选）
    const strategySelect = document.getElementById('aiStrategySelect');
    const selectedStrategies = Array.from(strategySelect.selectedOptions)
        .filter(option => option.value)
        .map(option => option.value);

    return {
        ai_model: aiModelName,
        ai_strategies: selectedStrategies, // 改为复数形式，支持多个策略
        trading_symbols: symbols,
        analysis_interval: parseInt(document.getElementById('analysisInterval').value),
        trading_time_slot: document.getElementById('tradingTimeSlot').value,
        user_requirements: document.getElementById('userRequirements').value.trim(),
        risk_settings: {
            max_risk_percent: parseFloat(document.getElementById('maxRiskPercent').value),
            min_lot_size: parseFloat(document.getElementById('minLotSize').value),
            max_lot_size: parseFloat(document.getElementById('maxLotSize').value),
            stop_loss_points: parseInt(document.getElementById('stopLossPoints').value),
            take_profit_points: parseInt(document.getElementById('takeProfitPoints').value),
            consider_risk_events: document.getElementById('considerRiskEvents').checked,
            order_limit_per_round: getOrderLimit(),
            auto_stop_rounds: getAutoStopRounds()
        }
    };
}

// 保存配置
function saveConfiguration() {
    const configName = document.getElementById('configName').value.trim();

    if (!configName) {
        alert('请输入配置名称');
        return;
    }

    const config = getConfiguration();
    config.name = configName;
    config.created_at = new Date().toISOString();

    // 获取现有配置
    const savedConfigs = getSavedConfigurations();

    // 检查是否已存在同名配置
    if (savedConfigs[configName]) {
        if (!confirm(`配置"${configName}"已存在，是否覆盖？`)) {
            return;
        }
    }

    // 保存配置
    savedConfigs[configName] = config;
    localStorage.setItem('agentTradingRealConfigs', JSON.stringify(savedConfigs));

    // 更新配置列表（不自动加载）
    loadSavedConfigurationsList(false);

    // 清空配置名称输入框
    document.getElementById('configName').value = '';

    alert(`配置"${configName}"已保存`);
    logAI(`配置已保存: ${configName}`);
}

// 加载选中的配置
function loadSelectedConfiguration() {
    const selectedConfig = document.getElementById('savedConfigs').value;

    if (!selectedConfig) {
        alert('请选择要加载的配置');
        return;
    }

    loadConfiguration(selectedConfig);
}

// 预览选中的配置
function previewSelectedConfiguration() {
    const selectedConfig = document.getElementById('savedConfigs').value;

    if (!selectedConfig) {
        alert('请选择要预览的配置');
        return;
    }

    const savedConfigs = getSavedConfigurations();
    const config = savedConfigs[selectedConfig];

    if (!config) {
        alert(`配置"${selectedConfig}"不存在`);
        return;
    }

    // 构建预览内容
    const previewContent = `
配置名称：${config.name}
创建时间：${config.created_at ? new Date(config.created_at).toLocaleString() : '未知'}

AI模型：${config.ai_model || '未设置'}
AI策略：${config.ai_strategies ? (Array.isArray(config.ai_strategies) ? config.ai_strategies.join(', ') : config.ai_strategies) : '未设置'}
分析间隔：${config.analysis_interval || 15} 分钟
交易时段：${config.trading_time_slot ? getTimeSlotName(config.trading_time_slot) : '亚洲时段'}

交易货币对：${config.trading_symbols ? config.trading_symbols.join(', ') : '未设置'}

用户要求：
${config.user_requirements || '未设置'}

风险设置：
- 最大风险：${config.risk_settings?.max_risk_percent || 2}%
- 手数区间：${config.risk_settings?.min_lot_size || 0.01} - ${config.risk_settings?.max_lot_size || 0.02}手
- 止损点数：${config.risk_settings?.stop_loss_points || 50}
- 止盈点数：${config.risk_settings?.take_profit_points || 100}
- 订单数量限制：${config.risk_settings?.order_limit_per_round || 20}单/轮
- 自动停止轮次：${config.risk_settings?.auto_stop_rounds || 5 === 0 ? '不自动停止' : config.risk_settings?.auto_stop_rounds || 5 + '轮'}
- 考虑风险事件：${config.risk_settings?.consider_risk_events !== false ? '是' : '否'}
    `.trim();

    // 显示预览对话框
    if (confirm(`配置预览：\n\n${previewContent}\n\n是否要加载此配置？`)) {
        loadConfiguration(selectedConfig);
    }
}

// 删除选中的配置
function deleteSelectedConfiguration() {
    const selectedConfig = document.getElementById('savedConfigs').value;

    if (!selectedConfig) {
        alert('请选择要删除的配置');
        return;
    }

    if (!confirm(`确定要删除配置"${selectedConfig}"吗？`)) {
        return;
    }

    const savedConfigs = getSavedConfigurations();
    delete savedConfigs[selectedConfig];
    localStorage.setItem('agentTradingRealConfigs', JSON.stringify(savedConfigs));

    // 更新配置列表（不自动加载）
    loadSavedConfigurationsList(false);

    alert(`配置"${selectedConfig}"已删除`);
    logAI(`配置已删除: ${selectedConfig}`);
}

// 加载配置（支持指定配置名称）
function loadConfiguration(configName = null) {
    let config = null;

    if (configName) {
        // 加载指定配置
        const savedConfigs = getSavedConfigurations();
        config = savedConfigs[configName];

        if (!config) {
            alert(`配置"${configName}"不存在`);
            return;
        }
    } else {
        // 兼容旧版本：加载默认配置
        const saved = localStorage.getItem('agentTradingRealConfig');
        if (saved) {
            config = JSON.parse(saved);
        }
    }

    if (config) {
        console.log(`🔧 开始应用配置: ${configName || '默认配置'}`);
        console.log('📋 配置内容:', config);

        // 检查元素是否存在
        const aiModelSelect = document.getElementById('aiModelSelect');
        const aiStrategySelect = document.getElementById('aiStrategySelect');
        const analysisInterval = document.getElementById('analysisInterval');
        const userRequirements = document.getElementById('userRequirements');

        console.log('🔍 元素检查:', {
            aiModelSelect: !!aiModelSelect,
            aiStrategySelect: !!aiStrategySelect,
            analysisInterval: !!analysisInterval,
            userRequirements: !!userRequirements
        });

        if (aiModelSelect) {
            console.log(`🔧 设置AI模型: ${config.ai_model}`);
            aiModelSelect.value = config.ai_model || '';
            console.log(`✅ AI模型设置结果: ${aiModelSelect.value}`);
        }
        if (aiStrategySelect) {
            // 支持新的多选格式和旧的单选格式
            const strategies = config.ai_strategies || (config.ai_strategy ? [config.ai_strategy] : []);
            console.log(`🔧 设置AI策略: ${strategies}`);

            // 清除所有选择
            Array.from(aiStrategySelect.options).forEach(option => {
                option.selected = false;
            });

            // 设置选中的策略（同时更新复选框和select）
            if (Array.isArray(strategies)) {
                strategies.forEach(strategyId => {
                    // 更新隐藏的select
                    const option = aiStrategySelect.querySelector(`option[value="${strategyId}"]`);
                    if (option) {
                        option.selected = true;
                    }

                    // 更新可见的复选框
                    const checkbox = document.getElementById(`strategy_${strategyId}`);
                    if (checkbox) {
                        checkbox.checked = true;
                    }
                });
            }

            // 更新AI策略显示
            updateSelectedStrategies();
            if (strategies.length > 0) {
                confirmStrategySelection();
            }

            console.log(`✅ AI策略设置结果: ${Array.from(aiStrategySelect.selectedOptions).map(opt => opt.value)}`);
        }
        if (analysisInterval) analysisInterval.value = config.analysis_interval || 15;

        // 设置交易时间段
        const tradingTimeSlot = document.getElementById('tradingTimeSlot');
        if (tradingTimeSlot) tradingTimeSlot.value = config.trading_time_slot || 'asia';

        if (userRequirements) userRequirements.value = config.user_requirements || '';

        if (config.risk_settings) {
            document.getElementById('maxRiskPercent').value = config.risk_settings.max_risk_percent || 2;
            document.getElementById('minLotSize').value = config.risk_settings.min_lot_size || 0.01;
            document.getElementById('maxLotSize').value = config.risk_settings.max_lot_size || 0.02;
            document.getElementById('stopLossPoints').value = config.risk_settings.stop_loss_points || 50;
            document.getElementById('takeProfitPoints').value = config.risk_settings.take_profit_points || 100;
            document.getElementById('considerRiskEvents').checked = config.risk_settings.consider_risk_events !== false; // 默认为true

            // 验证手数输入
            validateLotSizeInput();

            // 设置订单数量限制
            const orderLimit = config.risk_settings.order_limit_per_round || 20;
            const orderLimitSelect = document.getElementById('orderLimitSelect');
            const customOrderLimit = document.getElementById('customOrderLimit');

            if ([10, 20, 30].includes(orderLimit)) {
                orderLimitSelect.value = orderLimit.toString();
                customOrderLimit.style.display = 'none';
            } else {
                orderLimitSelect.value = 'custom';
                customOrderLimit.value = orderLimit;
                customOrderLimit.style.display = 'block';
            }

            // 设置自动停止轮次
            const autoStopRounds = config.risk_settings.auto_stop_rounds || 5;
            const autoStopSelect = document.getElementById('autoStopRounds');
            const customAutoStopRounds = document.getElementById('customAutoStopRounds');

            if ([0, 1, 2, 3, 5, 10].includes(autoStopRounds)) {
                autoStopSelect.value = autoStopRounds.toString();
                customAutoStopRounds.style.display = 'none';
            } else {
                autoStopSelect.value = 'custom';
                customAutoStopRounds.value = autoStopRounds;
                customAutoStopRounds.style.display = 'block';
            }
        }

        // 设置选中的货币对
        if (config.trading_symbols) {
            const symbolSelect = document.getElementById('tradingSymbols');
            Array.from(symbolSelect.options).forEach(option => {
                option.selected = config.trading_symbols.includes(option.value);
            });

            // 更新货币对显示
            updateSelectedSymbols();
            if (config.trading_symbols.length > 0) {
                confirmSymbolSelection();
            }
        }

        updateDisplay();

        if (configName) {
            logAI(`配置已加载: ${configName}`);
            updateCurrentConfigStatus(configName);

            // 强制刷新显示，确保配置内容正确显示
            setTimeout(() => {
                forceRefreshConfigDisplay();
            }, 50);
        } else {
            updateCurrentConfigStatus('默认配置');
        }
    }
}

// 获取已保存的配置
function getSavedConfigurations() {
    const saved = localStorage.getItem('agentTradingRealConfigs');
    return saved ? JSON.parse(saved) : {};
}

// 加载已保存配置列表
function loadSavedConfigurationsList(autoLoad = true) {
    const savedConfigs = getSavedConfigurations();
    const select = document.getElementById('savedConfigs');

    select.innerHTML = '<option value="">选择已保存的配置</option>';

    const configNames = Object.keys(savedConfigs).sort();

    if (configNames.length === 0) {
        const option = document.createElement('option');
        option.value = '';
        option.textContent = '暂无已保存的配置';
        option.disabled = true;
        select.appendChild(option);
    } else {
        configNames.forEach((name, index) => {
            const config = savedConfigs[name];
            const option = document.createElement('option');
            option.value = name;

            // 显示配置名称和创建时间
            const createdDate = config.created_at ? new Date(config.created_at).toLocaleDateString() : '';
            option.textContent = `${name} ${createdDate ? `(${createdDate})` : ''}`;

            select.appendChild(option);
        });

        // 只在页面初始化时自动加载第一个配置
        if (autoLoad && configNames.length > 0) {
            const firstConfigName = configNames[0];
            select.value = firstConfigName;

            // 自动加载第一个配置
            console.log(`🔄 自动加载第一个配置: ${firstConfigName}`);
            console.log(`📋 可用配置列表: ${configNames.join(', ')}`);

            // 延迟加载，确保页面元素已完全初始化
            setTimeout(() => {
                console.log(`🔄 开始加载配置内容: ${firstConfigName}`);
                loadConfiguration(firstConfigName);

                // 验证配置是否加载成功
                setTimeout(() => {
                    const aiModel = document.getElementById('aiModelSelect').value;
                    const aiStrategy = document.getElementById('aiStrategySelect').value;
                    console.log(`✅ 配置加载验证 - AI模型: ${aiModel}, AI策略: ${aiStrategy}`);
                    logAI(`✅ 已自动加载配置: ${firstConfigName} (共${configNames.length}个配置)`);
                }, 100);
            }, 200);
        } else if (autoLoad && configNames.length === 0) {
            console.log('⚠️ 没有找到已保存的配置，将使用默认设置');
            logAI('⚠️ 没有找到已保存的配置，建议先保存一个配置');
            clearCurrentConfigStatus();
        }
    }
}

// 加载预设配置模板
function loadPresetConfigurations() {
    if (!confirm('这将添加一些预设配置模板，是否继续？')) {
        return;
    }

    const presetConfigs = {
        '稳健策略': {
            name: '稳健策略',
            ai_model: 'deepseek_v3',
            ai_strategy: 'demo_1',
            trading_symbols: ['EURUSD', 'GBPUSD'],
            analysis_interval: 30,
            user_requirements: '稳健交易，控制风险，追求稳定收益。优先考虑风险控制，避免大幅回撤。',
            risk_settings: {
                max_risk_percent: 1,
                max_lot_size: 0.01,
                stop_loss_points: 30,
                consider_risk_events: true
            },
            created_at: new Date().toISOString()
        },
        '激进策略': {
            name: '激进策略',
            ai_model: 'gpt4',
            ai_strategy: 'demo_2',
            trading_symbols: ['EURUSD', 'GBPUSD', 'USDJPY'],
            analysis_interval: 15,
            user_requirements: '激进交易，追求高收益。可以承受较大风险，寻找高概率的突破机会。',
            risk_settings: {
                max_risk_percent: 3,
                max_lot_size: 0.1,
                stop_loss_points: 50,
                consider_risk_events: true
            },
            created_at: new Date().toISOString()
        },
        '黄金专用': {
            name: '黄金专用',
            ai_model: 'claude_3',
            ai_strategy: 'demo_1',
            trading_symbols: ['XAUUSD'],
            analysis_interval: 15,
            user_requirements: '专注黄金交易，根据美元指数、通胀预期、地缘政治等因素进行分析。',
            risk_settings: {
                max_risk_percent: 2,
                max_lot_size: 0.05,
                stop_loss_points: 100,
                consider_risk_events: true
            },
            created_at: new Date().toISOString()
        },
        '短线交易': {
            name: '短线交易',
            ai_model: 'zhipu',
            ai_strategy: 'demo_2',
            trading_symbols: ['EURUSD', 'GBPUSD', 'USDJPY', 'AUDUSD'],
            analysis_interval: 5,
            user_requirements: '短线快进快出，捕捉短期价格波动。关注技术指标和市场情绪变化。',
            risk_settings: {
                max_risk_percent: 1.5,
                max_lot_size: 0.02,
                stop_loss_points: 20,
                consider_risk_events: false  // 短线交易不考虑风险事件
            },
            created_at: new Date().toISOString()
        },
        '趋势跟踪': {
            name: '趋势跟踪',
            ai_model: 'kimi',
            ai_strategy: 'demo_1',
            trading_symbols: ['EURUSD', 'GBPUSD', 'XAUUSD'],
            analysis_interval: 60,
            user_requirements: '跟踪中长期趋势，在趋势确立后进入，持有至趋势反转。避免震荡市场。',
            risk_settings: {
                max_risk_percent: 2.5,
                max_lot_size: 0.08,
                stop_loss_points: 80,
                consider_risk_events: true
            },
            created_at: new Date().toISOString()
        }
    };

    // 获取现有配置
    const savedConfigs = getSavedConfigurations();

    // 添加预设配置（不覆盖已存在的）
    let addedCount = 0;
    Object.keys(presetConfigs).forEach(name => {
        if (!savedConfigs[name]) {
            savedConfigs[name] = presetConfigs[name];
            addedCount++;
        }
    });

    // 保存配置
    localStorage.setItem('agentTradingRealConfigs', JSON.stringify(savedConfigs));

    // 更新配置列表（不自动加载）
    loadSavedConfigurationsList(false);

    alert(`已添加 ${addedCount} 个预设配置模板`);
    logAI(`预设配置模板已加载: ${addedCount} 个`);
}

// 更新显示
function updateDisplay() {
    const aiModel = document.getElementById('aiModelSelect');
    const aiStrategy = document.getElementById('aiStrategySelect');

    document.getElementById('currentAIModel').textContent =
        aiModel.selectedOptions[0]?.textContent || '未选择';

    // 显示选中的AI策略（多选）
    const selectedStrategies = Array.from(aiStrategy.selectedOptions)
        .filter(option => option.value)
        .map(option => option.text);

    if (selectedStrategies.length === 0) {
        document.getElementById('currentAIStrategy').textContent = '未选择';
    } else if (selectedStrategies.length === 1) {
        document.getElementById('currentAIStrategy').textContent = selectedStrategies[0];
    } else {
        document.getElementById('currentAIStrategy').textContent = `${selectedStrategies.length}个策略 (智能体决策)`;
    }
    
    document.getElementById('maxRisk').textContent =
        document.getElementById('maxRiskPercent').value + '%';

    // 显示手数区间
    const minLotSize = document.getElementById('minLotSize').value;
    const maxLotSize = document.getElementById('maxLotSize').value;
    document.getElementById('maxTradeSize').textContent =
        minLotSize === maxLotSize ? maxLotSize + '手' : minLotSize + '-' + maxLotSize + '手';

    document.getElementById('stopLoss').textContent =
        document.getElementById('stopLossPoints').value + '点';
    document.getElementById('takeProfit').textContent =
        document.getElementById('takeProfitPoints').value + '点';

    // 更新订单数量限制显示
    const orderLimitElement = document.getElementById('orderLimitDisplay');
    if (orderLimitElement) {
        orderLimitElement.textContent = getOrderLimit() + '单/轮';
    }

    // 更新自动停止轮次显示
    const autoStopElement = document.getElementById('autoStopDisplay');
    if (autoStopElement) {
        const autoStopRounds = getAutoStopRounds();
        autoStopElement.textContent = autoStopRounds === 0 ? '不自动停止' : autoStopRounds + '轮后';
    }

    // 更新交易时间段显示
    const tradingTimeElement = document.getElementById('tradingTimeDisplay');
    if (tradingTimeElement) {
        const timeSlot = document.getElementById('tradingTimeSlot').value;
        const timeSlotNames = {
            'asia': '亚洲时段',
            'europe': '欧洲时段',
            'america': '美洲时段',
            'full': '全时段'
        };
        tradingTimeElement.textContent = timeSlotNames[timeSlot] || '亚洲时段';
    }
}

// 更新智能体状态
function updateAgentStatus(status, badgeClass) {
    const statusElement = document.getElementById('agentStatus');
    statusElement.textContent = status;
    statusElement.className = `badge ${badgeClass}`;

    // 更新按钮状态
    document.getElementById('startAgentBtn').disabled = agentRunning;
    document.getElementById('stopAgentBtn').disabled = !agentRunning;
    document.getElementById('pauseAgentBtn').disabled = !agentRunning;

    // 保存智能体状态到本地存储
    saveAgentStatus();
}

// 保存智能体状态
function saveAgentStatus() {
    const statusData = {
        running: agentRunning,
        startTime: agentStartTime ? agentStartTime.toISOString() : null,
        lastUpdate: new Date().toISOString()
    };

    localStorage.setItem(AGENT_STATUS_KEY, JSON.stringify(statusData));
    console.log('💾 智能体状态已保存:', statusData);
}

// 恢复智能体状态
function restoreAgentStatus() {
    try {
        const saved = localStorage.getItem(AGENT_STATUS_KEY);
        if (!saved) {
            console.log('📋 没有找到保存的智能体状态');
            return;
        }

        const statusData = JSON.parse(saved);
        console.log('🔄 恢复智能体状态:', statusData);

        // 检查状态是否过期（超过1小时认为过期）
        const lastUpdate = new Date(statusData.lastUpdate);
        const now = new Date();
        const timeDiff = (now - lastUpdate) / (1000 * 60 * 60); // 小时

        if (timeDiff > 1) {
            console.log('⏰ 智能体状态已过期，重置为停止状态');
            clearAgentStatus();
            return;
        }

        if (statusData.running) {
            // 验证服务器端状态
            verifyAgentStatusWithServer();
        } else {
            agentRunning = false;
            updateAgentStatus('已停止', 'bg-secondary');
        }

    } catch (error) {
        console.error('❌ 恢复智能体状态失败:', error);
        clearAgentStatus();
    }
}

// 验证服务器端智能体状态
function verifyAgentStatusWithServer() {
    console.log('🔍 验证服务器端智能体状态...');

    fetch('/api/agent-trading-real/status')
    .then(response => response.json())
    .then(data => {
        console.log('📊 服务器端状态:', data);

        if (data.success) {
            if (data.running && data.thread_alive) {
                // 服务器端确认智能体正在运行且线程活跃
                agentRunning = true;
                agentStartTime = data.start_time ? new Date(data.start_time) : new Date();

                // 根据具体状态显示不同信息
                if (data.status === 'sleeping') {
                    updateAgentStatus('休眠中 (等待交易时段)', 'bg-info');
                    logAI(`😴 智能体休眠中 - 等待交易时段开始 (总交易: ${data.total_trades}, 总盈亏: $${data.total_profit.toFixed(2)})`);
                } else if (data.status === 'auto_stopping') {
                    updateAgentStatus('自动停止中', 'bg-warning');
                    logAI(`🛑 智能体自动停止中 - 等待AI策略平仓 (总交易: ${data.total_trades}, 总盈亏: $${data.total_profit.toFixed(2)})`);
                } else {
                    updateAgentStatus('运行中', 'bg-success');
                    logAI(`✅ 智能体状态已恢复 - 运行中 (总交易: ${data.total_trades}, 总盈亏: $${data.total_profit.toFixed(2)})`);
                }

                startTimers();
            } else if (data.status === 'running' && !data.thread_alive) {
                // 状态显示运行但线程已死，需要重启
                agentRunning = false;
                updateAgentStatus('线程异常', 'bg-warning');
                logAI('⚠️ 检测到智能体线程异常，请重新启动');
            } else {
                // 服务器端智能体未运行
                agentRunning = false;
                updateAgentStatus('已停止', 'bg-secondary');
                logAI('⚠️ 服务器端智能体未运行，状态已同步');
            }
        } else {
            console.error('❌ 获取服务器状态失败:', data.error);
            agentRunning = false;
            updateAgentStatus('状态异常', 'bg-danger');
            logAI(`❌ 获取智能体状态失败: ${data.error}`);
        }
    })
    .catch(error => {
        console.error('❌ 验证服务器状态失败:', error);
        // 验证失败时保守处理，设为停止状态
        agentRunning = false;
        updateAgentStatus('连接异常', 'bg-warning');
        logAI(`❌ 验证智能体状态异常: ${error.message}`);
    });
}

// 清除智能体状态
function clearAgentStatus() {
    localStorage.removeItem(AGENT_STATUS_KEY);
    agentRunning = false;
    agentStartTime = null;
    updateAgentStatus('已停止', 'bg-secondary');
    console.log('🗑️ 智能体状态已清除');
}

// 启动定时器
function startTimers() {
    // 运行时间计时器
    runningTimer = setInterval(updateRunningTime, 1000);
    
    // AI分析定时器
    const interval = parseInt(document.getElementById('analysisInterval').value) * 60 * 1000;
    analysisTimer = setInterval(performAIAnalysis, interval);
    
    // 立即执行一次AI分析
    performAIAnalysis();
}

// 停止定时器
function stopTimers() {
    if (runningTimer) {
        clearInterval(runningTimer);
        runningTimer = null;
    }
    
    if (analysisTimer) {
        clearInterval(analysisTimer);
        analysisTimer = null;
    }
}

// 更新运行时间
function updateRunningTime() {
    if (agentStartTime) {
        const now = new Date();
        const diff = now - agentStartTime;
        const hours = Math.floor(diff / 3600000);
        const minutes = Math.floor((diff % 3600000) / 60000);
        const seconds = Math.floor((diff % 60000) / 1000);
        
        document.getElementById('runningTime').textContent = 
            `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
}

// 执行AI分析
function performAIAnalysis() {
    if (!agentRunning) return;
    
    logAI('开始AI市场分析...');
    
    const config = getConfiguration();
    
    fetch('/api/agent-trading-real/analyze', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(config)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            logAI(`AI分析完成: ${data.analysis}`);
            document.getElementById('lastAnalysis').textContent = new Date().toLocaleTimeString();
            
            // 如果有交易建议，执行交易
            if (data.trading_signal) {
                executeTrade(data.trading_signal);
            }
        } else {
            logAI(`AI分析失败: ${data.error}`);
        }
    })
    .catch(error => {
        console.error('AI分析失败:', error);
        logAI(`AI分析异常: ${error.message}`);
    });
}

// 执行交易
function executeTrade(signal) {
    logTrading(`收到交易信号: ${signal.action} ${signal.symbol} ${signal.volume}手`);
    
    fetch('/api/agent-trading-real/execute', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(signal)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            logTrading(`交易执行成功: 订单号 ${data.ticket}`);
            updateTradingStats();
        } else {
            logTrading(`交易执行失败: ${data.error}`);
        }
    })
    .catch(error => {
        console.error('交易执行失败:', error);
        logTrading(`交易执行异常: ${error.message}`);
    });
}

// 更新交易统计
function updateTradingStats() {
    fetch('/api/agent-trading-real/stats')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('totalTrades').textContent = data.total_trades;
            document.getElementById('winRate').textContent = data.win_rate + '%';
            document.getElementById('totalProfit').textContent = '$' + data.total_profit.toFixed(2);
            document.getElementById('todayProfit').textContent = '$' + data.today_profit.toFixed(2);
        }
    })
    .catch(error => console.error('更新统计失败:', error));
}

// AI日志
function logAI(message) {
    const log = document.getElementById('aiAnalysisLog');
    const timestamp = new Date().toLocaleTimeString();
    const entry = document.createElement('div');
    entry.innerHTML = `<span class="text-muted">[${timestamp}]</span> ${message}`;
    log.appendChild(entry);
    log.scrollTop = log.scrollHeight;
}

// 交易日志
function logTrading(message) {
    const log = document.getElementById('tradingLog');
    const timestamp = new Date().toLocaleTimeString();
    const entry = document.createElement('div');
    entry.innerHTML = `<span class="text-muted">[${timestamp}]</span> ${message}`;
    log.appendChild(entry);
    log.scrollTop = log.scrollHeight;
}

// 处理订单数量限制选择变化
function handleOrderLimitChange() {
    const selectElement = document.getElementById('orderLimitSelect');
    const customInput = document.getElementById('customOrderLimit');

    if (selectElement.value === 'custom') {
        customInput.style.display = 'block';
        customInput.focus();
    } else {
        customInput.style.display = 'none';
        customInput.value = selectElement.value;
    }
    updateDisplay();
}

// 处理自动停止轮次选择变化
function handleAutoStopChange() {
    const selectElement = document.getElementById('autoStopRounds');
    const customInput = document.getElementById('customAutoStopRounds');

    if (selectElement.value === 'custom') {
        customInput.style.display = 'block';
        customInput.focus();
    } else {
        customInput.style.display = 'none';
        customInput.value = selectElement.value;
    }
    updateDisplay();
}

// 获取当前订单数量限制
function getOrderLimit() {
    const selectElement = document.getElementById('orderLimitSelect');
    const customInput = document.getElementById('customOrderLimit');

    if (selectElement.value === 'custom') {
        return parseInt(customInput.value) || 20;
    } else {
        return parseInt(selectElement.value) || 20;
    }
}

// 获取自动停止轮次
function getAutoStopRounds() {
    const selectElement = document.getElementById('autoStopRounds');
    const customInput = document.getElementById('customAutoStopRounds');

    if (selectElement.value === 'custom') {
        return parseInt(customInput.value) || 5;
    } else {
        return parseInt(selectElement.value) || 0;
    }
}

// 获取时间段名称
function getTimeSlotName(timeSlot) {
    const timeSlotNames = {
        'asia': '亚洲时段 (06:00-16:00)',
        'europe': '欧洲时段 (14:00-24:00)',
        'america': '美洲时段 (20:00-04:00)',
        'full': '全时段 (24小时)'
    };
    return timeSlotNames[timeSlot] || '亚洲时段';
}

// 验证手数输入
function validateLotSizeInput() {
    const minInput = document.getElementById('minLotSize');
    const maxInput = document.getElementById('maxLotSize');

    let minValue = parseFloat(minInput.value) || 0.01;
    let maxValue = parseFloat(maxInput.value) || 0.02;

    // 验证范围：0.01-0.1
    minValue = Math.max(0.01, Math.min(0.1, minValue));
    maxValue = Math.max(0.01, Math.min(0.1, maxValue));

    // 确保是0.01的整数倍
    minValue = Math.round(minValue * 100) / 100;
    maxValue = Math.round(maxValue * 100) / 100;

    // 确保最小值不大于最大值
    if (minValue > maxValue) {
        maxValue = minValue;
        console.log(`🔧 自动调整最大手数: ${maxInput.value} → ${maxValue.toFixed(2)}`);
    }

    // 更新输入框值
    minInput.value = minValue.toFixed(2);
    maxInput.value = maxValue.toFixed(2);

    // 添加视觉反馈
    validateLotSizeVisual();

    // 更新显示
    updateDisplay();

    console.log(`📊 手数区间验证: ${minValue.toFixed(2)} - ${maxValue.toFixed(2)}`);
}

// 添加视觉验证反馈
function validateLotSizeVisual() {
    const minInput = document.getElementById('minLotSize');
    const maxInput = document.getElementById('maxLotSize');

    const minValue = parseFloat(minInput.value);
    const maxValue = parseFloat(maxInput.value);

    // 移除之前的样式
    minInput.classList.remove('is-invalid', 'is-valid');
    maxInput.classList.remove('is-invalid', 'is-valid');

    // 验证规则
    const minValid = minValue >= 0.01 && minValue <= 0.1 && (minValue * 100) % 1 === 0;
    const maxValid = maxValue >= 0.01 && maxValue <= 0.1 && (maxValue * 100) % 1 === 0;
    const rangeValid = minValue <= maxValue;

    // 添加样式
    if (minValid && rangeValid) {
        minInput.classList.add('is-valid');
    } else {
        minInput.classList.add('is-invalid');
    }

    if (maxValid && rangeValid) {
        maxInput.classList.add('is-valid');
    } else {
        maxInput.classList.add('is-invalid');
    }
}

// ==================== 货币对选择管理 ====================

// 更新选中的货币对显示
function updateSelectedSymbols() {
    const symbolSelect = document.getElementById('tradingSymbols');
    const selectedOptions = Array.from(symbolSelect.selectedOptions);
    const tagsContainer = document.getElementById('selectedSymbolsTags');

    // 清空现有标签
    tagsContainer.innerHTML = '';

    // 为每个选中的货币对创建标签
    selectedOptions.forEach(option => {
        const tag = document.createElement('span');
        tag.className = 'badge bg-primary me-1 mb-1';
        tag.innerHTML = `
            ${option.text}
            <button type="button" class="btn-close btn-close-white ms-1"
                    onclick="removeSymbol('${option.value}')" style="font-size: 0.7em;"></button>
        `;
        tagsContainer.appendChild(tag);
    });

    // 如果有选中的货币对，显示标签区域
    if (selectedOptions.length > 0) {
        document.getElementById('selectedSymbolsDisplay').style.display = 'block';
    }

    updateDisplay();
}

// 移除单个货币对
function removeSymbol(symbolValue) {
    const symbolSelect = document.getElementById('tradingSymbols');
    const option = symbolSelect.querySelector(`option[value="${symbolValue}"]`);
    if (option) {
        option.selected = false;
    }
    updateSelectedSymbols();
}

// 确认货币对选择
function confirmSymbolSelection() {
    const symbolSelect = document.getElementById('tradingSymbols');
    const selectedOptions = Array.from(symbolSelect.selectedOptions);

    if (selectedOptions.length === 0) {
        alert('请至少选择一个交易货币对');
        return;
    }

    // 隐藏选择器，显示标签
    document.getElementById('symbolSelectorContainer').style.display = 'none';
    document.getElementById('selectedSymbolsDisplay').style.display = 'block';

    updateSelectedSymbols();
    updateDisplay();
}

// 切换货币对选择器显示状态
function toggleSymbolSelector() {
    const selectorContainer = document.getElementById('symbolSelectorContainer');
    const displayContainer = document.getElementById('selectedSymbolsDisplay');

    if (selectorContainer.style.display === 'none') {
        selectorContainer.style.display = 'block';
        displayContainer.style.display = 'none';
    } else {
        selectorContainer.style.display = 'none';
        displayContainer.style.display = 'block';
    }
}

// ==================== AI策略多选管理 ====================

// 处理策略复选框变化
function handleStrategyCheckboxChange() {
    const checkboxes = document.querySelectorAll('#strategyCheckboxContainer input[type="checkbox"]');
    const select = document.getElementById('aiStrategySelect');

    // 同步复选框状态到隐藏的select元素
    Array.from(select.options).forEach(option => {
        const checkbox = document.getElementById(`strategy_${option.value}`);
        if (checkbox) {
            option.selected = checkbox.checked;
        }
    });

    // 更新显示
    updateSelectedStrategies();

    // 调试信息
    const checkedBoxes = Array.from(checkboxes).filter(cb => cb.checked);
    console.log(`📋 策略复选框变化 - 选中数量: ${checkedBoxes.length}`);
    checkedBoxes.forEach(cb => {
        const label = document.querySelector(`label[for="${cb.id}"]`);
        console.log(`  ✓ ${label ? label.textContent.trim() : cb.value}`);
    });
}

// 更新选中的AI策略显示
function updateSelectedStrategies() {
    const strategySelect = document.getElementById('aiStrategySelect');
    const selectedOptions = Array.from(strategySelect.selectedOptions);
    const tagsContainer = document.getElementById('selectedStrategiesTags');

    console.log(`🔄 更新AI策略选择 - 选中数量: ${selectedOptions.length}`);
    selectedOptions.forEach(option => {
        console.log(`  - 选中策略: ${option.text} (${option.value})`);
    });

    // 清空现有标签
    tagsContainer.innerHTML = '';

    // 为每个选中的AI策略创建标签
    selectedOptions.forEach(option => {
        if (option.value) { // 排除空值选项
            const tag = document.createElement('span');
            tag.className = 'badge bg-success me-1 mb-1';
            tag.innerHTML = `
                ${option.text}
                <button type="button" class="btn-close btn-close-white ms-1"
                        onclick="removeStrategy('${option.value}')" style="font-size: 0.7em;"></button>
            `;
            tagsContainer.appendChild(tag);
        }
    });

    // 如果有选中的AI策略，显示标签区域
    if (selectedOptions.length > 0 && selectedOptions.some(opt => opt.value)) {
        document.getElementById('selectedStrategiesDisplay').style.display = 'block';
        console.log(`✅ 显示策略标签区域`);
    } else {
        console.log(`⚠️ 没有选中的策略，隐藏标签区域`);
    }

    updateDisplay();
}

// 移除单个AI策略
function removeStrategy(strategyValue) {
    // 更新隐藏的select
    const strategySelect = document.getElementById('aiStrategySelect');
    const option = strategySelect.querySelector(`option[value="${strategyValue}"]`);
    if (option) {
        option.selected = false;
    }

    // 更新可见的复选框
    const checkbox = document.getElementById(`strategy_${strategyValue}`);
    if (checkbox) {
        checkbox.checked = false;
    }

    updateSelectedStrategies();
    console.log(`🗑️ 移除策略: ${strategyValue}`);
}

// 确认AI策略选择
function confirmStrategySelection() {
    const strategySelect = document.getElementById('aiStrategySelect');
    const selectedOptions = Array.from(strategySelect.selectedOptions).filter(opt => opt.value);

    if (selectedOptions.length === 0) {
        alert('请至少选择一个AI策略');
        return;
    }

    // 隐藏选择器，显示标签
    document.getElementById('strategySelectorContainer').style.display = 'none';
    document.getElementById('selectedStrategiesDisplay').style.display = 'block';

    updateSelectedStrategies();
    updateDisplay();
}

// 切换AI策略选择器显示状态
function toggleStrategySelector() {
    const selectorContainer = document.getElementById('strategySelectorContainer');
    const displayContainer = document.getElementById('selectedStrategiesDisplay');

    if (selectorContainer.style.display === 'none') {
        selectorContainer.style.display = 'block';
        displayContainer.style.display = 'none';
    } else {
        selectorContainer.style.display = 'none';
        displayContainer.style.display = 'block';
    }
}

// 全选AI策略
function selectAllStrategies() {
    const checkboxes = document.querySelectorAll('#strategyCheckboxContainer input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
    handleStrategyCheckboxChange();
    console.log(`✅ 全选AI策略 - 共${checkboxes.length}个策略`);
}

// 清空AI策略选择
function clearAllStrategies() {
    const checkboxes = document.querySelectorAll('#strategyCheckboxContainer input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
    handleStrategyCheckboxChange();
    console.log(`🗑️ 清空AI策略选择`);
}

// 监听配置变化
document.getElementById('aiModelSelect').addEventListener('change', updateDisplay);
document.getElementById('aiStrategySelect').addEventListener('change', updateDisplay);
document.getElementById('maxRiskPercent').addEventListener('change', updateDisplay);
document.getElementById('minLotSize').addEventListener('change', updateDisplay);
document.getElementById('maxLotSize').addEventListener('change', updateDisplay);
document.getElementById('stopLossPoints').addEventListener('change', updateDisplay);
document.getElementById('takeProfitPoints').addEventListener('change', updateDisplay);
document.getElementById('orderLimitSelect').addEventListener('change', updateDisplay);
document.getElementById('customOrderLimit').addEventListener('change', updateDisplay);
document.getElementById('autoStopRounds').addEventListener('change', updateDisplay);
document.getElementById('customAutoStopRounds').addEventListener('change', updateDisplay);
document.getElementById('tradingTimeSlot').addEventListener('change', updateDisplay);

// 智能重连机制
let reconnectAttempts = 0;
const maxReconnectAttempts = 3;
let lastConnectionStatus = null;

function smartReconnect() {
    const autoConnect = document.getElementById('autoConnectSwitch').checked;

    if (!autoConnect) {
        console.log('⚠️ 自动连接已关闭，跳过重连');
        return;
    }

    if (reconnectAttempts >= maxReconnectAttempts) {
        console.log(`❌ 已达到最大重连次数 (${maxReconnectAttempts})，停止重连`);
        logAI(`MT5重连失败，已尝试 ${maxReconnectAttempts} 次`);
        return;
    }

    reconnectAttempts++;
    console.log(`🔄 智能重连 MT5 (第 ${reconnectAttempts} 次)...`);
    logAI(`MT5连接断开，正在尝试重连 (${reconnectAttempts}/${maxReconnectAttempts})`);

    // 延迟重连，避免频繁请求
    setTimeout(() => {
        connectMT5(true);
    }, reconnectAttempts * 5000); // 递增延迟：5秒、10秒、15秒
}

// 增强的MT5连接检查
function enhancedMT5Check() {
    fetch('/api/mt5/status')
    .then(response => response.json())
    .then(data => {
        const currentStatus = data.success && data.connected;

        // 检测连接状态变化
        if (lastConnectionStatus !== null && lastConnectionStatus !== currentStatus) {
            if (currentStatus) {
                console.log('✅ MT5连接已恢复');
                logAI('MT5连接已恢复');
                reconnectAttempts = 0; // 重置重连计数
            } else {
                console.log('❌ 检测到MT5连接断开');
                smartReconnect();
            }
        }

        lastConnectionStatus = currentStatus;

        // 更新UI
        updateMT5Status(data);
    })
    .catch(error => {
        console.error('❌ MT5状态检查失败:', error);
        if (lastConnectionStatus !== false) {
            smartReconnect();
        }
        lastConnectionStatus = false;
    });
}

function updateMT5Status(data) {
    const statusElement = document.getElementById('connectionStatus');
    const connectBtn = document.getElementById('connectBtn');
    const reconnectBtn = document.getElementById('reconnectBtn');
    const connectionDetails = document.getElementById('connectionDetails');
    const connectionInfo = document.getElementById('connectionInfo');

    if (data.success && data.connected) {
        statusElement.textContent = 'MT5已连接';
        statusElement.className = 'badge bg-success';
        connectBtn.style.display = 'none';
        reconnectBtn.style.display = 'inline-block';

        // 显示连接详情
        if (data.account_info) {
            connectionDetails.style.display = 'block';
            connectionInfo.textContent = `账户: ${data.account_info.login || 'Unknown'} | 服务器: ${data.account_info.server || 'Unknown'}`;
        } else {
            connectionDetails.style.display = 'none';
        }

        // 重置重连计数
        reconnectAttempts = 0;

    } else {
        statusElement.textContent = 'MT5未连接';
        statusElement.className = 'badge bg-danger';
        connectBtn.style.display = 'inline-block';
        reconnectBtn.style.display = 'none';
        connectionDetails.style.display = 'none';
    }
}

// 使用增强的检查替换原来的定期检查
setInterval(enhancedMT5Check, 30000);

// ==================== 持仓管理功能 ====================

// 刷新持仓
function refreshPositions() {
    console.log('🔄 刷新持仓...');

    fetch('/api/mt5/positions')
    .then(response => response.json())
    .then(data => {
        console.log('📊 持仓数据:', data);
        displayPositions(data);
    })
    .catch(error => {
        console.error('❌ 获取持仓失败:', error);
        document.getElementById('positionsContainer').innerHTML = `
            <div class="text-center text-danger py-3">
                <i class="fas fa-exclamation-triangle"></i> 获取持仓失败: ${error.message}
            </div>
        `;
    });
}

// 显示持仓
function displayPositions(data) {
    const container = document.getElementById('positionsContainer');

    if (!data.success) {
        container.innerHTML = `
            <div class="text-center text-danger py-3">
                <i class="fas fa-exclamation-triangle"></i> ${data.error}
            </div>
        `;
        return;
    }

    const positions = data.positions || [];

    if (positions.length === 0) {
        container.innerHTML = `
            <div class="text-center text-muted py-3">
                <i class="fas fa-inbox"></i> 暂无持仓
            </div>
        `;
        return;
    }

    let html = '';
    let totalProfit = 0;

    positions.forEach(position => {
        const profit = parseFloat(position.profit) || 0;
        totalProfit += profit;

        const profitClass = profit >= 0 ? 'text-success' : 'text-danger';
        const profitIcon = profit >= 0 ? 'fa-arrow-up' : 'fa-arrow-down';

        html += `
            <div class="border-bottom py-2">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <strong>${position.symbol}</strong>
                        <span class="badge ${position.type === 0 ? 'bg-success' : 'bg-danger'} ms-2">
                            ${position.type === 0 ? 'BUY' : 'SELL'}
                        </span>
                    </div>
                    <div class="text-end">
                        <div class="${profitClass}">
                            <i class="fas ${profitIcon}"></i>
                            $${profit.toFixed(2)}
                        </div>
                        <small class="text-muted">手数: ${position.volume}</small>
                    </div>
                </div>
                <div class="d-flex justify-content-between mt-1">
                    <small class="text-muted">
                        开仓: ${position.price_open} | 当前: ${position.price_current}
                    </small>
                    <button class="btn btn-sm btn-outline-danger" onclick="closePosition(${position.ticket})">
                        <i class="fas fa-times"></i> 平仓
                    </button>
                </div>
            </div>
        `;
    });

    // 添加总计
    const totalProfitClass = totalProfit >= 0 ? 'text-success' : 'text-danger';
    html += `
        <div class="py-2 border-top bg-light">
            <div class="d-flex justify-content-between align-items-center">
                <strong>总计 (${positions.length} 个持仓)</strong>
                <strong class="${totalProfitClass}">$${totalProfit.toFixed(2)}</strong>
            </div>
        </div>
    `;

    container.innerHTML = html;
}

// 平仓单个持仓
function closePosition(ticket) {
    if (!confirm(`确定要平仓订单 ${ticket} 吗？`)) {
        return;
    }

    console.log(`🔄 平仓订单: ${ticket}`);

    fetch('/api/mt5/close-position', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ ticket: ticket })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            logTrading(`✅ 平仓成功: 订单 ${ticket}`);
            refreshPositions(); // 刷新持仓
            refreshTradingHistory(); // 刷新交易历史
        } else {
            logTrading(`❌ 平仓失败: ${data.error}`);
            alert(`平仓失败: ${data.error}`);
        }
    })
    .catch(error => {
        console.error('❌ 平仓异常:', error);
        logTrading(`❌ 平仓异常: ${error.message}`);
        alert(`平仓异常: ${error.message}`);
    });
}

// 一键平仓
function closeAllPositions() {
    const container = document.getElementById('positionsContainer');
    const positionElements = container.querySelectorAll('[onclick*="closePosition"]');

    if (positionElements.length === 0) {
        alert('当前没有持仓');
        return;
    }

    if (!confirm(`确定要平仓所有 ${positionElements.length} 个持仓吗？`)) {
        return;
    }

    console.log('🔄 一键平仓所有持仓...');
    logTrading('🔄 开始一键平仓...');

    fetch('/api/mt5/close-all-positions', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            logTrading(`✅ 一键平仓完成: 成功 ${data.closed_count} 个，失败 ${data.failed_count} 个`);

            if (data.failed_count > 0) {
                alert(`平仓完成，但有 ${data.failed_count} 个订单平仓失败`);
            }

            refreshPositions(); // 刷新持仓
            refreshTradingHistory(); // 刷新交易历史
        } else {
            logTrading(`❌ 一键平仓失败: ${data.error}`);
            alert(`一键平仓失败: ${data.error}`);
        }
    })
    .catch(error => {
        console.error('❌ 一键平仓异常:', error);
        logTrading(`❌ 一键平仓异常: ${error.message}`);
        alert(`一键平仓异常: ${error.message}`);
    });
}

// ==================== 交易历史功能 ====================

// 刷新交易历史
function refreshTradingHistory() {
    console.log('🔄 刷新当天交易历史...');

    fetch('/api/mt5/trading-history-today')
    .then(response => response.json())
    .then(data => {
        console.log('📊 交易历史数据:', data);
        displayTradingHistory(data);
    })
    .catch(error => {
        console.error('❌ 获取交易历史失败:', error);
        document.getElementById('tradingHistoryContainer').innerHTML = `
            <div class="text-center text-danger py-3">
                <i class="fas fa-exclamation-triangle"></i> 获取交易历史失败: ${error.message}
            </div>
        `;
    });
}

// 显示交易历史
function displayTradingHistory(data) {
    const container = document.getElementById('tradingHistoryContainer');

    if (!data.success) {
        container.innerHTML = `
            <div class="text-center text-danger py-3">
                <i class="fas fa-exclamation-triangle"></i> ${data.error}
            </div>
        `;
        return;
    }

    const trades = data.trades || [];

    if (trades.length === 0) {
        container.innerHTML = `
            <div class="text-center text-muted py-3">
                <i class="fas fa-inbox"></i> 今日暂无交易记录
            </div>
        `;
        return;
    }

    let html = '';
    let totalProfit = 0;
    let winCount = 0;

    trades.forEach(trade => {
        const profit = parseFloat(trade.profit) || 0;
        totalProfit += profit;

        if (profit > 0) winCount++;

        const profitClass = profit >= 0 ? 'text-success' : 'text-danger';
        const profitIcon = profit >= 0 ? 'fa-arrow-up' : 'fa-arrow-down';

        // 格式化时间
        const openTime = new Date(trade.time_open * 1000).toLocaleTimeString();
        const closeTime = trade.time_close ? new Date(trade.time_close * 1000).toLocaleTimeString() : '持仓中';

        html += `
            <div class="border-bottom py-2">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <strong>${trade.symbol}</strong>
                        <span class="badge ${trade.type === 0 ? 'bg-success' : 'bg-danger'} ms-2">
                            ${trade.type === 0 ? 'BUY' : 'SELL'}
                        </span>
                        <small class="text-muted ms-2">${trade.volume} 手</small>
                    </div>
                    <div class="text-end">
                        <div class="${profitClass}">
                            <i class="fas ${profitIcon}"></i>
                            $${profit.toFixed(2)}
                        </div>
                    </div>
                </div>
                <div class="d-flex justify-content-between mt-1">
                    <small class="text-muted">
                        开仓: ${openTime} @ ${trade.price_open}
                    </small>
                    <small class="text-muted">
                        ${trade.time_close ? `平仓: ${closeTime} @ ${trade.price_close}` : '持仓中'}
                    </small>
                </div>
            </div>
        `;
    });

    // 添加统计信息
    const winRate = trades.length > 0 ? (winCount / trades.length * 100).toFixed(1) : 0;
    const totalProfitClass = totalProfit >= 0 ? 'text-success' : 'text-danger';

    html += `
        <div class="py-2 border-top bg-light">
            <div class="row">
                <div class="col-6">
                    <small class="text-muted">总交易: ${trades.length}</small><br>
                    <small class="text-muted">胜率: ${winRate}%</small>
                </div>
                <div class="col-6 text-end">
                    <strong class="${totalProfitClass}">
                        总盈亏: $${totalProfit.toFixed(2)}
                    </strong>
                </div>
            </div>
        </div>
    `;

    container.innerHTML = html;
}

// ==================== 定期刷新功能 ====================

// 设置定期刷新持仓和交易历史
setInterval(() => {
    if (agentRunning) {
        refreshPositions();
        refreshTradingHistory();
    }
}, 10000); // 每10秒刷新一次

// 设置定期状态检查，确保前后端状态同步
setInterval(() => {
    verifyAgentStatusWithServer();
}, 30000); // 每30秒检查一次状态

// ==================== 账户名称管理 ====================

// 更新账户名称显示
function updateAccountName(accountInfo) {
    const accountNameElement = document.getElementById('accountName');

    if (accountInfo && accountInfo.login) {
        // 优先显示账户号码，更直观
        let displayText = `账户: ${accountInfo.login}`;

        // 如果有服务器信息，也显示出来
        if (accountInfo.server) {
            displayText += ` | ${accountInfo.server}`;
        }

        // 如果有账户类型信息，显示类型
        if (accountInfo.account_type) {
            const typeText = accountInfo.account_type === 'demo' ? '模拟' : '真实';
            displayText += ` (${typeText})`;
        }

        accountNameElement.textContent = displayText;
        accountNameElement.style.display = 'inline-block';
        console.log(`✅ 显示账户信息: ${displayText}`);
    } else {
        accountNameElement.textContent = '账户: 未知';
        accountNameElement.style.display = 'inline-block';
        console.log('⚠️ 账户信息不完整');
    }
}

// 隐藏账户名称
function hideAccountName() {
    const accountNameElement = document.getElementById('accountName');
    accountNameElement.style.display = 'none';
    console.log('🔒 隐藏账户名称');
}

// ==================== 配置状态管理 ====================

// 更新当前配置状态显示
function updateCurrentConfigStatus(configName) {
    const statusElement = document.getElementById('currentConfigStatus');
    const textElement = document.getElementById('currentConfigText');

    if (configName) {
        textElement.textContent = `当前配置: ${configName}`;
        statusElement.style.display = 'block';
        statusElement.className = 'alert alert-success py-2';
        console.log(`✅ 配置状态更新: ${configName}`);
    } else {
        textElement.textContent = '当前配置: 无';
        statusElement.style.display = 'block';
        statusElement.className = 'alert alert-warning py-2';
        console.log('⚠️ 配置状态: 无配置');
    }
}

// 清除配置状态
function clearCurrentConfigStatus() {
    const statusElement = document.getElementById('currentConfigStatus');
    statusElement.style.display = 'none';
    console.log('🔒 隐藏配置状态');
}

// 创建默认配置
function createDefaultConfiguration() {
    const defaultConfig = {
        name: '默认配置',
        ai_model: 'deepseek_v3',
        ai_strategy: 'demo_1',
        trading_symbols: ['EURUSD', 'XAUUSD'],
        analysis_interval: 15,
        user_requirements: '智能交易，平衡风险与收益。根据市场情况灵活调整策略。',
        risk_settings: {
            max_risk_percent: 2,
            max_lot_size: 0.1,
            stop_loss_points: 50,
            consider_risk_events: true
        },
        created_at: new Date().toISOString()
    };

    // 保存默认配置
    const savedConfigs = getSavedConfigurations();
    savedConfigs['默认配置'] = defaultConfig;
    localStorage.setItem('agentTradingRealConfigs', JSON.stringify(savedConfigs));

    console.log('✅ 已创建默认配置');
    logAI('✅ 已自动创建默认配置');
}

// 强制刷新配置显示
function forceRefreshConfigDisplay() {
    console.log('🔄 强制刷新配置显示...');

    // 获取当前选中的配置
    const selectedConfig = document.getElementById('savedConfigs').value;

    if (selectedConfig) {
        const savedConfigs = getSavedConfigurations();
        const config = savedConfigs[selectedConfig];

        if (config) {
            // 强制设置表单值
            const aiModelSelect = document.getElementById('aiModelSelect');
            const aiStrategySelect = document.getElementById('aiStrategySelect');
            const analysisInterval = document.getElementById('analysisInterval');
            const userRequirements = document.getElementById('userRequirements');

            if (aiModelSelect) {
                console.log(`🔧 强制刷新AI模型: ${config.ai_model}`);
                aiModelSelect.value = config.ai_model || '';
                console.log(`✅ 强制刷新AI模型结果: ${aiModelSelect.value}`);
            }
            if (aiStrategySelect) {
                console.log(`🔧 强制刷新AI策略: ${config.ai_strategy}`);
                aiStrategySelect.value = config.ai_strategy || '';
                console.log(`✅ 强制刷新AI策略结果: ${aiStrategySelect.value}`);
            }
            if (analysisInterval) analysisInterval.value = config.analysis_interval || 15;
            if (userRequirements) userRequirements.value = config.user_requirements || '';

            // 设置交易货币对
            if (config.trading_symbols && Array.isArray(config.trading_symbols)) {
                config.trading_symbols.forEach(symbol => {
                    const checkbox = document.querySelector(`input[value="${symbol}"]`);
                    if (checkbox) checkbox.checked = true;
                });
            }

            // 设置风险参数
            if (config.risk_settings) {
                const maxRisk = document.getElementById('maxRiskPercent');
                const minLot = document.getElementById('minLotSize');
                const maxLot = document.getElementById('maxLotSize');
                const stopLoss = document.getElementById('stopLossPoints');
                const takeProfit = document.getElementById('takeProfitPoints');
                const considerRiskEvents = document.getElementById('considerRiskEvents');

                if (maxRisk) maxRisk.value = config.risk_settings.max_risk_percent || 2;
                if (minLot) minLot.value = config.risk_settings.min_lot_size || 0.01;
                if (maxLot) maxLot.value = config.risk_settings.max_lot_size || 0.02;
                if (stopLoss) stopLoss.value = config.risk_settings.stop_loss_points || 50;
                if (takeProfit) takeProfit.value = config.risk_settings.take_profit_points || 100;
                if (considerRiskEvents) considerRiskEvents.checked = config.risk_settings.consider_risk_events !== false;
            }

            console.log(`✅ 强制刷新完成: ${selectedConfig}`);
        }
    }
}

</script>
{% endblock %}
