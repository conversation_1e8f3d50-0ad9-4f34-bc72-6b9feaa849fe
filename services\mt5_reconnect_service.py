#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
MT5自动重连服务
监控MT5连接状态，断线时自动尝试重连
"""

import threading
import time
import MetaTrader5 as mt5
from datetime import datetime, timedelta
import logging

class MT5ReconnectService:
    """MT5自动重连服务"""
    
    def __init__(self, mt5_service):
        self.mt5_service = mt5_service
        self.monitoring = False
        self.monitor_thread = None
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = 5
        self.reconnect_interval = 30  # 秒
        self.check_interval = 10  # 检查间隔（秒）
        self.last_successful_connection = None
        self.connection_lost_time = None
        
        # 重连配置
        self.auto_reconnect_enabled = True
        self.last_known_login = None
        self.last_known_server = None
        
        # 日志配置
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def start_monitoring(self):
        """开始监控MT5连接状态"""
        if self.monitoring:
            self.logger.info("MT5连接监控已在运行")
            return
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_connection, daemon=True)
        self.monitor_thread.start()
        self.logger.info("🔄 MT5连接监控已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        self.logger.info("⏹️ MT5连接监控已停止")
    
    def _monitor_connection(self):
        """监控连接状态的主循环"""
        while self.monitoring:
            try:
                if self._check_connection_status():
                    # 连接正常
                    if self.reconnect_attempts > 0:
                        self.logger.info("✅ MT5连接已恢复")
                        self.reconnect_attempts = 0
                        self.connection_lost_time = None
                    
                    self.last_successful_connection = datetime.now()
                    
                    # 更新最后已知的连接信息
                    self._update_last_known_credentials()
                    
                else:
                    # 连接断开
                    if self.connection_lost_time is None:
                        self.connection_lost_time = datetime.now()
                        self.logger.warning("⚠️ 检测到MT5连接断开")
                    
                    if self.auto_reconnect_enabled:
                        self._attempt_reconnect()
                
                time.sleep(self.check_interval)
                
            except Exception as e:
                self.logger.error(f"❌ 连接监控异常: {e}")
                time.sleep(self.check_interval)
    
    def _check_connection_status(self):
        """检查MT5连接状态"""
        try:
            # 检查MT5是否初始化
            if not self.mt5_service.connected:
                return False
            
            # 尝试获取账户信息来验证连接
            account_info = mt5.account_info()
            if account_info is None:
                return False
            
            # 尝试获取终端信息
            terminal_info = mt5.terminal_info()
            if terminal_info is None:
                return False
            
            # 检查终端是否连接到服务器
            if not terminal_info.connected:
                return False
            
            return True
            
        except Exception as e:
            self.logger.debug(f"连接检查异常: {e}")
            return False
    
    def _update_last_known_credentials(self):
        """更新最后已知的连接凭据"""
        try:
            account_info = mt5.account_info()
            if account_info:
                self.last_known_login = account_info.login
                self.last_known_server = account_info.server
        except Exception as e:
            self.logger.debug(f"更新连接凭据失败: {e}")
    
    def _attempt_reconnect(self):
        """尝试重连"""
        if self.reconnect_attempts >= self.max_reconnect_attempts:
            self.logger.error(f"❌ 已达到最大重连次数 ({self.max_reconnect_attempts})")
            return
        
        # 检查重连间隔
        if (self.connection_lost_time and 
            datetime.now() - self.connection_lost_time < timedelta(seconds=self.reconnect_interval)):
            return
        
        self.reconnect_attempts += 1
        self.logger.info(f"🔄 尝试重连 MT5 ({self.reconnect_attempts}/{self.max_reconnect_attempts})")
        
        try:
            # 先关闭现有连接
            self.mt5_service.disconnect()
            time.sleep(2)
            
            # 尝试重新连接
            success = False
            
            # 如果有最后已知的凭据，尝试使用它们
            if self.last_known_login and self.last_known_server:
                self.logger.info(f"🔄 使用已知凭据重连: {self.last_known_login}@{self.last_known_server}")
                success = self.mt5_service.connect(
                    login=str(self.last_known_login),
                    server=self.last_known_server
                )
            
            # 如果凭据重连失败，尝试自动连接
            if not success:
                self.logger.info("🔄 尝试自动重连")
                success = self.mt5_service.connect()
            
            if success:
                self.logger.info("✅ MT5重连成功")
                self.reconnect_attempts = 0
                self.connection_lost_time = None
                
                # 通知前端连接状态变化
                self._notify_connection_restored()
            else:
                self.logger.warning(f"❌ 重连失败，将在 {self.reconnect_interval} 秒后重试")
                
        except Exception as e:
            self.logger.error(f"❌ 重连过程异常: {e}")
    
    def _notify_connection_restored(self):
        """通知前端连接已恢复"""
        try:
            # 这里可以添加WebSocket通知或其他实时通知机制
            # 目前先记录日志
            self.logger.info("📡 连接恢复通知已发送")
        except Exception as e:
            self.logger.error(f"发送连接恢复通知失败: {e}")
    
    def force_reconnect(self):
        """强制重连"""
        self.logger.info("🔄 执行强制重连")
        self.reconnect_attempts = 0
        self.connection_lost_time = datetime.now()
        self._attempt_reconnect()
    
    def get_connection_status(self):
        """获取连接状态信息"""
        return {
            'monitoring': self.monitoring,
            'connected': self._check_connection_status(),
            'reconnect_attempts': self.reconnect_attempts,
            'max_attempts': self.max_reconnect_attempts,
            'last_successful_connection': self.last_successful_connection,
            'connection_lost_time': self.connection_lost_time,
            'auto_reconnect_enabled': self.auto_reconnect_enabled,
            'last_known_login': self.last_known_login,
            'last_known_server': self.last_known_server
        }
    
    def set_auto_reconnect(self, enabled):
        """设置自动重连开关"""
        self.auto_reconnect_enabled = enabled
        self.logger.info(f"🔧 自动重连已{'启用' if enabled else '禁用'}")
    
    def set_reconnect_config(self, max_attempts=None, interval=None, check_interval=None):
        """设置重连配置"""
        if max_attempts is not None:
            self.max_reconnect_attempts = max_attempts
        if interval is not None:
            self.reconnect_interval = interval
        if check_interval is not None:
            self.check_interval = check_interval
        
        self.logger.info(f"🔧 重连配置已更新: 最大尝试{self.max_reconnect_attempts}次, 间隔{self.reconnect_interval}秒")

# 全局重连服务实例
mt5_reconnect_service = None

def get_reconnect_service(mt5_service):
    """获取重连服务实例"""
    global mt5_reconnect_service
    if mt5_reconnect_service is None:
        mt5_reconnect_service = MT5ReconnectService(mt5_service)
    return mt5_reconnect_service
