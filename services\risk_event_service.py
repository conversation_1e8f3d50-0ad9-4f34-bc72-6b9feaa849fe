"""
风险事件监控服务
获取实时的高风险市场事件和信号
"""

import requests
import json
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional
import threading
import time


class RiskEventService:
    """风险事件监控服务"""
    
    def __init__(self):
        self.risk_events = []
        self.last_update = None
        self.monitoring = False
        self.monitor_thread = None
        
        # 风险等级定义
        self.risk_levels = {
            'LOW': 1,
            'MEDIUM': 2, 
            'HIGH': 3,
            'CRITICAL': 4
        }
        
    def get_current_risk_events(self) -> List[Dict]:
        """获取当前的风险事件"""
        try:
            # 合并多个数据源的风险事件
            events = []
            
            # 1. 经济日历高影响事件
            events.extend(self._get_economic_calendar_events())
            
            # 2. 市场波动性监控
            events.extend(self._get_volatility_events())
            
            # 3. 技术指标风险信号
            events.extend(self._get_technical_risk_signals())
            
            # 4. 新闻情绪分析
            events.extend(self._get_news_sentiment_events())
            
            # 按风险等级排序
            events.sort(key=lambda x: x.get('risk_level', 0), reverse=True)
            
            self.risk_events = events
            china_tz = timezone(timedelta(hours=8))
            self.last_update = datetime.now(china_tz)
            
            return events
            
        except Exception as e:
            print(f"❌ 获取风险事件失败: {e}")
            return []
    
    def _get_economic_calendar_events(self) -> List[Dict]:
        """获取经济日历高影响事件"""
        events = []
        
        try:
            # 模拟经济日历API调用
            # 实际应用中可以接入 ForexFactory, Investing.com 等API
            
            # 获取中国时区的当前时间 (UTC+8)
            china_tz = timezone(timedelta(hours=8))
            current_time = datetime.now(china_tz)

            # 模拟一些高影响事件（使用中国时区时间）
            high_impact_events = [
                {
                    'type': 'economic_event',
                    'title': 'US Non-Farm Payrolls',
                    'time': current_time.replace(hour=21, minute=30),  # 北京时间晚上9:30 (美国东部时间上午8:30)
                    'impact': 'HIGH',
                    'currency': 'USD',
                    'description': '美国非农就业数据发布，预期对USD产生重大影响',
                    'risk_level': 3
                },
                {
                    'type': 'economic_event',
                    'title': 'ECB Interest Rate Decision',
                    'time': current_time.replace(hour=20, minute=45),  # 北京时间晚上8:45 (欧洲时间下午1:45)
                    'impact': 'HIGH',
                    'currency': 'EUR',
                    'description': '欧央行利率决议，预期对EUR产生重大影响',
                    'risk_level': 3
                },
                {
                    'type': 'economic_event',
                    'title': 'China CPI Data',
                    'time': current_time.replace(hour=9, minute=30),   # 北京时间上午9:30
                    'impact': 'MEDIUM',
                    'currency': 'CNY',
                    'description': '中国CPI数据发布，影响人民币汇率',
                    'risk_level': 2
                }
            ]
            
            # 只返回未来2小时内的事件
            for event in high_impact_events:
                time_diff = (event['time'] - current_time).total_seconds() / 3600
                if -1 <= time_diff <= 2:  # 过去1小时到未来2小时
                    events.append(event)
            
        except Exception as e:
            print(f"❌ 获取经济日历事件失败: {e}")
        
        return events
    
    def _get_volatility_events(self) -> List[Dict]:
        """获取市场波动性事件"""
        events = []
        
        try:
            # 模拟波动性监控
            # 实际应用中可以计算ATR、VIX等指标
            
            # 检查主要货币对的波动性
            major_pairs = ['EURUSD', 'GBPUSD', 'USDJPY', 'XAUUSD']
            
            for pair in major_pairs:
                # 模拟波动性检查
                volatility_score = self._calculate_volatility_score(pair)
                
                if volatility_score > 80:
                    china_tz = timezone(timedelta(hours=8))
                    events.append({
                        'type': 'volatility_spike',
                        'title': f'{pair} 波动性异常',
                        'time': datetime.now(china_tz),
                        'symbol': pair,
                        'volatility_score': volatility_score,
                        'description': f'{pair} 当前波动性达到 {volatility_score}%，远超正常水平',
                        'risk_level': 3 if volatility_score > 90 else 2
                    })
            
        except Exception as e:
            print(f"❌ 获取波动性事件失败: {e}")
        
        return events
    
    def _get_technical_risk_signals(self) -> List[Dict]:
        """获取技术指标风险信号"""
        events = []
        
        try:
            # 模拟技术指标风险检测
            # 实际应用中可以计算RSI、MACD、布林带等指标
            
            china_tz = timezone(timedelta(hours=8))
            risk_signals = [
                {
                    'type': 'technical_signal',
                    'title': 'EURUSD RSI超买',
                    'time': datetime.now(china_tz),
                    'symbol': 'EURUSD',
                    'indicator': 'RSI',
                    'value': 85,
                    'description': 'EURUSD RSI达到85，处于严重超买状态，可能面临回调风险',
                    'risk_level': 2
                },
                {
                    'type': 'technical_signal',
                    'title': 'XAUUSD 突破关键阻力',
                    'time': datetime.now(china_tz),
                    'symbol': 'XAUUSD',
                    'indicator': 'Support/Resistance',
                    'description': 'XAUUSD突破关键阻力位2050，可能引发大幅波动',
                    'risk_level': 2
                }
            ]
            
            events.extend(risk_signals)
            
        except Exception as e:
            print(f"❌ 获取技术风险信号失败: {e}")
        
        return events
    
    def _get_news_sentiment_events(self) -> List[Dict]:
        """获取新闻情绪事件"""
        events = []
        
        try:
            # 模拟新闻情绪分析
            # 实际应用中可以接入新闻API和情绪分析服务
            
            china_tz = timezone(timedelta(hours=8))
            news_events = [
                {
                    'type': 'news_sentiment',
                    'title': '地缘政治紧张局势升级',
                    'time': datetime.now(china_tz),
                    'sentiment': 'NEGATIVE',
                    'confidence': 0.85,
                    'description': '国际地缘政治紧张局势升级，避险情绪上升，可能影响风险资产',
                    'risk_level': 3
                },
                {
                    'type': 'news_sentiment',
                    'title': '美联储官员鹰派言论',
                    'time': datetime.now(china_tz),
                    'sentiment': 'HAWKISH',
                    'confidence': 0.78,
                    'description': '美联储官员发表鹰派言论，暗示可能加息，USD可能走强',
                    'risk_level': 2
                }
            ]
            
            events.extend(news_events)
            
        except Exception as e:
            print(f"❌ 获取新闻情绪事件失败: {e}")
        
        return events
    
    def _calculate_volatility_score(self, symbol: str) -> float:
        """计算波动性评分（基于真实数据）"""
        try:
            # 系统严禁使用模拟数据，应该基于MT5真实数据计算波动性
            logger.error(f"❌ 系统严禁使用模拟波动性数据，请基于MT5真实数据计算 {symbol} 的波动性")
            return 0  # 返回0表示无法计算，需要真实数据
        except:
            return 0
    
    def get_risk_summary(self) -> Dict:
        """获取风险摘要"""
        events = self.get_current_risk_events()
        
        # 统计不同风险等级的事件数量
        risk_counts = {'LOW': 0, 'MEDIUM': 0, 'HIGH': 0, 'CRITICAL': 0}
        
        for event in events:
            risk_level = event.get('risk_level', 1)
            if risk_level == 1:
                risk_counts['LOW'] += 1
            elif risk_level == 2:
                risk_counts['MEDIUM'] += 1
            elif risk_level == 3:
                risk_counts['HIGH'] += 1
            elif risk_level == 4:
                risk_counts['CRITICAL'] += 1
        
        # 计算总体风险等级
        total_risk_score = sum(event.get('risk_level', 1) for event in events)
        
        if total_risk_score >= 10:
            overall_risk = 'CRITICAL'
        elif total_risk_score >= 6:
            overall_risk = 'HIGH'
        elif total_risk_score >= 3:
            overall_risk = 'MEDIUM'
        else:
            overall_risk = 'LOW'
        
        return {
            'overall_risk': overall_risk,
            'total_events': len(events),
            'risk_counts': risk_counts,
            'total_risk_score': total_risk_score,
            'last_update': self.last_update.isoformat() if self.last_update else None,
            'events': events[:5]  # 返回前5个最重要的事件
        }
    
    def format_risk_events_for_ai(self) -> str:
        """为AI分析格式化风险事件"""
        events = self.get_current_risk_events()
        
        if not events:
            return "当前无特殊风险事件"
        
        formatted_events = []
        
        for event in events[:10]:  # 最多返回10个事件
            risk_level_text = {1: '低风险', 2: '中风险', 3: '高风险', 4: '极高风险'}
            
            # 确保时间显示为中国时区
            event_time = event.get('time')
            if event_time:
                # 如果时间没有时区信息，假设为中国时区
                if event_time.tzinfo is None:
                    china_tz = timezone(timedelta(hours=8))
                    event_time = event_time.replace(tzinfo=china_tz)
                time_str = event_time.strftime('%Y-%m-%d %H:%M (北京时间)')
            else:
                china_tz = timezone(timedelta(hours=8))
                time_str = datetime.now(china_tz).strftime('%Y-%m-%d %H:%M (北京时间)')

            formatted_event = {
                '事件类型': event.get('type', 'unknown'),
                '标题': event.get('title', ''),
                '时间': time_str,
                '风险等级': risk_level_text.get(event.get('risk_level', 1), '未知'),
                '描述': event.get('description', ''),
                '影响货币': event.get('currency', event.get('symbol', ''))
            }
            
            formatted_events.append(formatted_event)
        
        return json.dumps(formatted_events, indent=2, ensure_ascii=False)


# 创建全局实例
risk_event_service = RiskEventService()
