#!/usr/bin/env python3
"""
快速检查训练状态
"""

import sqlite3
import os
from datetime import datetime, timed<PERSON><PERSON>

def main():
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        print("=== 训练任务状态检查 ===")
        
        # 检查表结构
        cursor.execute("PRAGMA table_info(training_tasks)")
        columns = cursor.fetchall()
        print("表字段:", [col[1] for col in columns])
        
        # 检查所有任务
        cursor.execute("SELECT * FROM training_tasks ORDER BY created_at DESC LIMIT 5")
        tasks = cursor.fetchall()
        
        print(f"\n最近的 {len(tasks)} 个任务:")
        for i, task in enumerate(tasks):
            print(f"{i+1}. ID: {task[0]} | 状态: {task[4] if len(task) > 4 else 'N/A'}")
        
        # 检查正在运行的任务
        cursor.execute("SELECT COUNT(*) FROM training_tasks WHERE status = 'running'")
        running_count = cursor.fetchone()[0]
        print(f"\n正在运行的任务数: {running_count}")
        
        if running_count > 0:
            cursor.execute("SELECT id, status, progress, updated_at FROM training_tasks WHERE status = 'running'")
            running_tasks = cursor.fetchall()
            
            for task in running_tasks:
                task_id, status, progress, updated_at = task
                print(f"  任务 {task_id}: {status} - {progress}% - 更新时间: {updated_at}")
                
                # 检查是否卡住
                if updated_at:
                    try:
                        last_update = datetime.fromisoformat(updated_at.replace('Z', '+00:00'))
                        now = datetime.now()
                        time_diff = now - last_update
                        
                        if time_diff > timedelta(minutes=5):
                            print(f"    ⚠️ 可能卡住: {time_diff} 没有更新")
                            print(f"    💡 建议重置任务 {task_id}")
                    except Exception as e:
                        print(f"    ❌ 时间解析错误: {e}")
        
        conn.close()
        
        # 检查GPU使用情况
        print(f"\n=== GPU状态检查 ===")
        try:
            import torch
            if torch.cuda.is_available():
                print(f"GPU可用: {torch.cuda.get_device_name(0)}")
                print(f"GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f}GB")
                
                # 检查GPU内存使用
                allocated = torch.cuda.memory_allocated(0) / 1024**3
                cached = torch.cuda.memory_reserved(0) / 1024**3
                print(f"已分配内存: {allocated:.1f}GB")
                print(f"缓存内存: {cached:.1f}GB")
                
                if allocated > 6:  # 如果使用超过6GB
                    print("⚠️ GPU内存使用较高，可能导致训练卡住")
            else:
                print("❌ GPU不可用")
        except Exception as e:
            print(f"❌ GPU检查失败: {e}")
        
        # 检查进程
        print(f"\n=== 进程检查 ===")
        try:
            import psutil
            python_processes = []
            
            for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'memory_info']):
                try:
                    if 'python' in proc.info['name'].lower():
                        cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                        if 'training' in cmdline.lower() or 'deep_learning' in cmdline.lower():
                            memory_mb = proc.info['memory_info'].rss / 1024 / 1024
                            python_processes.append({
                                'pid': proc.info['pid'],
                                'memory': memory_mb,
                                'cmdline': cmdline[:100]
                            })
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            if python_processes:
                print("发现训练相关进程:")
                for proc in python_processes:
                    print(f"  PID: {proc['pid']} | 内存: {proc['memory']:.1f}MB | 命令: {proc['cmdline']}...")
            else:
                print("没有发现训练相关进程")
                
        except Exception as e:
            print(f"❌ 进程检查失败: {e}")
        
        print(f"\n=== 建议操作 ===")
        if running_count > 0:
            print("1. 如果任务卡住超过10分钟，建议重置:")
            print("   - 访问深度学习训练页面")
            print("   - 点击'停止训练'按钮")
            print("   - 重新开始训练")
            print("2. 检查GPU内存是否充足")
            print("3. 考虑降低批次大小")
        else:
            print("✅ 当前没有运行中的训练任务")
            print("可以开始新的训练")
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")

if __name__ == '__main__':
    main()
