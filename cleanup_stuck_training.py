#!/usr/bin/env python3
"""
清理卡住的训练任务
"""

import sqlite3
from datetime import datetime

def cleanup_stuck_training_tasks():
    """清理卡住的训练任务"""
    
    print("🧹 清理卡住的训练任务")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 查找状态为running但长时间未更新的任务
        cursor.execute("""
            SELECT id, model_id, status, progress, created_at, updated_at,
                   CASE 
                       WHEN updated_at IS NULL THEN 
                           (julianday('now') - julianday(created_at)) * 24 * 60
                       ELSE 
                           (julianday('now') - julianday(updated_at)) * 24 * 60
                   END as minutes_since_update
            FROM training_tasks 
            WHERE status = 'running'
            ORDER BY created_at DESC
        """)
        
        stuck_tasks = cursor.fetchall()
        
        if not stuck_tasks:
            print("✅ 没有发现卡住的训练任务")
            conn.close()
            return
        
        print(f"🔍 发现 {len(stuck_tasks)} 个运行中的任务:")
        
        cleaned_count = 0
        
        for task in stuck_tasks:
            task_id, model_id, status, progress, created_at, updated_at, minutes_since_update = task
            
            print(f"\n🔹 任务: {task_id}")
            print(f"   模型: {model_id}")
            print(f"   进度: {progress}%")
            print(f"   创建: {created_at}")
            print(f"   更新: {updated_at}")
            print(f"   距离上次更新: {minutes_since_update:.1f} 分钟")
            
            # 如果超过5分钟没有更新，标记为失败
            if minutes_since_update > 5:
                print(f"   ⚠️ 任务可能卡住，将标记为失败")
                
                cursor.execute("""
                    UPDATE training_tasks 
                    SET status = 'failed', 
                        completed_at = CURRENT_TIMESTAMP,
                        updated_at = CURRENT_TIMESTAMP,
                        logs = json_set(COALESCE(logs, '{}'), '$.error', '任务超时，自动标记为失败')
                    WHERE id = ?
                """, (task_id,))
                
                cleaned_count += 1
                print(f"   ✅ 已标记为失败")
            else:
                print(f"   ✅ 任务状态正常")
        
        if cleaned_count > 0:
            conn.commit()
            print(f"\n🎉 清理完成，共处理 {cleaned_count} 个卡住的任务")
        else:
            print(f"\n✅ 所有任务状态正常，无需清理")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 清理失败: {e}")

def test_training_after_fix():
    """修复后测试训练"""
    
    print(f"\n🧪 测试修复后的训练功能")
    print("=" * 50)
    
    import requests
    import json
    import time
    
    # 登录
    session = requests.Session()
    
    try:
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post('http://127.0.0.1:5000/login', data=login_data)
        
        if response.status_code != 200:
            print("❌ 登录失败")
            return
        
        print("✅ 登录成功")
        
        # 启动一个简单的训练任务
        training_config = {
            'model_name': f'fix_test_{int(time.time())}',
            'model_type': 'LSTM',
            'symbol': 'XAUUSD',
            'timeframe': 'H1',
            'epochs': 2,  # 只训练2轮
            'batch_size': 16,
            'learning_rate': 0.01,
            'validation_split': 0.2,
            'sequence_length': 10,
            'features': ['close', 'volume']
        }
        
        print(f"🚀 启动测试训练...")
        
        response = session.post(
            'http://127.0.0.1:5000/api/deep-learning/start-training',
            json=training_config,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                task_id = result.get('task_id')
                print(f"✅ 训练启动成功!")
                print(f"   任务ID: {task_id}")
                
                # 监控前30秒的状态
                print(f"\n📊 监控训练状态 (30秒):")
                
                for i in range(10):  # 检查10次，每次3秒
                    time.sleep(3)
                    
                    try:
                        progress_response = session.get(
                            f'http://127.0.0.1:5000/api/deep-learning/training-progress/{task_id}'
                        )
                        
                        if progress_response.status_code == 200:
                            progress_result = progress_response.json()
                            
                            if progress_result.get('success'):
                                progress_data = progress_result['progress']
                                status = progress_data.get('status', 'unknown')
                                progress = progress_data.get('progress', 0)
                                epoch = progress_data.get('epoch', 0)
                                total_epochs = progress_data.get('total_epochs', 0)
                                
                                print(f"   第{i+1}次检查: 状态={status}, 进度={progress}%, 轮次={epoch}/{total_epochs}")
                                
                                if status in ['completed', 'failed']:
                                    print(f"   🏁 训练结束: {status}")
                                    break
                                    
                            else:
                                print(f"   ❌ 获取进度失败: {progress_result.get('error')}")
                        else:
                            print(f"   ❌ 进度API错误: {progress_response.status_code}")
                            
                    except Exception as e:
                        print(f"   ❌ 监控异常: {e}")
                
                print(f"\n🎯 测试结果:")
                print(f"   如果看到进度变化，说明修复成功")
                print(f"   如果状态一直是pending或running且进度为0，可能还有其他问题")
                
            else:
                print(f"❌ 训练启动失败: {result.get('error')}")
        else:
            print(f"❌ 训练请求失败: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   错误详情: {error_data}")
            except:
                print(f"   响应内容: {response.text[:200]}...")
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def main():
    """主函数"""
    
    print("🔧 深度学习训练问题修复")
    print("=" * 60)
    
    # 清理卡住的任务
    cleanup_stuck_training_tasks()
    
    # 测试修复后的训练
    test_training_after_fix()
    
    print(f"\n📋 修复总结")
    print("=" * 60)
    
    print(f"🔧 已修复的问题:")
    print(f"✅ 修复了 _prepare_training_data 函数中 task_id 未定义的错误")
    print(f"✅ 添加了 task_id 参数到数据准备函数")
    print(f"✅ 清理了卡住的训练任务")
    print(f"✅ 添加了错误回调的安全检查")
    
    print(f"\n💡 使用建议:")
    print(f"• 现在可以正常启动训练任务")
    print(f"• 训练进度应该能正常显示")
    print(f"• 如果仍有问题，检查GPU内存和数据准备过程")
    print(f"• 建议从小批次和短序列开始测试")

if __name__ == '__main__':
    main()
