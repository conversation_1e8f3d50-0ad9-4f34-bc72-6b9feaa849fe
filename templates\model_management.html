{% extends "base.html" %}

{% block title %}模型管理{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0 text-gray-800">
                    <i class="fas fa-database text-primary me-2"></i>
                    深度学习模型管理
                </h1>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-primary" onclick="refreshModels()">
                        <i class="fas fa-sync-alt me-1"></i>刷新
                    </button>
                    <a href="{{ url_for('model_training') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>训练新模型
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 模型统计卡片 -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                已完成模型
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="completedModels">0</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                训练中模型
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="trainingModels">0</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dumbbell fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                最佳准确率
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="bestAccuracy">0%</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-bullseye fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                存储空间
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="storageUsed">0 MB</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-hdd fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 模型列表 -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-list me-2"></i>模型列表
                    </h6>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" 
                                data-bs-toggle="dropdown">
                            <i class="fas fa-filter me-1"></i>筛选
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="filterModels('all')">全部模型</a></li>
                            <li><a class="dropdown-item" href="#" onclick="filterModels('completed')">已完成</a></li>
                            <li><a class="dropdown-item" href="#" onclick="filterModels('training')">训练中</a></li>
                            <li><a class="dropdown-item" href="#" onclick="filterModels('failed')">失败</a></li>
                        </ul>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="modelsTable">
                            <thead>
                                <tr>
                                    <th>模型名称</th>
                                    <th>类型</th>
                                    <th>交易品种</th>
                                    <th>时间框架</th>
                                    <th>状态</th>
                                    <th>准确率</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="modelsTableBody">
                                <tr>
                                    <td colspan="8" class="text-center py-4">
                                        <i class="fas fa-spinner fa-spin fa-2x text-muted mb-3"></i>
                                        <p class="text-muted">加载模型列表...</p>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 模型详情模态框 -->
<div class="modal fade" id="modelDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-info-circle me-2"></i>模型详情
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="modelDetailContent">
                <!-- 模型详情内容将在这里显示 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" id="deployModelBtn">
                    <i class="fas fa-rocket me-1"></i>部署模型
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteConfirmModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle text-warning me-2"></i>确认删除
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>您确定要删除这个模型吗？此操作不可撤销。</p>
                <div class="alert alert-warning">
                    <strong>注意：</strong>删除模型将同时删除相关的训练历史和性能数据。
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                    <i class="fas fa-trash me-1"></i>确认删除
                </button>
            </div>
        </div>
    </div>
</div>

<script>
let currentModels = [];
let selectedModelId = null;

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    loadModels();
});

// 加载模型列表
async function loadModels() {
    try {
        const response = await fetch('/api/deep-learning/models');
        const data = await response.json();
        
        if (data.success) {
            currentModels = data.models;
            updateModelStats(data.models);
            renderModelsTable(data.models);
        } else {
            showError('加载模型列表失败: ' + data.error);
        }
    } catch (error) {
        console.error('加载模型列表失败:', error);
        showError('加载模型列表失败: ' + error.message);
    }
}

// 更新模型统计
function updateModelStats(models) {
    const completed = models.filter(m => m.status === 'completed').length;
    const training = models.filter(m => m.status === 'training').length;
    
    let bestAccuracy = 0;
    let totalSize = 0;
    
    models.forEach(model => {
        if (model.performance && model.performance.accuracy) {
            bestAccuracy = Math.max(bestAccuracy, model.performance.accuracy);
        }
        if (model.size) {
            totalSize += model.size;
        }
    });
    
    document.getElementById('completedModels').textContent = completed;
    document.getElementById('trainingModels').textContent = training;
    document.getElementById('bestAccuracy').textContent = (bestAccuracy * 100).toFixed(1) + '%';
    document.getElementById('storageUsed').textContent = (totalSize / 1024 / 1024).toFixed(1) + ' MB';
}

// 渲染模型表格
function renderModelsTable(models) {
    const tbody = document.getElementById('modelsTableBody');
    
    if (models.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center py-4">
                    <i class="fas fa-inbox fa-2x text-muted mb-3"></i>
                    <p class="text-muted">暂无模型</p>
                    <a href="{{ url_for('model_training') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>训练第一个模型
                    </a>
                </td>
            </tr>
        `;
        return;
    }
    
    let html = '';
    models.forEach(model => {
        const statusBadge = getStatusBadge(model.status);
        const accuracy = model.performance && model.performance.accuracy 
            ? (model.performance.accuracy * 100).toFixed(1) + '%' 
            : 'N/A';
        
        html += `
            <tr>
                <td>
                    <strong>${model.name}</strong>
                    <br>
                    <small class="text-muted">${model.id}</small>
                </td>
                <td>
                    <span class="badge bg-info">${model.model_type.toUpperCase()}</span>
                </td>
                <td>${model.symbol}</td>
                <td>${model.timeframe}</td>
                <td>${statusBadge}</td>
                <td>${accuracy}</td>
                <td>
                    <small>${formatDate(model.created_at)}</small>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="showModelDetail('${model.id}')" 
                                title="查看详情">
                            <i class="fas fa-eye"></i>
                        </button>
                        ${model.status === 'completed' ? `
                            <button class="btn btn-outline-success" onclick="testModel('${model.id}')" 
                                    title="测试模型">
                                <i class="fas fa-play"></i>
                            </button>
                            <button class="btn btn-outline-info" onclick="downloadModel('${model.id}')" 
                                    title="下载模型">
                                <i class="fas fa-download"></i>
                            </button>
                        ` : ''}
                        <button class="btn btn-outline-danger" onclick="confirmDeleteModel('${model.id}')" 
                                title="删除模型">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    });
    
    tbody.innerHTML = html;
}

// 获取状态徽章
function getStatusBadge(status) {
    const badges = {
        'training': '<span class="badge bg-primary">训练中</span>',
        'completed': '<span class="badge bg-success">已完成</span>',
        'failed': '<span class="badge bg-danger">失败</span>',
        'pending': '<span class="badge bg-warning">等待中</span>'
    };
    return badges[status] || '<span class="badge bg-secondary">未知</span>';
}

// 格式化日期
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
}

// 筛选模型
function filterModels(filter) {
    let filteredModels = currentModels;
    
    if (filter !== 'all') {
        filteredModels = currentModels.filter(model => model.status === filter);
    }
    
    renderModelsTable(filteredModels);
}

// 显示模型详情
async function showModelDetail(modelId) {
    try {
        const response = await fetch(`/api/deep-learning/models/${modelId}`);
        const data = await response.json();
        
        if (data.success) {
            const model = data.model;
            selectedModelId = modelId;
            
            const content = `
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary"><i class="fas fa-info-circle me-1"></i>基本信息</h6>
                        <table class="table table-sm">
                            <tr><td>模型名称:</td><td><strong>${model.name}</strong></td></tr>
                            <tr><td>模型类型:</td><td><span class="badge bg-secondary">${model.model_type}</span></td></tr>
                            <tr><td>交易品种:</td><td><span class="badge bg-info">${model.symbol}</span></td></tr>
                            <tr><td>时间框架:</td><td><span class="badge bg-success">${model.timeframe}</span></td></tr>
                            <tr><td>状态:</td><td>${getStatusBadge(model.status)}</td></tr>
                            <tr><td>模型大小:</td><td>${model.model_size_mb ? model.model_size_mb.toFixed(2) + ' MB' : 'N/A'}</td></tr>
                            <tr><td>创建时间:</td><td>${formatDate(model.created_at)}</td></tr>
                            <tr><td>完成时间:</td><td>${model.completed_at ? formatDate(model.completed_at) : 'N/A'}</td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-success"><i class="fas fa-chart-line me-1"></i>性能指标</h6>
                        <table class="table table-sm">
                            <tr><td>准确率:</td><td>${model.performance?.accuracy ? '<span class="text-success fw-bold">' + (model.performance.accuracy * 100).toFixed(2) + '%</span>' : '<span class="text-muted">N/A</span>'}</td></tr>
                            <tr><td>精确率:</td><td>${model.performance?.precision ? '<span class="text-info fw-bold">' + (model.performance.precision * 100).toFixed(2) + '%</span>' : '<span class="text-muted">N/A</span>'}</td></tr>
                            <tr><td>召回率:</td><td>${model.performance?.recall ? '<span class="text-warning fw-bold">' + (model.performance.recall * 100).toFixed(2) + '%</span>' : '<span class="text-muted">N/A</span>'}</td></tr>
                            <tr><td>F1分数:</td><td>${model.performance?.f1_score ? '<span class="text-primary fw-bold">' + model.performance.f1_score.toFixed(3) + '</span>' : '<span class="text-muted">N/A</span>'}</td></tr>
                            <tr><td>训练损失:</td><td>${model.performance?.final_train_loss ? '<span class="text-danger">' + model.performance.final_train_loss.toFixed(4) + '</span>' : '<span class="text-muted">N/A</span>'}</td></tr>
                            <tr><td>验证损失:</td><td>${model.performance?.final_val_loss ? '<span class="text-warning">' + model.performance.final_val_loss.toFixed(4) + '</span>' : '<span class="text-muted">N/A</span>'}</td></tr>
                        </table>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-md-6">
                        <h6 class="text-info"><i class="fas fa-database me-1"></i>训练数据信息</h6>
                        <table class="table table-sm">
                            <tr><td>数据时间范围:</td><td>
                                ${model.data_info?.start_date && model.data_info?.end_date ?
                                    `<span class="text-primary">${model.data_info.start_date}</span> 至 <span class="text-primary">${model.data_info.end_date}</span>` :
                                    '<span class="text-muted">N/A</span>'}
                            </td></tr>
                            <tr><td>总样本数:</td><td>${model.data_info?.total_samples ? '<span class="fw-bold text-success">' + model.data_info.total_samples.toLocaleString() + '</span>' : '<span class="text-muted">N/A</span>'}</td></tr>
                            <tr><td>训练样本:</td><td>${model.data_info?.training_samples ? '<span class="text-info">' + model.data_info.training_samples.toLocaleString() + '</span>' : '<span class="text-muted">N/A</span>'}</td></tr>
                            <tr><td>验证样本:</td><td>${model.data_info?.validation_samples ? '<span class="text-warning">' + model.data_info.validation_samples.toLocaleString() + '</span>' : '<span class="text-muted">N/A</span>'}</td></tr>
                            <tr><td>使用特征:</td><td>
                                ${model.data_info?.features_used && model.data_info.features_used.length > 0 ?
                                    model.data_info.features_used.map(f => `<span class="badge bg-light text-dark me-1">${f}</span>`).join('') :
                                    '<span class="text-muted">N/A</span>'}
                            </td></tr>
                            <tr><td>数据质量:</td><td>
                                ${model.data_info?.data_quality === 'good' ? '<span class="badge bg-success">良好</span>' :
                                  model.data_info?.data_quality === 'fair' ? '<span class="badge bg-warning">一般</span>' :
                                  model.data_info?.data_quality === 'poor' ? '<span class="badge bg-danger">较差</span>' :
                                  '<span class="badge bg-secondary">未知</span>'}
                            </td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-warning"><i class="fas fa-cogs me-1"></i>模型配置</h6>
                        <table class="table table-sm">
                            <tr><td>序列长度:</td><td><span class="badge bg-primary">${model.config?.sequence_length || 'N/A'}</span></td></tr>
                            <tr><td>隐藏层大小:</td><td><span class="badge bg-info">${model.config?.hidden_size || 'N/A'}</span></td></tr>
                            <tr><td>网络层数:</td><td><span class="badge bg-success">${model.config?.num_layers || 'N/A'}</span></td></tr>
                            <tr><td>批次大小:</td><td><span class="badge bg-warning">${model.config?.batch_size || 'N/A'}</span></td></tr>
                            <tr><td>学习率:</td><td><span class="badge bg-danger">${model.config?.learning_rate || 'N/A'}</span></td></tr>
                            <tr><td>训练轮次:</td><td><span class="badge bg-secondary">${model.config?.epochs || 'N/A'}</span></td></tr>
                            <tr><td>验证比例:</td><td><span class="badge bg-dark">${model.config?.validation_split ? (model.config.validation_split * 100).toFixed(0) + '%' : 'N/A'}</span></td></tr>
                            <tr><td>早停耐心:</td><td><span class="badge bg-light text-dark">${model.config?.patience || 'N/A'}</span></td></tr>
                        </table>
                    </div>
                </div>
            `;
            
            document.getElementById('modelDetailContent').innerHTML = content;
            
            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('modelDetailModal'));
            modal.show();
        } else {
            showError('获取模型详情失败: ' + data.error);
        }
    } catch (error) {
        console.error('获取模型详情失败:', error);
        showError('获取模型详情失败: ' + error.message);
    }
}

// 确认删除模型
function confirmDeleteModel(modelId) {
    selectedModelId = modelId;
    const modal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
    modal.show();
}

// 删除模型
document.getElementById('confirmDeleteBtn').addEventListener('click', async function() {
    if (!selectedModelId) return;
    
    try {
        const response = await fetch(`/api/deep-learning/models/${selectedModelId}`, {
            method: 'DELETE'
        });
        
        const data = await response.json();
        
        if (data.success) {
            showSuccess('模型删除成功');
            loadModels(); // 重新加载列表
            
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal'));
            modal.hide();
        } else {
            showError('删除模型失败: ' + data.error);
        }
    } catch (error) {
        console.error('删除模型失败:', error);
        showError('删除模型失败: ' + error.message);
    }
});

// 测试模型
function testModel(modelId) {
    // 跳转到模型推理页面
    window.location.href = `{{ url_for('model_inference') }}?model=${modelId}`;
}

// 下载模型
function downloadModel(modelId) {
    window.open(`/api/deep-learning/models/${modelId}/download`, '_blank');
}

// 刷新模型列表
function refreshModels() {
    loadModels();
}

// 显示成功消息
function showSuccess(message) {
    // 这里可以实现一个更好的提示组件
    alert('✅ ' + message);
}

// 显示错误消息
function showError(message) {
    // 这里可以实现一个更好的提示组件
    alert('❌ ' + message);
}

// 格式化日期显示
function formatDate(dateString) {
    if (!dateString) return 'N/A';

    try {
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    } catch (e) {
        return dateString;
    }
}
</script>

<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

.text-xs {
    font-size: 0.7rem;
}

.shadow {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
}
</style>
{% endblock %}
