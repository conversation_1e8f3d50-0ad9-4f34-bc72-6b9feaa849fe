#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试模型推理导出问题
"""

import sys
import os
import sqlite3

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_optimization_results_table():
    """检查参数优化结果表"""
    print("🔍 检查参数优化结果表")
    print("=" * 50)
    
    db_path = 'trading_system.db'
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='parameter_optimization_results'
        """)
        
        if not cursor.fetchone():
            print("❌ parameter_optimization_results表不存在")
            conn.close()
            return False
        
        print("✅ parameter_optimization_results表存在")
        
        # 检查表结构
        cursor.execute("PRAGMA table_info(parameter_optimization_results)")
        columns = cursor.fetchall()
        
        print("📋 表结构:")
        for col in columns:
            print(f"   {col[1]} ({col[2]})")
        
        # 检查记录数
        cursor.execute("SELECT COUNT(*) FROM parameter_optimization_results")
        total_count = cursor.fetchone()[0]
        
        print(f"📊 总记录数: {total_count}")
        
        if total_count > 0:
            # 显示最近的记录
            cursor.execute("""
                SELECT model_id, symbol, timeframe, risk_preference, created_at
                FROM parameter_optimization_results 
                ORDER BY created_at DESC 
                LIMIT 5
            """)
            
            recent_records = cursor.fetchall()
            print("📋 最近的记录:")
            for record in recent_records:
                model_id, symbol, timeframe, risk_preference, created_at = record
                print(f"   {model_id} | {symbol} | {timeframe} | {risk_preference} | {created_at}")
        
        conn.close()
        return total_count > 0
        
    except Exception as e:
        print(f"❌ 检查表失败: {e}")
        return False

def test_export_function():
    """测试导出函数"""
    print("\n🧪 测试导出函数")
    print("=" * 50)
    
    try:
        from services.deep_learning_service import DeepLearningService
        
        service = DeepLearningService()
        
        # 测试获取保存的优化结果
        print("📋 测试获取保存的优化结果...")
        
        # 先获取所有可用的模型ID
        db_path = 'trading_system.db'
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT DISTINCT model_id, symbol, timeframe, risk_preference
            FROM parameter_optimization_results 
            ORDER BY created_at DESC 
            LIMIT 3
        """)
        
        available_results = cursor.fetchall()
        conn.close()
        
        if not available_results:
            print("❌ 没有可用的优化结果进行测试")
            return False
        
        print(f"✅ 找到 {len(available_results)} 个可用的优化结果")
        
        # 测试第一个结果
        model_id, symbol, timeframe, risk_preference = available_results[0]
        print(f"📊 测试导出: {model_id} | {symbol} | {timeframe} | {risk_preference}")
        
        # 测试获取保存的结果
        saved_results = service.get_saved_optimization_results(
            model_id, symbol, timeframe, risk_preference
        )
        
        if saved_results.get('success'):
            print("✅ 成功获取保存的优化结果")
            optimization_results = saved_results.get('optimization_results', [])
            print(f"   优化结果数量: {len(optimization_results)}")
            
            # 测试导出CSV
            export_result = service.export_optimization_results_csv(
                model_id, symbol, timeframe, 'week', risk_preference
            )
            
            if export_result.get('success'):
                print("✅ 导出CSV成功")
                print(f"   文件名: {export_result.get('filename')}")
                print(f"   记录数: {export_result.get('record_count')}")
                
                # 检查CSV内容
                csv_content = export_result.get('csv_content', '')
                lines = csv_content.split('\n')
                print(f"   CSV行数: {len(lines)}")
                
                if len(lines) > 0:
                    print(f"   标题行: {lines[0][:100]}...")
                
                return True
            else:
                print(f"❌ 导出CSV失败: {export_result.get('error')}")
                return False
        else:
            print(f"❌ 获取保存的优化结果失败: {saved_results.get('error')}")
            return False
            
    except Exception as e:
        print(f"❌ 测试导出函数失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def test_api_endpoint():
    """测试API端点"""
    print("\n🌐 测试API端点")
    print("=" * 50)
    
    try:
        import requests
        import json
        
        # 获取一个可用的优化结果
        db_path = 'trading_system.db'
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT model_id, symbol, timeframe, risk_preference
            FROM parameter_optimization_results 
            ORDER BY created_at DESC 
            LIMIT 1
        """)
        
        result = cursor.fetchone()
        conn.close()
        
        if not result:
            print("❌ 没有可用的优化结果进行API测试")
            return False
        
        model_id, symbol, timeframe, risk_preference = result
        
        # 测试API请求
        url = 'http://127.0.0.1:5000/api/deep-learning/export-optimization-results'
        data = {
            'model_id': model_id,
            'symbol': symbol,
            'timeframe': timeframe,
            'optimization_period': 'week',
            'risk_preference': risk_preference
        }
        
        print(f"📡 测试API请求: {url}")
        print(f"   数据: {data}")
        
        response = requests.post(url, json=data, timeout=30)
        
        print(f"📊 响应状态: {response.status_code}")
        print(f"   响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            # 检查是否是CSV文件
            content_type = response.headers.get('Content-Type', '')
            if 'text/csv' in content_type:
                print("✅ 成功获取CSV文件")
                content = response.text
                lines = content.split('\n')
                print(f"   CSV行数: {len(lines)}")
                if len(lines) > 0:
                    print(f"   标题行: {lines[0][:100]}...")
                return True
            else:
                print(f"❌ 响应不是CSV文件，Content-Type: {content_type}")
                print(f"   响应内容: {response.text[:200]}...")
                return False
        else:
            print(f"❌ API请求失败: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   错误信息: {error_data}")
            except:
                print(f"   响应内容: {response.text[:200]}...")
            return False
            
    except Exception as e:
        print(f"❌ 测试API端点失败: {e}")
        return False

def generate_fix_recommendations():
    """生成修复建议"""
    print("\n💡 修复建议")
    print("=" * 50)
    
    print("🔧 可能的问题和解决方案:")
    print()
    
    print("1. 数据库问题:")
    print("   • 检查 parameter_optimization_results 表是否存在")
    print("   • 确认表中有优化结果数据")
    print("   • 验证数据格式是否正确")
    print()
    
    print("2. 前端问题:")
    print("   • 确认选择了正确的模型")
    print("   • 检查是否执行了参数优化")
    print("   • 验证优化结果是否显示在页面上")
    print()
    
    print("3. API问题:")
    print("   • 检查API路由是否正确")
    print("   • 验证请求参数是否完整")
    print("   • 确认服务器响应格式")
    print()
    
    print("4. 权限问题:")
    print("   • 确认用户已登录")
    print("   • 检查文件下载权限")
    print("   • 验证浏览器安全设置")
    print()
    
    print("🚀 立即修复步骤:")
    print("1. 重启应用程序")
    print("2. 重新执行参数优化")
    print("3. 确认优化结果显示正常")
    print("4. 尝试导出功能")
    print("5. 检查浏览器控制台错误")

def main():
    """主函数"""
    print("🔧 模型推理导出问题调试工具")
    print("=" * 80)
    
    print("📋 问题描述:")
    print("• 模型推理 → 深度学习模型推理 → 优化参数回测 → 结果导出失败")
    print("• 显示：❌ 导出失败，请重试")
    print()
    
    # 检查数据库表
    table_ok = check_optimization_results_table()
    
    # 测试导出函数
    function_ok = test_export_function() if table_ok else False
    
    # 测试API端点
    api_ok = test_api_endpoint() if table_ok else False
    
    # 生成修复建议
    generate_fix_recommendations()
    
    print("\n📊 调试总结")
    print("=" * 80)
    
    if table_ok:
        print("✅ 数据库表检查通过")
    else:
        print("❌ 数据库表有问题")
    
    if function_ok:
        print("✅ 导出函数测试通过")
    else:
        print("❌ 导出函数有问题")
    
    if api_ok:
        print("✅ API端点测试通过")
    else:
        print("❌ API端点有问题")
    
    print("\n💡 修复状态:")
    print("✅ 已修复前端导出按钮逻辑")
    print("✅ 已添加推理结果导出功能")
    print("✅ 已增强错误处理")
    
    if table_ok and function_ok and api_ok:
        print("\n🎉 所有测试通过，导出功能应该正常工作")
    else:
        print("\n⚠️  发现问题，请根据上述建议进行修复")

if __name__ == "__main__":
    main()
