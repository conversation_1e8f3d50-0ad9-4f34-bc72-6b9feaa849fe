#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MT5连接和订单执行诊断工具
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_mt5_connection():
    """测试MT5连接"""
    print("🔧 测试MT5连接")
    print("=" * 50)
    
    try:
        import MetaTrader5 as mt5
        
        # 尝试初始化MT5
        print("📋 尝试初始化MT5...")
        if not mt5.initialize():
            error = mt5.last_error()
            print(f"❌ MT5初始化失败: {error}")
            return False
        
        print("✅ MT5初始化成功")
        
        # 获取账户信息
        account_info = mt5.account_info()
        if account_info is None:
            print("❌ 无法获取账户信息")
            mt5.shutdown()
            return False
        
        print(f"✅ 账户信息获取成功:")
        print(f"   账户号: {account_info.login}")
        print(f"   服务器: {account_info.server}")
        print(f"   余额: {account_info.balance}")
        print(f"   净值: {account_info.equity}")
        print(f"   保证金: {account_info.margin}")
        print(f"   自由保证金: {account_info.margin_free}")
        print(f"   交易权限: {account_info.trade_allowed}")
        print(f"   专家顾问权限: {account_info.trade_expert}")
        
        # 检查交易权限
        if not account_info.trade_allowed:
            print("⚠️  账户交易权限被禁用")
        
        if not account_info.trade_expert:
            print("⚠️  专家顾问交易权限被禁用")
        
        # 获取终端信息
        terminal_info = mt5.terminal_info()
        if terminal_info:
            print(f"✅ 终端信息:")
            print(f"   终端路径: {terminal_info.path}")
            print(f"   数据路径: {terminal_info.data_path}")
            print(f"   连接状态: {terminal_info.connected}")
            print(f"   交易权限: {terminal_info.trade_allowed}")
            print(f"   专家顾问权限: {terminal_info.tradeapi_disabled}")
            print(f"   DLL权限: {terminal_info.dlls_allowed}")
        
        # 测试获取品种信息
        print(f"\n📋 测试获取XAUUSD品种信息...")
        symbol_info = mt5.symbol_info("XAUUSD")
        if symbol_info is None:
            print("❌ 无法获取XAUUSD品种信息")
        else:
            print(f"✅ XAUUSD品种信息:")
            print(f"   点值: {symbol_info.point}")
            print(f"   最小手数: {symbol_info.volume_min}")
            print(f"   最大手数: {symbol_info.volume_max}")
            print(f"   手数步长: {symbol_info.volume_step}")
            print(f"   交易模式: {symbol_info.trade_mode}")
            print(f"   交易执行: {symbol_info.trade_execution}")
        
        # 测试获取当前价格
        tick = mt5.symbol_info_tick("XAUUSD")
        if tick is None:
            print("❌ 无法获取XAUUSD当前价格")
        else:
            print(f"✅ XAUUSD当前价格:")
            print(f"   买价: {tick.ask}")
            print(f"   卖价: {tick.bid}")
            print(f"   时间: {tick.time}")
        
        # 检查当前持仓
        positions = mt5.positions_get()
        if positions is None:
            print("❌ 无法获取持仓信息")
        else:
            print(f"✅ 当前持仓数量: {len(positions)}")
            if len(positions) > 0:
                print("   持仓详情:")
                for pos in positions:
                    print(f"     {pos.symbol} {pos.type} {pos.volume} | 订单号: {pos.ticket}")
        
        # 检查历史订单
        from datetime import datetime, timedelta
        end_time = datetime.now()
        start_time = end_time - timedelta(days=1)  # 最近24小时
        
        deals = mt5.history_deals_get(start_time, end_time)
        if deals is None:
            print("❌ 无法获取历史交易")
        else:
            print(f"✅ 最近24小时交易数量: {len(deals)}")
            if len(deals) > 0:
                print("   最近交易:")
                for deal in deals[-5:]:  # 显示最近5笔
                    deal_type = "买入" if deal.type == 0 else "卖出"
                    print(f"     {deal.symbol} {deal_type} {deal.volume} | 订单号: {deal.order} | 时间: {datetime.fromtimestamp(deal.time)}")
        
        mt5.shutdown()
        return True
        
    except ImportError:
        print("❌ MetaTrader5模块未安装")
        return False
    except Exception as e:
        print(f"❌ MT5连接测试失败: {e}")
        return False

def test_mt5_service():
    """测试MT5服务"""
    print("\n🔧 测试MT5服务")
    print("=" * 50)
    
    try:
        from services.mt5_service import MT5Service
        
        mt5_service = MT5Service()
        
        # 测试连接
        print("📋 测试MT5服务连接...")
        if mt5_service.connect():
            print("✅ MT5服务连接成功")
            
            # 测试获取价格
            tick = mt5_service.get_current_price("XAUUSD")
            if tick:
                print(f"✅ 获取XAUUSD价格成功: 买价={tick.ask}, 卖价={tick.bid}")
            else:
                print("❌ 获取价格失败")
            
            # 测试获取持仓
            positions = mt5_service.get_positions()
            print(f"✅ 当前持仓数量: {len(positions) if positions else 0}")
            
            # 检查连接状态
            if mt5_service.is_connected():
                print("✅ MT5服务连接状态正常")
            else:
                print("❌ MT5服务连接状态异常")
                
        else:
            print("❌ MT5服务连接失败")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ MT5服务测试失败: {e}")
        return False

def analyze_order_execution_issue():
    """分析订单执行问题"""
    print("\n🔍 分析订单执行问题")
    print("=" * 50)
    
    print("📋 可能的原因:")
    print("1. MT5连接问题")
    print("   • MT5终端未运行或连接断开")
    print("   • 账户登录状态异常")
    print("   • 网络连接不稳定")
    print()
    
    print("2. 交易权限问题")
    print("   • 账户交易权限被禁用")
    print("   • 专家顾问权限被禁用")
    print("   • DLL调用权限被禁用")
    print()
    
    print("3. 市场条件问题")
    print("   • 市场关闭时间")
    print("   • 品种交易时间限制")
    print("   • 流动性不足")
    print()
    
    print("4. 订单参数问题")
    print("   • 手数不符合最小/最大限制")
    print("   • 止损止盈距离不符合要求")
    print("   • 价格偏差过大")
    print()
    
    print("5. 资金问题")
    print("   • 账户余额不足")
    print("   • 保证金不足")
    print("   • 达到最大持仓限制")

def generate_troubleshooting_steps():
    """生成故障排除步骤"""
    print("\n🔧 故障排除步骤")
    print("=" * 50)
    
    print("📋 立即检查:")
    print("1. 确保MT5终端正在运行")
    print("2. 确保账户已正确登录")
    print("3. 检查账户余额和保证金")
    print("4. 验证交易权限设置")
    print("5. 检查当前市场时间")
    print()
    
    print("📋 详细诊断:")
    print("1. 运行此诊断脚本")
    print("2. 查看MT5终端的专家日志")
    print("3. 检查Python应用程序日志")
    print("4. 尝试手动在MT5中下单")
    print("5. 测试小额订单执行")
    print()
    
    print("📋 配置检查:")
    print("1. MT5设置 → 选项 → 专家顾问")
    print("   • 允许自动交易: ✓")
    print("   • 允许DLL导入: ✓")
    print("   • 允许导入外部专家: ✓")
    print("2. 账户设置")
    print("   • 交易权限: 启用")
    print("   • API访问: 启用")
    print("3. 防火墙和安全软件")
    print("   • 允许MT5和Python程序网络访问")

def main():
    """主函数"""
    print("🔧 MT5连接和订单执行诊断工具")
    print("=" * 80)
    
    print("📋 诊断目标:")
    print("• 检查MT5连接状态")
    print("• 验证交易权限设置")
    print("• 分析订单执行问题")
    print("• 提供故障排除建议")
    print()
    
    # 测试MT5连接
    mt5_ok = test_mt5_connection()
    
    # 测试MT5服务
    service_ok = test_mt5_service()
    
    # 分析问题
    analyze_order_execution_issue()
    
    # 生成故障排除步骤
    generate_troubleshooting_steps()
    
    print("\n📊 诊断总结")
    print("=" * 80)
    
    if mt5_ok and service_ok:
        print("✅ MT5连接和服务正常")
        print("💡 如果仍有订单执行问题，可能是:")
        print("   • 市场时间或流动性问题")
        print("   • 订单参数不符合要求")
        print("   • 临时的网络或服务器问题")
    elif mt5_ok and not service_ok:
        print("⚠️  MT5连接正常，但服务层有问题")
        print("💡 建议检查MT5Service类的实现")
    elif not mt5_ok:
        print("❌ MT5连接有问题")
        print("💡 请先解决MT5连接问题")
    
    print("\n💡 下一步建议:")
    print("1. 解决发现的连接问题")
    print("2. 尝试执行小额测试交易")
    print("3. 监控MT5终端和应用程序日志")
    print("4. 如有必要，联系经纪商技术支持")

if __name__ == "__main__":
    main()
