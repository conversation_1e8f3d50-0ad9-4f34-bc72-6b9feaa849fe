#!/usr/bin/env python3
"""
AI推理模型训练进度诊断和修复工具
"""

import sqlite3
import os
import sys
import time
from datetime import datetime, timedelta

class TrainingProgressDiagnostic:
    def __init__(self):
        self.db_path = 'trading_system.db'
        
    def check_database_connection(self):
        """检查数据库连接"""
        print("🔍 检查数据库连接...")
        
        if not os.path.exists(self.db_path):
            print(f"❌ 数据库文件不存在: {self.db_path}")
            return False
            
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 检查表是否存在
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='training_tasks'")
            if not cursor.fetchone():
                print("❌ training_tasks表不存在")
                conn.close()
                return False
                
            print("✅ 数据库连接正常")
            conn.close()
            return True
            
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            return False
    
    def get_training_tasks_status(self):
        """获取训练任务状态"""
        print("\n📊 检查训练任务状态...")
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 获取所有任务统计
            cursor.execute("SELECT status, COUNT(*) FROM training_tasks GROUP BY status")
            status_counts = cursor.fetchall()
            
            print("任务状态统计:")
            for status, count in status_counts:
                print(f"  {status}: {count} 个")
            
            # 获取最近的任务详情
            cursor.execute("""
                SELECT id, name, symbol, timeframe, status, progress, current_epoch, total_epochs,
                       created_at, updated_at
                FROM training_tasks 
                ORDER BY updated_at DESC 
                LIMIT 5
            """)
            
            recent_tasks = cursor.fetchall()
            
            if recent_tasks:
                print(f"\n📋 最近的 {len(recent_tasks)} 个任务:")
                
                stuck_tasks = []
                for task in recent_tasks:
                    task_id, name, symbol, timeframe, status, progress, epoch, total_epochs, created_at, updated_at = task
                    
                    print(f"\n🔹 {name}")
                    print(f"   ID: {task_id[:8]}...")
                    print(f"   品种: {symbol} {timeframe}")
                    print(f"   状态: {status}")
                    print(f"   进度: {progress}%")
                    print(f"   轮次: {epoch}/{total_epochs}")
                    print(f"   创建: {created_at}")
                    print(f"   更新: {updated_at}")
                    
                    # 检查是否卡住
                    if status in ['running', 'pending'] and updated_at:
                        try:
                            last_update = datetime.fromisoformat(updated_at.replace('Z', '+00:00').replace('+00:00', ''))
                            now = datetime.now()
                            time_diff = now - last_update
                            
                            if time_diff > timedelta(minutes=5):
                                print(f"   ⚠️ 可能卡住 (已 {time_diff} 没有更新)")
                                stuck_tasks.append(task_id)
                            else:
                                print(f"   ✅ 正常 (最后更新: {time_diff} 前)")
                        except Exception as e:
                            print(f"   ❓ 时间解析错误: {e}")
                            stuck_tasks.append(task_id)
                
                conn.close()
                return recent_tasks, stuck_tasks
            else:
                print("✅ 没有找到任何训练任务")
                conn.close()
                return [], []
                
        except Exception as e:
            print(f"❌ 获取任务状态失败: {e}")
            return [], []
    
    def fix_stuck_tasks(self, stuck_task_ids):
        """修复卡住的任务"""
        if not stuck_task_ids:
            print("✅ 没有卡住的任务需要修复")
            return
            
        print(f"\n🔧 修复 {len(stuck_task_ids)} 个卡住的任务...")
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            for task_id in stuck_task_ids:
                print(f"🔄 修复任务: {task_id[:8]}...")
                
                # 重置任务状态
                cursor.execute("""
                    UPDATE training_tasks
                    SET status = 'pending',
                        progress = 0,
                        current_epoch = 0,
                        train_loss = 0,
                        val_loss = 0,
                        updated_at = ?
                    WHERE id = ?
                """, (datetime.now().isoformat(), task_id))
                
                print(f"   ✅ 任务已重置为pending状态")
            
            conn.commit()
            conn.close()
            
            print(f"✅ 已修复 {len(stuck_task_ids)} 个任务")
            print("💡 请在前端页面重新开始训练")
            
        except Exception as e:
            print(f"❌ 修复任务失败: {e}")
    
    def create_test_task(self):
        """创建测试任务来验证系统"""
        print("\n🧪 创建测试任务...")
        
        try:
            import uuid
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            test_task_id = str(uuid.uuid4())
            test_name = f"测试任务_{datetime.now().strftime('%H%M%S')}"
            
            cursor.execute("""
                INSERT INTO training_tasks
                (id, name, symbol, timeframe, status, progress, current_epoch, total_epochs,
                 train_loss, val_loss, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                test_task_id, test_name, 'XAUUSD', 'H1', 'pending', 0, 0, 10,
                0.0, 0.0, datetime.now().isoformat(), datetime.now().isoformat()
            ))
            
            conn.commit()
            
            # 验证创建成功
            cursor.execute("SELECT name, status FROM training_tasks WHERE id = ?", (test_task_id,))
            result = cursor.fetchone()
            
            if result:
                print(f"✅ 测试任务创建成功: {result[0]} ({result[1]})")
                
                # 模拟进度更新
                for progress in [10, 30, 50, 80, 100]:
                    cursor.execute("""
                        UPDATE training_tasks
                        SET progress = ?, updated_at = ?
                        WHERE id = ?
                    """, (progress, datetime.now().isoformat(), test_task_id))
                    conn.commit()
                    print(f"   📈 进度更新: {progress}%")
                    time.sleep(0.5)
                
                # 标记完成
                cursor.execute("""
                    UPDATE training_tasks
                    SET status = 'completed', updated_at = ?
                    WHERE id = ?
                """, (datetime.now().isoformat(), test_task_id))
                conn.commit()
                
                print("✅ 测试任务完成，进度更新机制正常")
                
                # 清理测试任务
                cursor.execute("DELETE FROM training_tasks WHERE id = ?", (test_task_id,))
                conn.commit()
                print("🗑️ 测试任务已清理")
                
            else:
                print("❌ 测试任务创建失败")
            
            conn.close()
            
        except Exception as e:
            print(f"❌ 创建测试任务失败: {e}")
    
    def run_diagnostic(self):
        """运行完整诊断"""
        print("🚀 AI推理模型训练进度诊断工具")
        print("=" * 80)
        
        # 1. 检查数据库连接
        if not self.check_database_connection():
            print("\n💡 建议:")
            print("1. 检查数据库文件是否存在")
            print("2. 运行 python init_database.py 初始化数据库")
            return
        
        # 2. 检查训练任务状态
        recent_tasks, stuck_tasks = self.get_training_tasks_status()
        
        # 3. 修复卡住的任务
        if stuck_tasks:
            print(f"\n⚠️ 发现 {len(stuck_tasks)} 个可能卡住的任务")
            
            choice = input("是否要修复这些任务? (y/n): ").lower().strip()
            if choice == 'y':
                self.fix_stuck_tasks(stuck_tasks)
            else:
                print("⏭️ 跳过修复")
        
        # 4. 测试进度更新机制
        if not recent_tasks or len([t for t in recent_tasks if t[4] == 'running']) == 0:
            print(f"\n🧪 没有正在运行的任务，测试进度更新机制...")
            self.create_test_task()
        
        print(f"\n🎯 诊断完成")
        print("=" * 80)
        print("💡 如果问题仍然存在:")
        print("1. 重启应用程序: python app.py")
        print("2. 在前端页面重新开始训练")
        print("3. 监控训练进度是否正常更新")
        print("4. 检查系统资源使用情况")
        print("5. 考虑减少模型复杂度或批次大小")

def main():
    diagnostic = TrainingProgressDiagnostic()
    diagnostic.run_diagnostic()

if __name__ == '__main__':
    main()
