#!/usr/bin/env python3
"""
手动创建管理员用户
"""

from app import app, db
from models import User
import secrets
from werkzeug.security import generate_password_hash

def create_admin_user():
    """创建管理员用户"""
    with app.app_context():
        try:
            # 检查是否已有管理员
            existing_admin = User.query.filter_by(username='admin').first()
            if existing_admin:
                print("管理员用户已存在")
                return
            
            # 创建管理员用户
            salt = secrets.token_hex(16)
            password = "admin123"
            password_hash = generate_password_hash(password + salt)
            
            admin_user = User(
                username='admin',
                email='<EMAIL>',
                password_hash=password_hash,
                password_salt=salt,
                user_type='admin',
                is_approved=True,
                is_active=True,
                real_name='系统管理员'
            )
            
            db.session.add(admin_user)
            db.session.commit()
            
            print("✅ 管理员用户创建成功")
            print("   用户名: admin")
            print("   密码: admin123")
            print("   邮箱: <EMAIL>")
            
            # 验证创建结果
            created_user = User.query.filter_by(username='admin').first()
            if created_user:
                print(f"✅ 验证成功: 用户ID {created_user.id}")
                
                # 测试密码
                if created_user.check_password('admin123'):
                    print("✅ 密码验证成功")
                else:
                    print("❌ 密码验证失败")
            
        except Exception as e:
            print(f"❌ 创建管理员用户失败: {e}")
            import traceback
            traceback.print_exc()
            db.session.rollback()

if __name__ == "__main__":
    create_admin_user()
