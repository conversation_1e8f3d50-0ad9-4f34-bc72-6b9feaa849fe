#!/usr/bin/env python3
"""
创建缺失的数据库表
"""

from app import app, db
from models import User, AIModelConfig, TradingAccount, Trade, Strategy, BacktestResult

def create_missing_tables():
    """创建缺失的数据库表"""
    with app.app_context():
        try:
            print("正在创建缺失的数据库表...")

            # 创建所有表
            db.create_all()

            print("✅ 数据库表创建完成")

            # 检查表是否存在
            from sqlalchemy import inspect
            inspector = inspect(db.engine)
            tables = inspector.get_table_names()
            print(f"数据库中的表: {tables}")

            # 创建一些示例数据
            print("正在创建示例数据...")

            # 检查是否有管理员用户
            admin_user = User.query.filter_by(username='admin').first()
            if admin_user:
                print(f"找到管理员用户: {admin_user.username}")

                # 创建示例交易账户
                existing_account = TradingAccount.query.filter_by(user_id=admin_user.id).first()
                if not existing_account:
                    demo_account = TradingAccount(
                        user_id=admin_user.id,
                        account_name='模拟账户',
                        account_type='demo',
                        broker='Demo Broker',
                        balance=10000.0,
                        equity=10000.0,
                        margin=0.0,
                        is_active=True
                    )

                    real_account = TradingAccount(
                        user_id=admin_user.id,
                        account_name='真实账户',
                        account_type='real',
                        broker='Real Broker',
                        balance=5000.0,
                        equity=5000.0,
                        margin=0.0,
                        is_active=True
                    )

                    db.session.add(demo_account)
                    db.session.add(real_account)
                    db.session.commit()
                    print("✅ 示例交易账户创建完成")
                else:
                    print("交易账户已存在")

                # 创建示例策略
                existing_strategy = Strategy.query.filter_by(user_id=admin_user.id).first()
                if not existing_strategy:
                    sample_strategy = Strategy(
                        user_id=admin_user.id,
                        name='示例策略',
                        description='这是一个示例交易策略',
                        strategy_type='technical',
                        parameters='{"ma_period": 20, "rsi_period": 14}',
                        is_active=True
                    )

                    db.session.add(sample_strategy)
                    db.session.commit()
                    print("✅ 示例策略创建完成")
                else:
                    print("策略已存在")

            print("✅ 示例数据创建完成")

        except Exception as e:
            print(f"❌ 创建表时出错: {e}")
            import traceback
            traceback.print_exc()
            db.session.rollback()

if __name__ == "__main__":
    create_missing_tables()
