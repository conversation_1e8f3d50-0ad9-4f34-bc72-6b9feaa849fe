#!/usr/bin/env python3
"""
诊断深度学习训练进度卡住的问题
"""

import sqlite3
import json
import time
import requests
from datetime import datetime, timedelta

def login_session():
    """登录并获取会话"""
    session = requests.Session()
    
    try:
        # 登录
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post('http://127.0.0.1:5000/login', data=login_data)
        
        if response.status_code == 200 and 'dashboard' in response.url:
            print("✅ 登录成功")
            return session
        else:
            print(f"❌ 登录失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return None

def check_database_status():
    """检查数据库中的训练任务状态"""
    print("\n📊 检查数据库中的训练任务状态")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 查找最近的训练任务
        cursor.execute("""
            SELECT id, model_id, status, progress, current_epoch, total_epochs,
                   train_loss, val_loss, created_at, updated_at, started_at,
                   (julianday('now') - julianday(updated_at)) * 24 * 60 as minutes_since_update
            FROM training_tasks 
            WHERE created_at > datetime('now', '-24 hours')
            ORDER BY created_at DESC 
            LIMIT 5
        """)
        
        tasks = cursor.fetchall()
        
        if not tasks:
            print("❌ 最近24小时内没有训练任务")
            return None
        
        print(f"📋 找到 {len(tasks)} 个最近的训练任务:")
        
        stuck_tasks = []
        
        for task in tasks:
            (task_id, model_id, status, progress, current_epoch, total_epochs,
             train_loss, val_loss, created_at, updated_at, started_at, minutes_since_update) = task
            
            print(f"\n🔹 任务: {task_id[:8]}...")
            print(f"   模型ID: {model_id}")
            print(f"   状态: {status}")
            print(f"   进度: {progress}%")
            print(f"   轮次: {current_epoch}/{total_epochs}")
            print(f"   创建时间: {created_at}")
            print(f"   更新时间: {updated_at}")
            print(f"   距离上次更新: {minutes_since_update:.1f} 分钟")
            
            # 判断是否卡住
            if status == 'running' and minutes_since_update > 5:
                print(f"   ⚠️ 可能卡住了 (超过5分钟无更新)")
                stuck_tasks.append(task_id)
            elif status == 'running' and progress == 25:
                print(f"   ⚠️ 进度卡在25% (数据准备阶段)")
                stuck_tasks.append(task_id)
        
        conn.close()
        
        return stuck_tasks
        
    except Exception as e:
        print(f"❌ 检查数据库状态失败: {e}")
        return None

def check_api_responses(task_ids):
    """检查API响应"""
    print(f"\n📡 检查API响应")
    print("=" * 60)
    
    session = login_session()
    if not session:
        return
    
    for task_id in task_ids:
        print(f"\n🔍 检查任务: {task_id[:8]}...")
        
        try:
            # 检查进度API
            response = session.get(f'http://127.0.0.1:5000/api/deep-learning/training-progress/{task_id}')
            
            print(f"   进度API状态: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get('success'):
                    progress_data = result['progress']
                    
                    print(f"   ✅ API响应成功")
                    print(f"   状态: {progress_data.get('status')}")
                    print(f"   进度: {progress_data.get('progress')}%")
                    print(f"   轮次: {progress_data.get('epoch')}/{progress_data.get('total_epochs')}")
                    print(f"   训练损失: {progress_data.get('train_loss')}")
                    print(f"   验证损失: {progress_data.get('val_loss')}")
                    
                    # 检查日志信息
                    logs = progress_data.get('logs')
                    if logs:
                        try:
                            if isinstance(logs, str):
                                log_data = json.loads(logs)
                            else:
                                log_data = logs
                            
                            stage = log_data.get('stage', 'unknown')
                            message = log_data.get('message', '')
                            print(f"   阶段: {stage}")
                            print(f"   消息: {message}")
                            
                            if 'error' in log_data:
                                print(f"   ❌ 错误: {log_data['error']}")
                                
                        except Exception as e:
                            print(f"   ⚠️ 日志解析失败: {e}")
                            print(f"   原始日志: {logs}")
                else:
                    print(f"   ❌ API错误: {result.get('error')}")
            else:
                print(f"   ❌ HTTP错误: {response.status_code}")
                print(f"   响应内容: {response.text[:200]}...")
                
        except Exception as e:
            print(f"   ❌ API请求异常: {e}")

def monitor_progress_updates(task_id, duration=60):
    """监控进度更新"""
    print(f"\n📊 监控进度更新 (任务: {task_id[:8]}..., 时长: {duration}秒)")
    print("=" * 60)
    
    session = login_session()
    if not session:
        return
    
    last_progress = None
    last_epoch = None
    update_count = 0
    
    for i in range(duration // 5):  # 每5秒检查一次
        try:
            response = session.get(f'http://127.0.0.1:5000/api/deep-learning/training-progress/{task_id}')
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get('success'):
                    progress_data = result['progress']
                    current_progress = progress_data.get('progress', 0)
                    current_epoch = progress_data.get('epoch', 0)
                    status = progress_data.get('status', 'unknown')
                    
                    print(f"[{i*5:3d}s] 进度: {current_progress}%, 轮次: {current_epoch}, 状态: {status}")
                    
                    # 检查是否有更新
                    if last_progress is not None:
                        if current_progress != last_progress or current_epoch != last_epoch:
                            update_count += 1
                            print(f"       ✅ 检测到更新 (总更新次数: {update_count})")
                    
                    last_progress = current_progress
                    last_epoch = current_epoch
                    
                    # 如果训练完成或失败，退出监控
                    if status in ['completed', 'failed', 'stopped']:
                        print(f"       🏁 训练结束: {status}")
                        break
                else:
                    print(f"[{i*5:3d}s] ❌ API错误: {result.get('error')}")
            else:
                print(f"[{i*5:3d}s] ❌ HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"[{i*5:3d}s] ❌ 监控异常: {e}")
        
        time.sleep(5)
    
    print(f"\n📈 监控结果:")
    print(f"   总更新次数: {update_count}")
    print(f"   平均更新间隔: {duration / max(1, update_count):.1f} 秒")
    
    if update_count == 0:
        print(f"   ⚠️ 在 {duration} 秒内没有检测到任何进度更新")
        return False
    elif update_count < 3:
        print(f"   ⚠️ 更新频率较低，可能存在问题")
        return False
    else:
        print(f"   ✅ 进度更新正常")
        return True

def main():
    print("🔍 深度学习训练进度卡住问题诊断")
    print("=" * 60)
    
    # 1. 检查数据库状态
    stuck_tasks = check_database_status()
    
    if not stuck_tasks:
        print("\n✅ 没有发现卡住的训练任务")
        return
    
    # 2. 检查API响应
    check_api_responses(stuck_tasks)
    
    # 3. 监控第一个卡住的任务
    if stuck_tasks:
        task_id = stuck_tasks[0]
        is_updating = monitor_progress_updates(task_id, 60)
        
        if not is_updating:
            print(f"\n🔧 建议的修复方案:")
            print(f"1. 重启应用程序: python app.py")
            print(f"2. 停止卡住的训练任务")
            print(f"3. 重新开始训练")
            print(f"4. 检查GPU内存和系统资源")

if __name__ == '__main__':
    main()
