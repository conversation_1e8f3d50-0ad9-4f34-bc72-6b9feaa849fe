{% extends "base.html" %}

{% block page_title %}专业图表分析{% endblock %}

{% block extra_css %}
<style>
.chart-container {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    padding: 20px;
    margin-bottom: 20px;
}

.indicator-panel {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 15px;
}

.timeframe-buttons .btn {
    margin: 2px;
    border-radius: 20px;
}

.symbol-search {
    position: relative;
}

.symbol-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-radius: 5px;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
}

.symbol-item {
    padding: 10px;
    cursor: pointer;
    border-bottom: 1px solid #eee;
}

.symbol-item:hover {
    background-color: #f8f9fa;
}

.indicator-checkbox {
    margin: 5px 0;
}

.chart-loading {
    text-align: center;
    padding: 50px;
    color: #666;
}
</style>
{% endblock %}

{% block content %}
<div class="row">
    <!-- 左侧控制面板 -->
    <div class="col-lg-3 mb-4">
        <!-- 交易品种选择 -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-search"></i>
                    交易品种
                </h5>
            </div>
            <div class="card-body">
                <div class="symbol-search">
                    <input type="text" class="form-control" id="symbolSearch" 
                           placeholder="搜索交易品种..." autocomplete="off">
                    <div class="symbol-dropdown" id="symbolDropdown">
                        <!-- 搜索结果将在这里显示 -->
                    </div>
                </div>
                
                <div class="mt-3">
                    <label class="form-label">当前品种</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="currentSymbol" 
                               value="EURUSD" readonly>
                        <button class="btn btn-outline-primary" onclick="loadChart()">
                            <i class="fas fa-chart-line"></i>
                        </button>
                    </div>
                </div>
                
                <!-- 快速选择 -->
                <div class="mt-3">
                    <label class="form-label">热门品种</label>
                    <div class="d-grid gap-1">
                        <button class="btn btn-outline-secondary btn-sm" onclick="selectSymbol('EURUSD')">EUR/USD</button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="selectSymbol('GBPUSD')">GBP/USD</button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="selectSymbol('USDJPY')">USD/JPY</button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="selectSymbol('AUDUSD')">AUD/USD</button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="selectSymbol('USDCAD')">USD/CAD</button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="selectSymbol('XAUUSD')">XAU/USD</button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 时间框架 -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-clock"></i>
                    时间框架
                </h5>
            </div>
            <div class="card-body">
                <div class="timeframe-buttons">
                    <button class="btn btn-outline-primary btn-sm" onclick="setTimeframe('M1')">M1</button>
                    <button class="btn btn-outline-primary btn-sm" onclick="setTimeframe('M5')">M5</button>
                    <button class="btn btn-outline-primary btn-sm" onclick="setTimeframe('M15')">M15</button>
                    <button class="btn btn-outline-primary btn-sm" onclick="setTimeframe('M30')">M30</button>
                    <button class="btn btn-outline-primary btn-sm active" onclick="setTimeframe('H1')">H1</button>
                    <button class="btn btn-outline-primary btn-sm" onclick="setTimeframe('H4')">H4</button>
                    <button class="btn btn-outline-primary btn-sm" onclick="setTimeframe('D1')">D1</button>
                    <button class="btn btn-outline-primary btn-sm" onclick="setTimeframe('W1')">W1</button>
                    <button class="btn btn-outline-primary btn-sm" onclick="setTimeframe('MN1')">MN1</button>
                </div>
            </div>
        </div>
        
        <!-- 技术指标 -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar"></i>
                    技术指标
                </h5>
            </div>
            <div class="card-body">
                <!-- 移动平均线 -->
                <div class="indicator-panel">
                    <h6>移动平均线</h6>
                    <div class="indicator-checkbox">
                        <input type="checkbox" id="ma5" checked>
                        <label for="ma5">MA5</label>
                    </div>
                    <div class="indicator-checkbox">
                        <input type="checkbox" id="ma10" checked>
                        <label for="ma10">MA10</label>
                    </div>
                    <div class="indicator-checkbox">
                        <input type="checkbox" id="ma20" checked>
                        <label for="ma20">MA20</label>
                    </div>
                    <div class="indicator-checkbox">
                        <input type="checkbox" id="ma50">
                        <label for="ma50">MA50</label>
                    </div>
                </div>
                
                <!-- 布林带 -->
                <div class="indicator-panel">
                    <h6>布林带</h6>
                    <div class="indicator-checkbox">
                        <input type="checkbox" id="bollinger">
                        <label for="bollinger">Bollinger Bands</label>
                    </div>
                </div>
                
                <!-- 振荡器 -->
                <div class="indicator-panel">
                    <h6>振荡器</h6>
                    <div class="indicator-checkbox">
                        <input type="checkbox" id="rsi">
                        <label for="rsi">RSI</label>
                    </div>
                    <div class="indicator-checkbox">
                        <input type="checkbox" id="macd" checked>
                        <label for="macd">MACD</label>
                    </div>
                    <div class="indicator-checkbox">
                        <input type="checkbox" id="stochastic">
                        <label for="stochastic">Stochastic</label>
                    </div>
                </div>
                
                <!-- 成交量 -->
                <div class="indicator-panel">
                    <h6>成交量</h6>
                    <div class="indicator-checkbox">
                        <input type="checkbox" id="volume" checked>
                        <label for="volume">Volume</label>
                    </div>
                </div>
                
                <button class="btn btn-primary w-100 mt-3" onclick="updateIndicators()">
                    <i class="fas fa-sync"></i>
                    更新指标
                </button>
            </div>
        </div>
        
        <!-- 图表设置 -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-cog"></i>
                    图表设置
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label">图表类型</label>
                    <select class="form-select" id="chartType">
                        <option value="candlestick" selected>蜡烛图</option>
                        <option value="ohlc">OHLC</option>
                        <option value="line">线图</option>
                        <option value="area">面积图</option>
                    </select>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">数据条数</label>
                    <select class="form-select" id="dataCount">
                        <option value="100">100</option>
                        <option value="200">200</option>
                        <option value="500" selected>500</option>
                        <option value="1000">1000</option>
                    </select>
                </div>
                
                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="autoRefresh">
                        <label class="form-check-label" for="autoRefresh">
                            自动刷新 (30秒)
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 右侧图表区域 -->
    <div class="col-lg-9">
        <!-- 主图表 -->
        <div class="chart-container">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="mb-0">
                    <span id="chartTitle">EUR/USD H1</span>
                    <span class="badge bg-success ms-2" id="chartStatus">实时</span>
                </h5>
                <div>
                    <button class="btn btn-outline-primary btn-sm" onclick="fullscreen()">
                        <i class="fas fa-expand"></i>
                    </button>
                    <button class="btn btn-outline-secondary btn-sm" onclick="saveChart()">
                        <i class="fas fa-download"></i>
                    </button>
                </div>
            </div>
            
            <div id="mainChart" style="height: 600px;">
                <div class="chart-loading">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">正在加载图表数据...</p>
                </div>
            </div>
        </div>
        
        <!-- 市场信息 -->
        <div class="row mt-4">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body text-center">
                        <h6 class="text-muted">买入价</h6>
                        <h4 class="profit-positive" id="bidPrice">1.0856</h4>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body text-center">
                        <h6 class="text-muted">卖出价</h6>
                        <h4 class="profit-negative" id="askPrice">1.0858</h4>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body text-center">
                        <h6 class="text-muted">点差</h6>
                        <h4 id="spread">2.0</h4>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 技术分析摘要 -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line"></i>
                    技术分析摘要
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="text-center">
                            <h6 class="text-muted">总体趋势</h6>
                            <span class="badge bg-success fs-6" id="overallTrend">看涨</span>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <h6 class="text-muted">移动平均</h6>
                            <span class="badge bg-success fs-6" id="maTrend">看涨</span>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <h6 class="text-muted">振荡器</h6>
                            <span class="badge bg-warning fs-6" id="oscillatorTrend">中性</span>
                        </div>
                    </div>
                </div>
                
                <div class="mt-3">
                    <div class="row">
                        <div class="col-md-6">
                            <small class="text-muted">支撑位:</small>
                            <span id="supportLevel">1.0820</span>
                        </div>
                        <div class="col-md-6">
                            <small class="text-muted">阻力位:</small>
                            <span id="resistanceLevel">1.0890</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentSymbol = 'EURUSD';
let currentTimeframe = 'H1';
let chartInstance = null;
let autoRefreshInterval = null;

// 交易品种数据
const symbolsData = [
    { symbol: 'EURUSD', name: 'EUR/USD', category: 'Major' },
    { symbol: 'GBPUSD', name: 'GBP/USD', category: 'Major' },
    { symbol: 'USDJPY', name: 'USD/JPY', category: 'Major' },
    { symbol: 'AUDUSD', name: 'AUD/USD', category: 'Major' },
    { symbol: 'USDCAD', name: 'USD/CAD', category: 'Major' },
    { symbol: 'USDCHF', name: 'USD/CHF', category: 'Major' },
    { symbol: 'NZDUSD', name: 'NZD/USD', category: 'Major' },
    { symbol: 'XAUUSD', name: 'XAU/USD', category: 'Metal' },
    { symbol: 'XAGUSD', name: 'XAG/USD', category: 'Metal' },
    { symbol: 'BTCUSD', name: 'BTC/USD', category: 'Crypto' }
];

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeChart();
    setupEventListeners();
    loadChart();
});

// 设置事件监听器
function setupEventListeners() {
    // 品种搜索
    document.getElementById('symbolSearch').addEventListener('input', function() {
        const query = this.value.toLowerCase();
        const dropdown = document.getElementById('symbolDropdown');
        
        if (query.length > 0) {
            const filtered = symbolsData.filter(item => 
                item.symbol.toLowerCase().includes(query) || 
                item.name.toLowerCase().includes(query)
            );
            
            dropdown.innerHTML = '';
            filtered.forEach(item => {
                const div = document.createElement('div');
                div.className = 'symbol-item';
                div.innerHTML = `<strong>${item.symbol}</strong> - ${item.name}`;
                div.onclick = () => selectSymbol(item.symbol);
                dropdown.appendChild(div);
            });
            
            dropdown.style.display = 'block';
        } else {
            dropdown.style.display = 'none';
        }
    });
    
    // 点击外部关闭下拉框
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.symbol-search')) {
            document.getElementById('symbolDropdown').style.display = 'none';
        }
    });
    
    // 自动刷新
    document.getElementById('autoRefresh').addEventListener('change', function() {
        if (this.checked) {
            autoRefreshInterval = setInterval(loadChart, 30000);
        } else {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
            }
        }
    });
}

// 初始化图表
function initializeChart() {
    // 这里可以初始化图表库
    console.log('初始化图表...');
}

// 选择交易品种
function selectSymbol(symbol) {
    currentSymbol = symbol;
    document.getElementById('currentSymbol').value = symbol;
    document.getElementById('symbolDropdown').style.display = 'none';
    document.getElementById('symbolSearch').value = '';
    updateChartTitle();
    loadChart();
}

// 设置时间框架
function setTimeframe(timeframe) {
    // 更新按钮状态
    document.querySelectorAll('.timeframe-buttons .btn').forEach(btn => {
        btn.classList.remove('active');
    });
    event.target.classList.add('active');
    
    currentTimeframe = timeframe;
    updateChartTitle();
    loadChart();
}

// 更新图表标题
function updateChartTitle() {
    document.getElementById('chartTitle').textContent = `${currentSymbol} ${currentTimeframe}`;
}

// 加载图表数据
function loadChart() {
    const chartDiv = document.getElementById('mainChart');
    chartDiv.innerHTML = `
        <div class="chart-loading">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2">正在加载 ${currentSymbol} ${currentTimeframe} 数据...</p>
        </div>
    `;
    
    // 模拟API调用
    setTimeout(() => {
        createChart();
        updateMarketInfo();
        updateTechnicalSummary();
    }, 1500);
}

// 创建图表
function createChart() {
    // 生成模拟数据
    const data = generateMockData();
    
    // 获取选中的指标
    const indicators = getSelectedIndicators();
    
    // 创建Plotly图表
    const traces = [];
    
    // K线图
    traces.push({
        x: data.map(d => d.time),
        open: data.map(d => d.open),
        high: data.map(d => d.high),
        low: data.map(d => d.low),
        close: data.map(d => d.close),
        type: 'candlestick',
        name: currentSymbol,
        increasing: { line: { color: '#00ff88' } },
        decreasing: { line: { color: '#ff4444' } }
    });
    
    // 移动平均线
    if (indicators.ma5) {
        traces.push({
            x: data.map(d => d.time),
            y: data.map(d => d.ma5),
            type: 'scatter',
            mode: 'lines',
            name: 'MA5',
            line: { color: '#ff7f0e', width: 1 }
        });
    }
    
    if (indicators.ma20) {
        traces.push({
            x: data.map(d => d.time),
            y: data.map(d => d.ma20),
            type: 'scatter',
            mode: 'lines',
            name: 'MA20',
            line: { color: '#d62728', width: 1 }
        });
    }
    
    const layout = {
        title: `${currentSymbol} ${currentTimeframe}`,
        xaxis: {
            type: 'date',
            rangeslider: { visible: false }
        },
        yaxis: {
            title: '价格'
        },
        height: 600,
        margin: { t: 50, r: 50, b: 50, l: 80 },
        template: 'plotly_white'
    };
    
    Plotly.newPlot('mainChart', traces, layout, { responsive: true });
}

// 生成模拟数据
function generateMockData() {
    const data = [];
    let price = 1.0856;
    const now = new Date();
    
    for (let i = 500; i >= 0; i--) {
        const time = new Date(now.getTime() - i * 60 * 60 * 1000); // 1小时间隔
        
        const change = (Math.random() - 0.5) * 0.002;
        price += change;
        
        const high = price + Math.random() * 0.001;
        const low = price - Math.random() * 0.001;
        const open = price - change / 2;
        const close = price;
        
        // 简单移动平均
        const ma5 = data.length >= 4 ? 
            data.slice(-4).reduce((sum, d) => sum + d.close, 0) / 4 + close / 5 : close;
        const ma20 = data.length >= 19 ? 
            data.slice(-19).reduce((sum, d) => sum + d.close, 0) / 19 + close / 20 : close;
        
        data.push({
            time: time,
            open: open,
            high: high,
            low: low,
            close: close,
            volume: Math.random() * 1000000,
            ma5: ma5,
            ma20: ma20
        });
    }
    
    return data;
}

// 获取选中的指标
function getSelectedIndicators() {
    return {
        ma5: document.getElementById('ma5').checked,
        ma10: document.getElementById('ma10').checked,
        ma20: document.getElementById('ma20').checked,
        ma50: document.getElementById('ma50').checked,
        bollinger: document.getElementById('bollinger').checked,
        rsi: document.getElementById('rsi').checked,
        macd: document.getElementById('macd').checked,
        stochastic: document.getElementById('stochastic').checked,
        volume: document.getElementById('volume').checked
    };
}

// 更新指标
function updateIndicators() {
    loadChart();
}

// 更新市场信息
function updateMarketInfo() {
    const bid = 1.0856 + (Math.random() - 0.5) * 0.001;
    const ask = bid + 0.0002;
    const spread = ((ask - bid) * 10000).toFixed(1);
    
    document.getElementById('bidPrice').textContent = bid.toFixed(5);
    document.getElementById('askPrice').textContent = ask.toFixed(5);
    document.getElementById('spread').textContent = spread;
}

// 更新技术分析摘要
function updateTechnicalSummary() {
    // 模拟技术分析结果
    const trends = ['看涨', '看跌', '中性'];
    const colors = ['bg-success', 'bg-danger', 'bg-warning'];
    
    const overallIndex = Math.floor(Math.random() * 3);
    const maIndex = Math.floor(Math.random() * 3);
    const oscIndex = Math.floor(Math.random() * 3);
    
    document.getElementById('overallTrend').textContent = trends[overallIndex];
    document.getElementById('overallTrend').className = `badge ${colors[overallIndex]} fs-6`;
    
    document.getElementById('maTrend').textContent = trends[maIndex];
    document.getElementById('maTrend').className = `badge ${colors[maIndex]} fs-6`;
    
    document.getElementById('oscillatorTrend').textContent = trends[oscIndex];
    document.getElementById('oscillatorTrend').className = `badge ${colors[oscIndex]} fs-6`;
    
    // 支撑阻力位
    const currentPrice = 1.0856;
    document.getElementById('supportLevel').textContent = (currentPrice - 0.0036).toFixed(4);
    document.getElementById('resistanceLevel').textContent = (currentPrice + 0.0034).toFixed(4);
}

// 全屏显示
function fullscreen() {
    const chartElement = document.getElementById('mainChart');
    if (chartElement.requestFullscreen) {
        chartElement.requestFullscreen();
    }
}

// 保存图表
function saveChart() {
    Plotly.downloadImage('mainChart', {
        format: 'png',
        width: 1200,
        height: 800,
        filename: `${currentSymbol}_${currentTimeframe}_chart`
    });
}
</script>
{% endblock %}
