#!/usr/bin/env python3
import sqlite3
import json

conn = sqlite3.connect('trading_system.db')
cursor = conn.cursor()

cursor.execute('''
    SELECT id, status, progress, current_epoch, total_epochs, logs, updated_at 
    FROM training_tasks 
    WHERE status = "running" 
    ORDER BY created_at DESC 
    LIMIT 1
''')

result = cursor.fetchone()
if result:
    task_id, status, progress, current_epoch, total_epochs, logs, updated_at = result
    print(f'任务ID: {task_id}')
    print(f'状态: {status}')
    print(f'进度: {progress}%')
    print(f'轮次: {current_epoch}/{total_epochs}')
    print(f'更新时间: {updated_at}')
    
    if logs:
        try:
            logs_data = json.loads(logs)
            print(f'日志阶段: {logs_data.get("stage", "N/A")}')
            print(f'日志消息: {logs_data.get("message", "N/A")}')
        except:
            print(f'日志: {logs}')
    else:
        print('日志: 无')
else:
    print('没有正在运行的任务')

conn.close()
