#!/usr/bin/env python3
"""
GPU支持的AI策略模型加载器
"""

import json
import sqlite3
import pickle
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import logging
import os

# GPU相关导入
try:
    import torch
    import torch.nn as nn
    TORCH_AVAILABLE = True
    print("✅ PyTorch可用")
except ImportError:
    TORCH_AVAILABLE = False
    print("❌ PyTorch不可用")

try:
    import tensorflow as tf
    TF_AVAILABLE = True
    print("✅ TensorFlow可用")
except ImportError:
    TF_AVAILABLE = False
    print("❌ TensorFlow不可用")

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class GPUAIStrategyLoader:
    """支持GPU的AI策略模型加载器"""
    
    def __init__(self):
        self.db_path = 'trading_system.db'
        self.models_path = 'models'
        self.loaded_models = {}
        
        # GPU设备检测
        self.device = self._detect_gpu_device()
        logger.info(f"🔧 使用设备: {self.device}")
        
        # 确保模型目录存在
        if not os.path.exists(self.models_path):
            os.makedirs(self.models_path)
    
    def _detect_gpu_device(self):
        """检测可用的GPU设备"""
        # PyTorch GPU检测
        if TORCH_AVAILABLE and torch.cuda.is_available():
            device = torch.device('cuda')
            gpu_name = torch.cuda.get_device_name(0)
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
            logger.info(f"🎮 检测到PyTorch GPU: {gpu_name} ({gpu_memory:.1f}GB)")
            return device
        
        # TensorFlow GPU检测
        if TF_AVAILABLE:
            gpus = tf.config.experimental.list_physical_devices('GPU')
            if gpus:
                try:
                    # 启用内存增长
                    for gpu in gpus:
                        tf.config.experimental.set_memory_growth(gpu, True)
                    logger.info(f"🎮 检测到TensorFlow GPU: {len(gpus)}个设备")
                    return 'GPU'
                except RuntimeError as e:
                    logger.warning(f"⚠️ GPU配置失败: {e}")
        
        logger.info("💻 使用CPU设备")
        return 'cpu'
    
    def load_strategy_model(self, strategy_id: str) -> Optional[Dict[str, Any]]:
        """加载AI策略模型（支持GPU）"""
        try:
            # 检查缓存
            if strategy_id in self.loaded_models:
                return self.loaded_models[strategy_id]
            
            # 从数据库获取策略信息
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT id, name, timeframe, parameters, performance_metrics, 
                       training_data, model_path, status, model_type
                FROM strategy 
                WHERE id = ? AND strategy_type = 'ai' AND status = 'completed'
            """, (strategy_id,))
            
            result = cursor.fetchone()
            conn.close()
            
            if not result:
                logger.error(f"❌ 策略 {strategy_id} 不存在或未完成训练")
                return None
            
            strategy_id, name, timeframe, parameters, performance_metrics, training_data, model_path, status, model_type = result
            
            # 解析JSON数据
            try:
                parameters = json.loads(parameters) if parameters else {}
                performance_metrics = json.loads(performance_metrics) if performance_metrics else {}
                training_data = json.loads(training_data) if training_data else {}
            except:
                parameters = {}
                performance_metrics = {}
                training_data = {}
            
            # 根据模型类型加载模型
            model = self._load_model_by_type(model_path, model_type)
            
            # 构建策略对象
            strategy_obj = {
                'id': strategy_id,
                'name': name,
                'timeframe': timeframe,
                'parameters': parameters,
                'performance_metrics': performance_metrics,
                'training_data': training_data,
                'model': model,
                'model_type': model_type,
                'device': str(self.device),
                'created_at': datetime.now().isoformat(),
                'type': 'gpu_ai' if self.device != 'cpu' else 'cpu_ai'
            }
            
            # 缓存策略
            self.loaded_models[strategy_id] = strategy_obj
            
            logger.info(f"✅ 成功加载GPU策略: {name} (设备: {self.device})")
            return strategy_obj
            
        except Exception as e:
            logger.error(f"❌ 加载GPU策略失败: {e}")
            return None
    
    def _load_model_by_type(self, model_path: str, model_type: str):
        """根据模型类型加载模型"""
        if not model_path or not os.path.exists(model_path):
            return None
        
        try:
            if model_type == 'pytorch':
                return self._load_pytorch_model(model_path)
            elif model_type == 'tensorflow':
                return self._load_tensorflow_model(model_path)
            else:
                # 传统模型（sklearn等）
                return self._load_traditional_model(model_path)
        except Exception as e:
            logger.error(f"❌ 加载模型失败: {e}")
            return None
    
    def _load_pytorch_model(self, model_path: str):
        """加载PyTorch模型"""
        if not TORCH_AVAILABLE:
            raise Exception("PyTorch不可用")
        
        model = torch.load(model_path, map_location=self.device)
        model.to(self.device)
        model.eval()
        
        logger.info(f"✅ PyTorch模型已加载到 {self.device}")
        return model
    
    def _load_tensorflow_model(self, model_path: str):
        """加载TensorFlow模型"""
        if not TF_AVAILABLE:
            raise Exception("TensorFlow不可用")
        
        model = tf.keras.models.load_model(model_path)
        logger.info(f"✅ TensorFlow模型已加载")
        return model
    
    def _load_traditional_model(self, model_path: str):
        """加载传统机器学习模型"""
        with open(model_path, 'rb') as f:
            model = pickle.load(f)
        logger.info(f"✅ 传统模型已加载")
        return model
    
    def predict_with_gpu_model(self, model, features: np.ndarray, model_type: str) -> Optional[Dict[str, Any]]:
        """使用GPU模型进行预测"""
        try:
            if model_type == 'pytorch':
                return self._predict_pytorch(model, features)
            elif model_type == 'tensorflow':
                return self._predict_tensorflow(model, features)
            else:
                return self._predict_traditional(model, features)
        except Exception as e:
            logger.error(f"❌ GPU预测失败: {e}")
            return None
    
    def _predict_pytorch(self, model, features: np.ndarray):
        """PyTorch模型预测"""
        with torch.no_grad():
            # 转换为tensor并移到GPU
            input_tensor = torch.FloatTensor(features).to(self.device)
            
            # 预测
            output = model(input_tensor)
            
            # 转换回CPU numpy
            if isinstance(output, tuple):
                prediction = output[0].cpu().numpy()
                confidence = torch.softmax(output[0], dim=-1).max().cpu().numpy()
            else:
                prediction = output.cpu().numpy()
                confidence = torch.sigmoid(output).max().cpu().numpy()
            
            # 确定动作
            if len(prediction.shape) > 1 and prediction.shape[-1] >= 3:
                # 多分类：[sell, hold, buy]
                action_idx = np.argmax(prediction[0])
                if action_idx == 2 and confidence > 0.6:
                    action = 'buy'
                elif action_idx == 0 and confidence > 0.6:
                    action = 'sell'
                else:
                    return None
            else:
                # 二分类
                action = 'buy' if prediction[0] > 0.5 else 'sell'
            
            return {
                'action': action,
                'confidence': float(confidence),
                'model_output': prediction.tolist(),
                'device': str(self.device)
            }
    
    def _predict_tensorflow(self, model, features: np.ndarray):
        """TensorFlow模型预测"""
        with tf.device('/GPU:0' if self.device == 'GPU' else '/CPU:0'):
            prediction = model.predict(features, verbose=0)
            
            if len(prediction.shape) > 1 and prediction.shape[-1] >= 3:
                # 多分类
                confidence = np.max(prediction[0])
                action_idx = np.argmax(prediction[0])
                
                if action_idx == 2 and confidence > 0.6:
                    action = 'buy'
                elif action_idx == 0 and confidence > 0.6:
                    action = 'sell'
                else:
                    return None
            else:
                # 二分类
                confidence = float(prediction[0][0])
                action = 'buy' if confidence > 0.5 else 'sell'
            
            return {
                'action': action,
                'confidence': float(confidence),
                'model_output': prediction.tolist(),
                'device': str(self.device)
            }
    
    def _predict_traditional(self, model, features: np.ndarray):
        """传统模型预测"""
        if hasattr(model, 'predict'):
            prediction = model.predict(features)
            
            if hasattr(model, 'predict_proba'):
                probabilities = model.predict_proba(features)[0]
                confidence = max(probabilities)
                
                if len(probabilities) >= 3:
                    if probabilities[2] > probabilities[0] and probabilities[2] > 0.6:
                        action = 'buy'
                    elif probabilities[0] > probabilities[2] and probabilities[0] > 0.6:
                        action = 'sell'
                    else:
                        return None
                else:
                    action = 'buy' if prediction[0] > 0.5 else 'sell'
            else:
                action = 'buy' if prediction[0] > 0 else 'sell'
                confidence = 0.7
            
            return {
                'action': action,
                'confidence': float(confidence),
                'model_output': prediction.tolist(),
                'device': 'cpu'
            }
        
        return None
    
    def get_gpu_status(self):
        """获取GPU状态信息"""
        status = {
            'torch_available': TORCH_AVAILABLE,
            'tf_available': TF_AVAILABLE,
            'current_device': str(self.device),
            'gpu_detected': self.device != 'cpu'
        }
        
        if TORCH_AVAILABLE and torch.cuda.is_available():
            status.update({
                'gpu_name': torch.cuda.get_device_name(0),
                'gpu_memory_total': torch.cuda.get_device_properties(0).total_memory / 1024**3,
                'gpu_memory_allocated': torch.cuda.memory_allocated(0) / 1024**3,
                'gpu_memory_reserved': torch.cuda.memory_reserved(0) / 1024**3
            })
        
        return status

# 全局GPU AI策略加载器实例
gpu_ai_strategy_loader = GPUAIStrategyLoader()
