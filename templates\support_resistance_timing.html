{% extends "base.html" %}

{% block page_title %}支撑线时机{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- 左侧控制面板 -->
        <div class="col-md-4">
            <!-- 品种选择 -->
            <div class="card mb-3">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-line"></i> 品种选择
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="symbolSelect" class="form-label">交易品种</label>
                        <select class="form-select" id="symbolSelect" onchange="changeSymbol()">
                            <option value="XAUUSD">黄金 (XAUUSD)</option>
                            <option value="EURUSD">欧美 (EURUSD)</option>
                            <option value="GBPUSD">英美 (GBPUSD)</option>
                            <option value="USDJPY">美日 (USDJPY)</option>
                            <option value="USDCHF">美瑞 (USDCHF)</option>
                        </select>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="timeframeSelect" class="form-label">主要时间周期</label>
                                <select class="form-select" id="timeframeSelect" onchange="changeTimeframe()">
                                    <option value="5m">5分钟</option>
                                    <option value="15m">15分钟</option>
                                    <option value="1h">1小时</option>
                                    <option value="4h">4小时</option>
                                    <option value="1d">日线</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="updateFrequencySelect" class="form-label">
                                    <i class="fas fa-sync-alt"></i> 数据更新频率
                                </label>
                                <select class="form-select" id="updateFrequencySelect" onchange="changeUpdateFrequency()">
                                    <option value="5" selected>5秒</option>
                                    <option value="10">10秒</option>
                                    <option value="30">30秒</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <button id="startAnalysisBtn" class="btn btn-primary w-100" onclick="startAnalysis()">
                        <i class="fas fa-play"></i> 开始分析
                    </button>
                    <button id="stopAnalysisBtn" class="btn btn-secondary w-100 mt-2" onclick="stopAnalysis()" disabled>
                        <i class="fas fa-stop"></i> 停止分析
                    </button>
                </div>
            </div>
            
            <!-- 分析设置 -->
            <div class="card mb-3">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-cogs"></i> 分析设置
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="strengthThreshold" class="form-label">
                            支撑阻力强度阈值: <span id="strengthValue">3</span>
                        </label>
                        <input type="range" class="form-range" id="strengthThreshold" 
                               min="2" max="8" value="3" 
                               oninput="updateStrengthValue(this.value)">
                        <small class="text-muted">经过的点位越多，支撑阻力效果越好</small>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">多时间周期分析</label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="analyze1d" checked>
                            <label class="form-check-label" for="analyze1d">日线</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="analyze4h" checked>
                            <label class="form-check-label" for="analyze4h">4小时</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="analyze1h" checked>
                            <label class="form-check-label" for="analyze1h">1小时</label>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">形态识别</label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="detectEngulfing" checked>
                            <label class="form-check-label" for="detectEngulfing">阳包阴/阴包阳</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="detectHammer" checked>
                            <label class="form-check-label" for="detectHammer">锤子线/上吊线</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="detectDoji" checked>
                            <label class="form-check-label" for="detectDoji">十字星</label>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 状态显示 -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle"></i> 分析状态
                    </h5>
                </div>
                <div class="card-body">
                    <div id="analysisStatus" class="text-muted">
                        <i class="fas fa-circle text-secondary"></i> 未开始分析
                    </div>
                    <div id="analysisStats" class="mt-2">
                        <small class="text-muted">支撑位: <span id="supportCount">0</span> | 压力位: <span id="resistanceCount">0</span></small>
                    </div>
                    <div id="updateFrequencyStatus" class="mt-2">
                        <small class="text-muted">
                            <i class="fas fa-sync-alt"></i> 更新频率: <span id="currentUpdateFrequency">5</span>秒
                        </small>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 右侧图表和信号 -->
        <div class="col-md-8">
            <!-- 图表区域 -->
            <div class="card mb-3">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-candlestick"></i> K线图表
                        <span class="badge bg-info ms-2" id="currentSymbolDisplay">XAUUSD</span>
                        <span class="badge bg-secondary ms-1" id="currentTimeframeDisplay">5分钟</span>
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div id="supportResistanceChart" style="height: 500px;"></div>
                </div>
            </div>
            
            <!-- 交易信号 -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bullseye"></i> 交易时机信号
                        <span class="badge bg-success ms-2" id="signalCount">0</span>
                    </h5>
                </div>
                <div class="card-body">
                    <div id="tradingSignals">
                        <div class="text-center py-4">
                            <i class="fas fa-crosshairs fa-2x text-muted mb-3"></i>
                            <p class="text-muted">暂无交易时机信号</p>
                            <small class="text-muted">开始分析后将显示支撑阻力位的交易机会</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 交易确认模态框 -->
<div class="modal fade" id="tradeConfirmModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认交易</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="tradeDetails"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="executeTrade()">确认交易</button>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
<script>
// 全局变量
let currentSymbol = 'XAUUSD';
let currentTimeframe = '5m';
let analysisActive = false;
let analysisInterval = null;
let updateInterval = null; // 数据更新定时器
let chartData = {};
let supportResistanceLevels = {};
let tradingSignals = [];

// 分析设置
let analysisSettings = {
    strengthThreshold: 3,
    timeframes: ['1d', '4h', '1h'],
    updateFrequency: 5, // 默认5秒更新
    patterns: {
        engulfing: true,
        hammer: true,
        doji: true
    }
};

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('📊 支撑线时机页面初始化...');

    // 恢复页面状态
    restorePageState();

    // 初始化更新频率显示
    document.getElementById('currentUpdateFrequency').textContent = analysisSettings.updateFrequency;

    // 初始化图表
    initializeChart();

    // 加载初始数据
    loadInitialData();

    // 如果之前在分析中，恢复分析状态
    if (analysisActive) {
        console.log('🔄 恢复分析状态...');
        startAnalysis();
    }
});

// 初始化图表
function initializeChart() {
    const layout = {
        title: `${currentSymbol} - ${currentTimeframe}`,
        xaxis: {
            title: '时间',
            type: 'date'
        },
        yaxis: {
            title: '价格'
        },
        showlegend: false,
        margin: { t: 50, r: 50, b: 50, l: 50 },
        shapes: []
    };
    
    Plotly.newPlot('supportResistanceChart', [], layout);
    console.log('✅ 图表初始化完成');
}

// 加载初始数据
async function loadInitialData() {
    try {
        console.log('📥 加载初始数据...');
        await loadChartData(currentSymbol, currentTimeframe);
        console.log('✅ 初始数据加载完成');
    } catch (error) {
        console.error('❌ 初始数据加载失败:', error);
    }
}

// 加载图表数据
async function loadChartData(symbol, timeframe) {
    try {
        console.log(`📥 加载 ${symbol} ${timeframe} 数据...`);

        // 显示加载状态
        document.getElementById('analysisStatus').innerHTML =
            '<i class="fas fa-spinner fa-spin text-primary"></i> 加载数据中...';

        // 尝试多个数据源
        let data = null;

        // 1. 首先尝试从MT5获取实时数据
        try {
            const mt5Response = await fetch(`/api/mt5/get-rates?symbol=${symbol}&timeframe=${timeframe}&count=200`);
            const mt5Data = await mt5Response.json();

            if (mt5Data.success && mt5Data.data && mt5Data.data.length > 0) {
                data = mt5Data.data.map(item => ({
                    time: new Date(item.time * 1000).toISOString(),
                    open: item.open,
                    high: item.high,
                    low: item.low,
                    close: item.close,
                    volume: item.tick_volume || item.volume || 1000
                }));
                console.log(`✅ 从MT5获取 ${symbol} ${timeframe} 数据成功，${data.length}条记录`);
            }
        } catch (mt5Error) {
            console.log('⚠️ MT5数据获取失败，尝试其他数据源:', mt5Error.message);
        }

        // 2. 如果MT5失败，尝试从图表数据API获取
        if (!data) {
            try {
                const chartResponse = await fetch(`/api/chart-data?symbol=${symbol}&timeframe=${timeframe}&limit=200`);
                const chartData = await chartResponse.json();

                if (chartData.success && chartData.data && chartData.data.length > 0) {
                    data = chartData.data;
                    console.log(`✅ 从图表API获取 ${symbol} ${timeframe} 数据成功，${data.length}条记录`);
                }
            } catch (chartError) {
                console.log('⚠️ 图表API数据获取失败:', chartError.message);
            }
        }

        // 3. 如果都失败，生成模拟数据（仅用于演示）
        if (!data) {
            console.log('⚠️ 所有数据源都失败，生成模拟数据用于演示');
            data = generateSimulatedData(symbol, timeframe, 200);
        }

        if (data && data.length > 0) {
            chartData[timeframe] = data;
            updateChart(data);

            // 更新状态显示
            document.getElementById('analysisStatus').innerHTML =
                '<i class="fas fa-circle text-info"></i> 数据加载完成';

            console.log(`✅ ${symbol} ${timeframe} 数据处理完成，${data.length}条记录`);
        } else {
            throw new Error('无法获取任何数据');
        }

    } catch (error) {
        console.error('❌ 加载图表数据异常:', error);

        // 显示错误状态
        document.getElementById('analysisStatus').innerHTML =
            '<i class="fas fa-circle text-danger"></i> 数据加载失败';

        // 生成基础模拟数据作为备用
        const fallbackData = generateSimulatedData(symbol, timeframe, 100);
        chartData[timeframe] = fallbackData;
        updateChart(fallbackData);
    }
}

// 生成模拟数据（仅用于演示和备用）
function generateSimulatedData(symbol, timeframe, count) {
    console.log(`🔄 生成 ${symbol} ${timeframe} 模拟数据...`);

    const data = [];
    const basePrice = symbol === 'XAUUSD' ? 2650 : 1.1000;
    const volatility = symbol === 'XAUUSD' ? 20 : 0.01;

    let currentPrice = basePrice;
    const now = new Date();

    // 根据时间周期计算时间间隔
    const intervals = {
        '5m': 5 * 60 * 1000,
        '15m': 15 * 60 * 1000,
        '1h': 60 * 60 * 1000,
        '4h': 4 * 60 * 60 * 1000,
        '1d': 24 * 60 * 60 * 1000
    };

    const interval = intervals[timeframe] || intervals['5m'];

    for (let i = count - 1; i >= 0; i--) {
        const time = new Date(now.getTime() - i * interval);

        // 系统严禁生成模拟OHLC数据，必须从MT5获取真实数据
        console.error('❌ 系统严禁生成模拟OHLC数据，请使用MT5真实市场数据');
        return []; // 返回空数据，强制使用真实数据源
    }

    console.log(`✅ 生成了 ${data.length} 条模拟数据`);
    return data;
}

// 更新图表
function updateChart(data) {
    const trace = {
        x: data.map(d => d.time),
        open: data.map(d => d.open),
        high: data.map(d => d.high),
        low: data.map(d => d.low),
        close: data.map(d => d.close),
        type: 'candlestick',
        name: currentSymbol
    };
    
    const layout = {
        title: `${currentSymbol} - ${getTimeframeDisplay(currentTimeframe)}`,
        xaxis: { title: '时间', type: 'date' },
        yaxis: { title: '价格' },
        showlegend: false,
        margin: { t: 50, r: 50, b: 50, l: 50 },
        shapes: []
    };
    
    Plotly.react('supportResistanceChart', [trace], layout);
}

// 获取时间周期显示名称
function getTimeframeDisplay(timeframe) {
    const displays = {
        '5m': '5分钟',
        '15m': '15分钟',
        '1h': '1小时',
        '4h': '4小时',
        '1d': '日线'
    };
    return displays[timeframe] || timeframe;
}

// 保存页面状态到localStorage
function savePageState() {
    const state = {
        currentSymbol: currentSymbol,
        currentTimeframe: currentTimeframe,
        analysisActive: analysisActive,
        analysisSettings: analysisSettings,
        timestamp: Date.now()
    };

    try {
        localStorage.setItem('supportResistanceTimingState', JSON.stringify(state));
        console.log('💾 页面状态已保存');
    } catch (error) {
        console.error('❌ 保存页面状态失败:', error);
    }
}

// 恢复页面状态从localStorage
function restorePageState() {
    try {
        const savedState = localStorage.getItem('supportResistanceTimingState');
        if (savedState) {
            const state = JSON.parse(savedState);

            // 检查状态是否过期（1小时）
            if (Date.now() - state.timestamp < 3600000) {
                currentSymbol = state.currentSymbol || 'XAUUSD';
                currentTimeframe = state.currentTimeframe || '5m';
                analysisActive = state.analysisActive || false;
                analysisSettings = { ...analysisSettings, ...state.analysisSettings };

                // 更新UI
                document.getElementById('symbolSelect').value = currentSymbol;
                document.getElementById('timeframeSelect').value = currentTimeframe;
                document.getElementById('currentSymbolDisplay').textContent = currentSymbol;
                document.getElementById('currentTimeframeDisplay').textContent = getTimeframeDisplay(currentTimeframe);

                // 恢复分析设置
                if (analysisSettings.strengthThreshold) {
                    document.getElementById('strengthThreshold').value = analysisSettings.strengthThreshold;
                    document.getElementById('strengthValue').textContent = analysisSettings.strengthThreshold;
                }

                // 恢复更新频率设置
                if (analysisSettings.updateFrequency) {
                    document.getElementById('updateFrequencySelect').value = analysisSettings.updateFrequency;
                }

                console.log('✅ 页面状态已恢复:', state);
            } else {
                console.log('⏰ 保存的状态已过期，使用默认设置');
                localStorage.removeItem('supportResistanceTimingState');
            }
        }
    } catch (error) {
        console.error('❌ 恢复页面状态失败:', error);
    }
}

// 更新按钮状态
function updateButtonStates() {
    const startBtn = document.getElementById('startAnalysisBtn');
    const stopBtn = document.getElementById('stopAnalysisBtn');

    if (analysisActive) {
        startBtn.disabled = true;
        startBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 分析中...';
        startBtn.className = 'btn btn-success w-100';

        stopBtn.disabled = false;
        stopBtn.className = 'btn btn-danger w-100 mt-2';
    } else {
        startBtn.disabled = false;
        startBtn.innerHTML = '<i class="fas fa-play"></i> 开始分析';
        startBtn.className = 'btn btn-primary w-100';

        stopBtn.disabled = true;
        stopBtn.className = 'btn btn-secondary w-100 mt-2';
    }
}

// 更新强度值显示
function updateStrengthValue(value) {
    document.getElementById('strengthValue').textContent = value;
    analysisSettings.strengthThreshold = parseInt(value);

    // 保存状态
    savePageState();
}

// 切换品种
async function changeSymbol() {
    const newSymbol = document.getElementById('symbolSelect').value;
    if (newSymbol !== currentSymbol) {
        currentSymbol = newSymbol;
        document.getElementById('currentSymbolDisplay').textContent = currentSymbol;

        // 保存状态
        savePageState();

        // 重新加载数据
        await loadChartData(currentSymbol, currentTimeframe);

        // 如果正在分析，重新开始
        if (analysisActive) {
            stopAnalysis();
            setTimeout(() => startAnalysis(), 1000);
        }
    }
}

// 切换时间周期
async function changeTimeframe() {
    const newTimeframe = document.getElementById('timeframeSelect').value;
    if (newTimeframe !== currentTimeframe) {
        currentTimeframe = newTimeframe;
        document.getElementById('currentTimeframeDisplay').textContent = getTimeframeDisplay(currentTimeframe);

        // 保存状态
        savePageState();

        // 重新加载数据
        await loadChartData(currentSymbol, currentTimeframe);

        // 如果正在分析，重新开始
        if (analysisActive) {
            stopAnalysis();
            setTimeout(() => startAnalysis(), 1000);
        }
    }
}

// 切换更新频率
function changeUpdateFrequency() {
    const newFrequency = parseInt(document.getElementById('updateFrequencySelect').value);
    if (newFrequency !== analysisSettings.updateFrequency) {
        analysisSettings.updateFrequency = newFrequency;

        // 更新状态显示
        document.getElementById('currentUpdateFrequency').textContent = newFrequency;

        console.log(`📊 更新频率已改为 ${newFrequency} 秒`);

        // 保存状态
        savePageState();

        // 如果正在分析，重新设置更新定时器
        if (analysisActive) {
            restartUpdateTimer();

            // 显示更新频率变更通知
            showNotification(`数据更新频率已改为 ${newFrequency} 秒`, 'info');
        }
    }
}

// 开始分析
function startAnalysis() {
    if (analysisActive) {
        console.log('⚠️ 分析已在进行中');
        return;
    }

    console.log('🚀 开始支撑阻力位分析...');
    analysisActive = true;

    // 更新按钮状态
    updateButtonStates();

    // 更新状态显示
    document.getElementById('analysisStatus').innerHTML =
        '<i class="fas fa-circle text-success"></i> 分析中...';

    // 更新分析设置
    updateAnalysisSettings();

    // 保存状态
    savePageState();

    // 立即执行一次分析
    performSupportResistanceAnalysis();

    // 启动自动更新定时器
    startUpdateTimer();

    console.log(`✅ 支撑阻力位分析已启动，数据更新频率: ${analysisSettings.updateFrequency}秒`);
}

// 停止分析
function stopAnalysis() {
    if (!analysisActive) {
        console.log('⚠️ 分析未在进行中');
        return;
    }

    console.log('⏹️ 停止支撑阻力位分析...');
    analysisActive = false;

    // 更新按钮状态
    updateButtonStates();

    // 清理定时器
    if (analysisInterval) {
        clearInterval(analysisInterval);
        analysisInterval = null;
    }

    if (updateInterval) {
        clearInterval(updateInterval);
        updateInterval = null;
    }

    // 更新状态显示
    document.getElementById('analysisStatus').innerHTML =
        '<i class="fas fa-circle text-secondary"></i> 已停止分析';

    // 保存状态
    savePageState();

    console.log('✅ 支撑阻力位分析已停止');
}

// 启动更新定时器
function startUpdateTimer() {
    // 清理现有定时器
    if (updateInterval) {
        clearInterval(updateInterval);
        updateInterval = null;
    }

    const updateFrequency = analysisSettings.updateFrequency * 1000; // 转换为毫秒

    updateInterval = setInterval(async () => {
        if (!analysisActive) {
            return; // 如果分析已停止，不执行更新
        }

        try {
            console.log(`🔄 自动更新数据 (${analysisSettings.updateFrequency}秒间隔)...`);

            // 更新数据
            await loadChartData(currentSymbol, currentTimeframe);

            // 重新执行分析
            performSupportResistanceAnalysis();

            // 更新最后更新时间显示
            updateLastUpdateTime();

        } catch (error) {
            console.error('❌ 自动更新失败:', error);
        }
    }, updateFrequency);

    console.log(`⏰ 更新定时器已启动，间隔: ${analysisSettings.updateFrequency}秒`);
}

// 重启更新定时器
function restartUpdateTimer() {
    if (analysisActive) {
        console.log('🔄 重启更新定时器...');
        startUpdateTimer();
    }
}

// 更新最后更新时间显示
function updateLastUpdateTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('zh-CN');

    // 在状态显示中添加最后更新时间
    const statusElement = document.getElementById('analysisStatus');
    if (statusElement && analysisActive) {
        statusElement.innerHTML =
            `<i class="fas fa-circle text-success"></i> 分析中...
             <small class="text-muted">(最后更新: ${timeString})</small>`;
    }
}

// 显示通知
function showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        <i class="fas fa-info-circle"></i> ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // 添加到页面
    document.body.appendChild(notification);

    // 3秒后自动移除
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 3000);
}

// 更新分析设置
function updateAnalysisSettings() {
    // 更新时间周期设置
    analysisSettings.timeframes = [];
    if (document.getElementById('analyze1d').checked) analysisSettings.timeframes.push('1d');
    if (document.getElementById('analyze4h').checked) analysisSettings.timeframes.push('4h');
    if (document.getElementById('analyze1h').checked) analysisSettings.timeframes.push('1h');
    
    // 更新形态识别设置
    analysisSettings.patterns.engulfing = document.getElementById('detectEngulfing').checked;
    analysisSettings.patterns.hammer = document.getElementById('detectHammer').checked;
    analysisSettings.patterns.doji = document.getElementById('detectDoji').checked;
    
    console.log('📊 分析设置已更新:', analysisSettings);
}

// 执行支撑阻力位分析
async function performSupportResistanceAnalysis() {
    try {
        console.log('🔍 执行支撑阻力位分析...');
        
        // 确保有主要时间周期的数据
        if (!chartData[currentTimeframe]) {
            await loadChartData(currentSymbol, currentTimeframe);
        }
        
        // 加载其他时间周期的数据
        for (const tf of analysisSettings.timeframes) {
            if (!chartData[tf]) {
                await loadChartData(currentSymbol, tf);
            }
        }
        
        // 分析支撑阻力位
        const analysis = await analyzeSupportResistanceLevels();
        
        // 绘制支撑阻力线
        drawSupportResistanceLines(analysis);
        
        // 寻找交易时机
        const signals = findTradingOpportunities(analysis);
        
        // 显示交易信号
        displayTradingSignals(signals);
        
        // 更新统计信息
        updateAnalysisStats(analysis);
        
        console.log('✅ 支撑阻力位分析完成');
        
    } catch (error) {
        console.error('❌ 支撑阻力位分析失败:', error);
    }
}

// 分析支撑阻力位
async function analyzeSupportResistanceLevels() {
    console.log('🔍 开始分析支撑阻力位...');

    const analysis = {
        support: [],
        resistance: [],
        multiTimeframe: {}
    };

    // 分析主要时间周期
    const mainData = chartData[currentTimeframe];
    if (mainData && mainData.length > 20) {
        const mainLevels = findSupportResistanceInData(mainData, currentTimeframe);
        analysis.support = mainLevels.support;
        analysis.resistance = mainLevels.resistance;
    }

    // 分析其他时间周期
    for (const tf of analysisSettings.timeframes) {
        if (chartData[tf] && chartData[tf].length > 20) {
            const levels = findSupportResistanceInData(chartData[tf], tf);
            analysis.multiTimeframe[tf] = levels;

            // 合并到主分析中（权重更高）
            levels.support.forEach(level => {
                level.timeframe = tf;
                level.strength += 1; // 多时间周期确认增加强度
                analysis.support.push(level);
            });

            levels.resistance.forEach(level => {
                level.timeframe = tf;
                level.strength += 1;
                analysis.resistance.push(level);
            });
        }
    }

    // 合并相近的支撑阻力位
    analysis.support = mergeSimilarLevels(analysis.support);
    analysis.resistance = mergeSimilarLevels(analysis.resistance);

    // 按强度排序
    analysis.support.sort((a, b) => b.strength - a.strength);
    analysis.resistance.sort((a, b) => b.strength - a.strength);

    console.log(`✅ 分析完成: ${analysis.support.length}个支撑位, ${analysis.resistance.length}个压力位`);
    return analysis;
}

// 在数据中寻找支撑阻力位（改进版本）
function findSupportResistanceInData(data, timeframe) {
    const support = [];
    const resistance = [];
    const threshold = analysisSettings.strengthThreshold;

    // 使用更大的窗口寻找更可靠的支撑阻力位
    const windowSize = Math.min(5, Math.floor(data.length / 20)); // 动态窗口大小

    // 寻找局部高点和低点
    for (let i = windowSize; i < data.length - windowSize; i++) {
        const current = data[i];

        // 检查是否为局部低点（支撑位）
        let isLocalLow = true;
        for (let j = i - windowSize; j <= i + windowSize; j++) {
            if (j !== i && data[j].low < current.low) {
                isLocalLow = false;
                break;
            }
        }

        if (isLocalLow) {
            // 计算这个价位的强度和有效性
            const levelAnalysis = analyzeLevelStrength(data, current.low, 'support', i);

            if (levelAnalysis.strength >= threshold && levelAnalysis.isValid) {
                support.push({
                    price: current.low,
                    time: current.time,
                    index: i,
                    strength: levelAnalysis.strength,
                    timeframe: timeframe,
                    type: 'support',
                    touchCount: levelAnalysis.touchCount,
                    lastTouch: levelAnalysis.lastTouch,
                    validity: levelAnalysis.validity
                });
            }
        }

        // 检查是否为局部高点（压力位）
        let isLocalHigh = true;
        for (let j = i - windowSize; j <= i + windowSize; j++) {
            if (j !== i && data[j].high > current.high) {
                isLocalHigh = false;
                break;
            }
        }

        if (isLocalHigh) {
            const levelAnalysis = analyzeLevelStrength(data, current.high, 'resistance', i);

            if (levelAnalysis.strength >= threshold && levelAnalysis.isValid) {
                resistance.push({
                    price: current.high,
                    time: current.time,
                    index: i,
                    strength: levelAnalysis.strength,
                    timeframe: timeframe,
                    type: 'resistance',
                    touchCount: levelAnalysis.touchCount,
                    lastTouch: levelAnalysis.lastTouch,
                    validity: levelAnalysis.validity
                });
            }
        }
    }

    // 过滤掉过于接近的支撑阻力位
    const filteredSupport = filterNearbyLevels(support);
    const filteredResistance = filterNearbyLevels(resistance);

    return {
        support: filteredSupport,
        resistance: filteredResistance
    };
}

// 分析支撑阻力位的强度和有效性
function analyzeLevelStrength(data, price, type, centerIndex) {
    let touchCount = 0;
    let lastTouchIndex = -1;
    let firstTouchIndex = -1;
    const tolerance = price * 0.0015; // 0.15%容差

    // 统计触及该价位的次数
    for (let i = 0; i < data.length; i++) {
        const candle = data[i];
        let touches = false;

        if (type === 'support') {
            touches = Math.abs(candle.low - price) <= tolerance;
        } else {
            touches = Math.abs(candle.high - price) <= tolerance;
        }

        if (touches) {
            touchCount++;
            if (firstTouchIndex === -1) {
                firstTouchIndex = i;
            }
            lastTouchIndex = i;
        }
    }

    // 计算时间跨度（触及点的时间分布）
    const timeSpan = lastTouchIndex - firstTouchIndex;
    const dataSpan = data.length;
    const timeSpanRatio = timeSpan / dataSpan;

    // 计算有效性分数
    let validity = 0;

    // 基础分数：触及次数
    validity += Math.min(touchCount * 0.2, 1.0);

    // 时间分布分数：分布越广越有效
    validity += Math.min(timeSpanRatio * 0.5, 0.3);

    // 最近性分数：最近有触及更有效
    const recentness = (data.length - lastTouchIndex) / data.length;
    if (recentness < 0.3) { // 最近30%的数据内有触及
        validity += 0.2;
    }

    // 强度计算：结合触及次数和有效性
    const strength = Math.floor(touchCount * validity * 2);

    // 有效性判断
    const isValid = touchCount >= 2 && validity >= 0.3 && timeSpan >= 3;

    return {
        strength: strength,
        touchCount: touchCount,
        lastTouch: lastTouchIndex,
        validity: validity,
        isValid: isValid,
        timeSpan: timeSpan
    };
}

// 过滤相近的支撑阻力位
function filterNearbyLevels(levels) {
    if (levels.length === 0) return levels;

    // 按强度排序
    levels.sort((a, b) => b.strength - a.strength);

    const filtered = [];
    const minDistance = levels[0].price * 0.003; // 0.3%最小距离

    for (const level of levels) {
        let tooClose = false;

        for (const existing of filtered) {
            if (Math.abs(level.price - existing.price) < minDistance) {
                tooClose = true;
                break;
            }
        }

        if (!tooClose) {
            filtered.push(level);
        }
    }

    return filtered;
}

// 计算支撑阻力位强度
function calculateLevelStrength(data, price, type) {
    let strength = 1;
    const tolerance = price * 0.001; // 0.1%的容差

    for (let i = 0; i < data.length; i++) {
        const candle = data[i];

        if (type === 'support') {
            // 检查是否触及支撑位
            if (Math.abs(candle.low - price) <= tolerance) {
                strength++;
            }
        } else {
            // 检查是否触及压力位
            if (Math.abs(candle.high - price) <= tolerance) {
                strength++;
            }
        }
    }

    return strength;
}

// 合并相近的支撑阻力位
function mergeSimilarLevels(levels) {
    if (levels.length === 0) return levels;

    const merged = [];
    const tolerance = levels[0].price * 0.002; // 0.2%的容差

    levels.sort((a, b) => a.price - b.price);

    let currentGroup = [levels[0]];

    for (let i = 1; i < levels.length; i++) {
        const level = levels[i];
        const lastInGroup = currentGroup[currentGroup.length - 1];

        if (Math.abs(level.price - lastInGroup.price) <= tolerance) {
            currentGroup.push(level);
        } else {
            // 合并当前组
            if (currentGroup.length > 0) {
                merged.push(mergeGroup(currentGroup));
            }
            currentGroup = [level];
        }
    }

    // 处理最后一组
    if (currentGroup.length > 0) {
        merged.push(mergeGroup(currentGroup));
    }

    return merged;
}

// 合并一组相近的支撑阻力位
function mergeGroup(group) {
    const avgPrice = group.reduce((sum, level) => sum + level.price, 0) / group.length;
    const totalStrength = group.reduce((sum, level) => sum + level.strength, 0);
    const timeframes = [...new Set(group.map(level => level.timeframe))];

    return {
        price: avgPrice,
        strength: totalStrength,
        timeframes: timeframes,
        count: group.length,
        type: group[0].type,
        time: group[0].time,
        index: group[0].index
    };
}

// 绘制支撑阻力线（折线连接版本）
function drawSupportResistanceLines(analysis) {
    console.log('📊 绘制支撑阻力折线...');

    const currentData = chartData[currentTimeframe];

    if (!currentData || currentData.length === 0) {
        console.log('⚠️ 没有图表数据，无法绘制支撑阻力线');
        return;
    }

    // 找到支撑线（低点连低点）
    const supportLines = findSupportLines(currentData);

    // 找到压力线（高点连高点）
    const resistanceLines = findResistanceLines(currentData);

    // 绘制支撑阻力线
    drawTrendLines(supportLines, resistanceLines);
}

// 寻找支撑线（低点连低点）
function findSupportLines(data) {
    console.log('🔍 寻找支撑线（低点连低点）...');

    // 找到所有局部低点
    const localLows = findLocalExtremes(data, 'low');
    console.log(`📊 找到 ${localLows.length} 个局部低点`);

    // 连接低点形成支撑线
    const supportLines = [];

    for (let i = 0; i < localLows.length - 1; i++) {
        for (let j = i + 1; j < localLows.length; j++) {
            const point1 = localLows[i];
            const point2 = localLows[j];

            // 计算两点之间的直线
            const line = calculateTrendLine(point1, point2);

            // 验证这条线是否为有效的支撑线
            const validation = validateSupportLine(line, data, point1.index, point2.index);

            if (validation.isValid) {
                supportLines.push({
                    ...line,
                    type: 'support',
                    strength: validation.strength,
                    touchPoints: validation.touchPoints,
                    point1: point1,
                    point2: point2
                });
            }
        }
    }

    // 按强度排序，只保留最强的几条
    supportLines.sort((a, b) => b.strength - a.strength);
    return supportLines.slice(0, 3);
}

// 寻找压力线（高点连高点）
function findResistanceLines(data) {
    console.log('🔍 寻找压力线（高点连高点）...');

    // 找到所有局部高点
    const localHighs = findLocalExtremes(data, 'high');
    console.log(`📊 找到 ${localHighs.length} 个局部高点`);

    // 连接高点形成压力线
    const resistanceLines = [];

    for (let i = 0; i < localHighs.length - 1; i++) {
        for (let j = i + 1; j < localHighs.length; j++) {
            const point1 = localHighs[i];
            const point2 = localHighs[j];

            // 计算两点之间的直线
            const line = calculateTrendLine(point1, point2);

            // 验证这条线是否为有效的压力线
            const validation = validateResistanceLine(line, data, point1.index, point2.index);

            if (validation.isValid) {
                resistanceLines.push({
                    ...line,
                    type: 'resistance',
                    strength: validation.strength,
                    touchPoints: validation.touchPoints,
                    point1: point1,
                    point2: point2
                });
            }
        }
    }

    // 按强度排序，只保留最强的几条
    resistanceLines.sort((a, b) => b.strength - a.strength);
    return resistanceLines.slice(0, 3);
}

// 寻找局部极值点
function findLocalExtremes(data, type) {
    const extremes = [];
    const windowSize = 3; // 左右各3个点的窗口

    for (let i = windowSize; i < data.length - windowSize; i++) {
        const current = data[i];
        let isExtreme = true;

        // 检查窗口内是否为极值
        for (let j = i - windowSize; j <= i + windowSize; j++) {
            if (j !== i) {
                if (type === 'low' && data[j].low < current.low) {
                    isExtreme = false;
                    break;
                } else if (type === 'high' && data[j].high > current.high) {
                    isExtreme = false;
                    break;
                }
            }
        }

        if (isExtreme) {
            extremes.push({
                index: i,
                time: current.time,
                price: type === 'low' ? current.low : current.high,
                type: type
            });
        }
    }

    return extremes;
}

// 计算两点之间的趋势线
function calculateTrendLine(point1, point2) {
    // 计算斜率
    const slope = (point2.price - point1.price) / (point2.index - point1.index);

    // 计算截距
    const intercept = point1.price - slope * point1.index;

    return {
        slope: slope,
        intercept: intercept,
        startIndex: point1.index,
        endIndex: point2.index,
        startTime: point1.time,
        endTime: point2.time,
        startPrice: point1.price,
        endPrice: point2.price
    };
}

// 验证支撑线的有效性
function validateSupportLine(line, data, startIndex, endIndex) {
    let touchCount = 2; // 起始两个点
    let violations = 0;
    const tolerance = 0.001; // 0.1%容差

    const touchPoints = [startIndex, endIndex];

    // 检查线段范围内的所有点
    for (let i = startIndex + 1; i < endIndex; i++) {
        const expectedPrice = line.slope * i + line.intercept;
        const actualLow = data[i].low;
        const actualHigh = data[i].high;

        // 检查是否触及支撑线
        if (Math.abs(actualLow - expectedPrice) <= expectedPrice * tolerance) {
            touchCount++;
            touchPoints.push(i);
        }

        // 检查是否违反支撑线（价格明显跌破）
        if (actualLow < expectedPrice * (1 - tolerance * 2)) {
            violations++;
        }
    }

    // 延伸到未来，检查最近的价格行为
    const currentIndex = data.length - 1;
    if (endIndex < currentIndex) {
        for (let i = endIndex + 1; i <= currentIndex && i < endIndex + 20; i++) {
            const expectedPrice = line.slope * i + line.intercept;
            const actualLow = data[i].low;

            if (Math.abs(actualLow - expectedPrice) <= expectedPrice * tolerance) {
                touchCount++;
                touchPoints.push(i);
            }
        }
    }

    // 计算强度：触及次数多，违反次数少
    const strength = Math.max(0, touchCount * 2 - violations);

    // 有效性判断：至少3次触及，违反次数不超过触及次数的30%
    const isValid = touchCount >= 3 && violations <= touchCount * 0.3 && strength >= 3;

    return {
        isValid: isValid,
        strength: strength,
        touchPoints: touchPoints,
        touchCount: touchCount,
        violations: violations
    };
}

// 验证压力线的有效性
function validateResistanceLine(line, data, startIndex, endIndex) {
    let touchCount = 2; // 起始两个点
    let violations = 0;
    const tolerance = 0.001; // 0.1%容差

    const touchPoints = [startIndex, endIndex];

    // 检查线段范围内的所有点
    for (let i = startIndex + 1; i < endIndex; i++) {
        const expectedPrice = line.slope * i + line.intercept;
        const actualHigh = data[i].high;
        const actualLow = data[i].low;

        // 检查是否触及压力线
        if (Math.abs(actualHigh - expectedPrice) <= expectedPrice * tolerance) {
            touchCount++;
            touchPoints.push(i);
        }

        // 检查是否违反压力线（价格明显突破）
        if (actualHigh > expectedPrice * (1 + tolerance * 2)) {
            violations++;
        }
    }

    // 延伸到未来，检查最近的价格行为
    const currentIndex = data.length - 1;
    if (endIndex < currentIndex) {
        for (let i = endIndex + 1; i <= currentIndex && i < endIndex + 20; i++) {
            const expectedPrice = line.slope * i + line.intercept;
            const actualHigh = data[i].high;

            if (Math.abs(actualHigh - expectedPrice) <= expectedPrice * tolerance) {
                touchCount++;
                touchPoints.push(i);
            }
        }
    }

    // 计算强度
    const strength = Math.max(0, touchCount * 2 - violations);

    // 有效性判断
    const isValid = touchCount >= 3 && violations <= touchCount * 0.3 && strength >= 3;

    return {
        isValid: isValid,
        strength: strength,
        touchPoints: touchPoints,
        touchCount: touchCount,
        violations: violations
    };
}

// 绘制趋势线到图表上
function drawTrendLines(supportLines, resistanceLines) {
    console.log(`📊 绘制趋势线: ${supportLines.length}条支撑线, ${resistanceLines.length}条压力线`);

    const shapes = [];
    const currentData = chartData[currentTimeframe];

    // 绘制支撑线
    supportLines.forEach((line, index) => {
        // 延伸线条到图表边界
        const extendedLine = extendTrendLine(line, currentData);

        shapes.push({
            type: 'line',
            xref: 'x',
            x0: extendedLine.startTime,
            x1: extendedLine.endTime,
            yref: 'y',
            y0: extendedLine.startPrice,
            y1: extendedLine.endPrice,
            line: {
                color: '#00ff00', // 绿色支撑线
                width: Math.min(line.strength, 4),
                dash: line.strength >= 5 ? 'solid' : 'dash'
            },
            layer: 'above',
            opacity: 0.8,
            name: `支撑线 (强度:${line.strength}, 触及:${line.touchCount}次)`
        });

        // 在触及点添加标记
        line.touchPoints.forEach(pointIndex => {
            if (pointIndex < currentData.length) {
                const point = currentData[pointIndex];
                shapes.push({
                    type: 'circle',
                    xref: 'x',
                    x0: point.time,
                    x1: point.time,
                    yref: 'y',
                    y0: point.low - (point.low * 0.0003),
                    y1: point.low + (point.low * 0.0003),
                    line: {
                        color: '#00ff00',
                        width: 1
                    },
                    fillcolor: '#00ff00',
                    opacity: 0.6,
                    layer: 'above'
                });
            }
        });
    });

    // 绘制压力线
    resistanceLines.forEach((line, index) => {
        // 延伸线条到图表边界
        const extendedLine = extendTrendLine(line, currentData);

        shapes.push({
            type: 'line',
            xref: 'x',
            x0: extendedLine.startTime,
            x1: extendedLine.endTime,
            yref: 'y',
            y0: extendedLine.startPrice,
            y1: extendedLine.endPrice,
            line: {
                color: '#ff0000', // 红色压力线
                width: Math.min(line.strength, 4),
                dash: line.strength >= 5 ? 'solid' : 'dash'
            },
            layer: 'above',
            opacity: 0.8,
            name: `压力线 (强度:${line.strength}, 触及:${line.touchCount}次)`
        });

        // 在触及点添加标记
        line.touchPoints.forEach(pointIndex => {
            if (pointIndex < currentData.length) {
                const point = currentData[pointIndex];
                shapes.push({
                    type: 'circle',
                    xref: 'x',
                    x0: point.time,
                    x1: point.time,
                    yref: 'y',
                    y0: point.high - (point.high * 0.0003),
                    y1: point.high + (point.high * 0.0003),
                    line: {
                        color: '#ff0000',
                        width: 1
                    },
                    fillcolor: '#ff0000',
                    opacity: 0.6,
                    layer: 'above'
                });
            }
        });
    });

    // 更新图表
    const chartDiv = document.getElementById('supportResistanceChart');
    if (chartDiv) {
        Plotly.relayout(chartDiv, { shapes: shapes }).then(() => {
            console.log(`✅ 成功绘制 ${shapes.length} 个趋势线元素`);
        }).catch(error => {
            console.error('❌ 绘制趋势线失败:', error);
        });
    }
}

// 延伸趋势线到图表边界
function extendTrendLine(line, data) {
    const dataLength = data.length;

    // 向前延伸到图表开始
    const startIndex = Math.max(0, line.startIndex - 10);
    const startPrice = line.slope * startIndex + line.intercept;
    const startTime = data[startIndex].time;

    // 向后延伸到图表结束
    const endIndex = Math.min(dataLength - 1, line.endIndex + 30);
    const endPrice = line.slope * endIndex + line.intercept;
    const endTime = data[endIndex].time;

    return {
        startTime: startTime,
        endTime: endTime,
        startPrice: startPrice,
        endPrice: endPrice
    };
}

// 计算支撑阻力线的绘制参数（保留用于兼容性）
function calculateSupportResistanceLine(level, data, type, currentPrice) {
    const levelPrice = level.price;
    const levelTime = level.time;
    const strength = level.strength;

    // 计算价格距离当前价格的百分比
    const priceDistance = Math.abs(currentPrice - levelPrice) / currentPrice;

    // 如果价格距离太远（超过2%），不绘制
    if (priceDistance > 0.02) {
        return { shouldDraw: false };
    }

    // 找到该价位首次出现的时间
    let firstTouchIndex = -1;
    let lastTouchIndex = -1;
    const tolerance = levelPrice * 0.001; // 0.1%容差

    for (let i = 0; i < data.length; i++) {
        const candle = data[i];
        const touchesLevel = type === 'support' ?
            Math.abs(candle.low - levelPrice) <= tolerance :
            Math.abs(candle.high - levelPrice) <= tolerance;

        if (touchesLevel) {
            if (firstTouchIndex === -1) {
                firstTouchIndex = i;
            }
            lastTouchIndex = i;
        }
    }

    // 如果没有找到触及点，使用level的时间
    if (firstTouchIndex === -1) {
        firstTouchIndex = Math.max(0, data.length - 50); // 最近50根K线
        lastTouchIndex = data.length - 1;
    }

    // 计算线条的起始和结束时间
    const startIndex = Math.max(0, firstTouchIndex - 10); // 向前延伸10根K线
    const endIndex = Math.min(data.length - 1, lastTouchIndex + 20); // 向后延伸20根K线

    const startTime = data[startIndex].time;
    const endTime = data[endIndex].time;
    const labelTime = data[lastTouchIndex].time;

    // 根据强度和距离计算线条样式
    const baseColor = type === 'support' ? '#00ff00' : '#ff0000';
    const opacity = Math.max(0.4, Math.min(1.0, 1.0 - priceDistance * 20)); // 距离越近越不透明
    const width = Math.max(1, Math.min(4, strength)); // 强度越高越粗
    const dash = strength >= 4 ? 'solid' : strength >= 2 ? 'dash' : 'dot';

    return {
        shouldDraw: true,
        startTime: startTime,
        endTime: endTime,
        labelTime: labelTime,
        color: baseColor,
        opacity: opacity,
        width: width,
        dash: dash
    };
}

// 寻找交易时机（基于趋势线）
function findTradingOpportunities(analysis) {
    console.log('🎯 寻找交易时机...');

    const signals = [];
    const currentData = chartData[currentTimeframe];

    if (!currentData || currentData.length < 10) {
        return signals;
    }

    // 重新分析趋势线
    const supportLines = findSupportLines(currentData);
    const resistanceLines = findResistanceLines(currentData);

    const latestCandles = currentData.slice(-5); // 最近5根K线
    const currentIndex = currentData.length - 1;

    // 检查支撑线的交易机会
    supportLines.forEach(supportLine => {
        const opportunity = checkSupportLineOpportunity(supportLine, currentData, latestCandles, currentIndex);
        if (opportunity) {
            signals.push(opportunity);
        }
    });

    // 检查压力线的交易机会
    resistanceLines.forEach(resistanceLine => {
        const opportunity = checkResistanceLineOpportunity(resistanceLine, currentData, latestCandles, currentIndex);
        if (opportunity) {
            signals.push(opportunity);
        }
    });

    // 按信号强度排序
    signals.sort((a, b) => b.confidence - a.confidence);

    console.log(`✅ 找到 ${signals.length} 个交易时机`);
    return signals;
}

// 检查支撑线交易机会
function checkSupportLineOpportunity(supportLine, allData, recentCandles, currentIndex) {
    // 计算当前价格应该在的支撑线位置
    const expectedSupportPrice = supportLine.slope * currentIndex + supportLine.intercept;
    const currentPrice = allData[currentIndex].close;
    const currentLow = allData[currentIndex].low;

    const tolerance = expectedSupportPrice * 0.002; // 0.2%容差

    // 检查是否接近支撑线
    if (Math.abs(currentLow - expectedSupportPrice) <= tolerance) {

        // 检查最近几根K线的形态
        for (let i = recentCandles.length - 2; i >= 0; i--) {
            const current = recentCandles[i + 1] || recentCandles[recentCandles.length - 1];
            const prev = recentCandles[i];

            // 检查阳包阴形态
            if (analysisSettings.patterns.engulfing && prev &&
                prev.close < prev.open && // 前一根是阴线
                current.close > current.open && // 当前是阳线
                current.open < prev.close && // 阳线开盘低于阴线收盘
                current.close > prev.open) { // 阳线收盘高于阴线开盘

                return createTradingSignal({
                    type: 'BUY',
                    reason: '支撑线阳包阴反转',
                    price: current.close,
                    supportLevel: expectedSupportPrice,
                    stopLoss: Math.min(prev.low, expectedSupportPrice - tolerance),
                    takeProfit: calculateTakeProfit(current.close, expectedSupportPrice, 'BUY'),
                    confidence: calculateTrendLineConfidence(supportLine.strength, 'engulfing'),
                    timeframe: currentTimeframe,
                    time: current.time,
                    trendLineInfo: {
                        type: 'support',
                        strength: supportLine.strength,
                        touchCount: supportLine.touchCount
                    }
                });
            }

            // 检查锤子线形态
            if (analysisSettings.patterns.hammer) {
                const bodySize = Math.abs(current.close - current.open);
                const lowerShadow = current.open < current.close ?
                    current.open - current.low : current.close - current.low;
                const upperShadow = current.high - Math.max(current.open, current.close);

                if (lowerShadow > bodySize * 2 && upperShadow < bodySize * 0.5) {
                    return createTradingSignal({
                        type: 'BUY',
                        reason: '支撑线锤子线反转',
                        price: current.close,
                        supportLevel: expectedSupportPrice,
                        stopLoss: current.low - tolerance,
                        takeProfit: calculateTakeProfit(current.close, expectedSupportPrice, 'BUY'),
                        confidence: calculateTrendLineConfidence(supportLine.strength, 'hammer'),
                        timeframe: currentTimeframe,
                        time: current.time,
                        trendLineInfo: {
                            type: 'support',
                            strength: supportLine.strength,
                            touchCount: supportLine.touchCount
                        }
                    });
                }
            }
        }
    }

    return null;
}

// 检查压力线交易机会
function checkResistanceLineOpportunity(resistanceLine, allData, recentCandles, currentIndex) {
    // 计算当前价格应该在的压力线位置
    const expectedResistancePrice = resistanceLine.slope * currentIndex + resistanceLine.intercept;
    const currentPrice = allData[currentIndex].close;
    const currentHigh = allData[currentIndex].high;

    const tolerance = expectedResistancePrice * 0.002; // 0.2%容差

    // 检查是否接近压力线
    if (Math.abs(currentHigh - expectedResistancePrice) <= tolerance) {

        // 检查最近几根K线的形态
        for (let i = recentCandles.length - 2; i >= 0; i--) {
            const current = recentCandles[i + 1] || recentCandles[recentCandles.length - 1];
            const prev = recentCandles[i];

            // 检查阴包阳形态
            if (analysisSettings.patterns.engulfing && prev &&
                prev.close > prev.open && // 前一根是阳线
                current.close < current.open && // 当前是阴线
                current.open > prev.close && // 阴线开盘高于阳线收盘
                current.close < prev.open) { // 阴线收盘低于阳线开盘

                return createTradingSignal({
                    type: 'SELL',
                    reason: '压力线阴包阳反转',
                    price: current.close,
                    resistanceLevel: expectedResistancePrice,
                    stopLoss: Math.max(prev.high, expectedResistancePrice + tolerance),
                    takeProfit: calculateTakeProfit(current.close, expectedResistancePrice, 'SELL'),
                    confidence: calculateTrendLineConfidence(resistanceLine.strength, 'engulfing'),
                    timeframe: currentTimeframe,
                    time: current.time,
                    trendLineInfo: {
                        type: 'resistance',
                        strength: resistanceLine.strength,
                        touchCount: resistanceLine.touchCount
                    }
                });
            }

            // 检查上吊线形态
            if (analysisSettings.patterns.hammer) {
                const bodySize = Math.abs(current.close - current.open);
                const upperShadow = current.high - Math.max(current.open, current.close);
                const lowerShadow = Math.min(current.open, current.close) - current.low;

                if (upperShadow > bodySize * 2 && lowerShadow < bodySize * 0.5) {
                    return createTradingSignal({
                        type: 'SELL',
                        reason: '压力线上吊线反转',
                        price: current.close,
                        resistanceLevel: expectedResistancePrice,
                        stopLoss: current.high + tolerance,
                        takeProfit: calculateTakeProfit(current.close, expectedResistancePrice, 'SELL'),
                        confidence: calculateTrendLineConfidence(resistanceLine.strength, 'hammer'),
                        timeframe: currentTimeframe,
                        time: current.time,
                        trendLineInfo: {
                            type: 'resistance',
                            strength: resistanceLine.strength,
                            touchCount: resistanceLine.touchCount
                        }
                    });
                }
            }
        }
    }

    return null;
}

// 计算趋势线信号置信度
function calculateTrendLineConfidence(lineStrength, patternType) {
    let baseConfidence = Math.min(lineStrength * 0.12, 0.8); // 基于趋势线强度

    // 根据形态类型调整
    const patternBonus = {
        'engulfing': 0.15,
        'hammer': 0.10,
        'doji': 0.05
    };

    return Math.min(baseConfidence + (patternBonus[patternType] || 0), 0.95);
}

// 检查支撑位交易机会
function checkSupportOpportunity(support, candles) {
    const tolerance = support.price * 0.002; // 0.2%容差

    for (let i = candles.length - 3; i < candles.length; i++) {
        const current = candles[i];
        const prev = candles[i - 1];

        // 检查是否接近支撑位
        if (Math.abs(current.low - support.price) <= tolerance) {

            // 检查阳包阴形态
            if (analysisSettings.patterns.engulfing && prev &&
                prev.close < prev.open && // 前一根是阴线
                current.close > current.open && // 当前是阳线
                current.open < prev.close && // 阳线开盘低于阴线收盘
                current.close > prev.open) { // 阳线收盘高于阴线开盘

                return createTradingSignal({
                    type: 'BUY',
                    reason: '支撑位阳包阴',
                    price: current.close,
                    supportLevel: support.price,
                    stopLoss: Math.min(prev.low, support.price - tolerance),
                    takeProfit: calculateTakeProfit(current.close, support.price, 'BUY'),
                    confidence: calculateConfidence(support.strength, 'engulfing'),
                    timeframe: currentTimeframe,
                    time: current.time
                });
            }

            // 检查锤子线形态
            if (analysisSettings.patterns.hammer) {
                const bodySize = Math.abs(current.close - current.open);
                const lowerShadow = current.open < current.close ?
                    current.open - current.low : current.close - current.low;
                const upperShadow = current.high - Math.max(current.open, current.close);

                if (lowerShadow > bodySize * 2 && upperShadow < bodySize * 0.5) {
                    return createTradingSignal({
                        type: 'BUY',
                        reason: '支撑位锤子线',
                        price: current.close,
                        supportLevel: support.price,
                        stopLoss: current.low - tolerance,
                        takeProfit: calculateTakeProfit(current.close, support.price, 'BUY'),
                        confidence: calculateConfidence(support.strength, 'hammer'),
                        timeframe: currentTimeframe,
                        time: current.time
                    });
                }
            }
        }
    }

    return null;
}

// 检查压力位交易机会
function checkResistanceOpportunity(resistance, candles) {
    const tolerance = resistance.price * 0.002;

    for (let i = candles.length - 3; i < candles.length; i++) {
        const current = candles[i];
        const prev = candles[i - 1];

        // 检查是否接近压力位
        if (Math.abs(current.high - resistance.price) <= tolerance) {

            // 检查阴包阳形态
            if (analysisSettings.patterns.engulfing && prev &&
                prev.close > prev.open && // 前一根是阳线
                current.close < current.open && // 当前是阴线
                current.open > prev.close && // 阴线开盘高于阳线收盘
                current.close < prev.open) { // 阴线收盘低于阳线开盘

                return createTradingSignal({
                    type: 'SELL',
                    reason: '压力位阴包阳',
                    price: current.close,
                    resistanceLevel: resistance.price,
                    stopLoss: Math.max(prev.high, resistance.price + tolerance),
                    takeProfit: calculateTakeProfit(current.close, resistance.price, 'SELL'),
                    confidence: calculateConfidence(resistance.strength, 'engulfing'),
                    timeframe: currentTimeframe,
                    time: current.time
                });
            }

            // 检查上吊线形态
            if (analysisSettings.patterns.hammer) {
                const bodySize = Math.abs(current.close - current.open);
                const upperShadow = current.high - Math.max(current.open, current.close);
                const lowerShadow = Math.min(current.open, current.close) - current.low;

                if (upperShadow > bodySize * 2 && lowerShadow < bodySize * 0.5) {
                    return createTradingSignal({
                        type: 'SELL',
                        reason: '压力位上吊线',
                        price: current.close,
                        resistanceLevel: resistance.price,
                        stopLoss: current.high + tolerance,
                        takeProfit: calculateTakeProfit(current.close, resistance.price, 'SELL'),
                        confidence: calculateConfidence(resistance.strength, 'hammer'),
                        timeframe: currentTimeframe,
                        time: current.time
                    });
                }
            }
        }
    }

    return null;
}

// 创建交易信号
function createTradingSignal(params) {
    return {
        id: `signal_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        symbol: currentSymbol,
        type: params.type,
        reason: params.reason,
        price: params.price,
        supportLevel: params.supportLevel,
        resistanceLevel: params.resistanceLevel,
        stopLoss: params.stopLoss,
        takeProfit: params.takeProfit,
        confidence: params.confidence,
        timeframe: params.timeframe,
        time: params.time,
        timestamp: new Date()
    };
}

// 计算止盈价位
function calculateTakeProfit(entryPrice, levelPrice, direction) {
    const distance = Math.abs(entryPrice - levelPrice);
    const ratio = 2; // 风险回报比 1:2

    if (direction === 'BUY') {
        return entryPrice + (distance * ratio);
    } else {
        return entryPrice - (distance * ratio);
    }
}

// 计算信号置信度
function calculateConfidence(levelStrength, patternType) {
    let baseConfidence = Math.min(levelStrength * 0.15, 0.8); // 基于强度的基础置信度

    // 根据形态类型调整
    const patternBonus = {
        'engulfing': 0.15,
        'hammer': 0.10,
        'doji': 0.05
    };

    return Math.min(baseConfidence + (patternBonus[patternType] || 0), 0.95);
}

// 显示交易信号
function displayTradingSignals(signals) {
    const container = document.getElementById('tradingSignals');
    document.getElementById('signalCount').textContent = signals.length;

    if (signals.length === 0) {
        container.innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-crosshairs fa-2x text-muted mb-3"></i>
                <p class="text-muted">暂无交易时机信号</p>
                <small class="text-muted">继续监测中，寻找支撑阻力位的交易机会...</small>
            </div>
        `;
        return;
    }

    let html = '<div class="row">';

    signals.forEach((signal, index) => {
        const confidenceColor = signal.confidence >= 0.7 ? 'success' :
                               signal.confidence >= 0.5 ? 'warning' : 'secondary';
        const typeColor = signal.type === 'BUY' ? 'success' : 'danger';
        const typeIcon = signal.type === 'BUY' ? 'fa-arrow-up' : 'fa-arrow-down';

        html += `
            <div class="col-md-6 mb-3">
                <div class="card border-${typeColor}">
                    <div class="card-header bg-${typeColor} text-white">
                        <h6 class="mb-0">
                            <i class="fas ${typeIcon}"></i> ${signal.type} ${signal.symbol}
                            <span class="badge bg-light text-dark ms-2">${getTimeframeDisplay(signal.timeframe)}</span>
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-2">
                            <strong>信号原因:</strong> ${signal.reason}
                        </div>
                        <div class="mb-2">
                            <strong>入场价格:</strong> ${signal.price.toFixed(4)}
                        </div>
                        <div class="mb-2">
                            <strong>止损价格:</strong> ${signal.stopLoss.toFixed(4)}
                        </div>
                        <div class="mb-2">
                            <strong>止盈价格:</strong> ${signal.takeProfit.toFixed(4)}
                        </div>
                        <div class="mb-3">
                            <strong>置信度:</strong>
                            <span class="badge bg-${confidenceColor}">${(signal.confidence * 100).toFixed(1)}%</span>
                        </div>
                        <div class="d-grid gap-2">
                            <button class="btn btn-${typeColor} btn-sm" onclick="confirmTrade('${signal.id}')">
                                <i class="fas fa-hand-point-right"></i> 执行交易
                            </button>
                        </div>
                    </div>
                    <div class="card-footer text-muted">
                        <small>
                            <i class="fas fa-clock"></i> ${new Date(signal.time).toLocaleString()}
                        </small>
                    </div>
                </div>
            </div>
        `;
    });

    html += '</div>';
    container.innerHTML = html;

    // 保存信号到全局变量
    tradingSignals = signals;
}

// 更新分析统计
function updateAnalysisStats(analysis) {
    document.getElementById('supportCount').textContent = analysis.support.length;
    document.getElementById('resistanceCount').textContent = analysis.resistance.length;
}

// 确认交易
function confirmTrade(signalId) {
    const signal = tradingSignals.find(s => s.id === signalId);
    if (!signal) {
        console.error('❌ 未找到交易信号:', signalId);
        return;
    }

    const riskReward = Math.abs(signal.takeProfit - signal.price) / Math.abs(signal.price - signal.stopLoss);

    document.getElementById('tradeDetails').innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <p><strong>品种:</strong> ${signal.symbol}</p>
                <p><strong>方向:</strong> <span class="badge bg-${signal.type === 'BUY' ? 'success' : 'danger'}">${signal.type}</span></p>
                <p><strong>入场价格:</strong> ${signal.price.toFixed(4)}</p>
                <p><strong>手数:</strong> 0.01</p>
            </div>
            <div class="col-md-6">
                <p><strong>止损:</strong> ${signal.stopLoss.toFixed(4)}</p>
                <p><strong>止盈:</strong> ${signal.takeProfit.toFixed(4)}</p>
                <p><strong>风险回报比:</strong> 1:${riskReward.toFixed(2)}</p>
                <p><strong>置信度:</strong> ${(signal.confidence * 100).toFixed(1)}%</p>
            </div>
        </div>
        <div class="alert alert-info">
            <strong>信号原因:</strong> ${signal.reason}
        </div>
    `;

    // 保存当前信号ID到模态框
    document.getElementById('tradeConfirmModal').setAttribute('data-signal-id', signalId);

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('tradeConfirmModal'));
    modal.show();
}

// 执行交易
async function executeTrade() {
    const modal = bootstrap.Modal.getInstance(document.getElementById('tradeConfirmModal'));
    const signalId = document.getElementById('tradeConfirmModal').getAttribute('data-signal-id');
    const signal = tradingSignals.find(s => s.id === signalId);

    if (!signal) {
        console.error('❌ 未找到交易信号');
        return;
    }

    try {
        console.log('📤 执行交易:', signal);

        const orderData = {
            symbol: signal.symbol,
            order_type: signal.type === 'BUY' ? 'buy' : 'sell',
            volume: 0.01,
            sl: signal.stopLoss,
            tp: signal.takeProfit,
            comment: `支撑线时机-${signal.reason}`
        };

        const response = await fetch('/api/mt5/send-order', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(orderData)
        });

        const result = await response.json();

        if (result.success) {
            console.log('✅ 交易执行成功:', result);
            alert('交易执行成功！');
            modal.hide();
        } else {
            console.error('❌ 交易执行失败:', result.error);
            alert('交易执行失败: ' + result.error);
        }

    } catch (error) {
        console.error('❌ 交易执行异常:', error);
        alert('交易执行异常: ' + error.message);
    }
}
</script>
{% endblock %}
