#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
技术指标库
基于MT4/MT5原生算法实现的专业技术指标
"""

import pandas as pd
import numpy as np
from typing import Dict, Union, Tuple
import logging

logger = logging.getLogger(__name__)

class TechnicalIndicators:
    """技术指标计算类"""
    
    def __init__(self):
        pass
    
    def sma(self, data: pd.Series, period: int) -> pd.Series:
        """
        简单移动平均线 (Simple Moving Average)
        
        Args:
            data: 价格数据
            period: 周期
            
        Returns:
            pd.Series: SMA值
        """
        return data.rolling(window=period).mean()
    
    def ema(self, data: pd.Series, period: int) -> pd.Series:
        """
        指数移动平均线 (Exponential Moving Average)
        
        Args:
            data: 价格数据
            period: 周期
            
        Returns:
            pd.Series: EMA值
        """
        return data.ewm(span=period, adjust=False).mean()
    
    def wma(self, data: pd.Series, period: int) -> pd.Series:
        """
        加权移动平均线 (Weighted Moving Average)
        
        Args:
            data: 价格数据
            period: 周期
            
        Returns:
            pd.Series: WMA值
        """
        weights = np.arange(1, period + 1)
        return data.rolling(window=period).apply(
            lambda x: np.dot(x, weights) / weights.sum(), raw=True
        )
    
    def macd(self, data: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Dict[str, pd.Series]:
        """
        MACD指标 (Moving Average Convergence Divergence)
        
        Args:
            data: 价格数据
            fast: 快线周期
            slow: 慢线周期
            signal: 信号线周期
            
        Returns:
            Dict: 包含MACD线、信号线和柱状图的字典
        """
        ema_fast = self.ema(data, fast)
        ema_slow = self.ema(data, slow)
        macd_line = ema_fast - ema_slow
        signal_line = self.ema(macd_line, signal)
        histogram = macd_line - signal_line
        
        return {
            'macd': macd_line,
            'signal': signal_line,
            'histogram': histogram
        }
    
    def rsi(self, data: pd.Series, period: int = 14) -> pd.Series:
        """
        相对强弱指数 (Relative Strength Index)
        
        Args:
            data: 价格数据
            period: 周期
            
        Returns:
            pd.Series: RSI值
        """
        delta = data.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        
        return rsi
    
    def bollinger_bands(self, data: pd.Series, period: int = 20, std_dev: float = 2) -> Dict[str, pd.Series]:
        """
        布林带 (Bollinger Bands)
        
        Args:
            data: 价格数据
            period: 周期
            std_dev: 标准差倍数
            
        Returns:
            Dict: 包含上轨、中轨、下轨的字典
        """
        sma = self.sma(data, period)
        std = data.rolling(window=period).std()
        
        upper = sma + (std * std_dev)
        lower = sma - (std * std_dev)
        
        return {
            'upper': upper,
            'middle': sma,
            'lower': lower
        }
    
    def stochastic(self, high: pd.Series, low: pd.Series, close: pd.Series, 
                   k_period: int = 14, d_period: int = 3) -> Dict[str, pd.Series]:
        """
        随机指标 (Stochastic Oscillator)
        
        Args:
            high: 最高价
            low: 最低价
            close: 收盘价
            k_period: %K周期
            d_period: %D周期
            
        Returns:
            Dict: 包含%K和%D的字典
        """
        lowest_low = low.rolling(window=k_period).min()
        highest_high = high.rolling(window=k_period).max()
        
        k_percent = 100 * ((close - lowest_low) / (highest_high - lowest_low))
        d_percent = k_percent.rolling(window=d_period).mean()
        
        return {
            '%K': k_percent,
            '%D': d_percent
        }
    
    def williams_r(self, high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
        """
        威廉指标 (Williams %R)
        
        Args:
            high: 最高价
            low: 最低价
            close: 收盘价
            period: 周期
            
        Returns:
            pd.Series: Williams %R值
        """
        highest_high = high.rolling(window=period).max()
        lowest_low = low.rolling(window=period).min()
        
        williams_r = -100 * ((highest_high - close) / (highest_high - lowest_low))
        
        return williams_r
    
    def atr(self, high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
        """
        平均真实波幅 (Average True Range)
        
        Args:
            high: 最高价
            low: 最低价
            close: 收盘价
            period: 周期
            
        Returns:
            pd.Series: ATR值
        """
        high_low = high - low
        high_close = np.abs(high - close.shift())
        low_close = np.abs(low - close.shift())
        
        true_range = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
        atr = true_range.rolling(window=period).mean()
        
        return atr
    
    def cci(self, high: pd.Series, low: pd.Series, close: pd.Series, period: int = 20) -> pd.Series:
        """
        商品通道指数 (Commodity Channel Index)
        
        Args:
            high: 最高价
            low: 最低价
            close: 收盘价
            period: 周期
            
        Returns:
            pd.Series: CCI值
        """
        typical_price = (high + low + close) / 3
        sma_tp = typical_price.rolling(window=period).mean()
        mean_deviation = typical_price.rolling(window=period).apply(
            lambda x: np.mean(np.abs(x - x.mean())), raw=True
        )
        
        cci = (typical_price - sma_tp) / (0.015 * mean_deviation)
        
        return cci
    
    def adx(self, high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> Dict[str, pd.Series]:
        """
        平均趋向指数 (Average Directional Index)
        
        Args:
            high: 最高价
            low: 最低价
            close: 收盘价
            period: 周期
            
        Returns:
            Dict: 包含ADX、DI+、DI-的字典
        """
        # 计算真实波幅
        atr_values = self.atr(high, low, close, period)
        
        # 计算方向移动
        up_move = high - high.shift()
        down_move = low.shift() - low
        
        plus_dm = np.where((up_move > down_move) & (up_move > 0), up_move, 0)
        minus_dm = np.where((down_move > up_move) & (down_move > 0), down_move, 0)
        
        plus_dm = pd.Series(plus_dm, index=high.index)
        minus_dm = pd.Series(minus_dm, index=high.index)
        
        # 平滑处理
        plus_dm_smooth = plus_dm.rolling(window=period).mean()
        minus_dm_smooth = minus_dm.rolling(window=period).mean()
        
        # 计算DI
        plus_di = 100 * (plus_dm_smooth / atr_values)
        minus_di = 100 * (minus_dm_smooth / atr_values)
        
        # 计算ADX
        dx = 100 * np.abs(plus_di - minus_di) / (plus_di + minus_di)
        adx = dx.rolling(window=period).mean()
        
        return {
            'ADX': adx,
            'DI+': plus_di,
            'DI-': minus_di
        }
    
    def obv(self, close: pd.Series, volume: pd.Series) -> pd.Series:
        """
        能量潮 (On Balance Volume)
        
        Args:
            close: 收盘价
            volume: 成交量
            
        Returns:
            pd.Series: OBV值
        """
        price_change = close.diff()
        obv_values = []
        obv_current = 0
        
        for i, (price_diff, vol) in enumerate(zip(price_change, volume)):
            if i == 0:
                obv_values.append(vol)
                obv_current = vol
            elif price_diff > 0:
                obv_current += vol
                obv_values.append(obv_current)
            elif price_diff < 0:
                obv_current -= vol
                obv_values.append(obv_current)
            else:
                obv_values.append(obv_current)
        
        return pd.Series(obv_values, index=close.index)
    
    def momentum(self, data: pd.Series, period: int = 10) -> pd.Series:
        """
        动量指标 (Momentum)
        
        Args:
            data: 价格数据
            period: 周期
            
        Returns:
            pd.Series: 动量值
        """
        return data - data.shift(period)
    
    def roc(self, data: pd.Series, period: int = 10) -> pd.Series:
        """
        变化率 (Rate of Change)
        
        Args:
            data: 价格数据
            period: 周期
            
        Returns:
            pd.Series: ROC值
        """
        return ((data - data.shift(period)) / data.shift(period)) * 100
    
    def ichimoku(self, high: pd.Series, low: pd.Series, close: pd.Series) -> Dict[str, pd.Series]:
        """
        一目均衡表 (Ichimoku Cloud)
        
        Args:
            high: 最高价
            low: 最低价
            close: 收盘价
            
        Returns:
            Dict: 包含各条线的字典
        """
        # 转换线 (Tenkan-sen)
        tenkan_sen = (high.rolling(9).max() + low.rolling(9).min()) / 2
        
        # 基准线 (Kijun-sen)
        kijun_sen = (high.rolling(26).max() + low.rolling(26).min()) / 2
        
        # 先行带A (Senkou Span A)
        senkou_span_a = ((tenkan_sen + kijun_sen) / 2).shift(26)
        
        # 先行带B (Senkou Span B)
        senkou_span_b = ((high.rolling(52).max() + low.rolling(52).min()) / 2).shift(26)
        
        # 滞后线 (Chikou Span)
        chikou_span = close.shift(-26)
        
        return {
            'tenkan_sen': tenkan_sen,
            'kijun_sen': kijun_sen,
            'senkou_span_a': senkou_span_a,
            'senkou_span_b': senkou_span_b,
            'chikou_span': chikou_span
        }
    
    def fibonacci_retracement(self, high_price: float, low_price: float) -> Dict[str, float]:
        """
        斐波那契回调 (Fibonacci Retracement)
        
        Args:
            high_price: 最高价
            low_price: 最低价
            
        Returns:
            Dict: 斐波那契回调水平
        """
        diff = high_price - low_price
        
        levels = {
            '0%': high_price,
            '23.6%': high_price - 0.236 * diff,
            '38.2%': high_price - 0.382 * diff,
            '50%': high_price - 0.5 * diff,
            '61.8%': high_price - 0.618 * diff,
            '78.6%': high_price - 0.786 * diff,
            '100%': low_price
        }
        
        return levels
