#!/usr/bin/env python3
"""
AI策略模型加载器 - 加载和使用用户训练好的AI策略模型
"""

import json
import sqlite3
import pickle
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import logging
import os

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AIStrategyLoader:
    """AI策略模型加载器"""
    
    def __init__(self):
        self.db_path = 'trading_system.db'
        self.models_path = 'models'  # 模型文件存储路径
        self.loaded_models = {}  # 缓存已加载的模型

        # 策略模型只使用CPU（深度学习模型使用专门的GPU服务）
        logger.info(f"💻 策略模型使用CPU模式")

        # 确保模型目录存在
        if not os.path.exists(self.models_path):
            os.makedirs(self.models_path)
    
    def get_available_strategies(self) -> List[Dict[str, Any]]:
        """获取可用的AI策略列表"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT id, name, timeframe, parameters, performance_metrics, 
                       training_data, model_path, status, created_at
                FROM strategy 
                WHERE strategy_type = 'ai' AND status = 'completed'
                ORDER BY created_at DESC
            """)
            
            strategies = []
            for row in cursor.fetchall():
                strategy_id, name, timeframe, parameters, performance_metrics, training_data, model_path, status, created_at = row
                
                # 解析JSON数据
                try:
                    parameters = json.loads(parameters) if parameters else {}
                    performance_metrics = json.loads(performance_metrics) if performance_metrics else {}
                    training_data = json.loads(training_data) if training_data else {}
                except:
                    parameters = {}
                    performance_metrics = {}
                    training_data = {}
                
                strategies.append({
                    'id': strategy_id,
                    'name': name,
                    'timeframe': timeframe,
                    'parameters': parameters,
                    'performance_metrics': performance_metrics,
                    'training_data': training_data,
                    'model_path': model_path,
                    'status': status,
                    'created_at': created_at
                })
            
            conn.close()
            return strategies
            
        except Exception as e:
            logger.error(f"❌ 获取AI策略列表失败: {e}")
            return []
    
    def load_strategy_model(self, strategy_id: str) -> Optional[Dict[str, Any]]:
        """加载AI策略模型"""
        try:
            # 检查缓存
            if strategy_id in self.loaded_models:
                return self.loaded_models[strategy_id]
            
            # 从数据库获取策略信息
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT id, name, timeframe, parameters, performance_metrics, 
                       training_data, model_path, status
                FROM strategy 
                WHERE id = ? AND strategy_type = 'ai' AND status = 'completed'
            """, (strategy_id,))
            
            result = cursor.fetchone()
            conn.close()
            
            if not result:
                logger.error(f"❌ 策略 {strategy_id} 不存在或未完成训练")
                return None
            
            strategy_id, name, timeframe, parameters, performance_metrics, training_data, model_path, status = result
            
            # 解析JSON数据
            try:
                parameters = json.loads(parameters) if parameters else {}
                performance_metrics = json.loads(performance_metrics) if performance_metrics else {}
                training_data = json.loads(training_data) if training_data else {}
            except:
                parameters = {}
                performance_metrics = {}
                training_data = {}
            
            # 加载模型文件
            model = None
            if model_path and os.path.exists(model_path):
                try:
                    with open(model_path, 'rb') as f:
                        model = pickle.load(f)
                    logger.info(f"✅ 成功加载模型文件: {model_path}")
                except Exception as e:
                    logger.error(f"❌ 加载模型文件失败: {e}")
            
            # 构建策略对象
            strategy_obj = {
                'id': strategy_id,
                'name': name,
                'timeframe': timeframe,
                'parameters': parameters,
                'performance_metrics': performance_metrics,
                'training_data': training_data,
                'model': model,
                'model_path': model_path,
                'device': 'cpu',  # 策略模型强制使用CPU
                'device_type': 'CPU'  # 显示用的设备类型
            }
            
            # 缓存策略
            self.loaded_models[strategy_id] = strategy_obj
            
            logger.info(f"✅ 成功加载AI策略: {name}")
            return strategy_obj
            
        except Exception as e:
            logger.error(f"❌ 加载AI策略模型失败: {e}")
            return None
    
    def predict_signal(self, strategy_id: str, market_data: pd.DataFrame) -> Optional[Dict[str, Any]]:
        """使用AI策略预测交易信号"""
        try:
            # 加载策略
            strategy = self.load_strategy_model(strategy_id)
            if not strategy:
                logger.warning(f"❌ 无法加载策略: {strategy_id}")
                return None

            # 准备特征数据
            features = self.prepare_features(market_data, strategy['parameters'])
            if features is None or len(features) == 0:
                logger.warning(f"❌ 无法准备特征数据，市场数据长度: {len(market_data)}")
                return None

            logger.debug(f"🔍 策略 {strategy_id}: 特征数据准备完成，形状: {features.shape if hasattr(features, 'shape') else len(features)}")

            # 策略模型始终使用CPU（深度学习模型使用GPU）
            if strategy['model'] is not None:
                # 使用训练好的模型
                prediction = self.predict_with_model(strategy['model'], features)
                logger.debug(f"🤖 使用CPU模型预测: {prediction}")
            else:
                # 使用基于规则的策略
                prediction = self.predict_with_rules(features, strategy['parameters'])
                logger.debug(f"📏 使用规则策略预测: {prediction}")

            if prediction is None:
                logger.debug(f"🚫 策略 {strategy_id} 未生成预测")
                return None

            # 构建信号
            signal = {
                'action': prediction['action'],
                'confidence': prediction['confidence'],
                'price': market_data.iloc[-1]['close'],
                'strategy_id': strategy_id,
                'strategy_name': strategy['name'],
                'timestamp': datetime.now().isoformat(),
                'features': prediction.get('features', {}),
                'model_output': prediction.get('model_output', {}),
                'device': prediction.get('device', 'cpu')
            }

            logger.debug(f"✅ 策略 {strategy_id} 生成信号: {signal['action']} (置信度: {signal['confidence']:.3f}, 设备: {signal.get('device', 'cpu')})")
            return signal

        except Exception as e:
            logger.error(f"❌ AI策略预测失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return None
    
    def prepare_features(self, market_data: pd.DataFrame, parameters: Dict[str, Any]) -> Optional[np.ndarray]:
        """准备模型特征数据"""
        try:
            if len(market_data) < 50:  # 需要足够的历史数据
                return None
            
            features = []
            
            # 基础价格特征
            close_prices = market_data['close'].values
            high_prices = market_data['high'].values
            low_prices = market_data['low'].values
            volume = market_data['tick_volume'].values if 'tick_volume' in market_data.columns else np.ones(len(close_prices))
            
            # 技术指标特征
            # 移动平均线
            sma_5 = pd.Series(close_prices).rolling(window=5).mean().bfill()
            sma_10 = pd.Series(close_prices).rolling(window=10).mean().bfill()
            sma_20 = pd.Series(close_prices).rolling(window=20).mean().bfill()
            
            # RSI
            rsi = self.calculate_rsi(close_prices, 14)
            
            # MACD
            macd, macd_signal = self.calculate_macd(close_prices)
            
            # 布林带
            bb_upper, bb_middle, bb_lower = self.calculate_bollinger_bands(close_prices, 20, 2)
            
            # 价格变化率
            price_change = np.diff(close_prices, prepend=close_prices[0]) / close_prices
            
            # 波动率
            volatility = pd.Series(price_change).rolling(window=10).std().fillna(0)
            
            # 组合特征 (使用最新的值)
            latest_idx = -1
            features = [
                close_prices[latest_idx],
                sma_5.iloc[latest_idx],
                sma_10.iloc[latest_idx],
                sma_20.iloc[latest_idx],
                rsi[latest_idx],
                macd[latest_idx],
                macd_signal[latest_idx],
                bb_upper[latest_idx],
                bb_middle[latest_idx],
                bb_lower[latest_idx],
                price_change[latest_idx],
                volatility.iloc[latest_idx],
                high_prices[latest_idx],
                low_prices[latest_idx],
                volume[latest_idx]
            ]
            
            return np.array(features).reshape(1, -1)
            
        except Exception as e:
            logger.error(f"❌ 准备特征数据失败: {e}")
            return None
    
    def calculate_rsi(self, prices: np.ndarray, period: int = 14) -> np.ndarray:
        """计算RSI指标"""
        try:
            deltas = np.diff(prices)
            gains = np.where(deltas > 0, deltas, 0)
            losses = np.where(deltas < 0, -deltas, 0)
            
            avg_gains = pd.Series(gains).rolling(window=period).mean()
            avg_losses = pd.Series(losses).rolling(window=period).mean()
            
            rs = avg_gains / avg_losses
            rsi = 100 - (100 / (1 + rs))
            
            return rsi.fillna(50).values
        except:
            return np.full(len(prices), 50)
    
    def calculate_macd(self, prices: np.ndarray, fast: int = 12, slow: int = 26, signal: int = 9) -> Tuple[np.ndarray, np.ndarray]:
        """计算MACD指标"""
        try:
            prices_series = pd.Series(prices)
            ema_fast = prices_series.ewm(span=fast).mean()
            ema_slow = prices_series.ewm(span=slow).mean()
            
            macd = ema_fast - ema_slow
            macd_signal = macd.ewm(span=signal).mean()
            
            return macd.fillna(0).values, macd_signal.fillna(0).values
        except:
            return np.zeros(len(prices)), np.zeros(len(prices))
    
    def calculate_bollinger_bands(self, prices: np.ndarray, period: int = 20, std_dev: float = 2) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """计算布林带"""
        try:
            prices_series = pd.Series(prices)
            middle = prices_series.rolling(window=period).mean()
            std = prices_series.rolling(window=period).std()
            
            upper = middle + (std * std_dev)
            lower = middle - (std * std_dev)
            
            return upper.bfill().values, middle.bfill().values, lower.bfill().values
        except:
            return prices, prices, prices
    
    def predict_with_model(self, model, features: np.ndarray) -> Optional[Dict[str, Any]]:
        """使用训练好的模型进行预测"""
        try:
            # 这里假设模型有predict方法
            if hasattr(model, 'predict'):
                prediction = model.predict(features)
                
                # 根据预测结果确定动作
                if hasattr(model, 'predict_proba'):
                    probabilities = model.predict_proba(features)[0]
                    confidence = max(probabilities)
                    
                    if len(probabilities) >= 3:  # [sell, hold, buy]
                        if probabilities[2] > probabilities[0] and probabilities[2] > 0.6:
                            action = 'buy'
                        elif probabilities[0] > probabilities[2] and probabilities[0] > 0.6:
                            action = 'sell'
                        else:
                            return None  # 不交易
                    else:
                        action = 'buy' if prediction[0] > 0.5 else 'sell'
                else:
                    action = 'buy' if prediction[0] > 0 else 'sell'
                    confidence = 0.7
                
                return {
                    'action': action,
                    'confidence': confidence,
                    'model_output': {'prediction': prediction.tolist() if hasattr(prediction, 'tolist') else prediction}
                }
            
            return None
            
        except Exception as e:
            logger.error(f"❌ 模型预测失败: {e}")
            return None
    
    def predict_with_rules(self, features: np.ndarray, parameters: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """使用基于规则的策略进行预测"""
        try:
            # 解析特征
            close, sma_5, sma_10, sma_20, rsi, macd, macd_signal, bb_upper, bb_middle, bb_lower = features[0][:10]
            
            # 多重条件判断
            buy_signals = 0
            sell_signals = 0
            
            # 移动平均线信号
            if sma_5 > sma_10 > sma_20:
                buy_signals += 1
            elif sma_5 < sma_10 < sma_20:
                sell_signals += 1
            
            # RSI信号
            if rsi < 30:
                buy_signals += 1
            elif rsi > 70:
                sell_signals += 1
            
            # MACD信号
            if macd > macd_signal and macd > 0:
                buy_signals += 1
            elif macd < macd_signal and macd < 0:
                sell_signals += 1
            
            # 布林带信号
            if close < bb_lower:
                buy_signals += 1
            elif close > bb_upper:
                sell_signals += 1
            
            # 决策
            if buy_signals >= 2 and buy_signals > sell_signals:
                return {
                    'action': 'buy',
                    'confidence': min(0.9, 0.5 + buy_signals * 0.1),
                    'features': {
                        'buy_signals': buy_signals,
                        'sell_signals': sell_signals,
                        'rsi': rsi,
                        'macd': macd
                    }
                }
            elif sell_signals >= 2 and sell_signals > buy_signals:
                return {
                    'action': 'sell',
                    'confidence': min(0.9, 0.5 + sell_signals * 0.1),
                    'features': {
                        'buy_signals': buy_signals,
                        'sell_signals': sell_signals,
                        'rsi': rsi,
                        'macd': macd
                    }
                }
            
            return None  # 不交易
            
        except Exception as e:
            logger.error(f"❌ 规则预测失败: {e}")
            return None

    # GPU相关功能已移除，策略模型只使用CPU
    # 深度学习模型使用专门的deep_learning_service.py中的GPU功能

    # 策略模型不再使用GPU，已移除GPU预测功能
    # 深度学习模型使用专门的deep_learning_service.py中的GPU功能

    def _predict_pytorch_gpu(self, model, features):
        """PyTorch CPU预测（策略模型使用CPU）"""
        try:
            import torch

            device = torch.device('cpu')  # 策略模型强制使用CPU

            # 转换为tensor并移到CPU
            with torch.no_grad():
                input_tensor = torch.FloatTensor(features).to(device)

                # 确保模型在CPU上
                if next(model.parameters()).device != device:
                    model = model.to(device)

                # 预测
                output = model(input_tensor)

                # 转换回CPU
                if isinstance(output, tuple):
                    prediction = output[0].cpu().numpy()
                    confidence = torch.softmax(output[0], dim=-1).max().cpu().numpy()
                else:
                    prediction = output.cpu().numpy()
                    confidence = torch.sigmoid(output).max().cpu().numpy()

                # 确定动作
                if len(prediction.shape) > 1 and prediction.shape[-1] >= 3:
                    action_idx = np.argmax(prediction[0])
                    if action_idx == 2 and confidence > 0.6:
                        action = 'buy'
                    elif action_idx == 0 and confidence > 0.6:
                        action = 'sell'
                    else:
                        return None
                else:
                    action = 'buy' if prediction[0] > 0.5 else 'sell'

                return {
                    'action': action,
                    'confidence': float(confidence),
                    'model_output': prediction.tolist(),
                    'device': 'cuda'
                }

        except Exception as e:
            logger.error(f"❌ PyTorch GPU预测失败: {e}")
            raise

    def _predict_tensorflow_gpu(self, model, features):
        """TensorFlow CPU预测（策略模型使用CPU）"""
        try:
            import tensorflow as tf

            with tf.device('/CPU:0'):  # 策略模型强制使用CPU
                prediction = model.predict(features, verbose=0)

                if len(prediction.shape) > 1 and prediction.shape[-1] >= 3:
                    confidence = np.max(prediction[0])
                    action_idx = np.argmax(prediction[0])

                    if action_idx == 2 and confidence > 0.6:
                        action = 'buy'
                    elif action_idx == 0 and confidence > 0.6:
                        action = 'sell'
                    else:
                        return None
                else:
                    confidence = float(prediction[0][0])
                    action = 'buy' if confidence > 0.5 else 'sell'

                return {
                    'action': action,
                    'confidence': float(confidence),
                    'model_output': prediction.tolist(),
                    'device': 'gpu'
                }

        except Exception as e:
            logger.error(f"❌ TensorFlow GPU预测失败: {e}")
            raise

# 全局AI策略加载器实例
ai_strategy_loader = AIStrategyLoader()
