{% extends "base.html" %}

{% block title %}AI策略分享管理{% endblock %}

{% block extra_css %}
<style>
/* AI策略管理页面样式 */
.strategy-card {
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.strategy-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.strategy-status {
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
}

.strategy-status.completed {
    background-color: #d4edda;
    color: #155724;
}

.strategy-status.training {
    background-color: #fff3cd;
    color: #856404;
}

.strategy-status.failed {
    background-color: #f8d7da;
    color: #721c24;
}

.share-status {
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
}

.share-status.shared {
    background-color: #d1ecf1;
    color: #0c5460;
}

.share-status.private {
    background-color: #f8f9fa;
    color: #6c757d;
}

.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.metric-item {
    text-align: center;
    padding: 0.5rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.metric-value {
    font-size: 1.2rem;
    font-weight: bold;
    color: #007bff;
}

.metric-label {
    font-size: 0.8rem;
    color: #6c757d;
    margin-top: 0.25rem;
}
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-share-alt text-primary"></i>
        AI策略分享管理
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshStrategies()">
                <i class="fas fa-sync-alt"></i> 刷新
            </button>
        </div>
    </div>
</div>

<!-- 统计信息 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-primary" id="totalStrategies">0</h5>
                <p class="card-text">总策略数</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-success" id="completedStrategies">0</h5>
                <p class="card-text">已完成</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-info" id="sharedStrategies">0</h5>
                <p class="card-text">已分享</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-warning" id="privateStrategies">0</h5>
                <p class="card-text">私有策略</p>
            </div>
        </div>
    </div>
</div>

<!-- 策略列表 -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list"></i>
            AI策略列表
        </h5>
    </div>
    <div class="card-body">
        <div id="strategiesContainer">
            <div class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-3 text-muted">正在加载AI策略...</p>
            </div>
        </div>
    </div>
</div>

<!-- 策略详情模态框 -->
<div class="modal fade" id="strategyDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-info-circle text-primary"></i>
                    策略详情
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="strategyDetailContent">
                <!-- 策略详情内容将在这里动态加载 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<script>
// 页面加载时获取策略列表
document.addEventListener('DOMContentLoaded', function() {
    loadStrategies();
});

// 加载策略列表
function loadStrategies() {
    console.log('🔄 开始加载AI策略列表...');
    
    fetch('/api/ai-strategies/list')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayStrategies(data.strategies);
            updateStatistics(data.strategies);
        } else {
            showError('加载策略失败: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showError('加载策略请求失败');
    });
}

// 显示策略列表
function displayStrategies(strategies) {
    const container = document.getElementById('strategiesContainer');
    
    if (strategies.length === 0) {
        container.innerHTML = `
            <div class="text-center py-5">
                <i class="fas fa-robot fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">暂无AI策略</h5>
                <p class="text-muted">还没有用户训练AI策略</p>
            </div>
        `;
        return;
    }
    
    const strategiesHtml = strategies.map(strategy => `
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card strategy-card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">${strategy.name}</h6>
                    <div>
                        <span class="strategy-status ${strategy.status}">${getStatusText(strategy.status)}</span>
                        <span class="share-status ${strategy.is_shared ? 'shared' : 'private'} ms-1">
                            ${strategy.is_shared ? '已分享' : '私有'}
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <p class="card-text text-muted small">${strategy.description || '暂无描述'}</p>
                    
                    <div class="mb-2">
                        <small class="text-muted">
                            <i class="fas fa-robot"></i> ${strategy.ai_model || 'Unknown'}
                            <i class="fas fa-clock ms-2"></i> ${strategy.timeframe || 'Unknown'}
                        </small>
                    </div>
                    
                    <div class="mb-2">
                        <small class="text-muted">
                            <i class="fas fa-chart-line"></i> 
                            ${(strategy.symbols || []).join(', ') || '无交易品种'}
                        </small>
                    </div>
                    
                    ${strategy.status === 'completed' ? `
                        <div class="metrics-grid">
                            <div class="metric-item">
                                <div class="metric-value">${((strategy.accuracy || 0) * 100).toFixed(1)}%</div>
                                <div class="metric-label">准确率</div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-value">${((strategy.win_rate || 0) * 100).toFixed(1)}%</div>
                                <div class="metric-label">胜率</div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-value">${(strategy.profit_factor || 0).toFixed(2)}</div>
                                <div class="metric-label">盈亏比</div>
                            </div>
                        </div>
                    ` : ''}
                </div>
                <div class="card-footer">
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">
                            <i class="fas fa-calendar"></i> ${strategy.created_at}
                        </small>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-info" onclick="viewStrategyDetail(${strategy.id})" title="查看详情">
                                <i class="fas fa-eye"></i>
                            </button>
                            ${strategy.status === 'completed' ? `
                                ${strategy.is_shared ? `
                                    <button class="btn btn-outline-warning" onclick="unshareStrategy(${strategy.id})" title="取消分享">
                                        <i class="fas fa-share-alt-square"></i>
                                    </button>
                                ` : `
                                    <button class="btn btn-outline-success" onclick="shareStrategy(${strategy.id})" title="分享给所有用户">
                                        <i class="fas fa-share-alt"></i>
                                    </button>
                                `}
                            ` : ''}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `).join('');
    
    container.innerHTML = `<div class="row">${strategiesHtml}</div>`;
}

// 更新统计信息
function updateStatistics(strategies) {
    const total = strategies.length;
    const completed = strategies.filter(s => s.status === 'completed').length;
    const shared = strategies.filter(s => s.is_shared).length;
    const private = strategies.filter(s => !s.is_shared).length;
    
    document.getElementById('totalStrategies').textContent = total;
    document.getElementById('completedStrategies').textContent = completed;
    document.getElementById('sharedStrategies').textContent = shared;
    document.getElementById('privateStrategies').textContent = private;
}

// 获取状态文本
function getStatusText(status) {
    const statusMap = {
        'training': '训练中',
        'completed': '已完成',
        'failed': '训练失败'
    };
    return statusMap[status] || status;
}

// 分享策略
function shareStrategy(strategyId) {
    if (!confirm('确定要将此策略分享给所有用户吗？')) {
        return;
    }
    
    fetch(`/api/ai-strategies/${strategyId}/share`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccess(data.message);
            loadStrategies(); // 重新加载列表
        } else {
            showError('分享失败: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showError('分享请求失败');
    });
}

// 取消分享策略
function unshareStrategy(strategyId) {
    if (!confirm('确定要取消分享此策略吗？')) {
        return;
    }
    
    fetch(`/api/ai-strategies/${strategyId}/unshare`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccess(data.message);
            loadStrategies(); // 重新加载列表
        } else {
            showError('取消分享失败: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showError('取消分享请求失败');
    });
}

// 查看策略详情
function viewStrategyDetail(strategyId) {
    // 这里可以实现策略详情查看功能
    showInfo('策略详情功能开发中');
}

// 刷新策略列表
function refreshStrategies() {
    loadStrategies();
}

// 显示成功消息
function showSuccess(message) {
    // 这里可以使用您的通知系统
    alert('✅ ' + message);
}

// 显示错误消息
function showError(message) {
    // 这里可以使用您的通知系统
    alert('❌ ' + message);
}

// 显示信息消息
function showInfo(message) {
    // 这里可以使用您的通知系统
    alert('ℹ️ ' + message);
}
</script>
{% endblock %}
