#!/usr/bin/env python3
"""
全新黄金量化交易策略 - 基于多指标组合
结合RSI、MACD、布林带和市场环境识别
"""

import math

def calculate_rsi(prices, period=14):
    """计算RSI指标"""
    if len(prices) < period + 1:
        return 50  # 默认中性值
    
    gains = []
    losses = []
    
    for i in range(1, len(prices)):
        change = prices[i] - prices[i-1]
        if change > 0:
            gains.append(change)
            losses.append(0)
        else:
            gains.append(0)
            losses.append(abs(change))
    
    if len(gains) < period:
        return 50
    
    avg_gain = sum(gains[-period:]) / period
    avg_loss = sum(losses[-period:]) / period
    
    if avg_loss == 0:
        return 100
    
    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))
    
    return rsi

def calculate_macd(prices, fast=12, slow=26, signal=9):
    """计算MACD指标"""
    if len(prices) < slow:
        return 0, 0, 0
    
    # 简化的EMA计算
    def ema(data, period):
        if len(data) < period:
            return data[-1] if data else 0
        multiplier = 2 / (period + 1)
        ema_val = data[0]
        for price in data[1:]:
            ema_val = (price * multiplier) + (ema_val * (1 - multiplier))
        return ema_val
    
    fast_ema = ema(prices, fast)
    slow_ema = ema(prices, slow)
    macd_line = fast_ema - slow_ema
    
    # 简化信号线计算
    signal_line = macd_line * 0.8  # 简化处理
    histogram = macd_line - signal_line
    
    return macd_line, signal_line, histogram

def calculate_bollinger_bands(prices, period=20, std_dev=2):
    """计算布林带指标"""
    if len(prices) < period:
        current_price = prices[-1] if prices else 2650
        return current_price * 1.01, current_price, current_price * 0.99
    
    recent_prices = prices[-period:]
    middle = sum(recent_prices) / len(recent_prices)
    
    # 计算标准差
    variance = sum((p - middle) ** 2 for p in recent_prices) / len(recent_prices)
    std = variance ** 0.5
    
    upper = middle + (std * std_dev)
    lower = middle - (std * std_dev)
    
    return upper, middle, lower

def identify_market_regime(prices, rsi, bb_upper, bb_lower):
    """识别市场环境"""
    current_price = prices[-1]
    
    # 计算价格变化趋势
    if len(prices) >= 10:
        price_change_5 = (prices[-1] - prices[-6]) / prices[-6] * 100
        price_change_10 = (prices[-1] - prices[-11]) / prices[-11] * 100
    else:
        price_change_5 = 0
        price_change_10 = 0
    
    # 布林带宽度
    bb_width = (bb_upper - bb_lower) / bb_lower * 100
    
    # 价格在布林带中的位置
    bb_position = (current_price - bb_lower) / (bb_upper - bb_lower)
    
    # 市场环境判断 - 调整为更宽松的条件以产生更多交易机会
    if price_change_10 > 0.5 and rsi > 52 and bb_position > 0.55:  # 降低上涨趋势要求
        return 'strong_uptrend'
    elif price_change_10 < -0.5 and rsi < 48 and bb_position < 0.45:  # 降低下跌趋势要求
        return 'strong_downtrend'
    elif bb_width < 2.0 and 40 < rsi < 60:  # 放宽整理区间
        return 'consolidation'
    elif current_price > bb_upper * 0.998 or current_price < bb_lower * 1.002:  # 接近布林带边界
        return 'mean_reversion'
    elif bb_width > 2.5:  # 降低高波动要求
        return 'high_volatility'
    elif abs(price_change_5) > 0.3:  # 添加中等趋势识别
        return 'moderate_trend'
    else:
        return 'neutral'

def generate_new_trading_signal(market_data_buffer, current_time, config):
    """全新的多指标交易信号生成 - 使用与自动交易一致的配置"""
    try:
        if len(market_data_buffer) < 25:  # 需要更多数据
            return None

        # 获取价格数据
        prices = [d['price'] for d in market_data_buffer[-25:]]
        current_price = prices[-1]

        # 计算技术指标
        rsi = calculate_rsi(prices, 14)
        macd_line, signal_line, histogram = calculate_macd(prices)
        bb_upper, bb_middle, bb_lower = calculate_bollinger_bands(prices, 20, 2)

        # 识别市场环境
        market_regime = identify_market_regime(prices, rsi, bb_upper, bb_lower)

        print(f"🔍 市场分析: RSI={rsi:.1f}, MACD={macd_line:.4f}, 环境={market_regime}")
        print(f"📊 价格信息: 当前={current_price:.2f}, BB上轨={bb_upper:.2f}, BB中轨={bb_middle:.2f}, BB下轨={bb_lower:.2f}")

        # 应用单边行情检测配置 - 回测模式下更宽松
        if config.get('trend_detection_enabled', True):
            # 计算趋势强度
            trend_strength = calculate_trend_strength(prices, rsi, macd_line)
            trend_threshold = config.get('trend_strength_threshold', 60)

            # 使用优化策略配置中的趋势强度阈值
            backtest_threshold = config.get('trend_threshold_general', 23)  # 使用配置中的一般阈值

            # 如果市场环境明确，使用更低的阈值
            if market_regime in ['strong_uptrend', 'strong_downtrend', 'mean_reversion']:
                backtest_threshold = config.get('trend_threshold_clear', 16)  # 使用配置中的明确环境阈值

            print(f"📊 单边行情检测: 趋势强度={trend_strength:.1f}%, 原阈值={trend_threshold}%, 回测阈值={backtest_threshold}%")

            # 如果趋势强度不足，不生成信号
            if trend_strength < backtest_threshold:
                print(f"🚫 趋势强度不足: {trend_strength:.1f}% < {backtest_threshold}%")
                return None
        else:
            print("📊 单边行情检测: 已禁用，跳过趋势强度检查")

        # 根据市场环境生成信号 - 增加更多交易机会
        signal = None
        print(f"🎯 开始根据市场环境 '{market_regime}' 生成信号...")

        if market_regime == 'strong_uptrend':
            signal = generate_trend_signal(current_price, current_time, rsi, macd_line, signal_line, bb_upper, bb_middle, bb_lower, 'buy', config)
        elif market_regime == 'strong_downtrend':
            signal = generate_trend_signal(current_price, current_time, rsi, macd_line, signal_line, bb_upper, bb_middle, bb_lower, 'sell', config)
        elif market_regime == 'moderate_trend':  # 新增中等趋势处理
            # 根据价格变化方向决定交易方向
            if len(prices) >= 5:
                price_change = (prices[-1] - prices[-5]) / prices[-5] * 100
                if price_change > 0.2:
                    signal = generate_trend_signal(current_price, current_time, rsi, macd_line, signal_line, bb_upper, bb_middle, bb_lower, 'buy', config)
                elif price_change < -0.2:
                    signal = generate_trend_signal(current_price, current_time, rsi, macd_line, signal_line, bb_upper, bb_middle, bb_lower, 'sell', config)
        elif market_regime == 'mean_reversion':
            signal = generate_reversion_signal(current_price, current_time, rsi, bb_upper, bb_middle, bb_lower, config)
        elif market_regime == 'consolidation':
            signal = generate_breakout_signal(current_price, current_time, prices, bb_upper, bb_lower, macd_line, config)
        elif market_regime == 'high_volatility':  # 新增高波动处理
            signal = generate_volatility_signal(current_price, current_time, rsi, macd_line, bb_upper, bb_middle, bb_lower, config)

        if signal:
            signal['market_regime'] = market_regime
            signal['rsi'] = rsi
            signal['macd'] = macd_line
            # 使用优化策略配置中的信号强度要求
            min_signals = config.get('min_signals', 2)
            # 使用配置中的优化信号强度值
            required_strength = config.get('min_strength_strategy', 0.13)  # 使用配置中的优化值
            if signal['strength'] >= required_strength:
                print(f"✅ 生成{signal['type']}信号: 强度={signal['strength']:.3f}, 原因={signal['reason']}")
                return signal
            else:
                print(f"🚫 信号强度不足最小要求: {signal['strength']:.3f} < {required_strength:.2f}")
        else:
            print(f"❌ 未生成任何信号，市场环境: {market_regime}")

        # 第三步微调：轻微放宽强制信号条件，增加交易机会
        if len(prices) >= 10:
            # 基于轻微放宽的RSI极值生成强制信号
            # 使用优化策略配置中的强制信号参数
            force_oversold_rsi = config.get('force_oversold_rsi', 24)
            force_signal_strength = config.get('force_signal_strength', 0.45)

            # 使用优化策略配置中的强制信号参数
            force_overbought_rsi = config.get('force_overbought_rsi', 76)

            if rsi < force_oversold_rsi:
                print(f"🔥 强制生成买入信号: RSI极度超卖 {rsi:.1f}")
                return {
                    'type': 'buy',
                    'strength': force_signal_strength,
                    'reason': 'FORCE_RSI_OVERSOLD',
                    'entry_price': current_price,
                    'timestamp': current_time,
                    'market_regime': market_regime,
                    'rsi': rsi,
                    'macd': macd_line
                }
            elif rsi > force_overbought_rsi:
                print(f"🔥 强制生成卖出信号: RSI极度超买 {rsi:.1f}")
                return {
                    'type': 'sell',
                    'strength': force_signal_strength,
                    'reason': 'FORCE_RSI_OVERBOUGHT',
                    'entry_price': current_price,
                    'timestamp': current_time,
                    'market_regime': market_regime,
                    'rsi': rsi,
                    'macd': macd_line
                }

        return None

    except Exception as e:
        print(f"❌ 新策略信号生成失败: {e}")
        return None

def calculate_trend_strength(prices, rsi, macd_line):
    """计算趋势强度百分比 - 修复版本"""
    try:
        # 价格趋势强度 (基于移动平均线和价格变化)
        price_trend = 0
        if len(prices) >= 10:
            # 短期移动平均线趋势
            short_ma = sum(prices[-5:]) / 5
            long_ma = sum(prices[-10:]) / 10
            ma_trend = abs((short_ma - long_ma) / long_ma) * 100

            # 价格变化趋势
            price_change_5 = abs((prices[-1] - prices[-5]) / prices[-5]) * 100
            price_change_10 = abs((prices[-1] - prices[-10]) / prices[-10]) * 100

            # 综合价格趋势强度
            price_trend = max(ma_trend, price_change_5, price_change_10)

        # RSI趋势强度 (偏离50的程度，放大系数)
        rsi_trend = abs(rsi - 50) * 1.5  # 降低RSI权重，增加敏感度

        # MACD趋势强度 (如果MACD为0，使用价格动量替代)
        if abs(macd_line) > 0.0001:
            macd_trend = min(abs(macd_line) * 10000, 50)  # 降低MACD权重
        else:
            # MACD为0时，使用价格动量
            if len(prices) >= 3:
                momentum = abs((prices[-1] - prices[-3]) / prices[-3]) * 100
                macd_trend = min(momentum * 10, 50)
            else:
                macd_trend = 0

        # 波动率强度 (增加波动率因子)
        if len(prices) >= 5:
            volatility = 0
            for i in range(1, 5):
                volatility += abs((prices[-i] - prices[-i-1]) / prices[-i-1])
            volatility = (volatility / 4) * 100
            volatility_trend = min(volatility * 5, 30)  # 波动率贡献
        else:
            volatility_trend = 0

        # 综合趋势强度 (调整权重)
        trend_strength = (price_trend * 0.4 + rsi_trend * 0.3 + macd_trend * 0.2 + volatility_trend * 0.1)

        # 确保结果在合理范围内
        result = min(max(trend_strength, 0), 100)

        # 调试输出
        if result > 35:  # 只在较高强度时输出详细信息
            print(f"🔍 趋势强度详情: 价格={price_trend:.1f}, RSI={rsi_trend:.1f}, MACD={macd_trend:.1f}, 波动={volatility_trend:.1f}, 总计={result:.1f}")

        return result

    except Exception as e:
        print(f"❌ 趋势强度计算失败: {e}")
        return 0

def generate_volatility_signal(current_price, current_time, rsi, macd_line, bb_upper, bb_middle, bb_lower, config):
    """生成高波动环境下的交易信号"""
    try:
        # 在高波动环境下，寻找反转机会
        if rsi < 35 and current_price < bb_lower * 1.01:  # 超卖且接近下轨
            strength = (35 - rsi) / 35 * 0.6 + 0.3  # 基础强度0.3-0.9
            return {
                'type': 'buy',
                'strength': min(strength, 0.8),
                'reason': 'VOLATILITY_OVERSOLD',
                'entry_price': current_price,
                'timestamp': current_time
            }
        elif rsi > 65 and current_price > bb_upper * 0.99:  # 超买且接近上轨
            strength = (rsi - 65) / 35 * 0.6 + 0.3  # 基础强度0.3-0.9
            return {
                'type': 'sell',
                'strength': min(strength, 0.8),
                'reason': 'VOLATILITY_OVERBOUGHT',
                'entry_price': current_price,
                'timestamp': current_time
            }

        return None
    except Exception as e:
        print(f"❌ 高波动信号生成失败: {e}")
        return None

def generate_trend_signal(current_price, current_time, rsi, macd_line, signal_line, bb_upper, bb_middle, bb_lower, direction, config):
    """生成趋势跟踪信号"""
    if direction == 'buy':
        # 买入条件：使用优化策略配置中的参数
        rsi_range = config.get('rsi_buy_range', [27, 78])
        macd_multiplier = config.get('macd_buy_multiplier', 0.82)
        bb_position = config.get('bb_buy_position', 0.996)

        macd_ok = (macd_line > signal_line * macd_multiplier) if abs(macd_line) > 0.0001 else True
        if rsi_range[0] < rsi < rsi_range[1] and macd_ok and current_price > bb_middle * bb_position:
            print(f"🎯 买入条件检查: RSI={rsi:.1f} ∈ (25,80)✅, MACD={macd_line:.4f}>={signal_line*0.8:.4f}{'✅' if macd_ok else '❌'}, 价格={current_price:.2f}>={bb_middle*0.995:.2f}✅")
            # 计算信号强度
            rsi_strength = min((rsi - 50) / 25, 1.0) if rsi > 50 else 0
            macd_strength = min(abs(macd_line) * 1000, 1.0)  # MACD强度
            bb_strength = (current_price - bb_middle) / (bb_upper - bb_middle)

            strength = (rsi_strength + macd_strength + bb_strength) / 3
            strength = max(0.3, min(strength, 1.0))

            return {
                'type': 'buy',
                'strength': strength,
                'reason': 'TREND_BUY',
                'entry_price': current_price,
                'timestamp': current_time,
                'components': {
                    'rsi_strength': rsi_strength,
                    'macd_strength': macd_strength,
                    'bb_strength': bb_strength
                }
            }
    else:  # sell
        # 卖出条件：使用优化策略配置中的参数
        rsi_range = config.get('rsi_sell_range', [22, 73])
        macd_multiplier = config.get('macd_sell_multiplier', 1.18)
        bb_position = config.get('bb_sell_position', 1.004)

        macd_ok = (macd_line < signal_line * macd_multiplier) if abs(macd_line) > 0.0001 else True
        if rsi_range[0] < rsi < rsi_range[1] and macd_ok and current_price < bb_middle * bb_position:
            print(f"🎯 卖出条件检查: RSI={rsi:.1f} ∈ (20,75)✅, MACD={macd_line:.4f}<={signal_line*1.2:.4f}{'✅' if macd_ok else '❌'}, 价格={current_price:.2f}<={bb_middle*1.005:.2f}✅")
            rsi_strength = min((50 - rsi) / 25, 1.0) if rsi < 50 else 0
            macd_strength = min(abs(macd_line) * 1000, 1.0)
            bb_strength = (bb_middle - current_price) / (bb_middle - bb_lower)
            
            strength = (rsi_strength + macd_strength + bb_strength) / 3
            strength = max(0.3, min(strength, 1.0))
            
            return {
                'type': 'sell',
                'strength': strength,
                'reason': 'TREND_SELL',
                'entry_price': current_price,
                'timestamp': current_time,
                'components': {
                    'rsi_strength': rsi_strength,
                    'macd_strength': macd_strength,
                    'bb_strength': bb_strength
                }
            }
    
    return None

def generate_reversion_signal(current_price, current_time, rsi, bb_upper, bb_middle, bb_lower, config):
    """生成均值回归信号 - 放宽条件"""
    # 超买区域，预期回调 - 使用优化策略配置参数
    bb_multiplier = config.get('bb_upper_multiplier', 0.998)
    overbought_rsi = config.get('mean_reversion_overbought_rsi', 66)

    if current_price > bb_upper * bb_multiplier and rsi > overbought_rsi:
        print(f"🎯 均值回归卖出检查: 价格={current_price:.2f}>BB上轨*{bb_multiplier}={bb_upper*bb_multiplier:.2f}✅, RSI={rsi:.1f}>{overbought_rsi}✅")
        deviation = (current_price - bb_upper) / bb_upper * 100
        rsi_extreme = (rsi - 70) / 30
        strength = min((deviation * 5 + rsi_extreme) / 2 + 0.4, 1.0)
        
        return {
            'type': 'sell',
            'strength': strength,
            'reason': 'MEAN_REVERSION_SELL',
            'entry_price': current_price,
            'timestamp': current_time,
            'deviation': deviation
        }

    # 超卖区域，预期反弹 - 使用优化策略配置参数
    bb_multiplier = config.get('bb_lower_multiplier', 1.002)
    oversold_rsi = config.get('mean_reversion_oversold_rsi', 34)

    if current_price < bb_lower * bb_multiplier and rsi < oversold_rsi:
        print(f"🎯 均值回归买入检查: 价格={current_price:.2f}<BB下轨*{bb_multiplier}={bb_lower*bb_multiplier:.2f}✅, RSI={rsi:.1f}<{oversold_rsi}✅")
        deviation = (bb_lower - current_price) / bb_lower * 100
        rsi_extreme = (30 - rsi) / 30
        strength = min((deviation * 5 + rsi_extreme) / 2 + 0.4, 1.0)
        
        return {
            'type': 'buy',
            'strength': strength,
            'reason': 'MEAN_REVERSION_BUY',
            'entry_price': current_price,
            'timestamp': current_time,
            'deviation': deviation
        }
    
    return None

def generate_breakout_signal(current_price, current_time, prices, bb_upper, bb_lower, macd_line):
    """生成突破信号"""
    # 计算价格动量
    if len(prices) >= 5:
        momentum = (prices[-1] - prices[-5]) / prices[-5] * 100
    else:
        momentum = 0
    
    # 向上突破
    if current_price > bb_upper and momentum > 0.2 and macd_line > 0:
        breakout_strength = (current_price - bb_upper) / bb_upper * 100
        momentum_strength = min(momentum * 2, 1.0)
        strength = min((breakout_strength * 10 + momentum_strength) / 2 + 0.3, 1.0)
        
        return {
            'type': 'buy',
            'strength': strength,
            'reason': 'BREAKOUT_BUY',
            'entry_price': current_price,
            'timestamp': current_time,
            'momentum': momentum
        }
    
    # 向下突破
    elif current_price < bb_lower and momentum < -0.2 and macd_line < 0:
        breakout_strength = (bb_lower - current_price) / bb_lower * 100
        momentum_strength = min(abs(momentum) * 2, 1.0)
        strength = min((breakout_strength * 10 + momentum_strength) / 2 + 0.3, 1.0)
        
        return {
            'type': 'sell',
            'strength': strength,
            'reason': 'BREAKOUT_SELL',
            'entry_price': current_price,
            'timestamp': current_time,
            'momentum': momentum
        }
    
    return None

def should_execute_new_trade(backtest_state, signal, current_time, config):
    """新的交易执行判断逻辑"""
    if not signal:
        return False
    
    # 更严格的信号强度要求
    min_strength = 0.5  # 提高到0.5
    if signal['strength'] < min_strength:
        print(f"🚫 信号强度不足: {signal['strength']:.3f} < {min_strength}")
        return False
    
    # 检查今日交易限制
    today_trades = len([t for t in backtest_state.get('trades', []) 
                       if t.get('entry_time', '').startswith(current_time.strftime('%Y-%m-%d'))])
    
    if today_trades >= config.get('daily_limit', 1):
        print(f"🚫 今日交易次数已达限制: {today_trades}/{config.get('daily_limit', 1)}")
        return False
    
    # 检查是否有相同方向的持仓
    active_positions = [t for t in backtest_state.get('trades', []) if not t.get('exit_time')]
    same_direction_positions = [p for p in active_positions if p.get('type') == signal['type']]
    
    if same_direction_positions:
        print(f"🚫 已有相同方向持仓: {signal['type']}")
        return False
    
    print(f"✅ 信号通过验证: {signal['type']}, 强度={signal['strength']:.3f}")
    return True

if __name__ == "__main__":
    # 测试新策略
    print("🧪 测试新黄金交易策略")
    
    # 模拟价格数据
    test_prices = [2650 + i * 0.5 + (i % 3 - 1) * 2 for i in range(30)]
    
    # 测试指标计算
    rsi = calculate_rsi(test_prices)
    macd_line, signal_line, histogram = calculate_macd(test_prices)
    bb_upper, bb_middle, bb_lower = calculate_bollinger_bands(test_prices)
    
    print(f"RSI: {rsi:.2f}")
    print(f"MACD: {macd_line:.4f}")
    print(f"布林带: {bb_lower:.2f} - {bb_middle:.2f} - {bb_upper:.2f}")
    
    # 测试市场环境识别
    regime = identify_market_regime(test_prices, rsi, bb_upper, bb_lower)
    print(f"市场环境: {regime}")
