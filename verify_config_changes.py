#!/usr/bin/env python3
"""
验证配置修改是否正确
"""

import re

def verify_confidence_changes():
    """验证置信度配置修改"""
    print("🔍 验证置信度配置修改")
    print("=" * 50)
    
    try:
        with open('templates/model_inference.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查各个置信度配置
        confidence_configs = [
            ('backtestMinConfidence', '回测最低置信度'),
            ('inferenceMinConfidence', '推理最低置信度'),
            ('minConfidence', 'AI交易最低置信度')
        ]
        
        all_correct = True
        
        for config_id, name in confidence_configs:
            # 查找配置元素
            pattern = rf'id="{config_id}"[^>]*value="([^"]*)"[^>]*min="([^"]*)"'
            match = re.search(pattern, content)
            
            if match:
                value = match.group(1)
                min_val = match.group(2)
                
                print(f"✅ {name}:")
                print(f"   默认值: {value}")
                print(f"   最小值: {min_val}")
                
                if value == "0.3" and min_val == "0.3":
                    print(f"   ✅ 配置正确")
                else:
                    print(f"   ❌ 配置错误 - 期望: value='0.3', min='0.3'")
                    all_correct = False
            else:
                print(f"❌ {name}: 未找到配置")
                all_correct = False
            
            print()
        
        # 检查推荐范围文本
        if "推荐: 0.3-0.6" in content:
            print("✅ 推荐范围文本已更新")
        else:
            print("❌ 推荐范围文本未更新")
            all_correct = False
        
        return all_correct
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def verify_max_positions_changes():
    """验证最大持仓数配置修改"""
    print("🔍 验证最大持仓数配置修改")
    print("=" * 50)
    
    try:
        with open('templates/model_inference.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找maxPositions配置
        pattern = r'id="maxPositions"[^>]*value="([^"]*)"'
        match = re.search(pattern, content)
        
        if match:
            value = match.group(1)
            print(f"✅ 最大持仓数配置:")
            print(f"   默认值: {value}")
            
            if value == "4":
                print(f"   ✅ 配置正确")
                return True
            else:
                print(f"   ❌ 配置错误 - 期望: value='4', 实际: value='{value}'")
                return False
        else:
            print(f"❌ 最大持仓数配置未找到")
            return False
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def verify_preset_changes():
    """验证预设配置修改"""
    print("🔍 验证预设配置修改")
    print("=" * 50)
    
    try:
        with open('templates/model_inference.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查各个预设配置
        presets = {
            'conservative': {
                'confidence': '0.6',
                'positions': '2',
                'description': '保守型'
            },
            'balanced': {
                'confidence': '0.4',
                'positions': '4',
                'description': '平衡型'
            },
            'aggressive': {
                'confidence': '0.3',
                'positions': '6',
                'description': '激进型'
            }
        }
        
        all_correct = True
        
        for preset_name, expected in presets.items():
            print(f"📊 {expected['description']}预设:")
            
            # 查找预设配置代码块
            pattern = rf"case '{preset_name}':(.*?)break;"
            match = re.search(pattern, content, re.DOTALL)
            
            if match:
                preset_code = match.group(1)
                
                # 检查置信度设置
                conf_pattern = r"minConfidence.*?value\s*=\s*([0-9.]+)"
                conf_match = re.search(conf_pattern, preset_code)
                
                if conf_match:
                    conf_value = conf_match.group(1)
                    print(f"   置信度: {conf_value}")
                    
                    if conf_value == expected['confidence']:
                        print(f"   ✅ 置信度正确")
                    else:
                        print(f"   ❌ 置信度错误 - 期望: {expected['confidence']}")
                        all_correct = False
                else:
                    print(f"   ❌ 未找到置信度设置")
                    all_correct = False
                
                # 检查持仓数设置
                pos_pattern = r"maxPositions.*?value\s*=\s*([0-9]+)"
                pos_match = re.search(pos_pattern, preset_code)
                
                if pos_match:
                    pos_value = pos_match.group(1)
                    print(f"   持仓数: {pos_value}")
                    
                    if pos_value == expected['positions']:
                        print(f"   ✅ 持仓数正确")
                    else:
                        print(f"   ❌ 持仓数错误 - 期望: {expected['positions']}")
                        all_correct = False
                else:
                    print(f"   ❌ 未找到持仓数设置")
                    all_correct = False
                    
            else:
                print(f"   ❌ 未找到预设配置代码")
                all_correct = False
            
            print()
        
        return all_correct
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def verify_validation_function():
    """验证配置验证函数修改"""
    print("🔍 验证配置验证函数修改")
    print("=" * 50)
    
    try:
        with open('templates/model_inference.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找验证函数中的置信度检查
        pattern = r"config\.min_confidence\s*<\s*([0-9.]+).*?推荐([0-9.-]+)"
        match = re.search(pattern, content)
        
        if match:
            min_val = match.group(1)
            recommend_range = match.group(2)
            
            print(f"✅ 验证函数配置:")
            print(f"   最小值检查: {min_val}")
            print(f"   推荐范围: {recommend_range}")
            
            if min_val == "0.3" and "0.3-0.6" in recommend_range:
                print(f"   ✅ 验证函数正确")
                return True
            else:
                print(f"   ❌ 验证函数错误")
                return False
        else:
            print(f"❌ 未找到验证函数配置")
            return False
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def create_summary_report():
    """创建修改总结报告"""
    print("📊 配置修改总结")
    print("=" * 50)
    
    print("🔧 已完成的修改:")
    print()
    
    print("1️⃣ 深度学习模型推理 - 最低置信度:")
    print("   • 默认值: 0.1 → 0.3")
    print("   • 最小值: 0.05 → 0.3")
    print("   • 推荐范围: 0.05-0.2 → 0.3-0.6")
    print("   • 影响范围: 回测配置、推理配置、AI交易配置")
    print()
    
    print("2️⃣ AI推理交易 - 最大持仓数:")
    print("   • 默认值: 3 → 4")
    print("   • 影响范围: AI交易配置")
    print()
    
    print("3️⃣ 交易预设配置更新:")
    print("   • 保守型: 置信度 20% → 60%, 持仓数 1 → 2")
    print("   • 平衡型: 置信度 10% → 40%, 持仓数 2 → 4")
    print("   • 激进型: 置信度 5% → 30%, 持仓数 3 → 6")
    print()
    
    print("4️⃣ 配置验证函数:")
    print("   • 置信度验证范围: 0.05-0.99 → 0.3-0.99")
    print("   • 错误提示信息已更新")
    print()
    
    print("💡 修改效果:")
    print("   ✅ 提高交易信号质量（更高置信度要求）")
    print("   ✅ 增加交易机会（更多持仓位）")
    print("   ✅ 降低假信号风险")
    print("   ✅ 保持风险可控")

def main():
    """主函数"""
    print("🔧 验证AI推理交易配置修改")
    print("=" * 80)
    
    # 1. 验证置信度修改
    confidence_ok = verify_confidence_changes()
    
    # 2. 验证最大持仓数修改
    positions_ok = verify_max_positions_changes()
    
    # 3. 验证预设配置修改
    presets_ok = verify_preset_changes()
    
    # 4. 验证验证函数修改
    validation_ok = verify_validation_function()
    
    # 5. 总结报告
    create_summary_report()
    
    # 6. 最终结果
    print(f"\n📊 验证结果总结")
    print("=" * 80)
    
    results = [
        ("置信度配置", confidence_ok),
        ("最大持仓数配置", positions_ok),
        ("预设配置", presets_ok),
        ("验证函数", validation_ok)
    ]
    
    all_success = True
    for name, result in results:
        status = "✅ 成功" if result else "❌ 失败"
        print(f"   {name}: {status}")
        if not result:
            all_success = False
    
    print()
    if all_success:
        print("🎉 所有配置修改验证通过！")
        print("💡 用户现在可以:")
        print("   • 使用更高质量的交易信号（置信度 ≥ 30%）")
        print("   • 同时持有更多仓位（默认4个）")
        print("   • 享受优化后的预设配置")
    else:
        print("❌ 部分配置修改验证失败，需要检查")

if __name__ == '__main__':
    main()
