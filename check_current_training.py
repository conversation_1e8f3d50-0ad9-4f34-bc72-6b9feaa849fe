#!/usr/bin/env python3
"""
检查当前深度学习训练状态
"""

import sqlite3
import json
import requests
from datetime import datetime

def login_session():
    """创建登录会话"""
    session = requests.Session()
    
    try:
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post('http://127.0.0.1:5000/login', data=login_data)
        return session if response.status_code == 200 else None
            
    except Exception as e:
        print(f"❌ 登录失败: {e}")
        return None

def check_current_training_status():
    """检查当前训练状态"""
    
    print("🔍 检查当前深度学习训练状态")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 获取所有运行中的训练任务
        cursor.execute("""
            SELECT id, model_id, status, progress, current_epoch, total_epochs,
                   train_loss, val_loss, logs, created_at, started_at, updated_at,
                   (julianday('now') - julianday(COALESCE(updated_at, created_at))) * 24 * 60 as minutes_since_update
            FROM training_tasks 
            WHERE status = 'running'
            ORDER BY created_at DESC
        """)
        
        running_tasks = cursor.fetchall()
        
        if not running_tasks:
            print("❌ 没有找到运行中的训练任务")
            conn.close()
            return None
        
        print(f"📊 找到 {len(running_tasks)} 个运行中的任务:")
        
        current_task = None
        
        for i, task in enumerate(running_tasks, 1):
            (task_id, model_id, status, progress, current_epoch, total_epochs,
             train_loss, val_loss, logs, created_at, started_at, updated_at, minutes_since_update) = task
            
            print(f"\n🔹 任务 {i}: {task_id}")
            print(f"   模型ID: {model_id}")
            print(f"   状态: {status}")
            print(f"   进度: {progress}% ({current_epoch}/{total_epochs})")
            print(f"   训练损失: {train_loss}")
            print(f"   验证损失: {val_loss}")
            print(f"   创建时间: {created_at}")
            print(f"   开始时间: {started_at}")
            print(f"   更新时间: {updated_at}")
            print(f"   距离上次更新: {minutes_since_update:.1f} 分钟")
            
            if logs:
                try:
                    log_data = json.loads(logs)
                    print(f"   📝 日志信息:")
                    for key, value in log_data.items():
                        print(f"      {key}: {value}")
                except:
                    print(f"   📝 原始日志: {logs[:100]}...")
            
            # 判断任务状态
            if minutes_since_update > 10:
                print(f"   ⚠️ 任务可能卡住: {minutes_since_update:.1f}分钟未更新")
            else:
                print(f"   ✅ 任务状态正常")
                current_task = task_id
        
        conn.close()
        return current_task
        
    except Exception as e:
        print(f"❌ 检查训练状态失败: {e}")
        return None

def test_training_api(task_id):
    """测试训练API"""
    
    print(f"\n🧪 测试训练API (任务: {task_id})")
    print("=" * 60)
    
    session = login_session()
    if not session:
        print("❌ 无法登录")
        return
    
    try:
        # 测试进度API
        response = session.get(f'http://127.0.0.1:5000/api/deep-learning/training-progress/{task_id}')
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                progress_data = result['progress']
                print(f"✅ 进度API响应正常:")
                print(f"   状态: {progress_data.get('status')}")
                print(f"   进度: {progress_data.get('progress')}%")
                print(f"   轮次: {progress_data.get('epoch')}/{progress_data.get('total_epochs')}")
                print(f"   训练损失: {progress_data.get('train_loss')}")
                print(f"   验证损失: {progress_data.get('val_loss')}")
            else:
                print(f"❌ 进度API错误: {result.get('error')}")
        else:
            print(f"❌ 进度API请求失败: {response.status_code}")
        
        # 测试控制状态API
        response = session.get(f'http://127.0.0.1:5000/api/deep-learning/training/{task_id}/control-status')
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                control_data = result['control']
                print(f"\n✅ 控制API响应正常:")
                print(f"   可控制: {control_data.get('can_control')}")
                print(f"   已暂停: {control_data.get('is_paused')}")
                print(f"   已停止: {control_data.get('is_stopped')}")
            else:
                print(f"❌ 控制API错误: {result.get('error')}")
        else:
            print(f"❌ 控制API请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ API测试失败: {e}")

def check_gpu_usage():
    """检查GPU使用情况"""
    
    print(f"\n🎮 检查GPU使用情况")
    print("=" * 60)
    
    session = login_session()
    if not session:
        print("❌ 无法登录")
        return
    
    try:
        response = session.get('http://127.0.0.1:5000/api/deep-learning/gpu-status')
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                gpu_data = result['gpu_status']
                print(f"✅ GPU状态API响应正常:")
                print(f"   GPU可用: {gpu_data.get('gpu_available')}")
                print(f"   设备: {gpu_data.get('device_name')}")
                print(f"   总内存: {gpu_data.get('total_memory_gb')}GB")
                print(f"   可用内存: {gpu_data.get('available_memory_gb')}GB")
                print(f"   使用率: {gpu_data.get('memory_usage_percent')}%")
                print(f"   CUDA版本: {gpu_data.get('cuda_version')}")
            else:
                print(f"❌ GPU状态API错误: {result.get('error')}")
        else:
            print(f"❌ GPU状态API请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ GPU检查失败: {e}")

def main():
    """主函数"""
    
    print("🔧 深度学习训练状态检查")
    print("=" * 80)
    
    # 检查当前训练状态
    current_task = check_current_training_status()
    
    if current_task:
        # 测试训练API
        test_training_api(current_task)
        
        # 检查GPU使用情况
        check_gpu_usage()
        
        print(f"\n📋 诊断结果")
        print("=" * 80)
        
        print(f"🔍 发现的问题:")
        print(f"• 有训练任务正在运行但进度可能没有更新")
        print(f"• GPU可能没有被使用")
        print(f"• 前端页面可能没有显示详细信息")
        
        print(f"\n💡 建议的修复方案:")
        print(f"1. 检查训练进程是否真正在运行")
        print(f"2. 修复进度更新机制")
        print(f"3. 增强前端页面显示")
        print(f"4. 添加数据准备过程的详细信息")
        print(f"5. 显示实时的数据获取状态")
        
    else:
        print(f"\n✅ 没有发现运行中的训练任务")
        print(f"💡 如果用户看到有训练在进行，可能是:")
        print(f"• 数据库状态没有正确更新")
        print(f"• 前端缓存了旧的状态")
        print(f"• 训练进程已经停止但状态未更新")

if __name__ == '__main__':
    main()
