#!/usr/bin/env python3
"""
形态分析配置文件
"""

# MT5连接配置
MT5_CONFIG = {
    'timeout': 60000,  # 连接超时时间(毫秒)
    'portable': False,  # 是否使用便携版MT5
}

# 形态识别参数
PATTERN_CONFIG = {
    # 通用参数
    'min_pattern_bars': 20,      # 形态最小K线数量
    'max_pattern_bars': 100,     # 形态最大K线数量
    'min_confidence': 0.5,       # 最小置信度阈值
    
    # 头肩形态参数
    'head_shoulders': {
        'shoulder_similarity_threshold': 0.02,  # 两肩相似度阈值(2%)
        'head_prominence_threshold': 0.02,      # 头部突出度阈值(2%)
        'volume_confirmation': True,            # 是否使用成交量确认
    },
    
    # 双顶双底参数
    'double_pattern': {
        'peak_similarity_threshold': 0.015,     # 双顶/双底相似度阈值(1.5%)
        'min_valley_depth': 0.01,              # 最小谷深度(1%)
    },
    
    # 三角形参数
    'triangle': {
        'horizontal_threshold': 0.01,           # 水平线阈值(1%)
        'convergence_threshold': 0.01,          # 收敛阈值(1%)
        'min_touch_points': 2,                  # 最小触点数量
    },
    
    # 楔形参数
    'wedge': {
        'slope_difference_threshold': 0.0001,   # 斜率差异阈值
        'min_slope': 0.0001,                   # 最小斜率
    },
    
    # 旗形参数
    'flag': {
        'flag_slope_threshold': 0.005,          # 旗形斜率阈值
        'pole_min_height': 0.02,               # 旗杆最小高度(2%)
        'consolidation_bars': 10,              # 整理期最小K线数
    }
}

# 可视化配置
VISUALIZATION_CONFIG = {
    'figure_size': (15, 10),
    'dpi': 300,
    'colors': {
        'bullish': '#2E8B57',    # 看涨绿色
        'bearish': '#DC143C',    # 看跌红色
        'neutral': '#4682B4',    # 中性蓝色
        'volume_up': '#90EE90',  # 成交量上涨
        'volume_down': '#FFB6C1' # 成交量下跌
    },
    'line_styles': {
        'support': '--',
        'resistance': '--',
        'trendline': '-',
        'neckline': ':'
    },
    'font_config': {
        'family': ['SimHei', 'Microsoft YaHei'],
        'size': 10
    }
}

# 交易品种配置
SYMBOL_CONFIG = {
    # 主要货币对
    'major_pairs': [
        'EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF',
        'AUDUSD', 'USDCAD', 'NZDUSD'
    ],
    
    # 次要货币对
    'minor_pairs': [
        'EURGBP', 'EURJPY', 'EURCHF', 'EURAUD',
        'EURCAD', 'GBPJPY', 'GBPCHF', 'GBPAUD'
    ],
    
    # 商品货币对
    'commodity_pairs': [
        'AUDJPY', 'AUDCHF', 'AUDCAD', 'CADJPY',
        'CADCHF', 'NZDJPY', 'NZDCHF', 'NZDCAD'
    ],
    
    # 黄金和原油
    'commodities': [
        'XAUUSD',  # 黄金
        'XAGUSD',  # 白银
        'USOIL',   # 原油
        'UKOIL'    # 布伦特原油
    ],
    
    # 股指
    'indices': [
        'US30',    # 道琼斯
        'US500',   # 标普500
        'NAS100',  # 纳斯达克
        'GER30',   # 德国DAX
        'UK100',   # 英国富时100
        'JPN225'   # 日经225
    ]
}

# 时间周期配置
TIMEFRAME_CONFIG = {
    '1m': {'name': '1分钟', 'bars_limit': 2000},
    '5m': {'name': '5分钟', 'bars_limit': 2000},
    '15m': {'name': '15分钟', 'bars_limit': 2000},
    '30m': {'name': '30分钟', 'bars_limit': 2000},
    '1h': {'name': '1小时', 'bars_limit': 2000},
    '4h': {'name': '4小时', 'bars_limit': 2000},
    '1d': {'name': '日线', 'bars_limit': 1000},
    '1w': {'name': '周线', 'bars_limit': 500},
    '1M': {'name': '月线', 'bars_limit': 200}
}

# 输出配置
OUTPUT_CONFIG = {
    'save_images': True,
    'save_csv': True,
    'save_json': True,
    'image_format': 'png',
    'csv_encoding': 'utf-8-sig',
    'max_detail_charts': 10,  # 最大详细图表数量
    'output_directory': 'output',
    'filename_format': '{pattern_type}_{symbol}_{timeframe}_{timestamp}'
}

# 分析策略配置
ANALYSIS_CONFIG = {
    # 启用的形态类型
    'enabled_patterns': [
        'head_shoulders_top',
        'head_shoulders_bottom',
        'double_top',
        'double_bottom',
        'ascending_triangle',
        'descending_triangle',
        'symmetric_triangle',
        'rising_wedge',
        'falling_wedge',
        'bull_flag',
        'bear_flag',
        'rectangle_top',
        'rectangle_bottom'
    ],
    
    # 过滤条件
    'filters': {
        'min_confidence': 0.6,      # 最小置信度
        'min_pattern_height': 0.01, # 最小形态高度(1%)
        'max_pattern_age': 50,      # 最大形态年龄(K线数)
    },
    
    # 排序方式
    'sort_by': 'confidence',  # confidence, pattern_type, direction
    'sort_order': 'desc',     # desc, asc
    
    # 批量分析配置
    'batch_analysis': {
        'max_concurrent': 5,        # 最大并发数
        'delay_between_requests': 1, # 请求间隔(秒)
        'retry_attempts': 3,        # 重试次数
        'timeout': 30               # 超时时间(秒)
    }
}

# 风险管理配置
RISK_CONFIG = {
    'default_risk_reward_ratio': 2.0,  # 默认风险回报比
    'max_risk_per_trade': 0.02,        # 每笔交易最大风险(2%)
    'position_sizing': {
        'method': 'fixed_risk',        # fixed_risk, fixed_amount, kelly
        'risk_per_trade': 0.01,        # 每笔交易风险(1%)
    },
    'stop_loss': {
        'method': 'pattern_based',     # pattern_based, atr_based, percentage
        'atr_multiplier': 2.0,         # ATR倍数
        'percentage': 0.02,            # 百分比止损(2%)
    },
    'take_profit': {
        'method': 'pattern_based',     # pattern_based, risk_reward, percentage
        'risk_reward_ratio': 2.0,      # 风险回报比
        'percentage': 0.04,            # 百分比止盈(4%)
    }
}

# 通知配置
NOTIFICATION_CONFIG = {
    'enabled': False,
    'methods': ['email', 'telegram'],  # email, telegram, webhook
    'email': {
        'smtp_server': 'smtp.gmail.com',
        'smtp_port': 587,
        'username': '',
        'password': '',
        'to_addresses': []
    },
    'telegram': {
        'bot_token': '',
        'chat_id': ''
    },
    'webhook': {
        'url': '',
        'headers': {},
        'timeout': 10
    }
}

# 日志配置
LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'file_handler': {
        'enabled': True,
        'filename': 'pattern_analysis.log',
        'max_bytes': 10 * 1024 * 1024,  # 10MB
        'backup_count': 5
    },
    'console_handler': {
        'enabled': True,
        'level': 'INFO'
    }
}

def get_symbol_list(category='all'):
    """获取指定类别的交易品种列表"""
    if category == 'all':
        all_symbols = []
        for symbols in SYMBOL_CONFIG.values():
            all_symbols.extend(symbols)
        return all_symbols
    elif category in SYMBOL_CONFIG:
        return SYMBOL_CONFIG[category]
    else:
        return []

def get_pattern_config(pattern_type):
    """获取指定形态的配置"""
    for key, config in PATTERN_CONFIG.items():
        if key != 'min_pattern_bars' and key != 'max_pattern_bars' and key != 'min_confidence':
            if pattern_type.startswith(key.replace('_', '')):
                return config
    return {}

def validate_config():
    """验证配置文件的有效性"""
    errors = []
    
    # 检查必要的配置项
    required_configs = [
        'PATTERN_CONFIG', 'VISUALIZATION_CONFIG', 'SYMBOL_CONFIG',
        'TIMEFRAME_CONFIG', 'OUTPUT_CONFIG', 'ANALYSIS_CONFIG'
    ]
    
    for config_name in required_configs:
        if config_name not in globals():
            errors.append(f"缺少必要配置: {config_name}")
    
    # 检查颜色配置
    if 'colors' in VISUALIZATION_CONFIG:
        required_colors = ['bullish', 'bearish', 'neutral']
        for color in required_colors:
            if color not in VISUALIZATION_CONFIG['colors']:
                errors.append(f"缺少颜色配置: {color}")
    
    return errors

if __name__ == "__main__":
    # 验证配置
    errors = validate_config()
    if errors:
        print("配置验证失败:")
        for error in errors:
            print(f"  - {error}")
    else:
        print("配置验证通过!")
        
    # 显示配置摘要
    print(f"\n配置摘要:")
    print(f"  支持的形态类型: {len(ANALYSIS_CONFIG['enabled_patterns'])} 种")
    print(f"  预定义交易品种: {len(get_symbol_list())} 个")
    print(f"  支持的时间周期: {len(TIMEFRAME_CONFIG)} 个")
    print(f"  最小置信度阈值: {ANALYSIS_CONFIG['filters']['min_confidence']}")
