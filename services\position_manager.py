#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
持仓管理服务
处理交易的开仓、平仓、盈亏计算等功能
"""

import random
import time
from datetime import datetime, timedelta
from models import db, Trade, TradingAccount
import logging

logger = logging.getLogger(__name__)

class PositionManager:
    """持仓管理器"""
    
    def __init__(self):
        # TODO: 集成真实价格数据源
        pass
    
    def get_current_price(self, symbol: str) -> float:
        """
        获取当前市场价格

        Args:
            symbol: 交易品种

        Returns:
            float: 当前价格
        """
        # TODO: 集成真实市场数据API
        raise NotImplementedError("需要集成真实市场数据源才能获取当前价格")
    
    def calculate_profit(self, trade: Trade, current_price: float = None) -> float:
        """
        计算交易盈亏
        
        Args:
            trade: 交易记录
            current_price: 当前价格，如果为None则获取实时价格
            
        Returns:
            float: 盈亏金额
        """
        if current_price is None:
            current_price = self.get_current_price(trade.symbol)
        
        # 计算价格差
        if trade.trade_type == 'buy':
            price_diff = current_price - trade.open_price
        else:  # sell
            price_diff = trade.open_price - current_price
        
        # 计算盈亏
        # 对于外汇，通常以USD为基准
        if 'JPY' in trade.symbol:
            # 日元对的点值不同
            profit = price_diff * trade.volume * 1000
        elif 'XAU' in trade.symbol:
            # 黄金的计算方式：1手=100盎司，价格差*手数*100盎司
            # 修正：黄金的盈亏计算应该是价格差*手数*100
            profit = price_diff * trade.volume * 100
            print(f"🥇 黄金盈亏计算: 价格差={price_diff:.2f}, 手数={trade.volume}, 盈亏=${profit:.2f}")
        else:
            # 标准外汇对
            profit = price_diff * trade.volume * 100000
        
        return round(profit, 2)
    
    def should_close_position(self, trade: Trade, current_price: float = None) -> tuple:
        """
        判断是否应该平仓
        
        Args:
            trade: 交易记录
            current_price: 当前价格
            
        Returns:
            tuple: (是否平仓, 平仓原因)
        """
        if trade.status != 'open':
            return False, None
        
        if current_price is None:
            current_price = self.get_current_price(trade.symbol)
        
        # 检查止损
        if trade.stop_loss:
            if trade.trade_type == 'buy' and current_price <= trade.stop_loss:
                return True, 'stop_loss'
            elif trade.trade_type == 'sell' and current_price >= trade.stop_loss:
                return True, 'stop_loss'
        
        # 检查止盈
        if trade.take_profit:
            if trade.trade_type == 'buy' and current_price >= trade.take_profit:
                return True, 'take_profit'
            elif trade.trade_type == 'sell' and current_price <= trade.take_profit:
                return True, 'take_profit'
        
        # 检查持仓时间 (AI交易自动平仓规则 - 调整为更保守)
        if trade.strategy_name and 'AI' in trade.strategy_name:
            hold_time = datetime.utcnow() - trade.open_time

            # AI交易持仓超过24小时才考虑自动平仓（从4小时调整为24小时）
            if hold_time > timedelta(hours=24):
                return True, 'time_limit'

            # 如果盈利超过100%，自动止盈（从50%调整为100%）
            profit = self.calculate_profit(trade, current_price)
            if profit > 0:
                profit_rate = profit / (trade.volume * trade.open_price * 100000) * 100
                if profit_rate > 100:  # 盈利超过100%
                    return True, 'profit_target'

            # 如果亏损超过50%，自动止损（从20%调整为50%）
            if profit < 0:
                loss_rate = abs(profit) / (trade.volume * trade.open_price * 100000) * 100
                if loss_rate > 50:  # 亏损超过50%
                    return True, 'loss_limit'
        
        return False, None
    
    def close_position(self, trade_id: int, close_reason: str = 'manual') -> dict:
        """
        平仓操作
        
        Args:
            trade_id: 交易ID
            close_reason: 平仓原因
            
        Returns:
            dict: 平仓结果
        """
        try:
            trade = Trade.query.get(trade_id)
            if not trade:
                return {'success': False, 'error': '交易记录不存在'}
            
            if trade.status != 'open':
                return {'success': False, 'error': '交易已经关闭'}
            
            # 获取当前价格
            current_price = self.get_current_price(trade.symbol)
            
            # 计算盈亏
            profit = self.calculate_profit(trade, current_price)
            
            # 更新交易记录
            trade.close_price = current_price
            trade.close_time = datetime.utcnow()
            trade.profit = profit
            trade.status = 'closed'
            
            # 更新账户余额
            account = TradingAccount.query.get(trade.account_id)
            if account:
                account.balance += profit
                account.equity = account.balance  # 简化处理
            
            db.session.commit()
            
            logger.info(f"交易 {trade_id} 已平仓: {trade.symbol} {trade.trade_type} "
                       f"开仓价格: {trade.open_price}, 平仓价格: {current_price}, "
                       f"盈亏: {profit}, 原因: {close_reason}")
            
            return {
                'success': True,
                'trade_id': trade_id,
                'close_price': current_price,
                'profit': profit,
                'close_reason': close_reason,
                'close_time': trade.close_time.isoformat()
            }
            
        except Exception as e:
            logger.error(f"平仓失败: {e}")
            db.session.rollback()
            return {'success': False, 'error': str(e)}
    
    def update_open_positions(self, account_id: int = None) -> dict:
        """
        更新所有开仓的盈亏并检查是否需要平仓
        
        Args:
            account_id: 账户ID，如果为None则更新所有账户
            
        Returns:
            dict: 更新结果
        """
        try:
            # 获取所有开仓的交易
            query = Trade.query.filter_by(status='open')
            if account_id:
                query = query.filter_by(account_id=account_id)
            
            open_trades = query.all()
            
            updated_count = 0
            closed_count = 0
            closed_trades = []
            
            for trade in open_trades:
                # 获取当前价格
                current_price = self.get_current_price(trade.symbol)
                
                # 计算当前盈亏
                current_profit = self.calculate_profit(trade, current_price)
                
                # 检查是否需要平仓
                should_close, close_reason = self.should_close_position(trade, current_price)
                
                if should_close:
                    # 执行平仓
                    close_result = self.close_position(trade.id, close_reason)
                    if close_result['success']:
                        closed_count += 1
                        closed_trades.append({
                            'trade_id': trade.id,
                            'symbol': trade.symbol,
                            'close_reason': close_reason,
                            'profit': close_result['profit']
                        })
                else:
                    # 更新当前盈亏 (不保存到数据库，只用于显示)
                    updated_count += 1
            
            return {
                'success': True,
                'updated_positions': updated_count,
                'closed_positions': closed_count,
                'closed_trades': closed_trades
            }
            
        except Exception as e:
            logger.error(f"更新持仓失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def get_position_summary(self, account_id: int) -> dict:
        """
        获取持仓汇总信息
        
        Args:
            account_id: 账户ID
            
        Returns:
            dict: 持仓汇总
        """
        try:
            # 获取开仓交易
            open_trades = Trade.query.filter_by(
                account_id=account_id,
                status='open'
            ).all()
            
            total_profit = 0
            positions = []
            
            for trade in open_trades:
                current_price = self.get_current_price(trade.symbol)
                current_profit = self.calculate_profit(trade, current_price)
                total_profit += current_profit
                
                positions.append({
                    'trade_id': trade.id,
                    'symbol': trade.symbol,
                    'trade_type': trade.trade_type,
                    'volume': trade.volume,
                    'open_price': trade.open_price,
                    'current_price': current_price,
                    'current_profit': current_profit,
                    'open_time': trade.open_time.isoformat(),
                    'strategy_name': trade.strategy_name
                })
            
            return {
                'success': True,
                'total_positions': len(positions),
                'total_profit': round(total_profit, 2),
                'positions': positions
            }
            
        except Exception as e:
            logger.error(f"获取持仓汇总失败: {e}")
            return {'success': False, 'error': str(e)}

# 全局持仓管理器实例
position_manager = PositionManager()
