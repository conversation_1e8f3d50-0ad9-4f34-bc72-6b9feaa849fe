#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查训练任务数据库状态
"""

import sqlite3
from datetime import datetime

def check_training_database():
    """检查训练任务数据库"""
    try:
        print('🔍 检查训练任务数据库状态...')
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='training_tasks'")
        if not cursor.fetchone():
            print('❌ training_tasks表不存在')
            conn.close()
            return
        
        print('✅ 找到training_tasks表')
        
        # 获取最新的训练任务
        cursor.execute('''
            SELECT id, model_id, status, progress, current_epoch, total_epochs,
                   created_at, updated_at
            FROM training_tasks
            ORDER BY created_at DESC
            LIMIT 5
        ''')
        
        tasks = cursor.fetchall()
        
        if tasks:
            print(f'📋 找到 {len(tasks)} 个训练任务:')
            print('-' * 80)
            for i, task in enumerate(tasks):
                task_id, model_id, status, progress, current_epoch, total_epochs, created_at, updated_at = task
                print(f'{i+1}. 任务ID: {task_id}')
                print(f'   模型ID: {model_id}')
                print(f'   状态: {status}')
                print(f'   进度: {progress}%')
                print(f'   轮次: {current_epoch}/{total_epochs}')
                print(f'   创建: {created_at}')
                print(f'   更新: {updated_at}')
                
                # 检查是否卡住
                if updated_at and status == 'running':
                    try:
                        updated_time = datetime.fromisoformat(updated_at)
                        time_diff = datetime.now() - updated_time
                        minutes_ago = time_diff.total_seconds() / 60
                        if minutes_ago > 5:
                            print(f'   ⚠️ 可能卡住: {minutes_ago:.1f}分钟没有更新')
                        else:
                            print(f'   ✅ 最近更新: {minutes_ago:.1f}分钟前')
                    except:
                        print(f'   ⚠️ 时间解析失败')
                
                print('-' * 80)
        else:
            print('📋 没有找到训练任务')
        
        conn.close()
        
    except Exception as e:
        print(f'❌ 检查失败: {e}')

def test_progress_api():
    """测试进度API"""
    try:
        print('\n🧪 测试训练进度API...')
        
        # 获取最新任务ID
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id FROM training_tasks
            WHERE status IN ('running', 'pending')
            ORDER BY created_at DESC
            LIMIT 1
        ''')
        
        result = cursor.fetchone()
        conn.close()
        
        if not result:
            print('📋 没有活跃的训练任务')
            return
        
        task_id = result[0]
        print(f'🎯 测试任务: {task_id}')
        
        # 测试API
        import requests
        try:
            response = requests.get(f'http://localhost:5000/api/deep-learning/training-progress/{task_id}')
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    progress = data['progress']
                    print(f'✅ API响应正常:')
                    print(f'   状态: {progress["status"]}')
                    print(f'   进度: {progress["progress"]}%')
                    print(f'   轮次: {progress["epoch"]}/{progress["total_epochs"]}')
                    print(f'   更新: {progress["updated_at"]}')
                else:
                    print(f'❌ API返回错误: {data.get("error")}')
            else:
                print(f'❌ API请求失败: {response.status_code}')
        except Exception as e:
            print(f'❌ API测试失败: {e}')
            
    except Exception as e:
        print(f'❌ 测试进度API失败: {e}')

if __name__ == "__main__":
    check_training_database()
    test_progress_api()
