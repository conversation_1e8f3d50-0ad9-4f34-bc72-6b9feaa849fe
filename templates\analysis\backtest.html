{% extends "base.html" %}

{% block page_title %}策略回测{% endblock %}

{% block extra_css %}
<style>
/* 快捷日期按钮样式 */
.btn-group .btn {
    transition: all 0.2s ease;
    font-size: 0.875rem;
    padding: 0.375rem 0.5rem;
}

.btn-group .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.btn-group .btn.active {
    background-color: #0d6efd !important;
    border-color: #0d6efd !important;
    color: white !important;
    box-shadow: 0 2px 8px rgba(13,110,253,0.3);
}

/* 日期范围信息样式 */
#dateRangeInfo .alert {
    border-left: 4px solid #0dcaf0;
    background-color: #f8f9fa;
    border-color: #e9ecef;
}

/* 日期输入框样式优化 */
.form-control[type="date"] {
    padding: 0.375rem 0.75rem;
}

/* 小标签样式 */
.form-label.small {
    font-size: 0.875rem;
    font-weight: 500;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

/* 回测详情模态框样式 */
#backtestDetailModal .modal-dialog {
    max-width: 90%;
}

#backtestDetailModal .card {
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

#backtestDetailModal .card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    padding: 0.75rem 1rem;
}

#backtestDetailModal .card-title {
    color: #495057;
    font-size: 0.9rem;
    font-weight: 600;
}

#backtestDetailModal .table td {
    padding: 0.5rem;
    border-top: 1px solid #f1f3f4;
}

#backtestDetailModal .table td:first-child {
    width: 40%;
    color: #6c757d;
    font-weight: 500;
}

/* 盈亏颜色样式 */
.profit-positive {
    color: #198754 !important;
    font-weight: 600;
}

.profit-negative {
    color: #dc3545 !important;
    font-weight: 600;
}

/* 当前策略显示区域样式 */
#currentStrategyCard {
    border: 2px solid #0d6efd;
    box-shadow: 0 4px 12px rgba(13, 110, 253, 0.15);
    transition: all 0.3s ease;
}

#currentStrategyCard:hover {
    box-shadow: 0 6px 20px rgba(13, 110, 253, 0.25);
    transform: translateY(-2px);
}

.strategy-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border-radius: 12px;
    border: 2px solid #2196f3;
}

.strategy-status .badge {
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
}

#strategyName {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2c3e50;
}

#strategyDescription {
    font-size: 0.95rem;
    line-height: 1.4;
}

#strategyDetails {
    font-size: 0.85rem;
    color: #6c757d;
}

/* 策略类型图标样式 */
.strategy-icon .fa-robot {
    color: #2196f3;
}

.strategy-icon .fa-chart-line {
    color: #ff9800;
}

.strategy-icon .fa-brain {
    color: #9c27b0;
}
</style>
{% endblock %}

{% block content %}
<!-- 当前策略显示区域 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-primary" id="currentStrategyCard" style="display: none;">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chess-queen"></i>
                    当前选用策略
                </h5>
            </div>
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <div class="d-flex align-items-center">
                            <div class="strategy-icon me-3">
                                <i id="strategyIcon" class="fas fa-robot fa-2x text-primary"></i>
                            </div>
                            <div>
                                <h6 class="mb-1" id="strategyName">未选择策略</h6>
                                <p class="mb-0 text-muted" id="strategyDescription">请在左侧配置区域选择策略类型和具体策略</p>
                                <small class="text-muted">
                                    <span id="strategyDetails"></span>
                                </small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="strategy-status">
                            <span class="badge bg-success" id="strategyStatus">
                                <i class="fas fa-check-circle"></i>
                                策略已选择
                            </span>
                            <div class="mt-2">
                                <small class="text-muted">
                                    货币对: <span id="selectedSymbol">-</span><br>
                                    初始资金: <span id="selectedCapital">-</span>
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 回测配置 -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-cog"></i>
                    回测配置
                </h5>
            </div>
            <div class="card-body">
                <form id="backtestForm">


                    <div class="mb-3">
                        <label class="form-label">货币对</label>
                        <select class="form-select" id="symbolSelect" required>
                            <option value="">选择货币对</option>
                            <option value="EURUSD=X">EUR/USD (欧元/美元)</option>
                            <option value="GBPUSD=X">GBP/USD (英镑/美元)</option>
                            <option value="USDJPY=X">USD/JPY (美元/日元)</option>
                            <option value="AUDUSD=X">AUD/USD (澳元/美元)</option>
                            <option value="USDCAD=X">USD/CAD (美元/加元)</option>
                            <option value="USDCHF=X">USD/CHF (美元/瑞郎)</option>
                            <option value="NZDUSD=X">NZD/USD (纽元/美元)</option>
                            <option value="EURJPY=X">EUR/JPY (欧元/日元)</option>
                            <option value="GBPJPY=X">GBP/JPY (英镑/日元)</option>
                            <option value="XAUUSD=X">XAU/USD (黄金/美元)</option>
                            <option value="XAGUSD=X">XAG/USD (白银/美元)</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">时间间隔</label>
                        <select class="form-select" id="timeframeSelect" required>
                            <option value="">选择时间间隔</option>
                            <option value="1m">1分钟</option>
                            <option value="5m">5分钟</option>
                            <option value="15m" selected>15分钟 (推荐)</option>
                            <option value="30m">30分钟</option>
                            <option value="1h">1小时</option>
                        </select>
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            时间间隔影响策略的交易频率和精度，建议与AI策略训练时使用的时间框架保持一致
                        </small>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">回测时间范围</label>

                        <!-- 快捷日期选择 -->
                        <div class="mb-2">
                            <div class="btn-group w-100" role="group">
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="setQuickDateRange('1week')">
                                    最近一周
                                </button>
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="setQuickDateRange('2weeks')">
                                    最近两周
                                </button>
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="setQuickDateRange('1month')">
                                    最近一个月
                                </button>
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="setQuickDateRange('3months')">
                                    最近三个月
                                </button>
                            </div>
                            <small class="text-muted">点击快捷选项自动设置日期范围</small>
                        </div>

                        <!-- 自定义日期选择 -->
                        <div class="row">
                            <div class="col-6">
                                <label class="form-label small">开始日期</label>
                                <input type="date" class="form-control" id="startDate" required>
                            </div>
                            <div class="col-6">
                                <label class="form-label small">结束日期</label>
                                <input type="date" class="form-control" id="endDate" required>
                            </div>
                        </div>

                        <!-- 日期范围信息显示 -->
                        <div id="dateRangeInfo" class="mt-2" style="display: none;">
                            <div class="alert alert-info py-2">
                                <small>
                                    <i class="fas fa-info-circle"></i>
                                    <strong>回测周期：</strong><span id="backtestDuration">-</span> |
                                    <strong>交易日：</strong><span id="tradingDays">-</span>
                                </small>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">初始资金</label>
                        <input type="number" class="form-control" id="initialCapital" value="10000" step="100" required>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">策略类型</label>
                        <select class="form-select" id="strategyTypeSelect" required onchange="onStrategyTypeChange()">
                            <option value="">选择策略类型</option>
                            <option value="ai_strategy">AI训练策略</option>
                            <option value="technical">技术指标策略</option>
                        </select>
                    </div>

                    <!-- AI策略选择 -->
                    <div class="mb-3" id="aiStrategySection" style="display: none;">
                        <label class="form-label">选择AI策略</label>
                        <select class="form-select" id="aiStrategySelect">
                            <option value="">选择已训练的AI策略</option>
                        </select>
                        <small class="text-muted">只显示已完成训练且激活的AI策略</small>
                    </div>

                    <!-- 技术策略选择 -->
                    <div class="mb-3" id="technicalStrategySection" style="display: none;">
                        <label class="form-label">技术策略类型</label>
                        <select class="form-select" id="technicalStrategySelect">
                            <option value="">选择技术策略</option>
                            <option value="sma_crossover">SMA交叉策略</option>
                            <option value="rsi_oversold">RSI超卖策略</option>
                            <option value="macd_signal">MACD信号策略</option>
                            <option value="bollinger_bands">布林带策略</option>
                            <option value="momentum">动量策略</option>
                        </select>
                    </div>

                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-play"></i>
                        开始回测
                    </button>
                </form>
            </div>
        </div>


    </div>

    <!-- 回测结果 -->
    <div class="col-lg-8">
        <div class="card" id="backtestResults" style="display: none;">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar"></i>
                    回测结果
                </h5>
            </div>
            <div class="card-body">
                <div id="loadingSpinner" class="text-center" style="display: none;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">回测中...</span>
                    </div>
                    <p class="mt-2">正在进行回测，请稍候...</p>
                </div>

                <div id="resultsContent">
                    <!-- 关键指标 -->
                    <div class="row mb-4" id="keyMetrics" style="display: none;">
                        <div class="col-md-3">
                            <div class="text-center">
                                <h6 class="text-muted">总收益率</h6>
                                <h4 id="totalReturn" class="profit-positive">-</h4>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h6 class="text-muted">年化收益率</h6>
                                <h4 id="annualReturn">-</h4>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h6 class="text-muted">最大回撤</h6>
                                <h4 id="maxDrawdown" class="profit-negative">-</h4>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h6 class="text-muted">夏普比率</h6>
                                <h4 id="sharpeRatio">-</h4>
                            </div>
                        </div>
                    </div>

                    <!-- 交易统计 -->
                    <div class="row mb-4" id="tradeStats" style="display: none;">
                        <div class="col-md-3">
                            <div class="text-center">
                                <h6 class="text-muted">总交易次数</h6>
                                <h5 id="totalTrades">-</h5>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h6 class="text-muted">胜率</h6>
                                <h5 id="winRate">-</h5>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h6 class="text-muted">最终资金</h6>
                                <h5 id="finalValue">-</h5>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h6 class="text-muted">回测时间</h6>
                                <h6 id="backtestTime">-</h6>
                            </div>
                        </div>
                    </div>

                    <!-- 收益曲线图 -->
                    <div class="mb-4" id="chartSection" style="display: none;">
                        <h6><i class="fas fa-chart-line"></i> 收益曲线</h6>
                        <div id="portfolioChart" style="height: 400px;"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 历史回测记录 -->
        <div class="card mt-4">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-history"></i>
                        历史回测记录
                    </h5>
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="testBacktestDetail()" title="测试回测详情功能">
                        <i class="fas fa-bug"></i>
                        测试详情
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="backtestHistoryTable">
                        <thead>
                            <tr>
                                <th>时间</th>
                                <th>策略</th>
                                <th>货币对</th>
                                <th>期间</th>
                                <th>总收益率</th>
                                <th>最大回撤</th>
                                <th>夏普比率</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="8" class="text-center text-muted">暂无回测记录</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 回测详情模态框 -->
<div class="modal fade" id="backtestDetailModal" tabindex="-1" aria-labelledby="backtestDetailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="backtestDetailModalLabel">
                    <i class="fas fa-chart-bar"></i>
                    回测详情
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" onclick="closeBacktestDetailModal()"></button>
            </div>
            <div class="modal-body">
                <div id="backtestDetailContent">
                    <!-- 回测基本信息 -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">
                                        <i class="fas fa-info-circle"></i>
                                        回测基本信息
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-sm">
                                        <tr><td><strong>回测ID:</strong></td><td id="detailBacktestId">-</td></tr>
                                        <tr><td><strong>策略名称:</strong></td><td id="detailStrategyName">-</td></tr>
                                        <tr><td><strong>交易品种:</strong></td><td id="detailSymbol">-</td></tr>
                                        <tr><td><strong>回测期间:</strong></td><td id="detailPeriod">-</td></tr>
                                        <tr><td><strong>初始资金:</strong></td><td id="detailInitialCapital">-</td></tr>
                                        <tr><td><strong>回测时间:</strong></td><td id="detailBacktestTime">-</td></tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">
                                        <i class="fas fa-chart-line"></i>
                                        关键性能指标
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-sm">
                                        <tr><td><strong>总收益率:</strong></td><td id="detailTotalReturn" class="profit-positive">-</td></tr>
                                        <tr><td><strong>年化收益率:</strong></td><td id="detailAnnualReturn">-</td></tr>
                                        <tr><td><strong>最大回撤:</strong></td><td id="detailMaxDrawdown" class="profit-negative">-</td></tr>
                                        <tr><td><strong>夏普比率:</strong></td><td id="detailSharpeRatio">-</td></tr>
                                        <tr><td><strong>最终资金:</strong></td><td id="detailFinalValue">-</td></tr>
                                        <tr><td><strong>收益风险比:</strong></td><td id="detailRiskReward">-</td></tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 交易统计 -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">
                                        <i class="fas fa-exchange-alt"></i>
                                        交易统计
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <div class="text-center">
                                                <h6 class="text-muted">总交易次数</h6>
                                                <h4 id="detailTotalTrades">-</h4>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="text-center">
                                                <h6 class="text-muted">胜率</h6>
                                                <h4 id="detailWinRate" class="profit-positive">-</h4>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="text-center">
                                                <h6 class="text-muted">盈利交易</h6>
                                                <h4 id="detailWinTrades" class="profit-positive">-</h4>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="text-center">
                                                <h6 class="text-muted">亏损交易</h6>
                                                <h4 id="detailLossTrades" class="profit-negative">-</h4>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 收益曲线图 -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">
                                        <i class="fas fa-chart-area"></i>
                                        收益曲线
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div id="detailPortfolioChart" style="height: 400px;"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 详细交易记录 -->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">
                                        <i class="fas fa-list"></i>
                                        交易记录
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm table-hover" id="detailTradesTable">
                                            <thead>
                                                <tr>
                                                    <th>时间</th>
                                                    <th>类型</th>
                                                    <th>价格</th>
                                                    <th>数量</th>
                                                    <th>盈亏</th>
                                                    <th>累计收益</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td colspan="6" class="text-center text-muted">暂无交易记录</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" onclick="closeBacktestDetailModal()">关闭</button>
                <button type="button" class="btn btn-primary" onclick="exportBacktestReport()">
                    <i class="fas fa-download"></i>
                    导出报告
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>


// 设置快捷日期范围
function setQuickDateRange(period) {
    const endDate = new Date();
    const startDate = new Date();

    // 根据选择的周期设置开始日期
    switch(period) {
        case '1week':
            startDate.setDate(endDate.getDate() - 7);
            break;
        case '2weeks':
            startDate.setDate(endDate.getDate() - 14);
            break;
        case '1month':
            startDate.setMonth(endDate.getMonth() - 1);
            break;
        case '3months':
            startDate.setMonth(endDate.getMonth() - 3);
            break;
        default:
            return;
    }

    // 设置日期输入框的值
    document.getElementById('startDate').value = startDate.toISOString().split('T')[0];
    document.getElementById('endDate').value = endDate.toISOString().split('T')[0];

    // 更新按钮状态
    updateQuickDateButtons(period);

    // 更新日期范围信息
    updateDateRangeInfo();

    // 显示成功提示
    const periodNames = {
        '1week': '最近一周',
        '2weeks': '最近两周',
        '1month': '最近一个月',
        '3months': '最近三个月'
    };

    console.log(`已设置回测时间范围为：${periodNames[period]}`);
}

// 更新快捷日期按钮状态
function updateQuickDateButtons(activePeriod) {
    // 移除所有按钮的激活状态
    document.querySelectorAll('.btn-group .btn').forEach(btn => {
        btn.classList.remove('active');
        btn.classList.add('btn-outline-primary');
        btn.classList.remove('btn-primary');
    });

    // 激活当前选择的按钮
    const buttonMap = {
        '1week': 0,
        '2weeks': 1,
        '1month': 2,
        '3months': 3
    };

    const buttons = document.querySelectorAll('.btn-group .btn');
    if (buttons[buttonMap[activePeriod]]) {
        buttons[buttonMap[activePeriod]].classList.add('active');
        buttons[buttonMap[activePeriod]].classList.remove('btn-outline-primary');
        buttons[buttonMap[activePeriod]].classList.add('btn-primary');
    }
}

// 更新日期范围信息
function updateDateRangeInfo() {
    const startDate = new Date(document.getElementById('startDate').value);
    const endDate = new Date(document.getElementById('endDate').value);

    if (startDate && endDate && startDate < endDate) {
        // 计算天数差
        const timeDiff = endDate.getTime() - startDate.getTime();
        const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24));

        // 估算交易日（排除周末，约70%的日子是交易日）
        const tradingDays = Math.floor(daysDiff * 0.7);

        // 更新显示
        document.getElementById('backtestDuration').textContent = `${daysDiff}天`;
        document.getElementById('tradingDays').textContent = `约${tradingDays}天`;
        document.getElementById('dateRangeInfo').style.display = 'block';
    } else {
        document.getElementById('dateRangeInfo').style.display = 'none';
    }
}

// 设置默认日期
document.addEventListener('DOMContentLoaded', function() {
    // 默认设置为最近一个月
    setQuickDateRange('1month');

    // 加载AI策略列表
    loadAIStrategiesForBacktest();

    // 监听日期变化，更新日期范围信息
    document.getElementById('startDate').addEventListener('change', function() {
        updateDateRangeInfo();
        // 清除快捷按钮的激活状态（因为是手动修改）
        updateQuickDateButtons('');
    });

    document.getElementById('endDate').addEventListener('change', function() {
        updateDateRangeInfo();
        // 清除快捷按钮的激活状态（因为是手动修改）
        updateQuickDateButtons('');
    });

    // 添加表单元素的事件监听器，实时更新策略显示
    document.getElementById('symbolSelect').addEventListener('change', updateCurrentStrategyDisplay);
    document.getElementById('initialCapital').addEventListener('input', updateCurrentStrategyDisplay);
    document.getElementById('aiStrategySelect').addEventListener('change', updateCurrentStrategyDisplay);
    document.getElementById('technicalStrategySelect').addEventListener('change', updateCurrentStrategyDisplay);

    // 检查URL参数，如果有ai_strategy参数，自动选择
    const urlParams = new URLSearchParams(window.location.search);
    const aiStrategyId = urlParams.get('ai_strategy');
    if (aiStrategyId) {
        document.getElementById('strategyTypeSelect').value = 'ai_strategy';
        onStrategyTypeChange();
        setTimeout(() => {
            document.getElementById('aiStrategySelect').value = aiStrategyId;
            updateCurrentStrategyDisplay();
        }, 500);
    }

    // 初始化策略显示
    updateCurrentStrategyDisplay();
});

// 策略类型变化处理
function onStrategyTypeChange() {
    const strategyType = document.getElementById('strategyTypeSelect').value;
    const aiSection = document.getElementById('aiStrategySection');
    const technicalSection = document.getElementById('technicalStrategySection');

    // 隐藏所有策略选择区域
    aiSection.style.display = 'none';
    technicalSection.style.display = 'none';

    // 根据选择显示对应区域
    if (strategyType === 'ai_strategy') {
        aiSection.style.display = 'block';
        // 重新加载AI策略列表
        loadAIStrategiesForBacktest();
    } else if (strategyType === 'technical') {
        technicalSection.style.display = 'block';
    }

    // 更新当前策略显示
    updateCurrentStrategyDisplay();
}

// 加载AI策略列表用于回测
function loadAIStrategiesForBacktest() {
    console.log('开始加载AI策略列表...');

    fetch('/api/ai-strategies/list')
    .then(response => response.json())
    .then(data => {
        console.log('AI策略API响应:', data);

        if (data.success) {
            populateAIStrategySelect(data.strategies);
        } else {
            console.error('加载AI策略失败:', data.error);
            const select = document.getElementById('aiStrategySelect');
            select.innerHTML = '<option value="">暂无可用的AI策略，请先训练AI策略</option>';
        }
    })
    .catch(error => {
        console.error('加载AI策略请求失败:', error);
        const select = document.getElementById('aiStrategySelect');
        select.innerHTML = '<option value="">加载失败，请刷新页面重试</option>';
    });
}

// 填充AI策略选择框
function populateAIStrategySelect(strategies) {
    console.log('填充AI策略选择框，策略数量:', strategies.length);

    const select = document.getElementById('aiStrategySelect');
    select.innerHTML = '<option value="">选择已训练的AI策略</option>';

    if (!strategies || strategies.length === 0) {
        select.innerHTML = '<option value="">暂无AI策略，请先在AI策略训练页面训练策略</option>';
        return;
    }

    let completedStrategies = 0;

    strategies.forEach(strategy => {
        console.log('处理策略:', strategy.name, '状态:', strategy.status);

        // 只显示已完成训练的策略
        if (strategy.status === 'completed') {
            completedStrategies++;

            const option = document.createElement('option');
            option.value = strategy.id;

            // 显示策略名称和性能信息
            const statusText = strategy.is_active ? '✅' : '⏸️';
            let displayText = `${statusText} ${strategy.name}`;

            // 添加性能指标
            if (strategy.performance_metrics) {
                const metrics = strategy.performance_metrics;
                if (metrics.win_rate) {
                    displayText += ` (胜率${(metrics.win_rate * 100).toFixed(1)}%)`;
                }
            }

            option.textContent = displayText;
            select.appendChild(option);
        }
    });

    console.log('已完成的策略数量:', completedStrategies);

    if (completedStrategies === 0) {
        select.innerHTML = '<option value="">暂无已完成的AI策略，请先完成策略训练</option>';
    }

    // 添加提示信息
    const helpOption = document.createElement('option');
    helpOption.disabled = true;
    helpOption.textContent = '─────────────────────';
    select.appendChild(helpOption);

    const infoOption = document.createElement('option');
    infoOption.disabled = true;
    infoOption.textContent = '💡 如需更多策略，请前往AI策略训练页面';
    select.appendChild(infoOption);
}



// 提交回测表单
document.getElementById('backtestForm').addEventListener('submit', function(e) {
    e.preventDefault();

    // 获取策略ID
    let strategyId = null;
    const strategyType = document.getElementById('strategyTypeSelect').value;

    if (strategyType === 'ai_strategy') {
        strategyId = document.getElementById('aiStrategySelect').value;
    } else if (strategyType === 'technical') {
        strategyId = document.getElementById('technicalStrategySelect').value;
    }

    if (!strategyId) {
        alert('请选择一个策略');
        return;
    }

    const backtestData = {
        strategy_id: strategyId,
        strategy_type: strategyType,
        symbol: document.getElementById('symbolSelect').value,
        timeframe: document.getElementById('timeframeSelect').value,
        start_date: document.getElementById('startDate').value,
        end_date: document.getElementById('endDate').value,
        initial_capital: parseFloat(document.getElementById('initialCapital').value)
    };

    console.log('回测数据:', backtestData);

    // 验证必要字段
    if (!backtestData.symbol) {
        alert('请选择货币对');
        return;
    }
    if (!backtestData.timeframe) {
        alert('请选择时间间隔');
        return;
    }
    if (!backtestData.start_date || !backtestData.end_date) {
        alert('请选择开始和结束日期');
        return;
    }

    // 显示回测结果区域和加载动画
    document.getElementById('backtestResults').style.display = 'block';
    document.getElementById('loadingSpinner').style.display = 'block';
    document.getElementById('keyMetrics').style.display = 'none';
    document.getElementById('tradeStats').style.display = 'none';
    document.getElementById('chartSection').style.display = 'none';

    console.log('开始发送回测请求...');

    fetch('/api/backtest', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(backtestData)
    })
    .then(response => {
        console.log('回测响应状态:', response.status);

        if (!response.ok) {
            throw new Error(`HTTP错误! 状态: ${response.status}`);
        }

        return response.json();
    })
    .then(data => {
        console.log('回测响应数据:', data);
        document.getElementById('loadingSpinner').style.display = 'none';

        if (data.success) {
            console.log('回测成功，显示结果');
            displayBacktestResults(data.result);
        } else {
            console.error('回测失败:', data.error);
            alert('回测失败: ' + data.error);
        }
    })
    .catch(error => {
        document.getElementById('loadingSpinner').style.display = 'none';
        console.error('回测请求错误:', error);
        alert('回测请求失败: ' + error.message);
    });
});

// 显示回测结果
function displayBacktestResults(result) {
    const metrics = result.metrics;

    // 显示关键指标
    document.getElementById('totalReturn').textContent = (metrics.total_return * 100).toFixed(2) + '%';
    document.getElementById('totalReturn').className = metrics.total_return >= 0 ? 'profit-positive' : 'profit-negative';

    document.getElementById('annualReturn').textContent = (metrics.annual_return * 100).toFixed(2) + '%';
    document.getElementById('maxDrawdown').textContent = (metrics.max_drawdown * 100).toFixed(2) + '%';
    document.getElementById('sharpeRatio').textContent = metrics.sharpe_ratio.toFixed(2);

    document.getElementById('keyMetrics').style.display = 'block';

    // 显示交易统计
    document.getElementById('totalTrades').textContent = metrics.total_trades;
    document.getElementById('winRate').textContent = (metrics.win_rate * 100).toFixed(1) + '%';
    document.getElementById('finalValue').textContent = '$' + metrics.final_value.toFixed(2);
    document.getElementById('backtestTime').textContent = new Date().toLocaleString();

    document.getElementById('tradeStats').style.display = 'block';

    // 绘制收益曲线
    if (result.portfolio_data && result.portfolio_data.length > 0) {
        drawPortfolioChart(result.portfolio_data);
        document.getElementById('chartSection').style.display = 'block';
    }

    // 添加到历史记录
    addToBacktestHistory(result);
}

// 绘制收益曲线图
function drawPortfolioChart(portfolioData) {
    const dates = portfolioData.map(d => new Date(d.Date || d.index));
    const values = portfolioData.map(d => d.Total);

    const trace = {
        x: dates,
        y: values,
        type: 'scatter',
        mode: 'lines',
        name: '投资组合价值',
        line: {color: '#667eea'}
    };

    const layout = {
        title: '投资组合价值变化',
        xaxis: {title: '日期'},
        yaxis: {title: '价值 ($)'},
        margin: {t: 50, r: 50, b: 50, l: 80}
    };

    Plotly.newPlot('portfolioChart', [trace], layout, {responsive: true});
}

// 回测详情数据存储
let backtestDetailsStorage = new Map();

// 添加到回测历史
function addToBacktestHistory(result) {
    console.log('添加回测历史记录:', result);
    const tbody = document.querySelector('#backtestHistoryTable tbody');

    // 如果是第一条记录，清除占位符
    if (tbody.children.length === 1 && tbody.children[0].children[0] && tbody.children[0].children[0].getAttribute('colspan')) {
        console.log('清除回测历史占位符');
        tbody.innerHTML = '';
    }

    // 保存完整的回测数据到存储中
    const backtestData = {
        result: result,
        strategyName: getSelectedStrategyName(),
        symbol: document.getElementById('symbolSelect').value,
        startDate: document.getElementById('startDate').value,
        endDate: document.getElementById('endDate').value,
        initialCapital: parseFloat(document.getElementById('initialCapital').value),
        backtestTime: new Date().toISOString()
    };

    backtestDetailsStorage.set(result.backtest_id, backtestData);
    console.log('保存回测详情数据:', result.backtest_id, backtestData);

    // 获取策略名称（使用辅助函数）
    const strategyName = getSelectedStrategyName();
    const symbol = document.getElementById('symbolSelect').value;
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;

    const row = document.createElement('tr');
    row.innerHTML = `
        <td>${new Date().toLocaleString()}</td>
        <td>${strategyName}</td>
        <td>${symbol}</td>
        <td>${startDate} 至 ${endDate}</td>
        <td class="${result.metrics.total_return >= 0 ? 'profit-positive' : 'profit-negative'}">
            ${(result.metrics.total_return * 100).toFixed(2)}%
        </td>
        <td class="profit-negative">${(result.metrics.max_drawdown * 100).toFixed(2)}%</td>
        <td>${result.metrics.sharpe_ratio.toFixed(2)}</td>
        <td>
            <button class="btn btn-sm btn-outline-info" onclick="viewBacktestDetail('${result.backtest_id}')" title="查看详细回测报告">
                <i class="fas fa-eye"></i>
                详情
            </button>
        </td>
    `;

    // 插入到表格顶部
    tbody.insertBefore(row, tbody.firstChild);

    // 限制历史记录数量
    if (tbody.children.length > 10) {
        tbody.removeChild(tbody.lastChild);
    }
}

// 获取选中的策略名称
function getSelectedStrategyName() {
    let strategyName = 'AI策略';
    const strategyType = document.getElementById('strategyTypeSelect').value;

    if (strategyType === 'ai_strategy') {
        const aiSelect = document.getElementById('aiStrategySelect');
        const selectedOption = aiSelect.options[aiSelect.selectedIndex];
        if (selectedOption && selectedOption.value) {
            strategyName = selectedOption.textContent.replace(/^[✅⏸️]\s*/, '').split('(')[0].trim();
        }
    } else if (strategyType === 'technical') {
        const techSelect = document.getElementById('technicalStrategySelect');
        const selectedOption = techSelect.options[techSelect.selectedIndex];
        if (selectedOption && selectedOption.value) {
            strategyName = selectedOption.textContent;
        }
    }

    return strategyName;
}

// 查看回测详情
function viewBacktestDetail(backtestId) {
    console.log('=== 查看回测详情 ===');
    console.log('回测ID:', backtestId);
    console.log('当前存储的回测数据:', backtestDetailsStorage);
    console.log('存储中的键:', Array.from(backtestDetailsStorage.keys()));

    // 从存储中获取回测数据
    const backtestData = backtestDetailsStorage.get(backtestId);
    console.log('获取到的回测数据:', backtestData);

    if (!backtestData) {
        console.error('回测详情数据不存在，backtestId:', backtestId);
        console.error('可用的回测ID:', Array.from(backtestDetailsStorage.keys()));
        alert('回测详情数据不存在，可能是页面刷新后丢失\n\n调试信息:\n- 请求的ID: ' + backtestId + '\n- 可用的ID: ' + Array.from(backtestDetailsStorage.keys()).join(', '));
        return;
    }

    console.log('准备显示回测详情模态框...');
    // 显示回测详情模态框
    showBacktestDetailModal(backtestData);
}

// 显示回测详情模态框
function showBacktestDetailModal(backtestData) {
    console.log('=== 显示回测详情模态框 ===');
    console.log('回测数据:', backtestData);

    const { result, strategyName, symbol, startDate, endDate, initialCapital, backtestTime } = backtestData;
    const metrics = result.metrics;

    console.log('解析的数据:', { result, strategyName, symbol, startDate, endDate, initialCapital, backtestTime });
    console.log('性能指标:', metrics);

    // 填充基本信息
    document.getElementById('detailBacktestId').textContent = result.backtest_id;
    document.getElementById('detailStrategyName').textContent = strategyName;
    document.getElementById('detailSymbol').textContent = symbol;
    document.getElementById('detailPeriod').textContent = `${startDate} 至 ${endDate}`;
    document.getElementById('detailInitialCapital').textContent = `$${initialCapital.toFixed(2)}`;
    document.getElementById('detailBacktestTime').textContent = new Date(backtestTime).toLocaleString('zh-CN', {timeZone: 'Asia/Shanghai'});

    // 填充性能指标
    document.getElementById('detailTotalReturn').textContent = (metrics.total_return * 100).toFixed(2) + '%';
    document.getElementById('detailTotalReturn').className = metrics.total_return >= 0 ? 'profit-positive' : 'profit-negative';

    document.getElementById('detailAnnualReturn').textContent = (metrics.annual_return * 100).toFixed(2) + '%';
    document.getElementById('detailMaxDrawdown').textContent = (metrics.max_drawdown * 100).toFixed(2) + '%';
    document.getElementById('detailSharpeRatio').textContent = metrics.sharpe_ratio.toFixed(2);
    document.getElementById('detailFinalValue').textContent = `$${metrics.final_value.toFixed(2)}`;

    // 计算收益风险比
    const riskRewardRatio = Math.abs(metrics.total_return / metrics.max_drawdown);
    document.getElementById('detailRiskReward').textContent = riskRewardRatio.toFixed(2);

    // 填充交易统计
    document.getElementById('detailTotalTrades').textContent = metrics.total_trades;
    document.getElementById('detailWinRate').textContent = (metrics.win_rate * 100).toFixed(1) + '%';

    // 计算盈利和亏损交易数量
    const winTrades = Math.round(metrics.total_trades * metrics.win_rate);
    const lossTrades = metrics.total_trades - winTrades;
    document.getElementById('detailWinTrades').textContent = winTrades;
    document.getElementById('detailLossTrades').textContent = lossTrades;

    // 绘制详情页面的收益曲线
    drawDetailPortfolioChart(result);

    // 生成模拟交易记录
    generateMockTradeRecords(result);

    // 显示模态框
    try {
        const modalElement = document.getElementById('backtestDetailModal');
        if (modalElement) {
            // 尝试使用Bootstrap 5的方式
            if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
                const modal = new bootstrap.Modal(modalElement);
                modal.show();
            } else {
                // 如果Bootstrap不可用，使用jQuery方式（Bootstrap 4兼容）
                if (typeof $ !== 'undefined') {
                    $(modalElement).modal('show');
                } else {
                    // 最后的备选方案：直接显示
                    modalElement.style.display = 'block';
                    modalElement.classList.add('show');
                    document.body.classList.add('modal-open');

                    // 添加背景遮罩
                    const backdrop = document.createElement('div');
                    backdrop.className = 'modal-backdrop fade show';
                    backdrop.id = 'backtestDetailBackdrop';
                    document.body.appendChild(backdrop);
                }
            }
        } else {
            console.error('回测详情模态框元素未找到');
            alert('无法显示回测详情，请刷新页面重试');
        }
    } catch (error) {
        console.error('显示模态框时出错:', error);
        alert('显示回测详情时出错: ' + error.message);
    }
}

// 绘制详情页面的收益曲线
function drawDetailPortfolioChart(result) {
    // 生成模拟的收益曲线数据
    const portfolioData = generateMockPortfolioData(result);

    const ctx = document.getElementById('detailPortfolioChart');

    // 如果已存在图表，先销毁
    if (window.detailPortfolioChart) {
        window.detailPortfolioChart.destroy();
    }

    window.detailPortfolioChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: portfolioData.map(d => d.date),
            datasets: [{
                label: '账户价值',
                data: portfolioData.map(d => d.value),
                borderColor: '#0d6efd',
                backgroundColor: 'rgba(13, 110, 253, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    callbacks: {
                        label: function(context) {
                            return `账户价值: $${context.parsed.y.toFixed(2)}`;
                        }
                    }
                }
            },
            scales: {
                x: {
                    display: true,
                    title: {
                        display: true,
                        text: '日期'
                    }
                },
                y: {
                    display: true,
                    title: {
                        display: true,
                        text: '账户价值 ($)'
                    },
                    beginAtZero: false
                }
            },
            interaction: {
                mode: 'nearest',
                axis: 'x',
                intersect: false
            }
        }
    });
}

// 生成模拟的投资组合数据
function generateMockPortfolioData(result) {
    const startDate = new Date(document.getElementById('startDate').value);
    const endDate = new Date(document.getElementById('endDate').value);
    const initialCapital = parseFloat(document.getElementById('initialCapital').value);
    const finalValue = result.metrics.final_value;

    const data = [];
    const daysDiff = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));
    const dailyReturn = Math.pow(finalValue / initialCapital, 1 / daysDiff) - 1;

    let currentValue = initialCapital;

    for (let i = 0; i <= daysDiff; i++) {
        const currentDate = new Date(startDate);
        currentDate.setDate(startDate.getDate() + i);

        // 添加一些随机波动使曲线更真实
        const randomFactor = 1 + (Math.random() - 0.5) * 0.02; // ±1%的随机波动
        currentValue *= (1 + dailyReturn) * randomFactor;

        data.push({
            date: currentDate.toLocaleDateString('zh-CN'),
            value: currentValue
        });
    }

    // 确保最后一个值等于最终价值
    data[data.length - 1].value = finalValue;

    return data;
}

// 生成模拟交易记录
function generateMockTradeRecords(result) {
    const tbody = document.querySelector('#detailTradesTable tbody');
    tbody.innerHTML = '';

    const totalTrades = result.metrics.total_trades;
    const winRate = result.metrics.win_rate;
    const initialCapital = parseFloat(document.getElementById('initialCapital').value);

    let cumulativeReturn = 0;
    let currentBalance = initialCapital;

    for (let i = 0; i < totalTrades; i++) {
        const isWin = Math.random() < winRate;
        const tradeReturn = isWin ?
            (Math.random() * 0.05 + 0.01) : // 盈利交易：1-6%
            -(Math.random() * 0.03 + 0.005); // 亏损交易：-0.5-3.5%

        const tradeValue = currentBalance * 0.1; // 假设每次交易10%的资金
        const profit = tradeValue * tradeReturn;
        currentBalance += profit;
        cumulativeReturn += tradeReturn;

        // 生成随机交易时间
        const startDate = new Date(document.getElementById('startDate').value);
        const endDate = new Date(document.getElementById('endDate').value);
        const randomTime = new Date(startDate.getTime() + Math.random() * (endDate.getTime() - startDate.getTime()));

        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${randomTime.toLocaleDateString('zh-CN')}</td>
            <td>${isWin ? '买入' : '卖出'}</td>
            <td>$${(1000 + Math.random() * 100).toFixed(2)}</td>
            <td>${(tradeValue / 1000).toFixed(2)}</td>
            <td class="${profit >= 0 ? 'profit-positive' : 'profit-negative'}">
                ${profit >= 0 ? '+' : ''}$${profit.toFixed(2)}
            </td>
            <td class="${cumulativeReturn >= 0 ? 'profit-positive' : 'profit-negative'}">
                ${(cumulativeReturn * 100).toFixed(2)}%
            </td>
        `;

        tbody.appendChild(row);
    }
}

// 关闭回测详情模态框（备选方案）
function closeBacktestDetailModal() {
    const modalElement = document.getElementById('backtestDetailModal');
    const backdrop = document.getElementById('backtestDetailBackdrop');

    if (modalElement) {
        modalElement.style.display = 'none';
        modalElement.classList.remove('show');
        document.body.classList.remove('modal-open');
    }

    if (backdrop) {
        document.body.removeChild(backdrop);
    }
}

// 测试函数 - 验证按钮点击
function testBacktestDetail() {
    console.log('测试按钮点击功能');
    console.log('当前存储的回测数据:', backtestDetailsStorage);

    if (backtestDetailsStorage.size === 0) {
        alert('没有可用的回测数据，请先执行一次回测');
        return;
    }

    // 获取第一个回测数据进行测试
    const firstKey = backtestDetailsStorage.keys().next().value;
    console.log('使用第一个回测数据进行测试:', firstKey);
    viewBacktestDetail(firstKey);
}

// 导出回测报告
function exportBacktestReport() {
    alert('导出回测报告功能待实现');
}

// 更新当前策略显示
function updateCurrentStrategyDisplay() {
    const strategyCard = document.getElementById('currentStrategyCard');
    const strategyIcon = document.getElementById('strategyIcon');
    const strategyName = document.getElementById('strategyName');
    const strategyDescription = document.getElementById('strategyDescription');
    const strategyDetails = document.getElementById('strategyDetails');
    const strategyStatus = document.getElementById('strategyStatus');
    const selectedSymbol = document.getElementById('selectedSymbol');
    const selectedCapital = document.getElementById('selectedCapital');

    // 获取当前选择的值
    const strategyType = document.getElementById('strategyTypeSelect').value;
    const aiStrategy = document.getElementById('aiStrategySelect').value;
    const technicalStrategy = document.getElementById('technicalStrategySelect').value;
    const symbol = document.getElementById('symbolSelect').value;
    const capital = document.getElementById('initialCapital').value;

    // 更新基本信息
    selectedSymbol.textContent = symbol ? getSymbolDisplayName(symbol) : '-';
    selectedCapital.textContent = capital ? `$${parseInt(capital).toLocaleString()}` : '-';

    if (!strategyType) {
        // 未选择策略类型
        strategyCard.style.display = 'none';
        return;
    }

    // 显示策略卡片
    strategyCard.style.display = 'block';

    if (strategyType === 'ai_strategy') {
        // AI策略
        strategyIcon.className = 'fas fa-brain fa-2x text-primary';

        if (aiStrategy) {
            // 已选择具体AI策略
            const strategyOption = document.querySelector(`#aiStrategySelect option[value="${aiStrategy}"]`);
            const strategyText = strategyOption ? strategyOption.textContent : aiStrategy;

            strategyName.textContent = `AI策略: ${strategyText}`;
            strategyDescription.textContent = '基于机器学习算法训练的智能交易策略，能够自动识别市场模式并执行交易决策';
            strategyDetails.textContent = `策略ID: ${aiStrategy} | 类型: 人工智能 | 状态: 已激活`;

            strategyStatus.className = 'badge bg-success';
            strategyStatus.innerHTML = '<i class="fas fa-check-circle"></i> AI策略已选择';
        } else {
            // 只选择了AI策略类型，未选择具体策略
            strategyName.textContent = 'AI训练策略';
            strategyDescription.textContent = '请在下方选择具体的AI策略进行回测';
            strategyDetails.textContent = '类型: 人工智能 | 状态: 待选择具体策略';

            strategyStatus.className = 'badge bg-warning';
            strategyStatus.innerHTML = '<i class="fas fa-exclamation-triangle"></i> 请选择具体AI策略';
        }
    } else if (strategyType === 'technical') {
        // 技术指标策略
        strategyIcon.className = 'fas fa-chart-line fa-2x text-warning';

        if (technicalStrategy) {
            // 已选择具体技术策略
            const strategyNames = {
                'sma_crossover': 'SMA交叉策略',
                'rsi_oversold': 'RSI超卖策略',
                'bollinger_bands': '布林带策略',
                'macd_signal': 'MACD信号策略',
                'stochastic': '随机指标策略'
            };

            const strategyDescriptions = {
                'sma_crossover': '基于简单移动平均线交叉信号的经典技术分析策略',
                'rsi_oversold': '利用相对强弱指标识别超买超卖区域的反转策略',
                'bollinger_bands': '基于布林带通道突破和回归的波动性策略',
                'macd_signal': '使用MACD指标的金叉死叉信号进行趋势跟踪',
                'stochastic': '基于随机指标的超买超卖信号策略'
            };

            strategyName.textContent = strategyNames[technicalStrategy] || '技术指标策略';
            strategyDescription.textContent = strategyDescriptions[technicalStrategy] || '基于技术指标的量化交易策略';
            strategyDetails.textContent = `策略代码: ${technicalStrategy} | 类型: 技术分析 | 状态: 已配置`;

            strategyStatus.className = 'badge bg-success';
            strategyStatus.innerHTML = '<i class="fas fa-check-circle"></i> 技术策略已选择';
        } else {
            // 只选择了技术策略类型，未选择具体策略
            strategyName.textContent = '技术指标策略';
            strategyDescription.textContent = '请在下方选择具体的技术指标策略进行回测';
            strategyDetails.textContent = '类型: 技术分析 | 状态: 待选择具体策略';

            strategyStatus.className = 'badge bg-warning';
            strategyStatus.innerHTML = '<i class="fas fa-exclamation-triangle"></i> 请选择具体技术策略';
        }
    }
}

// 获取货币对显示名称
function getSymbolDisplayName(symbol) {
    const symbolNames = {
        'EURUSD=X': 'EUR/USD (欧元/美元)',
        'GBPUSD=X': 'GBP/USD (英镑/美元)',
        'USDJPY=X': 'USD/JPY (美元/日元)',
        'AUDUSD=X': 'AUD/USD (澳元/美元)',
        'USDCAD=X': 'USD/CAD (美元/加元)',
        'USDCHF=X': 'USD/CHF (美元/瑞郎)',
        'NZDUSD=X': 'NZD/USD (纽元/美元)',
        'EURJPY=X': 'EUR/JPY (欧元/日元)',
        'GBPJPY=X': 'GBP/JPY (英镑/日元)',
        'XAUUSD=X': 'XAU/USD (黄金/美元)',
        'XAGUSD=X': 'XAG/USD (白银/美元)'
    };
    return symbolNames[symbol] || symbol;
}
</script>
{% endblock %}
