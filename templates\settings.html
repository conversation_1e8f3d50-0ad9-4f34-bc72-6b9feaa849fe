{% extends "base.html" %}

{% block page_title %}系统设置{% endblock %}

{% block content %}
<div class="row">
    <!-- AI模型配置 -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-robot"></i>
                    AI模型配置
                </h5>
            </div>
            <div class="card-body">
                <div class="row" id="aiModelConfigs">
                    {% for config in ai_configs %}
                    <div class="col-md-6 mb-4">
                        <div class="card border-primary">
                            <div class="card-header bg-primary text-white">
                                <h6 class="card-title mb-0">{{ config.name }}</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-2">
                                    <strong>模型:</strong> {{ config.model_name }}
                                </div>
                                <div class="mb-2">
                                    <strong>API地址:</strong>
                                    <small class="text-muted">{{ config.base_url }}</small>
                                </div>
                                <div class="mb-2">
                                    <strong>API Key:</strong>
                                    <small class="text-muted">{{ config.api_key[:10] }}...</small>
                                </div>
                                <div class="mb-3">
                                    <strong>状态:</strong>
                                    <span class="badge bg-{{ 'success' if config.is_active else 'secondary' }}">
                                        {{ '活跃' if config.is_active else '禁用' }}
                                    </span>
                                </div>
                                <div class="btn-group w-100">
                                    <button class="btn btn-sm btn-outline-primary"
                                            onclick="testModel({{ config.id }})">
                                        <i class="fas fa-vial"></i>
                                        测试连接
                                    </button>
                                    <button class="btn btn-sm btn-outline-secondary"
                                            onclick="toggleModel({{ config.id }}, {{ config.is_active|lower }})">
                                        <i class="fas fa-power-off"></i>
                                        {{ '禁用' if config.is_active else '启用' }}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>

                <div class="text-center mt-4">
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addModelModal">
                        <i class="fas fa-plus"></i>
                        添加新模型
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 系统参数设置 -->
    <div class="col-lg-4">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-cog"></i>
                    系统参数
                </h5>
            </div>
            <div class="card-body">
                <form id="systemSettingsForm">
                    <div class="mb-3">
                        <label class="form-label">默认风险等级</label>
                        <select class="form-select" name="default_risk_level">
                            <option value="low">低风险</option>
                            <option value="medium" selected>中等风险</option>
                            <option value="high">高风险</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">最大持仓数量</label>
                        <input type="number" class="form-control" name="max_positions" value="10" min="1" max="50">
                    </div>

                    <div class="mb-3">
                        <label class="form-label">默认止损比例 (%)</label>
                        <input type="number" class="form-control" name="default_stop_loss" value="2" min="0.1" max="10" step="0.1">
                    </div>

                    <div class="mb-3">
                        <label class="form-label">默认止盈比例 (%)</label>
                        <input type="number" class="form-control" name="default_take_profit" value="5" min="0.1" max="20" step="0.1">
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="auto_trading" id="autoTrading">
                            <label class="form-check-label" for="autoTrading">
                                启用自动交易
                            </label>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="email_notifications" id="emailNotifications" checked>
                            <label class="form-check-label" for="emailNotifications">
                                邮件通知
                            </label>
                        </div>
                    </div>

                    <!-- 单边行情检测配置 -->
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="trend_detection" id="trendDetection">
                            <label class="form-check-label" for="trendDetection">
                                <i class="fas fa-chart-line"></i>
                                启用单边行情检测
                            </label>
                        </div>
                    </div>

                    <!-- 单边行情检测详细配置 -->
                    <div id="trendDetectionConfig" style="display: none;">
                        <div class="card border-info mb-3">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">
                                    <i class="fas fa-cogs"></i>
                                    单边行情检测参数
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="fas fa-tachometer-alt"></i>
                                        趋势强度阈值 (%)
                                        <small class="text-muted">- 超过此值认为是强趋势</small>
                                    </label>
                                    <input type="number" class="form-control" name="trend_strength_threshold"
                                           value="60" min="30" max="90" step="5">
                                    <div class="form-text">建议值: 60% (保守: 70%, 激进: 50%)</div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="fas fa-expand-arrows-alt"></i>
                                        波动突破倍数
                                        <small class="text-muted">- 相对平均波动的倍数</small>
                                    </label>
                                    <input type="number" class="form-control" name="volatility_breakout_multiplier"
                                           value="1.5" min="1.2" max="3.0" step="0.1">
                                    <div class="form-text">建议值: 1.5倍 (保守: 1.8倍, 激进: 1.3倍)</div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="fas fa-clock"></i>
                                        趋势确认时间 (分钟)
                                        <small class="text-muted">- 趋势持续多久才确认</small>
                                    </label>
                                    <select class="form-select" name="trend_confirmation_time">
                                        <option value="15">15分钟</option>
                                        <option value="30" selected>30分钟</option>
                                        <option value="45">45分钟</option>
                                        <option value="60">60分钟</option>
                                    </select>
                                    <div class="form-text">建议值: 30分钟 (快速: 15分钟, 稳健: 60分钟)</div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="fas fa-layer-group"></i>
                                        多时间框架确认
                                    </label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="multi_timeframe_confirmation"
                                               id="multiTimeframeConfirmation" checked>
                                        <label class="form-check-label" for="multiTimeframeConfirmation">
                                            要求1小时和15分钟趋势一致
                                        </label>
                                    </div>
                                    <div class="form-text">推荐开启，提高信号准确性</div>
                                </div>

                                <div class="alert alert-info">
                                    <i class="fas fa-lightbulb"></i>
                                    <strong>参数说明：</strong>
                                    <ul class="mb-0 mt-2">
                                        <li><strong>趋势强度</strong>: 基于EMA计算的趋势力度</li>
                                        <li><strong>波动突破</strong>: 当前波动超过平均波动的倍数</li>
                                        <li><strong>确认时间</strong>: 避免假突破的时间过滤</li>
                                        <li><strong>多时间框架</strong>: 提高信号可靠性</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-save"></i>
                        保存设置
                    </button>
                </form>
            </div>
        </div>

        <!-- 左侧栏模块控制 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bars"></i>
                    左侧栏模块显示控制
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>功能说明：</strong>可以控制左侧导航栏中哪些模块显示或隐藏，根据用户需求定制界面。
                </div>

                <form id="sidebarModulesForm">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-chart-bar"></i>
                                交易模块
                            </h6>
                            <div class="mb-2">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="module_demo_trading" checked>
                                    <label class="form-check-label" for="module_demo_trading">
                                        模拟交易
                                    </label>
                                </div>
                            </div>
                            <div class="mb-2">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="module_real_trading" checked>
                                    <label class="form-check-label" for="module_real_trading">
                                        真实交易
                                    </label>
                                </div>
                            </div>
                            <div class="mb-2">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="module_agent_trading" checked>
                                    <label class="form-check-label" for="module_agent_trading">
                                        智能交易
                                    </label>
                                </div>
                            </div>
                            <div class="mb-2">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="module_agent_trading_real" checked>
                                    <label class="form-check-label" for="module_agent_trading_real">
                                        智能交易(真实)
                                    </label>
                                </div>
                            </div>
                            <div class="mb-2">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="module_sn_position_adding" checked>
                                    <label class="form-check-label" for="module_sn_position_adding">
                                        SN加仓
                                    </label>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="module_trading_query" checked>
                                    <label class="form-check-label" for="module_trading_query">
                                        交易查询
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <h6 class="text-success mb-3">
                                <i class="fas fa-brain"></i>
                                分析模块
                            </h6>
                            <div class="mb-2">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="module_pattern_monitoring" checked>
                                    <label class="form-check-label" for="module_pattern_monitoring">
                                        模式监控
                                    </label>
                                </div>
                            </div>
                            <div class="mb-2">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="module_ai_training" checked>
                                    <label class="form-check-label" for="module_ai_training">
                                        AI训练
                                    </label>
                                </div>
                            </div>
                            <div class="mb-2">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="module_risk_events" checked>
                                    <label class="form-check-label" for="module_risk_events">
                                        风险事件
                                    </label>
                                </div>
                            </div>
                            <div class="mb-2">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="module_charts" checked>
                                    <label class="form-check-label" for="module_charts">
                                        图表分析
                                    </label>
                                </div>
                            </div>
                            <div class="mb-2">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="module_backtest" checked>
                                    <label class="form-check-label" for="module_backtest">
                                        回测分析
                                    </label>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="module_user_management" checked>
                                    <label class="form-check-label" for="module_user_management">
                                        用户管理
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <button type="button" class="btn btn-outline-secondary" onclick="resetSidebarModules()">
                            <i class="fas fa-undo"></i>
                            重置为默认
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            保存模块设置
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- MT5交易设置 -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line"></i>
                    MT5交易设置
                </h5>
            </div>
            <div class="card-body">
                <form id="mt5SettingsForm">
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="mt5DemoAsReal" checked>
                            <label class="form-check-label" for="mt5DemoAsReal">
                                <strong>MT5 Demo账户作为真实账户处理</strong>
                            </label>
                            <div class="form-text text-muted">
                                开启后，MT5的Demo账户可以在真实交易页面使用。关闭后，Demo账户只能在模拟交易页面使用。
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="enableMT5AutoConnect" checked>
                            <label class="form-check-label" for="enableMT5AutoConnect">
                                <strong>启用MT5自动连接</strong>
                            </label>
                            <div class="form-text text-muted">
                                页面加载时自动尝试连接MT5客户端
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="enableMT5OrderSync" checked>
                            <label class="form-check-label" for="enableMT5OrderSync">
                                <strong>启用MT5订单同步</strong>
                            </label>
                            <div class="form-text text-muted">
                                同步MT5客户端的订单到Web界面
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>说明：</strong>
                        <ul class="mb-0 mt-2">
                            <li>推荐开启"Demo账户作为真实账户处理"，这样可以使用Demo账户进行真实交易测试</li>
                            <li>如果您的MT5服务器名称包含"Demo"但实际是真实账户，请保持此选项开启</li>
                            <li>设置修改后会立即生效，无需重启系统</li>
                        </ul>
                    </div>

                    <button type="submit" class="btn btn-success w-100">
                        <i class="fas fa-save"></i>
                        保存MT5设置
                    </button>
                </form>
            </div>
        </div>

        <!-- 系统信息 -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle"></i>
                    系统信息
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-2">
                    <strong>系统版本:</strong> MateTrade4 v1.0.0
                </div>
                <div class="mb-2">
                    <strong>Python版本:</strong> 3.9+
                </div>
                <div class="mb-2">
                    <strong>Flask版本:</strong> 2.3.3
                </div>
                <div class="mb-2">
                    <strong>数据库:</strong> SQLite
                </div>
                <div class="mb-2">
                    <strong>启动时间:</strong> <span id="startupTime">-</span>
                </div>
                <div class="mb-3">
                    <strong>运行状态:</strong>
                    <span class="badge bg-success">正常运行</span>
                </div>

                <div class="d-grid gap-2">
                    <button class="btn btn-outline-info btn-sm" onclick="checkSystemHealth()">
                        <i class="fas fa-heartbeat"></i>
                        系统健康检查
                    </button>
                    <button class="btn btn-outline-warning btn-sm" onclick="clearCache()">
                        <i class="fas fa-broom"></i>
                        清理缓存
                    </button>
                    <button class="btn btn-outline-danger btn-sm" onclick="exportData()">
                        <i class="fas fa-download"></i>
                        导出数据
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 添加AI模型模态框 -->
<div class="modal fade" id="addModelModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">添加AI模型</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addModelForm">
                    <div class="mb-3">
                        <label class="form-label">模型名称</label>
                        <input type="text" class="form-control" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">模型标识</label>
                        <input type="text" class="form-control" name="model_name" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">API Key</label>
                        <input type="text" class="form-control" name="api_key" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">API地址</label>
                        <input type="url" class="form-control" name="base_url" placeholder="https://api.example.com/v1">
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="is_active" id="newModelActive" checked>
                            <label class="form-check-label" for="newModelActive">
                                启用此模型
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="addModel()">添加</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 显示启动时间
    const startupTime = new Date().toLocaleString('zh-CN');
    document.getElementById('startupTime').textContent = startupTime;

    // 加载MT5设置
    loadMT5Settings();
});

// 测试AI模型连接
function testModel(modelId) {
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 测试中...';
    button.disabled = true;

    fetch(`/api/ai-models/${modelId}/test`, {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('模型连接测试成功！');
        } else {
            alert('模型连接测试失败: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('测试请求失败');
    })
    .finally(() => {
        button.innerHTML = originalText;
        button.disabled = false;
    });
}

// 切换AI模型状态
function toggleModel(modelId, isActive) {
    const action = isActive ? '禁用' : '启用';
    if (confirm(`确定要${action}此模型吗？`)) {
        fetch(`/api/ai-models/${modelId}/toggle`, {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('操作失败: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('操作失败');
        });
    }
}

// 添加新AI模型
function addModel() {
    const form = document.getElementById('addModelForm');
    const formData = new FormData(form);
    const modelData = {
        name: formData.get('name'),
        model_name: formData.get('model_name'),
        api_key: formData.get('api_key'),
        base_url: formData.get('base_url'),
        is_active: formData.get('is_active') === 'on'
    };

    fetch('/api/ai-models', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(modelData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('AI模型添加成功！');
            location.reload();
        } else {
            alert('添加失败: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('添加失败');
    });
}

// 保存系统设置
document.getElementById('systemSettingsForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const settings = {
        default_risk_level: formData.get('default_risk_level'),
        max_positions: parseInt(formData.get('max_positions')),
        default_stop_loss: parseFloat(formData.get('default_stop_loss')),
        default_take_profit: parseFloat(formData.get('default_take_profit')),
        auto_trading: formData.get('auto_trading') === 'on',
        email_notifications: formData.get('email_notifications') === 'on',
        // 单边行情检测设置
        trend_detection: formData.get('trend_detection') === 'on',
        trend_strength_threshold: parseFloat(formData.get('trend_strength_threshold') || 60),
        volatility_breakout_multiplier: parseFloat(formData.get('volatility_breakout_multiplier') || 1.5),
        trend_confirmation_time: parseInt(formData.get('trend_confirmation_time') || 30),
        multi_timeframe_confirmation: formData.get('multi_timeframe_confirmation') === 'on'
    };

    // 同时保存到localStorage
    saveTrendDetectionSettings();

    fetch('/api/system-settings', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(settings)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('设置保存成功！');
        } else {
            alert('保存失败: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('保存失败');
    });
});

// MT5设置相关函数
// 加载MT5设置
function loadMT5Settings() {
    fetch('/api/system-settings/mt5')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('mt5DemoAsReal').checked = data.settings.mt5_demo_as_real;
            document.getElementById('enableMT5AutoConnect').checked = data.settings.enable_mt5_auto_connect;
            document.getElementById('enableMT5OrderSync').checked = data.settings.enable_mt5_order_sync;
        }
    })
    .catch(error => console.error('加载MT5设置失败:', error));
}

// 保存MT5设置
document.getElementById('mt5SettingsForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const settings = {
        mt5_demo_as_real: document.getElementById('mt5DemoAsReal').checked,
        enable_mt5_auto_connect: document.getElementById('enableMT5AutoConnect').checked,
        enable_mt5_order_sync: document.getElementById('enableMT5OrderSync').checked
    };

    fetch('/api/system-settings/mt5', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(settings)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('MT5设置保存成功！');
            // 显示成功提示
            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-success alert-dismissible fade show mt-3';
            alertDiv.innerHTML = `
                <i class="fas fa-check-circle"></i>
                MT5设置已保存并立即生效
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.getElementById('mt5SettingsForm').appendChild(alertDiv);

            // 3秒后自动移除提示
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 3000);
        } else {
            alert('保存失败: ' + data.error);
        }
    })
    .catch(error => {
        console.error('请求失败:', error);
        alert('保存失败，请重试');
    });
});



// 系统健康检查
function checkSystemHealth() {
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 检查中...';
    button.disabled = true;

    fetch('/api/system-health')
    .then(response => response.json())
    .then(data => {
        if (data.healthy) {
            alert('系统运行正常！\n' +
                  '数据库连接: ' + (data.database ? '正常' : '异常') + '\n' +
                  '内存使用: ' + data.memory_usage + '%\n' +
                  'CPU使用: ' + data.cpu_usage + '%');
        } else {
            alert('系统存在问题，请检查日志');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('健康检查失败');
    })
    .finally(() => {
        button.innerHTML = originalText;
        button.disabled = false;
    });
}

// 清理缓存
function clearCache() {
    if (confirm('确定要清理系统缓存吗？')) {
        fetch('/api/clear-cache', {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('缓存清理成功！');
            } else {
                alert('清理失败: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('清理失败');
        });
    }
}

// 导出数据
function exportData() {
    if (confirm('确定要导出系统数据吗？这可能需要一些时间。')) {
        window.open('/api/export-data', '_blank');
    }
}

// 单边行情检测配置控制
document.addEventListener('DOMContentLoaded', function() {
    const trendDetectionCheckbox = document.getElementById('trendDetection');
    const trendDetectionConfig = document.getElementById('trendDetectionConfig');

    // 初始化显示状态
    function toggleTrendDetectionConfig() {
        if (trendDetectionCheckbox.checked) {
            trendDetectionConfig.style.display = 'block';
        } else {
            trendDetectionConfig.style.display = 'none';
        }
    }

    // 监听复选框变化
    trendDetectionCheckbox.addEventListener('change', toggleTrendDetectionConfig);

    // 初始化
    toggleTrendDetectionConfig();

    // 加载保存的设置
    loadTrendDetectionSettings();
});

// 加载单边行情检测设置
function loadTrendDetectionSettings() {
    const savedSettings = localStorage.getItem('trendDetectionSettings');
    if (savedSettings) {
        try {
            const settings = JSON.parse(savedSettings);

            // 恢复设置值
            if (settings.enabled !== undefined) {
                document.getElementById('trendDetection').checked = settings.enabled;
            }
            if (settings.trend_strength_threshold !== undefined) {
                document.querySelector('input[name="trend_strength_threshold"]').value = settings.trend_strength_threshold;
            }
            if (settings.volatility_breakout_multiplier !== undefined) {
                document.querySelector('input[name="volatility_breakout_multiplier"]').value = settings.volatility_breakout_multiplier;
            }
            if (settings.trend_confirmation_time !== undefined) {
                document.querySelector('select[name="trend_confirmation_time"]').value = settings.trend_confirmation_time;
            }
            if (settings.multi_timeframe_confirmation !== undefined) {
                document.getElementById('multiTimeframeConfirmation').checked = settings.multi_timeframe_confirmation;
            }

            // 更新显示状态
            const trendDetectionConfig = document.getElementById('trendDetectionConfig');
            if (document.getElementById('trendDetection').checked) {
                trendDetectionConfig.style.display = 'block';
            }

            console.log('✅ 单边行情检测设置已加载');
        } catch (error) {
            console.error('❌ 加载单边行情检测设置失败:', error);
        }
    }
}

// 保存单边行情检测设置
function saveTrendDetectionSettings() {
    const settings = {
        enabled: document.getElementById('trendDetection').checked,
        trend_strength_threshold: parseFloat(document.querySelector('input[name="trend_strength_threshold"]').value),
        volatility_breakout_multiplier: parseFloat(document.querySelector('input[name="volatility_breakout_multiplier"]').value),
        trend_confirmation_time: parseInt(document.querySelector('select[name="trend_confirmation_time"]').value),
        multi_timeframe_confirmation: document.getElementById('multiTimeframeConfirmation').checked
    };

    localStorage.setItem('trendDetectionSettings', JSON.stringify(settings));
    console.log('✅ 单边行情检测设置已保存:', settings);
    return settings;
}

// 获取单边行情检测设置
function getTrendDetectionSettings() {
    const savedSettings = localStorage.getItem('trendDetectionSettings');
    if (savedSettings) {
        return JSON.parse(savedSettings);
    }

    // 默认设置
    return {
        enabled: false,
        trend_strength_threshold: 60,
        volatility_breakout_multiplier: 1.5,
        trend_confirmation_time: 30,
        multi_timeframe_confirmation: true
    };
}

// 左侧栏模块控制相关函数
// 保存左侧栏模块设置
document.getElementById('sidebarModulesForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const modules = {};

    // 获取所有复选框状态
    const checkboxes = this.querySelectorAll('input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        modules[checkbox.id] = checkbox.checked;
    });

    console.log('保存左侧栏模块设置:', modules);

    // 保存到localStorage
    localStorage.setItem('sidebarModulesSettings', JSON.stringify(modules));

    // 发送到服务器
    fetch('/api/sidebar-modules', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(modules)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 显示成功提示
            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-success alert-dismissible fade show mt-3';
            alertDiv.innerHTML = `
                <i class="fas fa-check-circle"></i>
                左侧栏模块设置已保存！页面将在3秒后刷新以应用更改。
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            this.appendChild(alertDiv);

            // 3秒后刷新页面以应用更改
            setTimeout(() => {
                window.location.reload();
            }, 3000);
        } else {
            alert('保存失败: ' + (data.error || '未知错误'));
        }
    })
    .catch(error => {
        console.error('保存失败:', error);
        alert('保存失败，请重试');
    });
});

// 重置左侧栏模块设置
function resetSidebarModules() {
    if (confirm('确定要重置为默认设置吗？这将启用所有模块。')) {
        const checkboxes = document.querySelectorAll('#sidebarModulesForm input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = true;
        });

        // 清除localStorage
        localStorage.removeItem('sidebarModulesSettings');

        console.log('已重置左侧栏模块设置为默认');
    }
}

// 加载左侧栏模块设置
function loadSidebarModulesSettings() {
    try {
        const savedSettings = localStorage.getItem('sidebarModulesSettings');
        if (savedSettings) {
            const modules = JSON.parse(savedSettings);
            console.log('加载左侧栏模块设置:', modules);

            // 应用保存的设置
            Object.keys(modules).forEach(moduleId => {
                const checkbox = document.getElementById(moduleId);
                if (checkbox) {
                    checkbox.checked = modules[moduleId];
                }
            });
        }
    } catch (error) {
        console.error('加载左侧栏模块设置失败:', error);
    }
}

// 页面加载时加载设置
document.addEventListener('DOMContentLoaded', function() {
    // 延迟加载，确保DOM完全加载
    setTimeout(() => {
        loadSidebarModulesSettings();
    }, 100);
});
</script>
{% endblock %}
