#!/usr/bin/env python3
"""
调试训练暂停功能问题
"""

import requests
import json
import sqlite3
import time
from datetime import datetime

def login_session():
    """创建登录会话"""
    session = requests.Session()
    
    try:
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post('http://127.0.0.1:5000/login', data=login_data)
        return session if response.status_code == 200 else None
            
    except Exception as e:
        print(f"❌ 登录失败: {e}")
        return None

def check_training_control_state():
    """检查训练控制状态"""
    
    print("🔍 检查训练控制状态")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 查找运行中的训练任务
        cursor.execute("""
            SELECT id, model_id, status, progress, current_epoch, total_epochs,
                   created_at, updated_at
            FROM training_tasks 
            WHERE status IN ('running', 'pending')
            ORDER BY created_at DESC
            LIMIT 5
        """)
        
        tasks = cursor.fetchall()
        
        if tasks:
            print(f"📊 发现 {len(tasks)} 个活跃训练任务:")
            
            for task in tasks:
                (task_id, model_id, status, progress, current_epoch, total_epochs,
                 created_at, updated_at) = task
                
                print(f"\n🔹 任务: {task_id}")
                print(f"   模型ID: {model_id}")
                print(f"   状态: {status}")
                print(f"   进度: {progress}%")
                print(f"   轮次: {current_epoch}/{total_epochs}")
                print(f"   创建时间: {created_at}")
                print(f"   更新时间: {updated_at}")
                
                # 检查这个任务的控制状态
                session = login_session()
                if session:
                    try:
                        response = session.get(f'http://127.0.0.1:5000/api/deep-learning/training/{task_id}/control-status')
                        
                        if response.status_code == 200:
                            result = response.json()
                            
                            if result.get('success'):
                                control_data = result['control']
                                print(f"   控制状态:")
                                print(f"     可控制: {control_data.get('can_control')}")
                                print(f"     已暂停: {control_data.get('is_paused')}")
                                print(f"     已停止: {control_data.get('is_stopped')}")
                            else:
                                print(f"   ❌ 控制状态API错误: {result.get('error')}")
                        else:
                            print(f"   ❌ 控制状态API请求失败: {response.status_code}")
                            
                    except Exception as e:
                        print(f"   ❌ 检查控制状态失败: {e}")
                
                return task_id  # 返回第一个任务ID用于测试
        else:
            print("❌ 没有发现活跃的训练任务")
            return None
            
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查训练控制状态失败: {e}")
        return None

def test_pause_functionality(task_id):
    """测试暂停功能"""
    
    print(f"\n⏸️ 测试暂停功能 (任务: {task_id})")
    print("=" * 50)
    
    session = login_session()
    if not session:
        print("❌ 无法登录")
        return False
    
    try:
        # 测试暂停API
        print("📡 发送暂停请求...")
        
        response = session.post(f'http://127.0.0.1:5000/api/deep-learning/training/{task_id}/pause')
        
        print(f"📊 暂停API响应状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            print(f"📊 API响应内容:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            
            if result.get('success'):
                print(f"✅ 暂停请求成功发送")
                
                # 等待一下，然后检查控制状态
                time.sleep(2)
                
                response = session.get(f'http://127.0.0.1:5000/api/deep-learning/training/{task_id}/control-status')
                
                if response.status_code == 200:
                    control_result = response.json()
                    
                    if control_result.get('success'):
                        control_data = control_result['control']
                        print(f"📊 暂停后的控制状态:")
                        print(f"   可控制: {control_data.get('can_control')}")
                        print(f"   已暂停: {control_data.get('is_paused')}")
                        print(f"   已停止: {control_data.get('is_stopped')}")
                        
                        if control_data.get('is_paused'):
                            print(f"✅ 训练已成功暂停")
                            return True
                        else:
                            print(f"⚠️ 暂停状态未生效")
                            return False
                    else:
                        print(f"❌ 获取控制状态失败: {control_result.get('error')}")
                        return False
                else:
                    print(f"❌ 控制状态API请求失败: {response.status_code}")
                    return False
            else:
                print(f"❌ 暂停请求失败: {result.get('error')}")
                return False
        else:
            print(f"❌ 暂停API请求失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试暂停功能失败: {e}")
        return False

def test_resume_functionality(task_id):
    """测试恢复功能"""
    
    print(f"\n▶️ 测试恢复功能 (任务: {task_id})")
    print("=" * 50)
    
    session = login_session()
    if not session:
        print("❌ 无法登录")
        return False
    
    try:
        # 测试恢复API
        print("📡 发送恢复请求...")
        
        response = session.post(f'http://127.0.0.1:5000/api/deep-learning/training/{task_id}/resume')
        
        print(f"📊 恢复API响应状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            print(f"📊 API响应内容:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            
            if result.get('success'):
                print(f"✅ 恢复请求成功发送")
                
                # 等待一下，然后检查控制状态
                time.sleep(2)
                
                response = session.get(f'http://127.0.0.1:5000/api/deep-learning/training/{task_id}/control-status')
                
                if response.status_code == 200:
                    control_result = response.json()
                    
                    if control_result.get('success'):
                        control_data = control_result['control']
                        print(f"📊 恢复后的控制状态:")
                        print(f"   可控制: {control_data.get('can_control')}")
                        print(f"   已暂停: {control_data.get('is_paused')}")
                        print(f"   已停止: {control_data.get('is_stopped')}")
                        
                        if not control_data.get('is_paused'):
                            print(f"✅ 训练已成功恢复")
                            return True
                        else:
                            print(f"⚠️ 恢复状态未生效")
                            return False
                    else:
                        print(f"❌ 获取控制状态失败: {control_result.get('error')}")
                        return False
                else:
                    print(f"❌ 控制状态API请求失败: {response.status_code}")
                    return False
            else:
                print(f"❌ 恢复请求失败: {result.get('error')}")
                return False
        else:
            print(f"❌ 恢复API请求失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试恢复功能失败: {e}")
        return False

def create_test_training_task():
    """创建测试训练任务"""
    
    print(f"\n🔧 创建测试训练任务")
    print("=" * 50)
    
    session = login_session()
    if not session:
        print("❌ 无法登录")
        return None
    
    # 启动一个简单的训练任务
    config = {
        'model_name': f'pause_test_{int(time.time())}',
        'model_type': 'LSTM',
        'symbol': 'XAUUSD',
        'timeframe': 'H1',
        'epochs': 100,  # 较多轮次，便于测试暂停
        'batch_size': 16,
        'learning_rate': 0.001,
        'validation_split': 0.2,
        'sequence_length': 30,
        'features': ['close', 'volume'],
        'early_stopping': False,  # 禁用早停，确保训练持续
        'use_gpu': True,
        'save_checkpoints': True
    }
    
    try:
        print(f"🚀 启动测试训练...")
        
        response = session.post(
            'http://127.0.0.1:5000/api/deep-learning/start-training',
            json=config,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                task_id = result.get('task_id')
                print(f"✅ 测试训练启动成功!")
                print(f"   任务ID: {task_id}")
                
                # 等待训练开始
                print(f"⏳ 等待训练开始...")
                time.sleep(5)
                
                return task_id
            else:
                print(f"❌ 训练启动失败: {result.get('error')}")
                return None
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 创建测试训练任务失败: {e}")
        return None

def main():
    """主函数"""
    
    print("🔧 训练暂停功能调试")
    print("=" * 80)
    
    # 检查现有的训练控制状态
    existing_task_id = check_training_control_state()
    
    if existing_task_id:
        print(f"\n💡 使用现有任务进行测试: {existing_task_id}")
        task_id = existing_task_id
    else:
        print(f"\n💡 创建新的测试任务...")
        task_id = create_test_training_task()
    
    if not task_id:
        print(f"\n❌ 无法获取或创建训练任务，无法进行测试")
        return
    
    # 测试暂停功能
    pause_ok = test_pause_functionality(task_id)
    
    if pause_ok:
        # 测试恢复功能
        resume_ok = test_resume_functionality(task_id)
    else:
        resume_ok = False
    
    print(f"\n📋 调试结果")
    print("=" * 80)
    
    if pause_ok and resume_ok:
        print(f"🎉 训练暂停/恢复功能正常!")
        print(f"✅ 暂停功能工作正常")
        print(f"✅ 恢复功能工作正常")
        
        print(f"\n💡 功能说明:")
        print(f"• 暂停功能通过设置控制标志实现")
        print(f"• 训练循环会检查暂停状态并等待")
        print(f"• 恢复功能清除暂停标志，训练继续")
        print(f"• 控制状态在内存中维护，重启后会丢失")
        
    else:
        print(f"⚠️ 训练暂停/恢复功能有问题")
        print(f"暂停功能: {'✅' if pause_ok else '❌'}")
        print(f"恢复功能: {'✅' if resume_ok else '❌'}")
        
        print(f"\n💡 可能的问题:")
        print(f"• 训练任务不在内存控制字典中")
        print(f"• 训练已经完成或失败")
        print(f"• 服务重启导致控制状态丢失")
        print(f"• 任务ID不匹配")
    
    print(f"\n🎯 使用建议")
    print("=" * 80)
    
    print(f"📊 正常使用流程:")
    print(f"1. 启动训练后，任务会在内存中注册控制状态")
    print(f"2. 训练过程中可以使用暂停/恢复/停止功能")
    print(f"3. 训练完成后，控制状态会自动清理")
    print(f"4. 服务重启后，需要重新启动训练才能控制")
    
    print(f"\n⚠️ 注意事项:")
    print(f"• 只有正在运行的训练任务才能暂停")
    print(f"• 已完成或失败的任务无法暂停")
    print(f"• 服务重启会清除所有控制状态")
    print(f"• 暂停状态不会持久化到数据库")

if __name__ == '__main__':
    main()
