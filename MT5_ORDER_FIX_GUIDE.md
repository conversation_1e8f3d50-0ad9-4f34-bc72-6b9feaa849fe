
# 立即修复MT5订单问题的步骤

## 1. 检查MT5连接
1. 确保MT5终端正在运行
2. 确保账户已登录
3. 检查网络连接
4. 验证交易权限

## 2. 统一数据库配置
检查以下文件中的数据库配置：
- app.py: SQLALCHEMY_DATABASE_URI
- services/deep_learning_service.py: self.db_path
- 其他服务文件中的 sqlite3.connect()

确保都指向 trading_system.db

## 3. 测试订单执行
1. 重启应用程序
2. 进入模型推理页面
3. 尝试手动执行一个小额测试交易
4. 检查MT5终端是否收到订单
5. 查看应用程序日志

## 4. 如果仍有问题
1. 检查MT5专家日志
2. 查看Python应用程序日志
3. 验证MT5 API权限设置
4. 检查防火墙和安全软件
