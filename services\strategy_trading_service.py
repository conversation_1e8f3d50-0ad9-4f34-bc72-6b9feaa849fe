#!/usr/bin/env python3
"""
策略交易服务 - 使用AI策略模型进行自动交易
"""

import json
import sqlite3
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class StrategyTradingService:
    """策略交易服务"""
    
    def __init__(self):
        self.db_path = 'trading_system.db'
        self.is_running = False
        self.trading_thread = None
        self.current_config = None
        self.trade_count = 0
        self.daily_trades = {}
        self.total_pnl = 0.0
        
        # 交易状态持久化
        self.status_file = 'strategy_trading_status.json'
        self.load_trading_status()
    
    def load_trading_status(self):
        """加载交易状态"""
        try:
            with open(self.status_file, 'r', encoding='utf-8') as f:
                status = json.load(f)
                self.is_running = status.get('is_running', False)
                self.current_config = status.get('current_config')
                self.trade_count = status.get('trade_count', 0)
                self.daily_trades = status.get('daily_trades', {})
                self.total_pnl = status.get('total_pnl', 0.0)
                
                # 如果系统重启时交易正在运行，重新启动
                if self.is_running and self.current_config:
                    logger.info("🔄 检测到交易状态，重新启动策略交易...")
                    self.start_trading_thread()
                    
        except FileNotFoundError:
            logger.info("📝 首次运行，创建新的交易状态")
        except Exception as e:
            logger.error(f"❌ 加载交易状态失败: {e}")
    
    def save_trading_status(self):
        """保存交易状态"""
        try:
            status = {
                'is_running': self.is_running,
                'current_config': self.current_config,
                'trade_count': self.trade_count,
                'daily_trades': self.daily_trades,
                'total_pnl': self.total_pnl,
                'last_updated': datetime.now().isoformat()
            }
            
            with open(self.status_file, 'w', encoding='utf-8') as f:
                json.dump(status, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            logger.error(f"❌ 保存交易状态失败: {e}")
    
    def start_trading(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """启动策略交易"""
        try:
            if self.is_running:
                return {'success': False, 'error': '策略交易已在运行中'}
            
            # 验证配置
            validation_result = self.validate_config(config)
            if not validation_result['valid']:
                return {'success': False, 'error': validation_result['error']}
            
            # 验证AI策略是否存在
            strategy_info = self.get_ai_strategy_info(config['aiStrategy'])
            if not strategy_info:
                return {'success': False, 'error': '选择的AI策略不存在'}
            
            # 保存配置
            self.current_config = config
            self.current_config['strategy_info'] = strategy_info
            self.is_running = True
            self.trade_count = 0
            
            # 启动交易线程
            self.start_trading_thread()
            
            # 保存状态
            self.save_trading_status()
            
            logger.info(f"🚀 策略交易已启动: {strategy_info['name']}")
            return {
                'success': True,
                'message': f'策略交易已启动: {strategy_info["name"]}',
                'config': self.current_config
            }
            
        except Exception as e:
            logger.error(f"❌ 启动策略交易失败: {e}")
            return {'success': False, 'error': f'启动失败: {str(e)}'}
    
    def stop_trading(self) -> Dict[str, Any]:
        """停止策略交易"""
        try:
            if not self.is_running:
                return {'success': False, 'error': '策略交易未在运行'}
            
            self.is_running = False
            
            # 等待交易线程结束
            if self.trading_thread and self.trading_thread.is_alive():
                self.trading_thread.join(timeout=5)
            
            # 保存状态
            self.save_trading_status()
            
            logger.info("🛑 策略交易已停止")
            return {
                'success': True,
                'message': '策略交易已停止',
                'total_trades': self.trade_count
            }
            
        except Exception as e:
            logger.error(f"❌ 停止策略交易失败: {e}")
            return {'success': False, 'error': f'停止失败: {str(e)}'}
    
    def get_trading_status(self) -> Dict[str, Any]:
        """获取交易状态"""
        return {
            'success': True,
            'status': 'running' if self.is_running else 'stopped',
            'config': self.current_config,
            'trade_count': self.trade_count,
            'total_pnl': self.total_pnl
        }
    
    def get_trading_stats(self) -> Dict[str, Any]:
        """获取交易统计"""
        today = datetime.now().strftime('%Y-%m-%d')
        today_trades = self.daily_trades.get(today, 0)
        
        return {
            'success': True,
            'todayTrades': today_trades,
            'totalTrades': self.trade_count,
            'totalPnL': self.total_pnl,
            'isRunning': self.is_running
        }
    
    def validate_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """验证交易配置"""
        required_fields = ['aiStrategy', 'symbol', 'lotSize', 'timeframe', 'stopLoss', 'takeProfit']
        
        for field in required_fields:
            if field not in config or not config[field]:
                return {'valid': False, 'error': f'缺少必要配置: {field}'}
        
        # 验证数值范围
        if not (0.01 <= config['lotSize'] <= 1.0):
            return {'valid': False, 'error': '交易手数必须在0.01-1.0之间'}
        
        if not (0.5 <= config['stopLoss'] <= 10.0):
            return {'valid': False, 'error': '止损百分比必须在0.5%-10%之间'}
        
        if not (1.0 <= config['takeProfit'] <= 20.0):
            return {'valid': False, 'error': '止盈百分比必须在1%-20%之间'}
        
        return {'valid': True}
    
    def get_ai_strategy_info(self, strategy_id: str) -> Optional[Dict[str, Any]]:
        """获取AI策略信息"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT id, name, timeframe, parameters, performance_metrics, training_data
                FROM strategy 
                WHERE id = ? AND strategy_type = 'ai' AND status = 'completed'
            """, (strategy_id,))
            
            result = cursor.fetchone()
            conn.close()
            
            if result:
                strategy_id, name, timeframe, parameters, performance_metrics, training_data = result
                
                # 解析JSON数据
                try:
                    parameters = json.loads(parameters) if parameters else {}
                    performance_metrics = json.loads(performance_metrics) if performance_metrics else {}
                    training_data = json.loads(training_data) if training_data else {}
                except:
                    parameters = {}
                    performance_metrics = {}
                    training_data = {}
                
                return {
                    'id': strategy_id,
                    'name': name,
                    'timeframe': timeframe,
                    'parameters': parameters,
                    'performance_metrics': performance_metrics,
                    'training_data': training_data
                }
            
            return None
            
        except Exception as e:
            logger.error(f"❌ 获取AI策略信息失败: {e}")
            return None
    
    def start_trading_thread(self):
        """启动交易线程"""
        self.trading_thread = threading.Thread(target=self.trading_loop, daemon=True)
        self.trading_thread.start()
    
    def trading_loop(self):
        """交易主循环"""
        logger.info("🔄 策略交易循环已启动")
        
        while self.is_running:
            try:
                # 检查交易条件
                if self.should_execute_trade():
                    # 生成交易信号
                    signal = self.generate_trading_signal()
                    
                    if signal:
                        # 执行交易
                        result = self.execute_trade(signal)
                        if result['success']:
                            self.trade_count += 1
                            
                            # 更新每日交易计数
                            today = datetime.now().strftime('%Y-%m-%d')
                            self.daily_trades[today] = self.daily_trades.get(today, 0) + 1
                            
                            # 保存状态
                            self.save_trading_status()
                            
                            logger.info(f"✅ 交易执行成功: {signal['action']} {signal['symbol']}")
                        else:
                            logger.error(f"❌ 交易执行失败: {result['error']}")
                
                # 管理全程自动交易持仓
                self.manage_full_auto_positions()

                # 检查是否达到交易限制
                if self.check_trade_limit():
                    logger.info("📊 达到交易次数限制，停止交易")
                    self.is_running = False
                    break

                # 等待下一个交易周期
                time.sleep(self.get_trading_interval())
                
            except Exception as e:
                logger.error(f"❌ 交易循环异常: {e}")
                time.sleep(60)  # 出错时等待1分钟
        
        logger.info("🛑 策略交易循环已结束")
    
    def should_execute_trade(self) -> bool:
        """检查是否应该执行交易"""
        if not self.current_config:
            return False
        
        # 检查市场开放时间
        if not self.is_market_open():
            return False
        
        # 检查交易限制
        if self.check_trade_limit():
            return False
        
        return True
    
    def is_market_open(self) -> bool:
        """检查市场是否开放"""
        # 简化版本：假设市场总是开放
        # 实际应用中应该检查外汇市场开放时间
        return True
    
    def check_trade_limit(self) -> bool:
        """检查是否达到交易限制"""
        if not self.current_config:
            return True
        
        trade_limit = self.current_config.get('tradeLimit', '30')
        if trade_limit == 'unlimited':
            return False
        
        try:
            limit = int(trade_limit)
            return self.trade_count >= limit
        except:
            return False
    
    def get_trading_interval(self) -> int:
        """获取交易间隔（秒）"""
        if not self.current_config:
            return 300  # 默认5分钟
        
        timeframe = self.current_config.get('timeframe', '15m')
        
        # 时间框架到秒数的映射
        timeframe_seconds = {
            '1m': 60,
            '5m': 300,
            '15m': 900,
            '30m': 1800,
            '1h': 3600,
            '4h': 14400,
            '1d': 86400
        }
        
        return timeframe_seconds.get(timeframe, 900)  # 默认15分钟
    
    def generate_trading_signal(self) -> Optional[Dict[str, Any]]:
        """生成交易信号 - 使用AI策略模型"""
        try:
            if not self.current_config or not self.current_config.get('strategy_info'):
                logger.error("❌ 缺少策略配置信息")
                return None

            # 获取MT5服务
            from services.mt5_service import mt5_service
            if not mt5_service.connected:
                logger.error("❌ MT5未连接，无法获取市场数据")
                return None

            import MetaTrader5 as mt5

            # 获取策略配置
            symbol = self.current_config['symbol']
            timeframe = self.current_config['timeframe']
            strategy_id = self.current_config['aiStrategy']

            # 转换时间框架到MT5格式
            timeframe_map = {
                '1m': mt5.TIMEFRAME_M1,
                '5m': mt5.TIMEFRAME_M5,
                '15m': mt5.TIMEFRAME_M15,
                '30m': mt5.TIMEFRAME_M30,
                '1h': mt5.TIMEFRAME_H1,
                '4h': mt5.TIMEFRAME_H4,
                '1d': mt5.TIMEFRAME_D1
            }
            mt5_timeframe = timeframe_map.get(timeframe, mt5.TIMEFRAME_H1)

            # 获取最新市场数据
            rates = mt5.copy_rates_from_pos(symbol, mt5_timeframe, 0, 100)
            if rates is None or len(rates) == 0:
                logger.error(f"❌ 无法获取 {symbol} 的市场数据")
                return None

            # 转换为DataFrame
            import pandas as pd
            rates_df = pd.DataFrame(rates)
            rates_df['time'] = pd.to_datetime(rates_df['time'], unit='s')

            # 使用AI策略加载器生成信号
            from services.ai_strategy_loader import ai_strategy_loader
            signal = ai_strategy_loader.predict_signal(strategy_id, rates_df)

            if signal:
                # 添加交易品种和策略名称到信号
                signal['symbol'] = self.current_config['symbol']
                signal['strategy_name'] = self.current_config['strategy_info']['name']
                logger.info(f"✅ AI策略生成信号: {signal['action']} (置信度: {signal['confidence']:.2f})")
                return signal

            return None

        except Exception as e:
            logger.error(f"❌ 生成交易信号失败: {e}")
            return None
    
    def execute_trade(self, signal: Dict[str, Any]) -> Dict[str, Any]:
        """执行交易 - 使用真实MT5交易"""
        try:
            logger.info(f"🔄 执行策略交易: {signal['action']} {signal['symbol']}")

            # 获取MT5服务
            from services.mt5_service import mt5_service
            if not mt5_service.connected:
                return {'success': False, 'error': 'MT5未连接'}

            # 获取交易配置
            symbol = signal['symbol']
            action = signal['action']
            lot_size = self.current_config['lotSize']
            strategy_name = signal['strategy_name']

            # 构建交易请求
            import MetaTrader5 as mt5

            # 获取当前价格
            tick = mt5.symbol_info_tick(symbol)
            if tick is None:
                return {'success': False, 'error': f'无法获取{symbol}价格信息'}

            # 确定交易类型和价格
            if action == 'buy':
                trade_type = mt5.ORDER_TYPE_BUY
                price = tick.ask
            else:
                trade_type = mt5.ORDER_TYPE_SELL
                price = tick.bid

            # 计算止损和止盈价格
            stop_loss_percent = self.current_config['stopLoss'] / 100.0
            take_profit_percent = self.current_config['takeProfit'] / 100.0

            # 检查是否启用全程自动交易
            full_auto_enabled = self.current_config.get('fullAutoTrading', {}).get('enabled', False)
            keep_stop_loss_take_profit = self.current_config.get('fullAutoTrading', {}).get('keepStopLossTakeProfit', True)

            # 如果启用全程自动且不保留止损止盈，则不设置止损止盈
            if full_auto_enabled and not keep_stop_loss_take_profit:
                stop_loss = 0
                take_profit = 0
                logger.info(f"🤖 全程自动模式：不设置止损止盈，由AI动态管理")
            else:
                # 正常计算止损止盈
                if action == 'buy':
                    stop_loss = price * (1 - stop_loss_percent)
                    take_profit = price * (1 + take_profit_percent)
                else:
                    stop_loss = price * (1 + stop_loss_percent)
                    take_profit = price * (1 - take_profit_percent)

                if full_auto_enabled:
                    logger.info(f"🤖 全程自动模式：保留止损止盈作为风险控制底线")

            # 构建交易请求
            request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": symbol,
                "volume": lot_size,
                "type": trade_type,
                "price": price,
                "sl": stop_loss,
                "tp": take_profit,
                "deviation": 20,
                "magic": 234000,  # 策略交易魔术号
                "comment": f"AI_{strategy_name}",
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": mt5.ORDER_FILLING_IOC,
            }

            # 执行交易
            result = mt5.order_send(request)

            if result is None:
                return {'success': False, 'error': 'MT5交易请求失败'}

            if result.retcode != mt5.TRADE_RETCODE_DONE:
                error_msg = f"交易失败: {result.retcode} - {result.comment}"
                logger.error(f"❌ {error_msg}")
                return {'success': False, 'error': error_msg}

            # 交易成功
            logger.info(f"✅ 策略交易成功: 订单号 {result.order}, 成交价 {result.price}")

            # 保存交易记录到数据库
            self.save_trade_record(signal, result)

            return {
                'success': True,
                'ticket': result.order,
                'price': result.price,
                'volume': result.volume,
                'signal': signal,
                'result': result
            }

        except Exception as e:
            logger.error(f"❌ 执行交易失败: {e}")
            return {'success': False, 'error': str(e)}

    def save_trade_record(self, signal: Dict[str, Any], result) -> None:
        """保存交易记录到数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 保存到trades表
            cursor.execute("""
                INSERT INTO trades (
                    user_id, symbol, action, volume, entry_price,
                    stop_loss, take_profit, status, strategy_type,
                    comment, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                1,  # 默认用户ID
                signal['symbol'],
                signal['action'],
                result.volume,
                result.price,
                result.request.sl if hasattr(result, 'request') else 0,
                result.request.tp if hasattr(result, 'request') else 0,
                'open',
                'strategy',
                f"AI_{signal['strategy_name']}",
                datetime.now().isoformat()
            ))

            conn.commit()
            conn.close()

            logger.info(f"✅ 交易记录已保存到数据库")

        except Exception as e:
            logger.error(f"❌ 保存交易记录失败: {e}")

    def manage_full_auto_positions(self) -> None:
        """管理全程自动交易的持仓"""
        try:
            if not self.current_config:
                return

            full_auto_config = self.current_config.get('fullAutoTrading', {})
            if not full_auto_config.get('enabled', False):
                return

            logger.info("🤖 检查全程自动交易持仓...")

            # 获取MT5服务
            from services.mt5_service import mt5_service
            if not mt5_service.connected:
                logger.warning("❌ MT5未连接，无法管理持仓")
                return

            import MetaTrader5 as mt5

            # 获取策略交易的持仓
            positions = mt5.positions_get()
            if positions is None:
                return

            strategy_positions = [pos for pos in positions
                                if pos.comment and 'AI_' in pos.comment]

            if not strategy_positions:
                logger.info("📊 当前无策略交易持仓")
                return

            logger.info(f"📊 发现 {len(strategy_positions)} 个策略交易持仓")

            # 获取策略信息
            strategy_id = self.current_config.get('strategy_info', {}).get('id')
            if not strategy_id:
                logger.warning("❌ 无法获取策略ID")
                return

            # 获取市场数据并生成离场信号
            symbol = self.current_config['symbol']
            rates = mt5.copy_rates_from_pos(symbol, mt5.TIMEFRAME_H1, 0, 100)
            if rates is None or len(rates) == 0:
                logger.warning(f"❌ 无法获取 {symbol} 的市场数据")
                return

            # 转换为DataFrame
            import pandas as pd
            rates_df = pd.DataFrame(rates)
            rates_df['time'] = pd.to_datetime(rates_df['time'], unit='s')

            # 使用AI策略生成离场信号
            from services.ai_strategy_loader import ai_strategy_loader
            exit_signal = ai_strategy_loader.predict_signal(str(strategy_id), rates_df)

            if not exit_signal:
                logger.info("📊 AI策略未生成离场信号")
                return

            # 检查离场信号阈值
            exit_threshold = full_auto_config.get('exitSignalThreshold', 0.7)
            if exit_signal['confidence'] < exit_threshold:
                logger.info(f"📊 离场信号置信度 {exit_signal['confidence']:.3f} 低于阈值 {exit_threshold}")
                return

            # 根据动态离场策略决定是否平仓
            exit_strategy = full_auto_config.get('dynamicExitStrategy', 'conservative')
            should_exit = self._should_exit_position(exit_signal, exit_strategy, strategy_positions)

            if should_exit:
                logger.info(f"🤖 AI决定执行动态离场: {exit_signal['action']} (置信度: {exit_signal['confidence']:.3f})")
                self._execute_dynamic_exit(strategy_positions, exit_signal)
            else:
                logger.info("📊 AI分析后决定继续持仓")

        except Exception as e:
            logger.error(f"❌ 管理全程自动交易持仓失败: {e}")

    def _should_exit_position(self, exit_signal: Dict[str, Any], exit_strategy: str, positions: list) -> bool:
        """判断是否应该离场"""
        try:
            # 如果信号是持有，不离场
            if exit_signal['action'] == 'hold':
                return False

            # 检查是否有相反方向的信号
            for pos in positions:
                pos_type = 'buy' if pos.type == 0 else 'sell'

                # 如果AI信号与持仓方向相反，考虑离场
                if (pos_type == 'buy' and exit_signal['action'] == 'sell') or \
                   (pos_type == 'sell' and exit_signal['action'] == 'buy'):

                    # 根据离场策略调整判断标准
                    if exit_strategy == 'conservative':
                        # 保守策略：只要有相反信号就离场
                        return True
                    elif exit_strategy == 'balanced':
                        # 平衡策略：考虑持仓盈亏情况
                        if pos.profit > 0:  # 有盈利时更容易离场
                            return exit_signal['confidence'] >= 0.6
                        else:  # 亏损时需要更高置信度
                            return exit_signal['confidence'] >= 0.8
                    elif exit_strategy == 'aggressive':
                        # 激进策略：只有高置信度才离场
                        return exit_signal['confidence'] >= 0.8

            return False

        except Exception as e:
            logger.error(f"❌ 判断离场条件失败: {e}")
            return False

    def _execute_dynamic_exit(self, positions: list, exit_signal: Dict[str, Any]) -> None:
        """执行动态离场"""
        try:
            from services.mt5_service import mt5_service

            closed_count = 0
            for pos in positions:
                result = mt5_service.close_position(pos.ticket)
                if result['success']:
                    closed_count += 1
                    logger.info(f"✅ 动态平仓成功: 订单 {pos.ticket}, 盈亏 {pos.profit:.2f}")
                else:
                    logger.error(f"❌ 动态平仓失败: 订单 {pos.ticket}, 错误: {result['error']}")

            if closed_count > 0:
                logger.info(f"🤖 全程自动交易动态离场完成: 平仓 {closed_count} 个持仓")

        except Exception as e:
            logger.error(f"❌ 执行动态离场失败: {e}")

# 全局策略交易服务实例
strategy_trading_service = StrategyTradingService()
