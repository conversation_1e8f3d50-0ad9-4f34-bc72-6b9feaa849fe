# MateTrade4 AI自动交易系统

一个基于Flask架构的专业AI自动交易系统，集成了多个AI大模型，支持真实数据分析和策略回测。

## 🚀 主要功能

### 1. 首页仪表盘
- 账户资产概览
- 实时交易统计
- 市场行情监控
- 快速操作面板

### 2. 交易管理
- **真实交易**: 连接真实交易所进行实盘交易
- **模拟交易**: 无风险的模拟交易环境
- 多交易所支持 (Binance, OKX, Huobi等)
- 实时持仓管理
- 交易历史记录

### 3. 分析工具
- **策略回测**: 基于历史数据的策略验证
- **AI策略分析过程**: 详细的AI分析流程展示
- **训练AI策略**: 使用真实历史数据训练AI模型
- **AI大模型分析**: 集成多个AI模型进行市场分析
- **🆕 MT5形态识别**: 从MT5获取真实数据进行技术形态识别

### 4. AI模型集成
系统集成了5个强大的AI模型：

1. **Deepseek-V3**
   - 模型: deepseek-chat
   - API: https://api.deepseek.com/v1

2. **Kimi**
   - 模型: moonshot-v1-128k
   - API: https://api.moonshot.cn/v1

3. **智谱清言**
   - 模型: glm-4-flash-250414
   - API: https://open.bigmodel.cn/api/paas/v4

4. **智谱清言-Z1**
   - 模型: glm-z1-flash
   - API: https://open.bigmodel.cn/api/paas/v4

5. **Deepseek-R1**
   - 模型: deepseek-reasoner
   - API: https://api.deepseek.com/v1

## 🛠️ 技术栈

- **后端**: Flask + SQLAlchemy
- **前端**: Bootstrap 5 + Chart.js + Plotly.js
- **数据库**: SQLite (可扩展到PostgreSQL/MySQL)
- **数据源**: Yahoo Finance (yfinance) + MetaTrader 5
- **技术分析**: 自实现技术指标库 + 形态识别算法
- **交易接口**: CCXT (支持多交易所) + MT5接口

## 📦 安装部署

### 1. 环境要求
- Python 3.8+
- pip

### 2. 克隆项目
```bash
git clone <repository-url>
cd MateTrade4_new
```

### 3. 安装依赖
```bash
pip install -r requirements.txt
```

### 4. 配置环境变量
复制 `.env` 文件并根据需要修改配置：
```bash
SECRET_KEY=your-secret-key-change-this-in-production
DATABASE_URL=sqlite:///matetrade4.db
FLASK_ENV=development
FLASK_DEBUG=True
```

### 5. 初始化数据库
```bash
python app.py
```
首次运行会自动创建数据库和默认用户。

### 6. 初始化示例数据 (可选)
```bash
python init_data.py
```

### 7. 启动应用
```bash
python app.py
```

访问 http://localhost:5000

## 🔐 默认登录信息

- **管理员账户**: admin / admin123
- **测试账户**: test_user / test123
- **演示账户**: demo_user / demo123

## 📊 核心特性

### 真实数据支持
- 使用Yahoo Finance获取真实市场数据
- 支持股票、加密货币、外汇等多种资产
- 实时价格更新和历史数据分析

### 智能策略系统
- 多种预置策略模板
- 自定义策略参数
- AI辅助策略优化
- 策略性能评估

### 风险管理
- 智能止损止盈
- 仓位管理
- 风险评估指标
- 回撤控制

### 技术指标
- 移动平均线 (SMA, EMA)
- MACD指标
- RSI相对强弱指数
- 布林带
- 随机指标
- 威廉指标
- ATR平均真实波幅

## 🔧 配置说明

### AI模型配置
在系统设置页面可以配置AI模型参数：
- API密钥管理
- 模型启用/禁用
- 连接测试
- 使用统计

### 交易账户配置
支持多个交易账户：
- 真实交易账户
- 模拟交易账户
- API密钥配置
- 账户余额监控

## 📈 使用指南

### 1. 策略回测
1. 进入"分析工具" -> "策略回测"
2. 选择或创建交易策略
3. 设置回测参数（时间范围、初始资金等）
4. 运行回测并查看结果

### 2. AI分析
1. 进入"分析工具" -> "AI大模型分析"
2. 选择要分析的交易对
3. 选择AI模型和分析类型
4. 查看AI生成的分析报告

### 3. 实盘交易
1. 配置交易账户API
2. 进入"交易管理" -> "真实交易"
3. 选择交易对和策略
4. 设置止损止盈参数
5. 提交订单

### 4. MT5形态识别
1. 确保MT5终端已启动并登录
2. 运行形态分析器：
   ```bash
   python real_pattern_analyzer.py --symbol EURUSD --timeframe 1h --bars 1000
   ```
3. 查看生成的图表和分析报告
4. 根据识别的形态制定交易策略

## 🚨 风险提示

- 本系统仅供学习和研究使用
- 实盘交易存在资金损失风险
- 请在充分了解风险的情况下使用
- 建议先在模拟环境中测试策略
- AI分析结果仅供参考，不构成投资建议

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进项目。

## 📄 许可证

本项目采用MIT许可证。

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 邮箱: <EMAIL>
- GitHub Issues: [项目Issues页面]

---

## 📊 MT5形态识别系统

### 支持的形态类型

#### 🔴 看跌形态
- **头肩顶**: 三个高点，中间最高，预示价格下跌
- **双顶**: 两个相近的高点，形成M型，看跌信号
- **下降三角形**: 水平支撑线+下降阻力线，通常向下突破
- **上升楔形**: 两条上升趋势线收敛，看跌形态
- **熊旗**: 下跌后的小幅上升整理，继续下跌

#### 🟢 看涨形态
- **头肩底**: 三个低点，中间最低，预示价格上涨
- **双底**: 两个相近的低点，形成W型，看涨信号
- **上升三角形**: 水平阻力线+上升支撑线，通常向上突破
- **下降楔形**: 两条下降趋势线收敛，看涨形态
- **牛旗**: 上涨后的小幅下降整理，继续上涨

#### 🔵 中性形态
- **对称三角形**: 收敛的趋势线，突破方向不确定
- **矩形**: 水平的支撑阻力区间，等待突破

### 使用示例

```bash
# 分析欧美货币对1小时图
python real_pattern_analyzer.py -s EURUSD -t 1h -b 1000

# 分析黄金4小时图
python real_pattern_analyzer.py -s XAUUSD -t 4h -b 500

# 分析标普500日线图
python real_pattern_analyzer.py -s US500 -t 1d -b 200

# 查看所有可用品种
python real_pattern_analyzer.py --list-symbols
```

### 输出文件说明

1. **patterns_overview_*.png**: 形态总览图，显示所有识别的形态
2. **pattern_detail_*.png**: 单个形态详细图，包含交易建议
3. **pattern_summary_*.csv**: 汇总报告，包含所有形态信息
4. **pattern_analysis.log**: 详细分析日志

### 形态置信度说明

- **90%+**: 🟢 极高置信度，强烈信号
- **70-90%**: 🟡 高置信度，可靠信号
- **50-70%**: 🟠 中等置信度，谨慎交易
- **50%以下**: 🔴 低置信度，建议观望

---

**免责声明**: 本软件仅用于教育和研究目的。使用本软件进行实际交易的任何损失，开发者概不负责。请在使用前充分了解相关风险。
