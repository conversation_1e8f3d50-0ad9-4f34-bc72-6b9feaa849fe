{% extends "base.html" %}

{% block page_title %}首页仪表盘{% endblock %}

{% block content %}
<!-- 仪表盘说明 -->
<div class="row mb-3">
    <div class="col-12">
        <div class="alert alert-info d-flex align-items-center" role="alert">
            <i class="fas fa-info-circle me-2"></i>
            <div>
                <strong>仪表盘说明：</strong>此页面仅显示真实交易账户的资产、净值和交易信息。模拟账户信息请前往
                <a href="{{ url_for('demo_trading') }}" class="alert-link">模拟交易页面</a> 查看。
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 统计卡片 -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">
                            真实账户总资产
                        </div>
                        <div class="h5 mb-0 font-weight-bold" id="totalBalance">
                            ${{ "%.2f"|format(total_balance) }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-dollar-sign fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 今日盈亏卡片 -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">
                            今日盈亏
                        </div>
                        <div class="h5 mb-0 font-weight-bold {{ 'profit-positive' if today_pnl >= 0 else 'profit-negative' }}" id="todayPnL">
                            ${{ "%.2f"|format(today_pnl) }}
                        </div>
                        <div class="text-xs text-muted" id="todayTradesCount">
                            今日交易: {{ today_trades_count }} 笔
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas {{ 'fa-arrow-up text-success' if today_pnl >= 0 else 'fa-arrow-down text-danger' }} fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">
                            真实账户净值
                        </div>
                        <div class="h5 mb-0 font-weight-bold" id="totalEquity">
                            ${{ "%.2f"|format(total_equity) }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-chart-line fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">
                            活跃策略
                        </div>
                        <div class="h5 mb-0 font-weight-bold">
                            {{ active_strategies }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-brain fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">
                            真实交易账户
                        </div>
                        <div class="h5 mb-0 font-weight-bold">
                            {{ accounts|length }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-wallet fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 账户概览 -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-area"></i>
                    真实账户概览
                </h5>
            </div>
            <div class="card-body">
                {% if accounts %}
                    <canvas id="accountChart" height="100"></canvas>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-chart-area fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">暂无真实交易账户</h5>
                        <p class="text-muted mb-3">您还没有添加真实交易账户，无法显示账户概览图表。</p>
                        <p class="text-muted">请联系管理员配置交易账户。</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- 快速操作 -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt"></i>
                    快速操作
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <!-- 所有用户都可以访问的功能 -->
                    <a href="{{ url_for('demo_trading') }}" class="btn btn-outline-primary">
                        <i class="fas fa-play-circle"></i>
                        模拟交易
                    </a>
                    <a href="{{ url_for('low_risk_trading') }}" class="btn btn-outline-info">
                        <i class="fas fa-shield-alt"></i>
                        低风险交易
                        <span class="badge bg-info text-dark ms-1">SAFE</span>
                    </a>

                    <!-- VIP和管理员专享功能 -->
                    {% if current_user.user_type in ['vip', 'admin'] %}

                    <a href="{{ url_for('ai_training') }}" class="btn btn-outline-success">
                        <i class="fas fa-graduation-cap"></i>
                        训练AI策略
                        <span class="badge bg-warning ms-1">VIP</span>
                    </a>
                    {% endif %}

                    <!-- 管理员专享功能 -->
                    {% if current_user.user_type == 'admin' %}
                    <a href="{{ url_for('user_management') }}" class="btn btn-outline-danger">
                        <i class="fas fa-users"></i>
                        用户管理
                        <span class="badge bg-danger ms-1">管理员</span>
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 最近交易 -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history"></i>
                    最近真实交易
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>时间</th>
                                <th>账户</th>
                                <th>货币对</th>
                                <th>类型</th>
                                <th>数量</th>
                                <th>价格</th>
                                <th>盈亏</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% if recent_trades %}
                                {% for trade in recent_trades %}
                                <tr>
                                <td>
                                    <span class="text-primary">{{ trade.china_time.strftime('%m-%d %H:%M') }}</span>
                                    <br>
                                    <small class="text-muted">CST</small>
                                </td>
                                <td>
                                    <span class="badge bg-{{ 'primary' if trade.account_type == 'real' else 'secondary' }}">
                                        {{ trade.account_name }}
                                    </span>
                                    <br>
                                    <small class="text-muted">{{ trade.broker }}</small>
                                </td>
                                <td>
                                    <strong>{{ trade.symbol }}</strong>
                                    {% if trade.mt5_ticket %}
                                    <br>
                                    <small class="text-muted">MT5: {{ trade.mt5_ticket }}</small>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-{{ 'success' if trade.trade_type == 'buy' else 'danger' }}">
                                        {{ '买入' if trade.trade_type == 'buy' else '卖出' }}
                                    </span>
                                </td>
                                <td>{{ trade.volume }}</td>
                                <td>
                                    ${{ "%.4f"|format(trade.open_price) }}
                                    {% if trade.close_price and trade.status == 'closed' %}
                                    <br>
                                    <small class="text-muted">平仓: ${{ "%.4f"|format(trade.close_price) }}</small>
                                    {% endif %}
                                </td>
                                <td class="{{ 'profit-positive' if trade.profit > 0 else 'profit-negative' }}">
                                    ${{ "%.2f"|format(trade.profit) }}
                                </td>
                                <td>
                                    <span class="badge bg-{{ 'success' if trade.status == 'closed' else 'warning' }}">
                                        {{ '已平仓' if trade.status == 'closed' else '持仓中' }}
                                    </span>
                                    {% if trade.strategy_name %}
                                    <br>
                                    <small class="text-muted">{{ trade.strategy_name }}</small>
                                    {% endif %}
                                </td>
                                </tr>
                                {% endfor %}
                            {% else %}
                                <tr>
                                    <td colspan="8" class="text-center py-4">
                                        <i class="fas fa-history fa-2x text-muted mb-2"></i>
                                        <br>
                                        <span class="text-muted">暂无真实交易记录</span>
                                        <br>
                                        <small class="text-muted">开始真实交易后，交易记录将在此显示</small>
                                    </td>
                                </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- 市场概览 -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-globe"></i>
                    市场概览
                </h5>
            </div>
            <div class="card-body">
                <div id="marketOverview">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span><i class="fas fa-coins text-warning"></i> XAU/USD</span>
                        <span class="profit-positive">$2,634.78 (+1.45%)</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>BTC/USD</span>
                        <span class="profit-positive">$45,234.56 (+2.34%)</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>ETH/USD</span>
                        <span class="profit-positive">$3,234.78 (+1.23%)</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>EUR/USD</span>
                        <span class="profit-negative">1.0856 (-0.45%)</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>GBP/USD</span>
                        <span class="profit-positive">1.2734 (+0.12%)</span>
                    </div>
                </div>
                <div class="text-center mt-3">
                    <small class="text-muted">数据更新时间: <span id="updateTime"></span></small>
                    <br>
                    <small class="mt5-status text-muted">
                        <i class="fas fa-circle"></i> 检查MT5连接中...
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 账户图表 - 只在有真实账户时初始化
{% if accounts %}
const ctx = document.getElementById('accountChart').getContext('2d');
const accountChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
        datasets: [{
            label: '真实账户净值',
            data: [10000, 10500, 10200, 11000, 10800, {{ total_equity }}],
            borderColor: 'rgb(102, 126, 234)',
            backgroundColor: 'rgba(102, 126, 234, 0.1)',
            tension: 0.4,
            fill: true
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: false,
                ticks: {
                    callback: function(value) {
                        return '$' + value.toLocaleString();
                    }
                }
            }
        }
    }
});
{% endif %}

// 更新时间显示
function updateTime() {
    const now = new Date();
    document.getElementById('updateTime').textContent = now.toLocaleTimeString();
}

// 初始化时间显示和数据
updateTime();
updateAccountInfo();
updateTodayPnL();
updateMarketOverview();

// 更新账户信息
function updateAccountInfo() {
    fetch('/api/dashboard/account-info')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 更新总资产和净值 - 使用更精确的选择器
                const totalBalanceElement = document.querySelector('#totalBalance');
                const totalEquityElement = document.querySelector('#totalEquity');

                if (totalBalanceElement) {
                    totalBalanceElement.textContent = `$${data.total_balance.toFixed(2)}`;
                }
                if (totalEquityElement) {
                    totalEquityElement.textContent = `$${data.total_equity.toFixed(2)}`;
                }

                // 更新MT5连接状态指示
                const statusIndicator = document.querySelector('.mt5-status');
                if (statusIndicator) {
                    if (data.mt5_connected) {
                        statusIndicator.className = 'mt5-status text-success';
                        statusIndicator.innerHTML = '<i class="fas fa-circle"></i> MT5已连接 - 实时数据';
                    } else {
                        statusIndicator.className = 'mt5-status text-warning';
                        statusIndicator.innerHTML = '<i class="fas fa-circle"></i> MT5未连接 - 缓存数据';
                    }
                }

                console.log(`✅ 账户信息更新: 余额=$${data.total_balance}, 净值=$${data.total_equity}, MT5连接=${data.mt5_connected}`);
            }
        })
        .catch(error => console.error('更新账户信息失败:', error));
}

// 更新今日盈亏
function updateTodayPnL() {
    fetch('/api/dashboard/today-pnl')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const pnlElement = document.querySelector('#todayPnL');
                const tradesElement = document.querySelector('#todayTradesCount');

                if (pnlElement) {
                    // 更新盈亏金额
                    pnlElement.textContent = '$' + data.today_pnl.toFixed(2);

                    // 更新样式
                    pnlElement.className = 'h5 mb-0 font-weight-bold ' +
                        (data.today_pnl >= 0 ? 'profit-positive' : 'profit-negative');
                }

                if (tradesElement) {
                    // 更新交易笔数
                    tradesElement.textContent = '今日交易: ' + data.today_trades + ' 笔';
                }
            }
        })
        .catch(error => console.error('更新今日盈亏失败:', error));
}

// 更新市场概览数据
function updateMarketOverview() {
    fetch('/api/dashboard/market-overview')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const marketOverview = document.getElementById('marketOverview');
                if (marketOverview) {
                    let html = '';
                    data.market_data.forEach(item => {
                        const changeClass = item.is_positive ? 'profit-positive' : 'profit-negative';
                        const changeSign = item.is_positive ? '+' : '';
                        const icon = item.icon ? `<i class="${item.icon}"></i> ` : '';

                        html += `
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>${icon}${item.display_name}</span>
                                <span class="${changeClass}">${item.price} (${changeSign}${item.change_percent.toFixed(2)}%)</span>
                            </div>
                        `;
                    });
                    marketOverview.innerHTML = html;
                }

                // 更新时间
                const updateTimeElement = document.getElementById('updateTime');
                if (updateTimeElement) {
                    updateTimeElement.textContent = data.update_time;
                }
            }
        })
        .catch(error => console.error('更新市场概览失败:', error));
}

// 定时刷新市场数据、账户信息和今日盈亏
setInterval(function() {
    updateTime();
    updateAccountInfo();
    updateTodayPnL();
    updateMarketOverview();
    console.log('更新账户信息、市场数据和今日盈亏...');
}, 30000); // 每30秒更新一次
</script>
{% endblock %}
