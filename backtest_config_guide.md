# AI推理回测配置界面使用指南

## 🎉 问题解决总结

### ✅ 已解决的问题

1. **时间范围问题**：修复了选择1周却执行2个月的问题
2. **置信度配置缺失**：添加了完整的回测配置界面
3. **用户无法自定义参数**：现在用户可以完全控制所有回测参数

### 🎨 新增的配置界面

#### 1. 回测配置面板
- **初始资金**：1,000 - 1,000,000 美元
- **交易手数**：0.01 - 10 手
- **止损设置**：10 - 500 pips
- **止盈设置**：10 - 1000 pips
- **最低置信度**：0.05 - 0.99 (推荐 0.05-0.2)
- **动态止盈止损**：基于市场波动性自动调整

#### 2. 配置预设
- **保守型**：置信度20%, 止损30, 止盈60, 手数0.01
- **平衡型**：置信度10%, 止损50, 止盈100, 手数0.01
- **激进型**：置信度5%, 止损80, 止盈150, 手数0.02
- **自定义**：用户完全自定义所有参数

## 📋 使用步骤

### 1. 打开回测配置
1. 在AI推理页面选择已训练完成的模型
2. 设置推理参数（品种、时间框架、日期范围等）
3. 点击 **"回测配置"** 按钮打开配置面板

### 2. 选择配置方式

#### 方式A：使用预设配置
1. 在"配置预设"下拉框中选择：
   - **保守型**：适合新手，风险较低
   - **平衡型**：适合有经验用户，风险适中
   - **激进型**：适合专业用户，风险较高
2. 系统会自动填入对应的参数值

#### 方式B：自定义配置
1. 选择"自定义"预设
2. 手动调整各项参数：
   - **初始资金**：根据你的资金量设置
   - **交易手数**：建议从0.01开始
   - **止损/止盈**：根据策略设置比例
   - **最低置信度**：推荐0.05-0.2范围

### 3. 执行回测
1. 确认所有参数设置正确
2. 点击 **"开始回测"** 按钮
3. 等待回测完成，查看结果

## 🎯 配置建议

### 置信度阈值设置
- **0.05 (5%)**：激进策略，交易频繁，适合短期交易
- **0.1 (10%)**：平衡策略，交易适中，推荐新手使用
- **0.2 (20%)**：保守策略，交易较少，风险较低

### 止盈止损比例
- **1:1 比例**：止损50, 止盈50 (保守)
- **1:2 比例**：止损50, 止盈100 (平衡)
- **1:3 比例**：止损50, 止盈150 (激进)

### 手数设置
- **新手**：0.01手，风险最小
- **进阶**：0.02-0.05手，适度风险
- **专业**：0.1手以上，需要充足资金

### 时间范围建议
- **最少1周**：获得足够的交易样本
- **推荐2-4周**：更全面的策略验证
- **1-3个月**：长期策略测试

## 📊 结果分析

### 关键指标
- **总交易数**：交易频率指标
- **胜率**：成功交易的比例
- **总收益率**：整体盈利能力
- **最大回撤**：风险控制效果
- **盈亏比**：平均盈利/平均亏损

### 配置优化
根据回测结果调整配置：
- **交易太少**：降低置信度阈值
- **胜率太低**：提高置信度阈值
- **回撤太大**：减小手数或调整止损
- **收益太低**：优化止盈止损比例

## 🔧 技术特性

### 动态风险管理
- 系统根据市场波动性自动调整止盈止损
- 在高波动期间增加止损距离
- 在低波动期间减少止损距离

### 参数验证
- 实时验证所有参数的合理性
- 防止设置过于极端的参数
- 提供推荐范围提示

### 配置保存
- 用户的配置会在会话中保持
- 支持快速切换不同的预设
- 可以基于预设进行微调

## 💡 最佳实践

### 1. 渐进式测试
- 从保守配置开始
- 逐步调整参数
- 观察结果变化

### 2. 多配置对比
- 同时测试多种配置
- 对比不同策略效果
- 选择最适合的参数

### 3. 风险控制
- 始终设置合理的止损
- 不要使用过大的手数
- 关注最大回撤指标

### 4. 策略验证
- 使用足够长的时间范围
- 测试不同市场条件
- 验证策略的稳定性

## 🚀 升级亮点

1. **完全可配置**：用户可以控制所有回测参数
2. **智能预设**：提供多种风险级别的预设配置
3. **实时验证**：防止错误配置，提供友好提示
4. **动态优化**：基于市场条件自动调整风险参数
5. **直观界面**：清晰的布局和操作流程

现在用户可以根据自己的风险偏好和交易策略，完全自定义AI推理回测的所有参数，获得更准确、更符合个人需求的回测结果！
