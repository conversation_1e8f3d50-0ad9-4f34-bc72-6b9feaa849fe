#!/usr/bin/env python3
"""
修复AI推理模型训练的MT5连接问题
"""

import sys
import os
import time
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_mt5_installation():
    """检查MT5安装和导入"""
    print("🔍 检查MT5安装和导入")
    print("=" * 60)
    
    try:
        import MetaTrader5 as mt5
        print("✅ MetaTrader5模块导入成功")
        
        # 检查MT5版本
        version = mt5.version()
        if version:
            print(f"✅ MT5版本: {version}")
        else:
            print("⚠️ 无法获取MT5版本信息")
        
        return True
    except ImportError as e:
        print(f"❌ MetaTrader5模块导入失败: {e}")
        print("💡 解决方案: pip install MetaTrader5")
        return False
    except Exception as e:
        print(f"❌ MT5检查异常: {e}")
        return False

def check_mt5_service_connection():
    """检查MT5服务连接状态"""
    print("\n🔗 检查MT5服务连接状态")
    print("=" * 60)
    
    try:
        from services.mt5_service import mt5_service
        
        # 获取连接状态
        status = mt5_service.get_connection_status()
        
        print(f"连接状态: {'✅ 已连接' if status.get('connected') else '❌ 未连接'}")
        print(f"重连服务: {'✅ 活跃' if status.get('reconnect_service_active') else '❌ 未活跃'}")
        
        if status.get('connected'):
            print("✅ MT5服务连接正常")
            return True
        else:
            print("❌ MT5服务未连接")
            return False
            
    except Exception as e:
        print(f"❌ 检查MT5服务连接失败: {e}")
        return False

def test_mt5_direct_connection():
    """直接测试MT5连接"""
    print("\n🔌 直接测试MT5连接")
    print("=" * 60)
    
    try:
        import MetaTrader5 as mt5
        
        # 尝试初始化MT5
        print("🔄 尝试初始化MT5...")
        if not mt5.initialize():
            error = mt5.last_error()
            print(f"❌ MT5初始化失败: {error}")
            return False
        
        print("✅ MT5初始化成功")
        
        # 检查账户信息
        account_info = mt5.account_info()
        if account_info is None:
            error = mt5.last_error()
            print(f"❌ 无法获取账户信息: {error}")
            mt5.shutdown()
            return False
        
        print(f"✅ 账户信息获取成功")
        print(f"   账户: {account_info.login}")
        print(f"   服务器: {account_info.server}")
        print(f"   余额: {account_info.balance}")
        
        # 测试获取历史数据
        print("\n📊 测试获取历史数据...")
        symbol = "XAUUSD"
        timeframe = mt5.TIMEFRAME_H1
        count = 100
        
        rates = mt5.copy_rates_from_pos(symbol, timeframe, 0, count)
        if rates is not None and len(rates) > 0:
            print(f"✅ 成功获取 {len(rates)} 条历史数据")
            print(f"   品种: {symbol}")
            print(f"   时间框架: H1")
            print(f"   最新时间: {datetime.fromtimestamp(rates[-1]['time'])}")
        else:
            error = mt5.last_error()
            print(f"❌ 获取历史数据失败: {error}")
            mt5.shutdown()
            return False
        
        mt5.shutdown()
        print("✅ MT5直接连接测试通过")
        return True
        
    except Exception as e:
        print(f"❌ MT5直接连接测试失败: {e}")
        return False

def test_mt5_service_data_fetch():
    """测试MT5服务数据获取"""
    print("\n📈 测试MT5服务数据获取")
    print("=" * 60)
    
    try:
        from services.mt5_service import mt5_service
        
        # 确保连接
        if not mt5_service.is_connected():
            print("🔄 尝试连接MT5...")
            if not mt5_service.connect():
                print("❌ MT5服务连接失败")
                return False
        
        # 测试获取历史数据
        symbol = "XAUUSD"
        timeframe = "H1"
        count = 100
        
        print(f"🔍 获取历史数据: {symbol} {timeframe} {count}条")
        
        data = mt5_service.get_historical_data(
            symbol=symbol,
            timeframe=timeframe,
            count=count
        )
        
        if data and len(data) > 0:
            print(f"✅ 成功获取 {len(data)} 条历史数据")
            print(f"   最新数据时间: {data[-1]['time']}")
            print(f"   最新收盘价: {data[-1]['close']}")
            return True
        else:
            print("❌ MT5服务数据获取失败")
            return False
            
    except Exception as e:
        print(f"❌ MT5服务数据获取测试失败: {e}")
        return False

def test_training_data_preparation():
    """测试训练数据准备"""
    print("\n🧠 测试训练数据准备")
    print("=" * 60)
    
    try:
        from services.deep_learning_service import DeepLearningService
        
        dl_service = DeepLearningService()
        
        # 模拟训练配置
        config = {
            'symbol': 'XAUUSD',
            'timeframe': 'H1',
            'data_config': {
                'days': 30  # 使用较短的时间范围进行测试
            },
            'sequence_length': 30
        }
        
        print(f"🔍 测试数据准备配置:")
        print(f"   品种: {config['symbol']}")
        print(f"   时间框架: {config['timeframe']}")
        print(f"   数据天数: {config['data_config']['days']}")
        print(f"   序列长度: {config['sequence_length']}")
        
        # 测试MT5历史数据获取
        print("\n📊 测试MT5历史数据获取...")
        price_data = dl_service._get_mt5_historical_data(config, config['data_config'], config['data_config']['days'])
        
        if price_data is not None and len(price_data) > 0:
            print(f"✅ 成功获取 {len(price_data)} 条价格数据")
            return True
        else:
            print("❌ 训练数据准备失败")
            return False
            
    except Exception as e:
        print(f"❌ 训练数据准备测试失败: {e}")
        return False

def fix_mt5_connection():
    """修复MT5连接问题"""
    print("\n🔧 修复MT5连接问题")
    print("=" * 60)
    
    try:
        from services.mt5_service import mt5_service
        
        # 1. 断开现有连接
        print("1. 断开现有连接...")
        mt5_service.disconnect()
        time.sleep(2)
        
        # 2. 重新连接
        print("2. 重新连接MT5...")
        if mt5_service.connect():
            print("✅ MT5重新连接成功")
            
            # 3. 验证连接
            print("3. 验证连接状态...")
            if mt5_service.is_connected():
                print("✅ 连接验证通过")
                
                # 4. 测试数据获取
                print("4. 测试数据获取...")
                data = mt5_service.get_historical_data("XAUUSD", "H1", 10)
                if data and len(data) > 0:
                    print("✅ 数据获取测试通过")
                    return True
                else:
                    print("❌ 数据获取测试失败")
                    return False
            else:
                print("❌ 连接验证失败")
                return False
        else:
            print("❌ MT5重新连接失败")
            return False
            
    except Exception as e:
        print(f"❌ 修复MT5连接失败: {e}")
        return False

def provide_solutions():
    """提供解决方案"""
    print("\n💡 解决方案建议")
    print("=" * 60)
    
    print("🔧 如果MT5连接问题持续存在，请尝试以下解决方案:")
    print()
    print("1️⃣ 检查MT5终端:")
    print("   • 确保MT5终端正在运行")
    print("   • 确保已登录交易账户")
    print("   • 检查网络连接是否正常")
    print()
    print("2️⃣ 重启MT5终端:")
    print("   • 完全关闭MT5终端")
    print("   • 重新启动MT5终端")
    print("   • 重新登录交易账户")
    print()
    print("3️⃣ 检查数据权限:")
    print("   • 确保账户有历史数据访问权限")
    print("   • 检查XAUUSD品种是否可用")
    print("   • 尝试在MT5中手动查看历史数据")
    print()
    print("4️⃣ 重启应用程序:")
    print("   • 关闭Python应用程序")
    print("   • 重新启动应用程序")
    print("   • 重新尝试模型训练")
    print()
    print("5️⃣ 使用自动连接:")
    print("   • 在训练配置中启用自动连接")
    print("   • 系统会自动尝试连接MT5")

def main():
    print("🔧 AI推理模型训练MT5连接问题修复")
    print("=" * 80)
    print("❌ 问题: 无法获取MT5历史数据，请检查MT5连接和数据权限")
    print("=" * 80)
    
    # 诊断步骤
    print("🔍 开始诊断...")
    
    # 1. 检查MT5安装
    mt5_installed = check_mt5_installation()
    
    # 2. 检查MT5服务连接
    service_connected = check_mt5_service_connection()
    
    # 3. 直接测试MT5连接
    direct_connected = test_mt5_direct_connection()
    
    # 4. 测试MT5服务数据获取
    service_data_ok = test_mt5_service_data_fetch()
    
    # 5. 测试训练数据准备
    training_data_ok = test_training_data_preparation()
    
    print("\n" + "=" * 80)
    print("📋 诊断结果总结")
    print("=" * 80)
    
    print(f"MT5安装: {'✅ 正常' if mt5_installed else '❌ 异常'}")
    print(f"MT5服务连接: {'✅ 正常' if service_connected else '❌ 异常'}")
    print(f"MT5直接连接: {'✅ 正常' if direct_connected else '❌ 异常'}")
    print(f"MT5服务数据: {'✅ 正常' if service_data_ok else '❌ 异常'}")
    print(f"训练数据准备: {'✅ 正常' if training_data_ok else '❌ 异常'}")
    
    # 如果所有测试都通过
    if all([mt5_installed, service_connected, direct_connected, service_data_ok, training_data_ok]):
        print("\n🎉 所有诊断测试通过！")
        print("✅ MT5连接和数据获取功能正常")
        print("💡 如果训练仍然失败，可能是其他原因，请检查训练日志")
    else:
        print("\n⚠️ 发现问题，尝试修复...")
        
        # 尝试修复
        if fix_mt5_connection():
            print("\n🎉 MT5连接问题修复成功！")
            print("✅ 现在可以重新尝试模型训练")
        else:
            print("\n❌ 自动修复失败")
            provide_solutions()

if __name__ == '__main__':
    main()
