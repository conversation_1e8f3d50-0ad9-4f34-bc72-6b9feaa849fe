{% extends "base.html" %}

{% block page_title %}模拟交易{% endblock %}

{% block content %}
<div class="row">
    <!-- 账户选择 -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-play-circle"></i>
                    模拟账户
                </h5>
            </div>
            <div class="card-body">
                <!-- 账户类型选择 -->
                <div class="mb-3">
                    <label class="form-label">账户类型</label>
                    <select class="form-select" id="accountTypeSelect" onchange="loadAccountsByType()">
                        <option value="">请选择账户类型</option>
                        <option value="mt5">MT5模拟账户</option>
                        <option value="internal">内部模拟账户</option>
                    </select>
                </div>

                <!-- 具体账户选择 -->
                <div class="mb-3">
                    <label class="form-label">交易账户 <span class="text-danger">*</span></label>
                    <select class="form-select" id="accountSelect" disabled>
                        <option value="">请先选择账户类型</option>
                    </select>
                    <small class="text-muted">必须选择交易账户才能进行模拟交易</small>
                </div>

                <div class="d-flex gap-2">
                    <button class="btn btn-outline-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addAccountModal">
                        <i class="fas fa-plus"></i>
                        添加账户
                    </button>
                    <button class="btn btn-outline-info btn-sm" onclick="refreshMT5Accounts()" id="refreshMT5Btn" style="display: none;">
                        <i class="fas fa-sync"></i>
                        刷新MT5
                    </button>
                </div>

                <div id="accountInfo" class="mt-3" style="display: none;">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h6 class="text-muted">模拟余额</h6>
                                <h5 id="accountBalance">$100,000.00</h5>
                            </div>
                        </div>
                        <div class="col-6">
                            <h6 class="text-muted">净值</h6>
                            <h5 id="accountEquity">$100,000.00</h5>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 下单面板 -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line"></i>
                    <span id="tradingModeTitle">模拟下单</span>
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    这是模拟交易环境，所有交易都是虚拟的，不会产生实际资金损失。
                </div>

                <!-- AI智能交易切换 -->
                <div class="mb-4">
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="aiTradingSwitch" onchange="toggleAITrading()" checked>
                        <label class="form-check-label" for="aiTradingSwitch">
                            <i class="fas fa-robot"></i>
                            <strong>AI智能交易</strong>
                        </label>
                    </div>
                    <small class="text-muted">启用后将使用AI策略进行自动化交易</small>
                </div>

                <!-- AI策略选择区域 -->
                <div id="aiTradingSection" style="display: block;">
                    <div class="alert alert-warning">
                        <i class="fas fa-robot"></i>
                        <strong>AI智能交易模式</strong> - 系统将根据选择的AI策略自动执行交易决策
                    </div>

                    <div class="mb-3">
                        <label class="form-label">选择AI策略</label>
                        <select class="form-select" id="aiStrategySelect">
                            <option value="">正在加载AI策略...</option>
                        </select>
                        <small class="text-muted">选择用于自动交易的AI策略</small>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">交易货币对</label>
                        <select class="form-select" id="aiTradingSymbol" required>
                            <option value="">选择货币对</option>

                            <optgroup label="📈 主要外汇对">
                                <option value="EURUSD=X">EUR/USD (欧元/美元)</option>
                                <option value="GBPUSD=X">GBP/USD (英镑/美元)</option>
                                <option value="USDJPY=X">USD/JPY (美元/日元)</option>
                                <option value="AUDUSD=X">AUD/USD (澳元/美元)</option>
                                <option value="USDCAD=X">USD/CAD (美元/加元)</option>
                                <option value="USDCHF=X">USD/CHF (美元/瑞郎)</option>
                                <option value="NZDUSD=X">NZD/USD (纽元/美元)</option>
                            </optgroup>

                            <optgroup label="🔄 交叉外汇对">
                                <option value="EURJPY=X">EUR/JPY (欧元/日元)</option>
                                <option value="GBPJPY=X">GBP/JPY (英镑/日元)</option>
                                <option value="EURGBP=X">EUR/GBP (欧元/英镑)</option>
                                <option value="AUDCAD=X">AUD/CAD (澳元/加元)</option>
                            </optgroup>

                            <optgroup label="🥇 贵金属">
                                <option value="XAUUSD">XAU/USD (黄金/美元)</option>
                                <option value="GC=F">黄金期货 (Gold Futures)</option>
                                <option value="GLD">黄金ETF (SPDR Gold Trust)</option>
                                <option value="XAGUSD">XAG/USD (白银/美元)</option>
                                <option value="SI=F">白银期货 (Silver Futures)</option>
                            </optgroup>

                            <optgroup label="🛢️ 大宗商品">
                                <option value="CL=F">原油期货 (WTI Crude Oil)</option>
                                <option value="BZ=F">布伦特原油期货</option>
                                <option value="NG=F">天然气期货</option>
                            </optgroup>

                            <optgroup label="₿ 加密货币">
                                <option value="BTC-USD">BTC/USD (比特币/美元)</option>
                                <option value="ETH-USD">ETH/USD (以太坊/美元)</option>
                            </optgroup>
                        </select>
                        <small class="text-muted">
                            选择AI交易的货币对
                            <button type="button" class="btn btn-link btn-sm p-0 ms-2" onclick="setAsDefaultSymbol()">
                                <i class="fas fa-star"></i> 设为默认
                            </button>
                        </small>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">风险等级</label>
                        <select class="form-select" id="riskLevel" onchange="updateStopLossTakeProfitByRisk()">
                            <option value="conservative">保守型 - 止损1% 止盈1.5%</option>
                            <option value="moderate" selected>稳健型 - 止损1% 止盈2%</option>
                            <option value="aggressive">激进型 - 止损1.5% 止盈3%</option>
                        </select>
                        <small class="text-muted">AI将根据风险等级自动调整止损止盈比例</small>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">交易时间段</label>
                        <select class="form-select" id="tradingTimeSlot">
                            <option value="09:00-12:00" selected>上午时段 (09:00-12:00)</option>
                            <option value="15:00-18:00">下午时段 (15:00-18:00)</option>
                            <option value="09:00-18:00">全天时段 (09:00-18:00)</option>
                            <option value="08:00-23:00">扩展时段 (08:00-23:00)</option>
                        </select>
                        <small class="text-muted">
                            <i class="fas fa-clock"></i>
                            AI只在指定时间段内进行交易，超出时间段将自动停止
                        </small>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">单笔交易金额上限</label>
                        <div class="input-group">
                            <span class="input-group-text">$</span>
                            <input type="number" class="form-control" id="maxTradeAmount" value="1000" min="100" step="100">
                        </div>
                        <small class="text-muted">单次交易的最大金额限制</small>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">
                            <i class="fas fa-chart-line"></i>
                            单次运行交易次数限制
                        </label>
                        <select class="form-select" id="maxTradeCount">
                            <option value="10">10次交易后停止</option>
                            <option value="20">20次交易后停止</option>
                            <option value="30" selected>30次交易后停止</option>
                            <option value="unlimited">不限制交易次数</option>
                        </select>
                        <small class="text-muted">
                            <i class="fas fa-info-circle"></i>
                            AI达到指定交易次数后将自动停止，防止过度交易
                        </small>
                    </div>

                    <!-- AI智能止盈止损设置 -->
                    <div class="card border-info mb-3">
                        <div class="card-header bg-light">
                            <div class="d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">
                                    <i class="fas fa-shield-alt text-info"></i>
                                    AI智能止盈止损
                                </h6>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="aiEnableStopLossTakeProfit" checked onchange="toggleAIStopLossTakeProfit()">
                                    <label class="form-check-label" for="aiEnableStopLossTakeProfit">启用</label>
                                </div>
                            </div>
                        </div>
                        <div class="card-body" id="aiStopLossTakeProfitSettings">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">
                                        <i class="fas fa-arrow-down text-danger"></i>
                                        智能止损比例 (%)
                                    </label>
                                    <input type="number" class="form-control" id="aiStopLossPercent" value="1.0" min="0.5" max="10" step="0.1">
                                    <small class="text-muted">保守型:1%, 稳健型:1%, 激进型:1.5%</small>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">
                                        <i class="fas fa-arrow-up text-success"></i>
                                        智能止盈比例 (%)
                                    </label>
                                    <input type="number" class="form-control" id="aiTakeProfitPercent" value="2.0" min="1" max="20" step="0.1">
                                    <small class="text-muted">保守型:1.5%, 稳健型:2%, 激进型:3%</small>
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="aiAutoAdjustStopLoss" checked>
                                    <label class="form-check-label" for="aiAutoAdjustStopLoss">
                                        <i class="fas fa-robot"></i>
                                        启用AI动态调整
                                    </label>
                                </div>
                                <small class="text-muted">AI将根据市场波动率、趋势强度和货币对特性动态调整止损止盈水平</small>
                            </div>

                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i>
                                <strong>AI智能调整说明：</strong>
                                <ul class="mb-0 mt-2">
                                    <li>高波动率市场：自动放宽止损，缩小止盈</li>
                                    <li>低波动率市场：自动收紧止损，扩大止盈</li>
                                    <li>趋势市场：根据趋势方向调整止盈目标</li>
                                    <li>震荡市场：采用更保守的止损止盈策略</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-6">
                            <button type="button" class="btn btn-success w-100" id="startAITrading" onclick="startAITrading()">
                                <i class="fas fa-play"></i>
                                开始AI交易
                            </button>
                        </div>
                        <div class="col-6">
                            <button type="button" class="btn btn-danger w-100" id="stopAITrading" onclick="stopAITrading()" style="display: none;">
                                <i class="fas fa-stop"></i>
                                停止AI交易
                            </button>
                        </div>
                    </div>

                    <!-- AI交易状态 -->
                    <div id="aiTradingStatus" style="display: none;">
                        <div class="alert alert-success">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="fas fa-robot"></i>
                                    <strong>AI交易运行中...</strong>
                                </div>
                                <div class="spinner-border spinner-border-sm" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                            </div>
                            <small class="d-block mt-2">
                                策略: <span id="currentAIStrategy">-</span> |
                                运行时间: <span id="aiRunningTime">00:00:00</span>
                            </small>
                            <small class="d-block mt-1">
                                <i class="fas fa-clock"></i>
                                中国标准时间: <span id="chinaTime">--:--:--</span> |
                                交易货币对: <span id="currentTradingSymbol">-</span>
                            </small>
                            <small class="d-block mt-1">
                                <i class="fas fa-chart-line"></i>
                                已执行交易: <span id="aiTradeCount" class="fw-bold text-primary">0</span> 次 |
                                交易限制: <span id="aiTradeLimit" class="fw-bold text-info">20</span> 次
                                <span id="aiTradeLimitWarning" class="text-warning ms-2" style="display: none;">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    即将达到限制
                                </span>
                            </small>
                        </div>
                    </div>
                </div>

                <!-- 手动交易表单 -->
                <div id="manualTradingSection">
                    <form id="orderForm">
                    <div class="mb-3">
                        <label class="form-label">货币对</label>
                        <select class="form-select" id="symbolSelect" required>
                            <option value="">选择货币对</option>
                            <option value="EURUSD">EUR/USD</option>
                            <option value="GBPUSD">GBP/USD</option>
                            <option value="USDJPY">USD/JPY</option>
                            <option value="AUDUSD">AUD/USD</option>
                            <option value="USDCAD">USD/CAD</option>
                            <option value="XAUUSD">XAU/USD</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">订单类型</label>
                        <select class="form-select" id="orderType" required>
                            <option value="market">市价单</option>
                            <option value="limit">限价单</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">交易方向</label>
                        <div class="btn-group w-100" role="group">
                            <input type="radio" class="btn-check" name="orderSide" id="buyRadio" value="buy" checked>
                            <label class="btn btn-outline-success" for="buyRadio">买入</label>

                            <input type="radio" class="btn-check" name="orderSide" id="sellRadio" value="sell">
                            <label class="btn btn-outline-danger" for="sellRadio">卖出</label>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">数量</label>
                        <input type="number" class="form-control" id="orderAmount" step="0.001" required>
                    </div>

                    <div class="mb-3" id="priceGroup" style="display: none;">
                        <label class="form-label">价格</label>
                        <input type="number" class="form-control" id="orderPrice" step="0.01">
                    </div>

                    <!-- 智能止盈止损设置 -->
                    <div class="card border-info mb-3">
                        <div class="card-header bg-info text-white py-2">
                            <div class="d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">
                                    <i class="fas fa-shield-alt"></i> 智能止盈止损
                                </h6>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="enableStopLossTakeProfit" checked>
                                    <label class="form-check-label text-white" for="enableStopLossTakeProfit">启用</label>
                                </div>
                            </div>
                        </div>
                        <div class="card-body py-2">
                            <div class="row">
                                <div class="col-md-4">
                                    <label for="stopLossPercent" class="form-label small">
                                        <i class="fas fa-arrow-down text-danger"></i> 止损比例 (%)
                                    </label>
                                    <input type="number" class="form-control form-control-sm" id="stopLossPercent"
                                           min="0.5" max="10" step="0.1" value="2.0">
                                    <small class="text-muted">建议: 1-5%</small>
                                </div>
                                <div class="col-md-4">
                                    <label for="takeProfitPercent" class="form-label small">
                                        <i class="fas fa-arrow-up text-success"></i> 止盈比例 (%)
                                    </label>
                                    <input type="number" class="form-control form-control-sm" id="takeProfitPercent"
                                           min="1" max="20" step="0.1" value="5.0">
                                    <small class="text-muted">建议: 3-10%</small>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check form-switch mt-4">
                                        <input class="form-check-input" type="checkbox" id="autoAdjustStopLoss" checked>
                                        <label class="form-check-label small" for="autoAdjustStopLoss">
                                            <i class="fas fa-robot"></i> AI智能调整
                                        </label>
                                    </div>
                                    <small class="text-muted">根据市场波动自动调整</small>
                                </div>
                            </div>

                            <!-- 手动设置 -->
                            <div class="row mt-2" id="manualStopLossSettings" style="display: none;">
                                <div class="col-md-6">
                                    <label class="form-label small">手动止损价格</label>
                                    <input type="number" class="form-control form-control-sm" id="stopLoss" step="0.00001">
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label small">手动止盈价格</label>
                                    <input type="number" class="form-control form-control-sm" id="takeProfit" step="0.00001">
                                </div>
                            </div>

                            <div class="text-center mt-2">
                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="toggleManualStopLoss()">
                                    <i class="fas fa-cog"></i> 手动设置
                                </button>
                            </div>
                        </div>
                    </div>

                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-paper-plane"></i>
                            提交模拟订单
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 持仓和订单 -->
    <div class="col-lg-8">
        <!-- 保证金计算信息 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calculator"></i>
                    保证金计算参考
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <!-- 风险等级对应手数 -->
                    <div class="col-md-6">
                        <h6 class="text-primary mb-3">
                            <i class="fas fa-chart-bar"></i>
                            XAUUSD风险等级参考
                        </h6>
                        <div class="table-responsive">
                            <table class="table table-sm table-bordered">
                                <thead class="table-light">
                                    <tr>
                                        <th>风险等级</th>
                                        <th>推荐手数</th>
                                        <th>保证金需求</th>
                                        <th>适合用户</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr class="table-success">
                                        <td><span class="badge bg-success">保守型</span></td>
                                        <td>0.05手</td>
                                        <td>$165</td>
                                        <td>小额账户</td>
                                    </tr>
                                    <tr class="table-warning">
                                        <td><span class="badge bg-warning">稳健型</span></td>
                                        <td>0.08手</td>
                                        <td>$264</td>
                                        <td>中等账户</td>
                                    </tr>
                                    <tr class="table-danger">
                                        <td><span class="badge bg-danger">激进型</span></td>
                                        <td>0.10手</td>
                                        <td>$330</td>
                                        <td>大额账户</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- 保证金计算公式 -->
                    <div class="col-md-6">
                        <h6 class="text-info mb-3">
                            <i class="fas fa-formula"></i>
                            保证金计算公式
                        </h6>
                        <div class="bg-light p-3 rounded">
                            <div class="mb-3">
                                <strong>合约规格:</strong> 1手 = 100盎司黄金<br>
                                <strong>当前价格:</strong> $3,300/盎司<br>
                                <strong>杠杆比例:</strong> 1:100
                            </div>

                            <div class="border-top pt-3">
                                <h6 class="text-success">计算示例 (0.1手 XAUUSD):</h6>
                                <div class="font-monospace small">
                                    <div>合约价值 = 0.1手 × 100盎司 × $3,300</div>
                                    <div class="text-muted">= $33,000</div>
                                    <div class="mt-2">保证金 = $33,000 ÷ 100</div>
                                    <div class="text-success fw-bold">= $330</div>
                                </div>
                            </div>

                            <div class="alert alert-info mt-3 mb-0">
                                <small>
                                    <i class="fas fa-info-circle"></i>
                                    <strong>模拟提示:</strong> 模拟交易无需真实保证金，但了解保证金计算有助于真实交易时的资金管理。
                                </small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 实时保证金计算器 -->
                <div class="row mt-4">
                    <div class="col-12">
                        <h6 class="text-secondary mb-3">
                            <i class="fas fa-calculator"></i>
                            实时保证金计算器
                        </h6>
                        <div class="row">
                            <div class="col-md-3">
                                <label class="form-label small">交易品种</label>
                                <select class="form-select form-select-sm" id="marginSymbol">
                                    <option value="XAUUSD">XAUUSD (黄金)</option>
                                    <option value="EURUSD">EURUSD (欧美)</option>
                                    <option value="GBPUSD">GBPUSD (镑美)</option>
                                    <option value="USDJPY">USDJPY (美日)</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label small">手数</label>
                                <input type="number" class="form-control form-control-sm" id="marginLots"
                                       value="0.1" min="0.01" max="10" step="0.01" onchange="calculateMargin()">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label small">价格</label>
                                <input type="number" class="form-control form-control-sm" id="marginPrice"
                                       value="3300" step="0.01" onchange="calculateMargin()">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label small">杠杆</label>
                                <select class="form-select form-select-sm" id="marginLeverage" onchange="calculateMargin()">
                                    <option value="50">1:50</option>
                                    <option value="100" selected>1:100</option>
                                    <option value="200">1:200</option>
                                    <option value="500">1:500</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label small">所需保证金</label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="text" class="form-control form-control-sm" id="marginResult" readonly>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 实时价格图表 -->
        <div class="card mb-4">
            <div class="card-header chart-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0 text-dark">
                        <i class="fas fa-chart-line text-primary"></i>
                        实时价格图表
                    </h5>
                    <div class="d-flex gap-2">
                        <!-- 交易品种选择 -->
                        <select class="form-select form-select-sm chart-symbol-select" id="chartSymbol" style="width: 120px;" onchange="updateChart()">
                            <option value="XAUUSD">XAUUSD</option>
                            <option value="EURUSD">EURUSD</option>
                            <option value="GBPUSD">GBPUSD</option>
                            <option value="USDJPY">USDJPY</option>
                            <option value="USDCHF">USDCHF</option>
                            <option value="AUDUSD">AUDUSD</option>
                        </select>

                        <!-- 时间周期选择 -->
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-dark btn-sm timeframe-btn active" data-timeframe="M1" onclick="setTimeframe('M1')">1分</button>
                            <button type="button" class="btn btn-outline-dark btn-sm timeframe-btn" data-timeframe="M5" onclick="setTimeframe('M5')">5分</button>
                            <button type="button" class="btn btn-outline-dark btn-sm timeframe-btn" data-timeframe="M15" onclick="setTimeframe('M15')">15分</button>
                            <button type="button" class="btn btn-outline-dark btn-sm timeframe-btn" data-timeframe="H1" onclick="setTimeframe('H1')">1时</button>
                            <button type="button" class="btn btn-outline-dark btn-sm timeframe-btn" data-timeframe="H4" onclick="setTimeframe('H4')">4时</button>
                            <button type="button" class="btn btn-outline-dark btn-sm timeframe-btn" data-timeframe="D1" onclick="setTimeframe('D1')">日线</button>
                        </div>

                        <!-- 图表控制按钮 -->
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-dark btn-sm chart-control-btn" onclick="toggleFullscreen()" title="全屏">
                                <i class="fas fa-expand"></i>
                            </button>
                            <button type="button" class="btn btn-outline-dark btn-sm chart-control-btn" onclick="saveChart()" title="保存图表">
                                <i class="fas fa-download"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body p-2">
                <style>
                    /* 图表头部样式优化 */
                    .chart-header {
                        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                        border-bottom: 2px solid #dee2e6;
                    }

                    /* 时间周期按钮样式 */
                    .timeframe-btn {
                        border-color: #6c757d !important;
                        color: #495057 !important;
                        background-color: transparent !important;
                        transition: all 0.2s ease;
                    }

                    .timeframe-btn:hover {
                        background-color: #6c757d !important;
                        color: white !important;
                        transform: translateY(-1px);
                    }

                    .timeframe-btn.active {
                        background-color: #495057 !important;
                        color: white !important;
                        border-color: #495057 !important;
                        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                    }

                    /* 图表控制按钮样式 */
                    .chart-control-btn {
                        border-color: #6c757d !important;
                        color: #495057 !important;
                        background-color: transparent !important;
                        transition: all 0.2s ease;
                    }

                    .chart-control-btn:hover {
                        background-color: #6c757d !important;
                        color: white !important;
                        transform: translateY(-1px);
                    }

                    /* 品种选择下拉框样式 */
                    .chart-symbol-select {
                        border-color: #6c757d !important;
                        color: #495057 !important;
                        background-color: white !important;
                    }

                    .chart-symbol-select:focus {
                        border-color: #495057 !important;
                        box-shadow: 0 0 0 0.2rem rgba(73, 80, 87, 0.25) !important;
                    }

                    /* 技术指标面板样式 */
                    .indicator-panel {
                        background-color: #f8f9fa;
                        border-radius: 8px;
                        border: 1px solid #e9ecef;
                    }

                    .indicator-section h6 {
                        color: #495057 !important;
                        font-weight: 600;
                    }

                    /* 技术分析摘要样式 */
                    .analysis-summary {
                        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
                        border: 1px solid #e9ecef;
                        border-radius: 6px;
                    }

                    /* 关键价位样式 */
                    .key-levels {
                        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
                        border: 1px solid #e9ecef;
                        border-radius: 6px;
                    }

                    /* 实时数据样式 */
                    .realtime-data {
                        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
                        border: 1px solid #e9ecef;
                        border-radius: 6px;
                    }
                </style>
                <div class="row">
                    <!-- 主图表区域 -->
                    <div class="col-lg-9">
                        <div id="tradingChart" style="height: 400px; width: 100%;"></div>
                    </div>

                    <!-- 技术指标控制面板 -->
                    <div class="col-lg-3">
                        <div class="h-100 indicator-panel p-3">
                            <!-- 技术指标选择 -->
                            <div class="mb-3 indicator-section">
                                <h6 class="text-dark mb-2">
                                    <i class="fas fa-chart-bar text-success"></i>
                                    技术指标
                                </h6>
                                <div class="form-check form-check-sm">
                                    <input class="form-check-input" type="checkbox" id="showMA5" checked onchange="updateChart()">
                                    <label class="form-check-label small" for="showMA5">MA5</label>
                                </div>
                                <div class="form-check form-check-sm">
                                    <input class="form-check-input" type="checkbox" id="showMA20" checked onchange="updateChart()">
                                    <label class="form-check-label small" for="showMA20">MA20</label>
                                </div>
                                <div class="form-check form-check-sm">
                                    <input class="form-check-input" type="checkbox" id="showMA50" onchange="updateChart()">
                                    <label class="form-check-label small" for="showMA50">MA50</label>
                                </div>
                                <div class="form-check form-check-sm">
                                    <input class="form-check-input" type="checkbox" id="showBollinger" onchange="updateChart()">
                                    <label class="form-check-label small" for="showBollinger">布林带</label>
                                </div>
                                <div class="form-check form-check-sm">
                                    <input class="form-check-input" type="checkbox" id="showVolume" onchange="updateChart()">
                                    <label class="form-check-label small" for="showVolume">成交量</label>
                                </div>
                            </div>

                            <!-- 振荡器指标 -->
                            <div class="mb-3">
                                <h6 class="text-info mb-2">
                                    <i class="fas fa-wave-square"></i>
                                    振荡器
                                </h6>
                                <div class="form-check form-check-sm">
                                    <input class="form-check-input" type="checkbox" id="showRSI" onchange="updateChart()">
                                    <label class="form-check-label small" for="showRSI">RSI</label>
                                </div>
                                <div class="form-check form-check-sm">
                                    <input class="form-check-input" type="checkbox" id="showMACD" onchange="updateChart()">
                                    <label class="form-check-label small" for="showMACD">MACD</label>
                                </div>
                                <div class="form-check form-check-sm">
                                    <input class="form-check-input" type="checkbox" id="showStoch" onchange="updateChart()">
                                    <label class="form-check-label small" for="showStoch">随机指标</label>
                                </div>
                            </div>

                            <!-- 技术分析摘要 -->
                            <div class="mb-3">
                                <h6 class="text-success mb-2">
                                    <i class="fas fa-brain"></i>
                                    技术分析摘要
                                </h6>
                                <div class="bg-light p-2 rounded small">
                                    <div class="d-flex justify-content-between mb-1">
                                        <span>移动平均:</span>
                                        <span class="badge bg-success" id="maTrend">买入</span>
                                    </div>
                                    <div class="d-flex justify-content-between mb-1">
                                        <span>技术指标:</span>
                                        <span class="badge bg-warning" id="indicatorTrend">中性</span>
                                    </div>
                                    <div class="d-flex justify-content-between mb-1">
                                        <span>总体评价:</span>
                                        <span class="badge bg-success" id="overallTrend">买入</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 关键价位 -->
                            <div class="mb-3">
                                <h6 class="text-warning mb-2">
                                    <i class="fas fa-crosshairs"></i>
                                    关键价位
                                </h6>
                                <div class="bg-light p-2 rounded small">
                                    <div class="d-flex justify-content-between mb-1">
                                        <span>阻力位:</span>
                                        <span class="text-danger fw-bold" id="resistanceLevel">3350.25</span>
                                    </div>
                                    <div class="d-flex justify-content-between mb-1">
                                        <span>当前价:</span>
                                        <span class="fw-bold" id="currentPrice">3305.55</span>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <span>支撑位:</span>
                                        <span class="text-success fw-bold" id="supportLevel">3260.80</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 实时数据 -->
                            <div class="mb-3">
                                <h6 class="text-secondary mb-2">
                                    <i class="fas fa-tachometer-alt"></i>
                                    实时数据
                                </h6>
                                <div class="bg-light p-2 rounded small">
                                    <div class="d-flex justify-content-between mb-1">
                                        <span>涨跌:</span>
                                        <span class="text-success" id="priceChange">+12.45</span>
                                    </div>
                                    <div class="d-flex justify-content-between mb-1">
                                        <span>涨跌幅:</span>
                                        <span class="text-success" id="priceChangePercent">+0.38%</span>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <span>更新时间:</span>
                                        <span id="lastUpdate">--:--:--</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 当前持仓 -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-coins"></i>
                    模拟持仓
                </h5>
                <button class="btn btn-outline-danger btn-sm" onclick="clearAllPositions(event)" title="清空所有持仓">
                    <i class="fas fa-trash"></i>
                    清空持仓
                </button>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="positionsTable">
                        <thead>
                            <tr>
                                <th>账户</th>
                                <th>货币对</th>
                                <th>方向</th>
                                <th>数量</th>
                                <th>开仓价</th>
                                <th>当前价</th>
                                <th>浮动盈亏</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="8" class="text-center text-muted">暂无持仓</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 交易历史 -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history"></i>
                    模拟交易历史
                </h5>
                <div>
                    <button class="btn btn-outline-warning btn-sm me-2" onclick="clearTradeHistory(event)" title="清空交易历史">
                        <i class="fas fa-broom"></i>
                        清空历史
                    </button>
                    <button class="btn btn-outline-danger btn-sm" onclick="clearAllData(event)" title="清空所有数据">
                        <i class="fas fa-trash-alt"></i>
                        清空所有
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="historyTable">
                        <thead>
                            <tr>
                                <th>时间</th>
                                <th>货币对</th>
                                <th>方向</th>
                                <th>数量</th>
                                <th>开仓价</th>
                                <th>平仓价</th>
                                <th>盈亏</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="8" class="text-center text-muted">暂无交易记录</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 添加模拟账户模态框 -->
<div class="modal fade" id="addAccountModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">添加模拟账户</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addAccountForm">
                    <div class="mb-3">
                        <label class="form-label">账户名称</label>
                        <input type="text" class="form-control" name="account_name" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">模拟交易所</label>
                        <select class="form-select" name="broker" required>
                            <option value="">选择交易所</option>
                            <option value="binance">Binance (模拟)</option>
                            <option value="okx">OKX (模拟)</option>
                            <option value="huobi">Huobi (模拟)</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">初始模拟资金</label>
                        <input type="number" class="form-control" name="balance" step="0.01" value="100000" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="addAccount()">添加</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentAccountId = null; // 必须选择账户
let currentAccountType = null; // 当前账户类型

// 页面加载时恢复状态
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔄 模拟交易页面加载，恢复状态...');
    restorePageState();
    initializeStopLossTakeProfit();
    initializeAIStopLossTakeProfit();
    initializeTradeCountLimit();
});

// 恢复页面状态
function restorePageState() {
    if (!window.GlobalStateManager) {
        console.warn('全局状态管理器未加载');
        return;
    }

    const demoState = GlobalStateManager.getDemoTradingState();
    console.log('恢复模拟交易状态:', demoState);

    // 恢复账户选择
    if (demoState.selectedAccountType && demoState.selectedAccountId) {
        console.log(`恢复账户: ${demoState.selectedAccountType} - ${demoState.selectedAccountId}`);

        // 设置账户类型
        const accountTypeSelect = document.getElementById('accountTypeSelect');
        if (accountTypeSelect) {
            accountTypeSelect.value = demoState.selectedAccountType;
            loadAccountsByType(); // 加载对应类型的账户

            // 等待账户加载完成后选择账户
            setTimeout(() => {
                const accountSelect = document.getElementById('accountSelect');
                if (accountSelect) {
                    accountSelect.value = demoState.selectedAccountId;
                    // 触发账户选择事件
                    accountSelect.dispatchEvent(new Event('change'));
                }
            }, 1000);
        }
    }

    // 恢复AI交易状态 - 延迟处理，等待AI交易管理器初始化
    setTimeout(() => {
        console.log('🔄 检查AI交易状态恢复...');
        restoreAITradingStateFromManager();
    }, 2500); // 在AI交易管理器初始化后执行

    // 恢复止盈止损设置
    const stopLossSettings = GlobalStateManager.getStopLossTakeProfitSettings();
    if (stopLossSettings) {
        restoreStopLossTakeProfitSettings(stopLossSettings);
    }
}

// 保存账户选择状态
function saveAccountState(accountType, accountId, accountInfo) {
    if (window.GlobalStateManager) {
        GlobalStateManager.setDemoTradingAccount(accountType, accountId, accountInfo);
        console.log('已保存账户状态:', { accountType, accountId });
    }
}

// 初始化止盈止损功能
function initializeStopLossTakeProfit() {
    // 监听启用/禁用开关
    const enableSwitch = document.getElementById('enableStopLossTakeProfit');
    if (enableSwitch) {
        enableSwitch.addEventListener('change', function() {
            const enabled = this.checked;
            updateStopLossTakeProfitUI(enabled);
            saveStopLossTakeProfitSettings();
        });
    }

    // 监听参数变化
    ['stopLossPercent', 'takeProfitPercent', 'autoAdjustStopLoss'].forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener('change', saveStopLossTakeProfitSettings);
        }
    });

    // 初始化UI状态
    updateStopLossTakeProfitUI(enableSwitch?.checked || true);
}

// 更新止盈止损UI状态
function updateStopLossTakeProfitUI(enabled) {
    const controls = ['stopLossPercent', 'takeProfitPercent', 'autoAdjustStopLoss'];
    controls.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.disabled = !enabled;
        }
    });
}

// 保存止盈止损设置
function saveStopLossTakeProfitSettings() {
    if (!window.GlobalStateManager) return;

    const settings = {
        enabled: document.getElementById('enableStopLossTakeProfit')?.checked || false,
        stopLossPercent: parseFloat(document.getElementById('stopLossPercent')?.value || 2.0),
        takeProfitPercent: parseFloat(document.getElementById('takeProfitPercent')?.value || 5.0),
        autoAdjust: document.getElementById('autoAdjustStopLoss')?.checked || false
    };

    GlobalStateManager.setStopLossTakeProfitSettings(settings);
    console.log('已保存止盈止损设置:', settings);
}

// 恢复止盈止损设置
function restoreStopLossTakeProfitSettings(settings) {
    if (settings.enabled !== undefined) {
        const enableSwitch = document.getElementById('enableStopLossTakeProfit');
        if (enableSwitch) enableSwitch.checked = settings.enabled;
    }

    if (settings.stopLossPercent !== undefined) {
        const stopLossInput = document.getElementById('stopLossPercent');
        if (stopLossInput) stopLossInput.value = settings.stopLossPercent;
    }

    if (settings.takeProfitPercent !== undefined) {
        const takeProfitInput = document.getElementById('takeProfitPercent');
        if (takeProfitInput) takeProfitInput.value = settings.takeProfitPercent;
    }

    if (settings.autoAdjust !== undefined) {
        const autoAdjustSwitch = document.getElementById('autoAdjustStopLoss');
        if (autoAdjustSwitch) autoAdjustSwitch.checked = settings.autoAdjust;
    }

    updateStopLossTakeProfitUI(settings.enabled);
    console.log('已恢复止盈止损设置:', settings);
}

// 切换手动止盈止损设置
function toggleManualStopLoss() {
    const manualSettings = document.getElementById('manualStopLossSettings');
    if (manualSettings) {
        const isVisible = manualSettings.style.display !== 'none';
        manualSettings.style.display = isVisible ? 'none' : 'block';
    }
}

// 切换AI智能止盈止损设置
function toggleAIStopLossTakeProfit() {
    const enabled = document.getElementById('aiEnableStopLossTakeProfit').checked;
    const settings = document.getElementById('aiStopLossTakeProfitSettings');

    if (settings) {
        if (enabled) {
            settings.style.display = 'block';
            settings.style.opacity = '1';
        } else {
            settings.style.opacity = '0.5';
            // 不完全隐藏，只是变灰显示
        }
    }

    // 保存AI止盈止损设置
    saveAIStopLossTakeProfitSettings();
}

// 保存AI止盈止损设置
function saveAIStopLossTakeProfitSettings() {
    if (!window.GlobalStateManager) return;

    const settings = {
        enabled: document.getElementById('aiEnableStopLossTakeProfit')?.checked || false,
        stopLossPercent: parseFloat(document.getElementById('aiStopLossPercent')?.value || 1.0),
        takeProfitPercent: parseFloat(document.getElementById('aiTakeProfitPercent')?.value || 2.0),
        autoAdjust: document.getElementById('aiAutoAdjustStopLoss')?.checked || false
    };

    window.GlobalStateManager.setStopLossTakeProfitSettings(settings);
    console.log('已保存AI止盈止损设置:', settings);
}

// 获取AI智能止盈止损设置
function getAIStopLossTakeProfitSettings() {
    const enabled = document.getElementById('aiEnableStopLossTakeProfit')?.checked || false;

    if (!enabled) {
        return {
            enabled: false,
            stopLoss: null,
            takeProfit: null
        };
    }

    const stopLossPercent = parseFloat(document.getElementById('aiStopLossPercent')?.value || 1.0);
    const takeProfitPercent = parseFloat(document.getElementById('aiTakeProfitPercent')?.value || 2.0);
    const autoAdjust = document.getElementById('aiAutoAdjustStopLoss')?.checked || false;

    // 这里可以添加AI智能计算逻辑
    // 暂时返回基础设置，后续可以集成AI算法
    return {
        enabled: true,
        stopLossPercent,
        takeProfitPercent,
        autoAdjust,
        // 实际的止损止盈价格需要根据当前价格计算
        stopLoss: null, // 将在下单时计算
        takeProfit: null // 将在下单时计算
    };
}

// 初始化AI智能止盈止损设置
function initializeAIStopLossTakeProfit() {
    console.log('🤖 初始化AI智能止盈止损设置...');

    // 从全局状态管理器恢复设置
    if (window.GlobalStateManager) {
        const settings = window.GlobalStateManager.getStopLossTakeProfitSettings();

        if (settings) {
            // 恢复AI止盈止损设置
            const enableCheckbox = document.getElementById('aiEnableStopLossTakeProfit');
            const stopLossInput = document.getElementById('aiStopLossPercent');
            const takeProfitInput = document.getElementById('aiTakeProfitPercent');
            const autoAdjustCheckbox = document.getElementById('aiAutoAdjustStopLoss');

            if (enableCheckbox) enableCheckbox.checked = settings.enabled || true;
            if (stopLossInput) stopLossInput.value = settings.stopLossPercent || 2.0;
            if (takeProfitInput) takeProfitInput.value = settings.takeProfitPercent || 5.0;
            if (autoAdjustCheckbox) autoAdjustCheckbox.checked = settings.autoAdjust || true;

            // 更新UI显示
            toggleAIStopLossTakeProfit();

            console.log('✅ AI智能止盈止损设置已恢复:', settings);
        }
    }

    // 添加事件监听器
    const inputs = ['aiStopLossPercent', 'aiTakeProfitPercent'];
    inputs.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener('change', saveAIStopLossTakeProfitSettings);
        }
    });

    const checkboxes = ['aiEnableStopLossTakeProfit', 'aiAutoAdjustStopLoss'];
    checkboxes.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener('change', saveAIStopLossTakeProfitSettings);
        }
    });

    console.log('✅ AI智能止盈止损初始化完成');
}

// 初始化交易次数限制功能
function initializeTradeCountLimit() {
    console.log('📊 初始化交易次数限制功能...');

    // 从全局状态管理器恢复设置
    if (window.GlobalStateManager) {
        const demoState = window.GlobalStateManager.getDemoTradingState();

        if (demoState && demoState.maxTradeCount) {
            const maxTradeCountSelect = document.getElementById('maxTradeCount');
            if (maxTradeCountSelect) {
                maxTradeCountSelect.value = demoState.maxTradeCount;
                console.log('✅ 恢复交易次数限制设置:', demoState.maxTradeCount);
            }
        }
    }

    // 添加事件监听器
    const maxTradeCountSelect = document.getElementById('maxTradeCount');
    if (maxTradeCountSelect) {
        maxTradeCountSelect.addEventListener('change', function() {
            const selectedValue = this.value;
            console.log('交易次数限制已更改为:', selectedValue);

            // 保存到全局状态管理器
            if (window.GlobalStateManager) {
                window.GlobalStateManager.setState('demoTrading.maxTradeCount', selectedValue);
            }

            // 更新显示
            updateTradeCountLimitDisplay();
        });
    }

    // 初始化显示
    updateTradeCountLimitDisplay();

    console.log('✅ 交易次数限制功能初始化完成');
}

// 更新交易次数限制显示
function updateTradeCountLimitDisplay() {
    const maxTradeCountSelect = document.getElementById('maxTradeCount');
    const aiTradeLimitElement = document.getElementById('aiTradeLimit');

    if (maxTradeCountSelect && aiTradeLimitElement) {
        const selectedValue = maxTradeCountSelect.value;

        if (selectedValue === 'unlimited') {
            aiTradeLimitElement.textContent = '不限制';
            aiTradeLimitElement.className = 'fw-bold text-success';
        } else {
            aiTradeLimitElement.textContent = selectedValue;
            aiTradeLimitElement.className = 'fw-bold text-info';
        }
    }
}

// 获取交易次数限制设置
function getTradeCountLimit() {
    const maxTradeCountSelect = document.getElementById('maxTradeCount');
    if (maxTradeCountSelect) {
        const value = maxTradeCountSelect.value;
        return value === 'unlimited' ? null : parseInt(value);
    }
    return 30; // 默认值
}

// 更新AI交易次数统计
function updateAITradeCount(currentCount) {
    const aiTradeCountElement = document.getElementById('aiTradeCount');
    const aiTradeLimitWarning = document.getElementById('aiTradeLimitWarning');

    if (aiTradeCountElement) {
        aiTradeCountElement.textContent = currentCount;

        // 检查是否接近限制
        const limit = getTradeCountLimit();
        if (limit && currentCount >= limit * 0.8) {
            // 达到80%时显示警告
            if (aiTradeLimitWarning) {
                aiTradeLimitWarning.style.display = 'inline';
            }
            aiTradeCountElement.className = 'fw-bold text-warning';
        } else {
            if (aiTradeLimitWarning) {
                aiTradeLimitWarning.style.display = 'none';
            }
            aiTradeCountElement.className = 'fw-bold text-primary';
        }
    }
}

// 检查是否达到交易次数限制
function checkTradeCountLimit(currentCount) {
    const limit = getTradeCountLimit();

    if (limit && currentCount >= limit) {
        console.log(`🛑 已达到交易次数限制: ${currentCount}/${limit}`);

        // 自动停止AI交易
        stopAITrading();

        // 显示通知
        showNotification(`🛑 AI交易已自动停止\n\n已达到单次运行交易次数限制: ${limit} 次\n实际执行: ${currentCount} 次交易`, 'warning');

        return true; // 已达到限制
    }

    return false; // 未达到限制
}

// 计算智能止盈止损
function calculateSmartStopLoss() {
    const enabled = document.getElementById('enableStopLossTakeProfit')?.checked || false;

    if (!enabled) {
        // 如果未启用，检查是否有手动设置
        const manualStopLoss = document.getElementById('stopLoss')?.value;
        const manualTakeProfit = document.getElementById('takeProfit')?.value;

        return {
            enabled: false,
            stopLoss: manualStopLoss ? parseFloat(manualStopLoss) : null,
            takeProfit: manualTakeProfit ? parseFloat(manualTakeProfit) : null
        };
    }

    // 获取设置参数
    const stopLossPercent = parseFloat(document.getElementById('stopLossPercent')?.value || 2.0);
    const takeProfitPercent = parseFloat(document.getElementById('takeProfitPercent')?.value || 5.0);
    const autoAdjust = document.getElementById('autoAdjustStopLoss')?.checked || false;

    // 获取当前交易信息
    const symbol = document.getElementById('symbolSelect')?.value || 'EURUSD';
    const side = document.querySelector('input[name="orderSide"]:checked')?.value || 'buy';

    // 获取当前价格（这里使用模拟价格，实际应该从API获取）
    const currentPrice = getCurrentPrice(symbol);

    let stopLoss = null;
    let takeProfit = null;

    if (currentPrice) {
        // 计算止损止盈价格
        const stopLossDistance = currentPrice * (stopLossPercent / 100);
        const takeProfitDistance = currentPrice * (takeProfitPercent / 100);

        if (side === 'buy') {
            stopLoss = currentPrice - stopLossDistance;
            takeProfit = currentPrice + takeProfitDistance;
        } else {
            stopLoss = currentPrice + stopLossDistance;
            takeProfit = currentPrice - takeProfitDistance;
        }

        // AI智能调整
        if (autoAdjust) {
            const adjustment = getAIAdjustment(symbol, side);
            stopLoss *= adjustment.stopLossMultiplier;
            takeProfit *= adjustment.takeProfitMultiplier;
        }

        // 保留5位小数
        stopLoss = Math.round(stopLoss * 100000) / 100000;
        takeProfit = Math.round(takeProfit * 100000) / 100000;
    }

    console.log('智能止盈止损计算结果:', {
        symbol,
        side,
        currentPrice,
        stopLossPercent,
        takeProfitPercent,
        stopLoss,
        takeProfit,
        autoAdjust
    });

    return {
        enabled: true,
        stopLoss,
        takeProfit,
        stopLossPercent,
        takeProfitPercent,
        autoAdjust
    };
}

// 获取当前价格（需要真实数据源）
function getCurrentPrice(symbol) {
    // TODO: 集成真实市场数据API
    console.error('❌ getCurrentPrice: 模拟价格数据已禁用，需要集成真实数据源');
    return null;
}

// AI智能调整
function getAIAdjustment(symbol, side) {
    // 根据货币对和市场条件进行AI调整
    const volatility = getSymbolVolatility(symbol);
    const marketTrend = getMarketTrend(symbol);

    let stopLossMultiplier = 1.0;
    let takeProfitMultiplier = 1.0;

    // 根据波动率调整
    if (volatility > 0.8) {
        // 高波动率：放宽止损，缩小止盈
        stopLossMultiplier = 1.2;
        takeProfitMultiplier = 0.8;
    } else if (volatility < 0.3) {
        // 低波动率：收紧止损，扩大止盈
        stopLossMultiplier = 0.8;
        takeProfitMultiplier = 1.2;
    }

    // 根据市场趋势调整
    if (marketTrend > 0.6 && side === 'buy') {
        // 强上升趋势且买入：扩大止盈
        takeProfitMultiplier *= 1.3;
    } else if (marketTrend < -0.6 && side === 'sell') {
        // 强下降趋势且卖出：扩大止盈
        takeProfitMultiplier *= 1.3;
    }

    return {
        stopLossMultiplier,
        takeProfitMultiplier
    };
}

// 获取货币对波动率（模拟）
function getSymbolVolatility(symbol) {
    const volatilities = {
        'EURUSD': 0.5,
        'GBPUSD': 0.7,
        'USDJPY': 0.6,
        'USDCHF': 0.4,
        'AUDUSD': 0.8,
        'USDCAD': 0.5,
        'NZDUSD': 0.9,
        'EURGBP': 0.3
    };

    return volatilities[symbol] || 0.5;
}

// 获取市场趋势（模拟）
function getMarketTrend(symbol) {
    // 返回-1到1之间的值，表示趋势强度
    return (Math.random() - 0.5) * 2;
}

// 根据账户类型加载账户列表
function loadAccountsByType() {
    const accountType = document.getElementById('accountTypeSelect').value;
    const accountSelect = document.getElementById('accountSelect');
    const refreshBtn = document.getElementById('refreshMT5Btn');

    currentAccountType = accountType;

    // 重置账户选择
    accountSelect.innerHTML = '<option value="">正在加载...</option>';
    accountSelect.disabled = true;

    if (accountType === 'mt5') {
        // 显示刷新MT5按钮
        refreshBtn.style.display = 'inline-block';
        loadMT5Accounts();
    } else if (accountType === 'internal') {
        // 隐藏刷新MT5按钮
        refreshBtn.style.display = 'none';
        loadInternalAccounts();
    } else {
        accountSelect.innerHTML = '<option value="">请先选择账户类型</option>';
        refreshBtn.style.display = 'none';
    }
}

// 加载MT5账户
function loadMT5Accounts() {
    console.log('🔍 开始加载MT5账户...');

    const accountSelect = document.getElementById('accountSelect');
    accountSelect.innerHTML = '<option value="">正在连接MT5...</option>';

    fetch('/api/mt5/accounts')
        .then(response => {
            console.log('MT5账户API响应状态:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('MT5账户API响应数据:', data);

            if (data.success && data.accounts && data.accounts.length > 0) {
                accountSelect.innerHTML = '<option value="">选择MT5账户</option>';

                data.accounts.forEach(account => {
                    const option = document.createElement('option');
                    option.value = `mt5_${account.login}`;

                    // 显示账户信息
                    const accountTypeText = account.account_type === 'demo' ? '模拟' : '真实';
                    option.textContent = `${account.login} - ${account.server} (${accountTypeText}, 余额: $${account.balance.toFixed(2)})`;
                    option.dataset.accountInfo = JSON.stringify(account);

                    accountSelect.appendChild(option);

                    console.log(`✅ 添加MT5账户: ${account.login} (${account.account_type})`);
                });

                accountSelect.disabled = false;
                showNotification(`✅ 成功加载 ${data.accounts.length} 个MT5账户`, 'success');
            } else {
                accountSelect.innerHTML = '<option value="">未找到MT5账户</option>';

                const errorMsg = data.error || '未找到MT5账户';
                console.warn('MT5账户加载问题:', errorMsg);

                showNotification(`⚠️ ${errorMsg}`, 'warning');

                // 显示详细的故障排除信息
                setTimeout(() => {
                    showNotification(`💡 故障排除提示：
1. 确保MT5客户端已启动并登录
2. 检查MT5设置：工具→选项→专家顾问
3. 确保勾选"允许自动交易"和"允许DLL导入"
4. 尝试重启MT5客户端`, 'info');
                }, 2000);
            }
        })
        .catch(error => {
            console.error('加载MT5账户失败:', error);
            const accountSelect = document.getElementById('accountSelect');
            accountSelect.innerHTML = '<option value="">连接失败，请重试</option>';

            showNotification('❌ MT5连接失败，请检查MT5客户端状态', 'error');

            // 显示详细错误信息
            setTimeout(() => {
                showNotification(`🔧 可能的解决方案：
1. 重启MT5客户端
2. 检查MetaTrader5库是否安装
3. 确保MT5允许API连接
4. 联系技术支持`, 'info');
            }, 3000);
        });
}

// 加载内部模拟账户
function loadInternalAccounts() {
    const accountSelect = document.getElementById('accountSelect');

    // 这里可以从服务器加载内部账户，暂时使用静态数据
    accountSelect.innerHTML = `
        <option value="">选择内部模拟账户</option>
        <option value="internal_demo_1">内部模拟账户 1 (余额: $100,000)</option>
        <option value="internal_demo_2">内部模拟账户 2 (余额: $50,000)</option>
    `;

    accountSelect.disabled = false;
}

// 刷新MT5账户
function refreshMT5Accounts() {
    if (currentAccountType === 'mt5') {
        showNotification('正在刷新MT5账户...', 'info');
        loadMT5Accounts();
    }
}



// 订单类型切换
document.getElementById('orderType').addEventListener('change', function() {
    const priceGroup = document.getElementById('priceGroup');
    if (this.value === 'limit') {
        priceGroup.style.display = 'block';
        document.getElementById('orderPrice').required = true;
    } else {
        priceGroup.style.display = 'none';
        document.getElementById('orderPrice').required = false;
    }
});

// 账户选择
document.getElementById('accountSelect').addEventListener('change', function() {
    const accountId = this.value;
    currentAccountId = accountId;

    if (accountId) {
        // 获取账户信息
        const selectedOption = this.options[this.selectedIndex];
        const accountInfo = selectedOption.dataset.accountInfo ?
            JSON.parse(selectedOption.dataset.accountInfo) : null;

        // 保存账户状态到全局状态管理器
        saveAccountState(currentAccountType, currentAccountId, accountInfo);

        // 显示账户信息
        document.getElementById('accountInfo').style.display = 'block';
        document.getElementById('selectedAccount').textContent = selectedOption.textContent;

        // 根据账户类型显示不同信息
        if (currentAccountType === 'mt5') {
            // MT5账户显示真实数据
            if (accountInfo) {
                document.getElementById('accountBalance').textContent = `$${accountInfo.balance.toFixed(2)}`;
                document.getElementById('accountEquity').textContent = `$${accountInfo.equity.toFixed(2)}`;
            }
        } else {
            // 内部账户显示默认数据
            document.getElementById('accountBalance').textContent = '$100,000.00';
            document.getElementById('accountEquity').textContent = '$100,000.00';
        }

        // 启用交易功能
        enableTradingControls(true);

        // 加载数据
        loadPositions();
        loadTradeHistory();

        // 显示成功消息
        const accountTypeLabel = currentAccountType === 'mt5' ? 'MT5模拟账户' : '内部模拟账户';
        showNotification(`✅ 已选择${accountTypeLabel}`, 'success');

        console.log('账户选择完成，状态已保存:', {
            accountType: currentAccountType,
            accountId: currentAccountId
        });
    } else {
        document.getElementById('accountInfo').style.display = 'none';
        enableTradingControls(false);

        // 清除保存的账户状态
        saveAccountState(null, null, null);
    }
});

// 启用/禁用交易控件
function enableTradingControls(enabled) {
    const controls = [
        'symbolSelect',
        'tradeType',
        'lotSize',
        'submitOrder'
    ];

    controls.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.disabled = !enabled;
        }
    });

    if (!enabled) {
        showNotification('请先选择交易账户才能进行模拟交易', 'warning');
    }
}

// 加载交易历史
function loadTradeHistory() {
    fetch('/api/demo-trading/history')
        .then(response => response.json())
        .then(data => {
            const tbody = document.querySelector('#historyTable tbody');
            tbody.innerHTML = '';

            if (data.success && data.trades.length > 0) {
                data.trades.forEach(trade => {
                    // 转换为中国标准时间显示
                    const chinaTime = convertToChinaTime(trade.open_time);

                    const row = `
                        <tr>
                            <td>${chinaTime}</td>
                            <td>${trade.symbol}</td>
                            <td><span class="badge bg-${trade.side === 'buy' ? 'success' : 'danger'}">${trade.side === 'buy' ? '买入' : '卖出'}</span></td>
                            <td>${trade.volume}</td>
                            <td>${trade.open_price.toFixed(5)}</td>
                            <td>${trade.close_price ? trade.close_price.toFixed(5) : '-'}</td>
                            <td class="${trade.profit >= 0 ? 'text-success' : 'text-danger'}">
                                ${trade.profit.toFixed(2)}
                            </td>
                            <td>
                                <span class="badge bg-${trade.status === 'closed' ? 'success' : 'warning'}">
                                    ${trade.status === 'closed' ? '已平仓' : '持仓中'}
                                </span>
                                ${trade.strategy_name ? '<br><small class="text-muted">' + trade.strategy_name + '</small>' : ''}
                            </td>
                        </tr>
                    `;
                    tbody.innerHTML += row;
                });
            } else {
                tbody.innerHTML = '<tr><td colspan="8" class="text-center text-muted">暂无交易记录</td></tr>';
            }
        })
        .catch(error => {
            console.error('Error loading trade history:', error);
            const tbody = document.querySelector('#historyTable tbody');
            tbody.innerHTML = '<tr><td colspan="8" class="text-center text-danger">加载交易历史失败</td></tr>';
        });
}

// 提交订单
document.getElementById('orderForm').addEventListener('submit', function(e) {
    e.preventDefault();

    if (!currentAccountId) {
        alert('请先选择模拟账户');
        return;
    }

    // 判断账户类型并显示相应提示
    const isM5Account = currentAccountId.startsWith('mt5_');
    const accountTypeText = isM5Account ? 'MT5客户端' : '内部模拟系统';

    console.log(`准备提交订单到${accountTypeText}:`, {
        accountId: currentAccountId,
        symbol: document.getElementById('symbolSelect').value,
        side: document.querySelector('input[name="orderSide"]:checked').value,
        amount: parseFloat(document.getElementById('orderAmount').value)
    });

    // 计算智能止盈止损
    const stopLossSettings = calculateSmartStopLoss();

    const orderData = {
        account_id: currentAccountId,  // 传递账户ID用于判断
        symbol: document.getElementById('symbolSelect').value,
        order_type: document.getElementById('orderType').value,
        side: document.querySelector('input[name="orderSide"]:checked').value,
        amount: parseFloat(document.getElementById('orderAmount').value),
        price: document.getElementById('orderPrice').value ? parseFloat(document.getElementById('orderPrice').value) : null,
        stop_loss: stopLossSettings.stopLoss,
        take_profit: stopLossSettings.takeProfit,
        smart_stop_loss: stopLossSettings.enabled  // 标记是否使用智能止盈止损
    };

    // 提交模拟订单到后端
    fetch('/api/demo-trading/order', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(orderData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 根据账户类型显示不同的成功信息
            const accountInfo = isM5Account ?
                `✅ 订单已发送到MT5客户端！\n订单号: ${data.order.mt5_ticket || data.order.trade_id}\n请在MT5软件中查看订单状态` :
                `💾 订单已在内部模拟系统记录！\n交易ID: ${data.order.trade_id}`;

            alert(accountInfo);
            this.reset();
            // 刷新交易历史和持仓
            loadTradeHistory();
            loadPositions();
        } else {
            alert('❌ 订单提交失败: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('订单提交失败');
    });
});

// 添加模拟账户
function addAccount() {
    const form = document.getElementById('addAccountForm');
    const formData = new FormData(form);
    const accountData = {
        account_name: formData.get('account_name'),
        account_type: 'demo',
        broker: formData.get('broker'),
        balance: parseFloat(formData.get('balance'))
    };

    fetch('/api/trading-accounts', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(accountData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('模拟账户添加成功！');
            location.reload();
        } else {
            alert('账户添加失败: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('账户添加失败');
    });
}

// AI交易相关变量 (现在使用全局管理器)
let aiTradingActive = false;
let aiTradingInterval = null;
let aiStartTime = null;

// 监听AI交易状态更新
window.addEventListener('aiTradingUpdate', function(event) {
    const { event: eventType, data } = event.detail;

    switch (eventType) {
        case 'tradingStarted':
            updateUIForTradingStarted(data.state);
            break;
        case 'tradingStopped':
            updateUIForTradingStopped();
            break;
        case 'tradingTimeStopped':
            alert(`已超出交易时间段 (${data.timeSlot})，AI交易已自动停止`);
            updateUIForTradingStopped();
            break;
        case 'tradingTimeError':
            alert(`当前时间不在交易时间段内 (${data.timeSlot})，无法启动AI交易`);
            break;
        case 'tradeExecuted':
            loadTradeHistory(); // 刷新交易历史

            // 更新交易次数统计
            if (data.state && data.state.todayTradeCount !== undefined) {
                updateAITradeCount(data.state.todayTradeCount);

                // 检查是否达到交易次数限制
                if (checkTradeCountLimit(data.state.todayTradeCount)) {
                    // 已达到限制，交易已自动停止
                    break;
                }
            }

            if (typeof MateTrade4 !== 'undefined' && MateTrade4.notifications) {
                MateTrade4.notifications.success('AI交易执行成功！');
            }
            break;
        case 'runningTimeUpdate':
            updateAIRunningTimeDisplay(data.state);
            break;
        case 'positionsUpdated':
            loadPositions(); // 重新加载持仓
            if (data.closedPositions > 0) {
                showNotification(`自动平仓 ${data.closedPositions} 个持仓`, 'info');
            }
            break;
        case 'tradeCountLimitReached':
            if (data.positionManagementMode) {
                updateUIForPositionManagementMode(data);
                showNotification(`🔄 AI交易已切换到持仓管理模式\n\n已达到单次运行交易次数限制: ${data.limit} 次\n实际执行: ${data.currentCount} 次交易\n\n✅ 停止新增订单，继续管理现有持仓\n📊 现有持仓将正常执行止盈止损`, 'info');
            } else {
                updateUIForTradingStopped();
                showNotification(`🛑 AI交易已自动停止\n\n已达到单次运行交易次数限制: ${data.limit} 次\n实际执行: ${data.currentCount} 次交易`, 'warning');
            }
            break;
        case 'positionsManaged':
            console.log('📊 持仓管理更新:', data.positions.length, '个持仓');
            loadPositions(); // 刷新持仓显示
            break;
        case 'allPositionsClosed':
            updateUIForTradingStopped();
            showNotification(`✅ 所有持仓已处理完毕\n\nAI交易已完全停止\n如需继续交易，请重新启动AI交易`, 'success');
            break;
        case 'tradingRestored':
            console.log('🔄 AI交易状态已恢复:', data.state);
            updateUIForTradingStarted(data.state);
            showNotification(`🔄 AI交易状态已恢复\n\n策略: ${data.state.strategyId}\n交易货币对: ${data.state.tradingSymbol}\n已执行: ${data.state.currentRunTradeCount || 0} 次交易`, 'info');
            break;
        case 'tradingTimeExpired':
            updateUIForTradingStopped();
            showNotification(`⏰ AI交易已停止\n\n超出交易时间段: ${data.state.tradingTimeSlot}\n请在交易时间内重新启动`, 'warning');
            break;
    }
});

// 更新UI为交易开始状态
function updateUIForTradingStarted(state) {
    aiTradingActive = true;
    aiStartTime = new Date(state.startTime);

    document.getElementById('startAITrading').style.display = 'none';
    document.getElementById('stopAITrading').style.display = 'block';
    document.getElementById('aiTradingStatus').style.display = 'block';

    // 显示当前策略
    const strategySelect = document.getElementById('aiStrategySelect');
    if (strategySelect && strategySelect.value === state.strategyId.toString()) {
        const selectedOption = strategySelect.options[strategySelect.selectedIndex];
        document.getElementById('currentAIStrategy').textContent = selectedOption.textContent;
    }

    // 显示当前交易货币对
    const currentTradingSymbolElement = document.getElementById('currentTradingSymbol');
    if (currentTradingSymbolElement && state.tradingSymbol) {
        currentTradingSymbolElement.textContent = state.tradingSymbol;
    }

    // 初始化交易次数显示
    updateAITradeCount(state.todayTradeCount || 0);
    updateTradeCountLimitDisplay();

    // 启动中国标准时间显示
    startChinaTimeDisplay();
}

// 更新UI为交易停止状态
function updateUIForTradingStopped() {
    aiTradingActive = false;
    aiStartTime = null;

    document.getElementById('startAITrading').style.display = 'block';
    document.getElementById('stopAITrading').style.display = 'none';
    document.getElementById('aiTradingStatus').style.display = 'none';

    // 停止中国标准时间显示
    stopChinaTimeDisplay();
}

// 更新UI为持仓管理模式
function updateUIForPositionManagementMode(data) {
    console.log('🔄 切换到持仓管理模式UI');

    // 保持AI交易状态显示，但修改内容
    const statusDiv = document.getElementById('aiTradingStatus');
    if (statusDiv) {
        statusDiv.innerHTML = `
            <div class="alert alert-warning">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fas fa-shield-alt"></i>
                        <strong>持仓管理模式</strong>
                        <span class="ms-2">运行时间: <span id="aiRunningTime">00:00:00</span></span>
                    </div>
                    <div>
                        <span class="badge bg-warning">管理中</span>
                    </div>
                </div>
                <small class="d-block mt-2">
                    策略: <span id="currentAIStrategy">-</span> |
                    运行时间: <span id="aiRunningTime">00:00:00</span>
                </small>
                <small class="d-block mt-1">
                    <i class="fas fa-clock"></i>
                    中国标准时间: <span id="chinaTime">--:--:--</span> |
                    交易货币对: <span id="currentTradingSymbol">-</span>
                </small>
                <small class="d-block mt-1">
                    <i class="fas fa-chart-line"></i>
                    已执行交易: <span id="aiTradeCount" class="fw-bold text-warning">${data.currentCount}</span> 次 |
                    交易限制: <span id="aiTradeLimit" class="fw-bold text-warning">${data.limit}</span> 次
                    <span class="text-warning ms-2">
                        <i class="fas fa-ban"></i>
                        已停止新增订单
                    </span>
                </small>
                <div class="mt-2">
                    <small class="text-muted">
                        <i class="fas fa-info-circle"></i>
                        已达到交易次数限制，停止新增订单，继续管理现有持仓直至全部平仓
                    </small>
                </div>
            </div>
        `;
    }

    // 修改停止按钮文本
    const stopBtn = document.getElementById('stopAITrading');
    if (stopBtn) {
        stopBtn.innerHTML = '<i class="fas fa-stop"></i> 强制停止管理';
        stopBtn.title = '强制停止持仓管理（可能导致持仓无法正常处理）';
    }

    // 继续显示中国标准时间
    startChinaTimeDisplay();
}

// 更新运行时间显示
function updateAIRunningTimeDisplay(state) {
    if (state.active && state.startTime) {
        const runningTime = Math.floor((Date.now() - state.startTime) / 1000);
        const hours = Math.floor(runningTime / 3600);
        const minutes = Math.floor((runningTime % 3600) / 60);
        const seconds = runningTime % 60;

        const timeString = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        const runningTimeElement = document.getElementById('aiRunningTime');
        if (runningTimeElement) {
            runningTimeElement.textContent = timeString;
        }
    }
}

// 中国标准时间显示
let chinaTimeInterval = null;

function startChinaTimeDisplay() {
    // 清除之前的定时器
    if (chinaTimeInterval) {
        clearInterval(chinaTimeInterval);
    }

    // 立即更新一次
    updateChinaTime();

    // 每秒更新一次
    chinaTimeInterval = setInterval(updateChinaTime, 1000);
}

function updateChinaTime() {
    const now = new Date();
    const utc = now.getTime() + (now.getTimezoneOffset() * 60000);
    const chinaTime = new Date(utc + (8 * 3600000)); // UTC+8

    const timeString = chinaTime.toLocaleTimeString('zh-CN', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });

    const chinaTimeElement = document.getElementById('chinaTime');
    if (chinaTimeElement) {
        chinaTimeElement.textContent = timeString;
    }
}

// 转换为中国标准时间显示
function convertToChinaTime(dateString) {
    try {
        let date;

        // 处理不同的时间格式
        if (typeof dateString === 'number') {
            // Unix时间戳 (秒)
            date = new Date(dateString * 1000);
        } else if (typeof dateString === 'string') {
            // 字符串格式
            date = new Date(dateString);
        } else {
            date = new Date(dateString);
        }

        // 检查日期是否有效
        if (isNaN(date.getTime())) {
            console.warn('无效的时间数据:', dateString);
            return '无效时间';
        }

        // 转换为中国标准时间 (UTC+8)
        const utc = date.getTime() + (date.getTimezoneOffset() * 60000);
        const chinaTime = new Date(utc + (8 * 3600000));

        // 格式化为中文显示
        return chinaTime.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
        });
    } catch (error) {
        console.error('时间转换失败:', error, '原始数据:', dateString);
        return '时间转换失败'; // 回退到错误提示
    }
}

function stopChinaTimeDisplay() {
    if (chinaTimeInterval) {
        clearInterval(chinaTimeInterval);
        chinaTimeInterval = null;
    }
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    loadAIStrategies();
});

// 加载AI策略列表
function loadAIStrategies() {
    fetch('/api/ai-strategies/list')
    .then(response => response.json())
    .then(data => {
        const select = document.getElementById('aiStrategySelect');
        select.innerHTML = '<option value="">选择AI策略</option>';

        if (data.success && data.strategies.length > 0) {
            let hasActiveStrategy = false;
            data.strategies.forEach(strategy => {
                if (strategy.status === 'completed') {
                    const option = document.createElement('option');
                    option.value = strategy.id;

                    const statusText = strategy.is_active ? '✅' : '⏸️';
                    let displayText = `${statusText} ${strategy.name}`;

                    if (strategy.performance_metrics && strategy.performance_metrics.win_rate) {
                        displayText += ` (胜率${(strategy.performance_metrics.win_rate * 100).toFixed(1)}%)`;
                    }

                    option.textContent = displayText;
                    select.appendChild(option);

                    // 默认选中第一个激活的策略
                    if (strategy.is_active && !hasActiveStrategy) {
                        option.selected = true;
                        hasActiveStrategy = true;
                    }
                }
            });

            if (select.options.length === 1) {
                select.innerHTML = '<option value="">暂无可用的AI策略，请先训练AI策略</option>';
            }
        } else {
            select.innerHTML = '<option value="">暂无可用的AI策略，请先训练AI策略</option>';
        }
    })
    .catch(error => {
        console.error('加载AI策略失败:', error);
        document.getElementById('aiStrategySelect').innerHTML = '<option value="">加载失败，请刷新重试</option>';
    });
}

// 切换AI交易模式
function toggleAITrading() {
    const isEnabled = document.getElementById('aiTradingSwitch').checked;
    const aiSection = document.getElementById('aiTradingSection');
    const manualSection = document.getElementById('manualTradingSection');
    const title = document.getElementById('tradingModeTitle');

    if (isEnabled) {
        aiSection.style.display = 'block';
        manualSection.style.display = 'none';
        title.textContent = 'AI智能交易';

        // 如果AI交易正在运行，先停止
        if (aiTradingActive) {
            stopAITrading();
        }
    } else {
        aiSection.style.display = 'none';
        manualSection.style.display = 'block';
        title.textContent = '模拟下单';

        // 停止AI交易
        if (aiTradingActive) {
            stopAITrading();
        }
    }
}

// 开始AI交易 (使用全局管理器)
function startAITrading() {
    const strategyId = document.getElementById('aiStrategySelect').value;
    const riskLevel = document.getElementById('riskLevel').value;
    const maxTradeAmount = parseFloat(document.getElementById('maxTradeAmount').value);
    const tradingSymbol = document.getElementById('aiTradingSymbol').value;
    const tradingTimeSlot = document.getElementById('tradingTimeSlot').value;

    if (!strategyId) {
        alert('请选择一个AI策略');
        return;
    }

    if (!tradingSymbol) {
        alert('请选择交易货币对');
        return;
    }

    if (!maxTradeAmount || maxTradeAmount < 100) {
        alert('请设置有效的交易金额上限（最少$100）');
        return;
    }

    // 确认开始AI交易
    if (!confirm('确定要开始AI自动交易吗？系统将根据选择的策略自动执行交易决策。')) {
        return;
    }

    // 获取交易次数限制
    const maxTradeCount = getTradeCountLimit();

    // 使用全局管理器启动AI交易
    const config = {
        strategyId: parseInt(strategyId),
        riskLevel: riskLevel,
        maxTradeAmount: maxTradeAmount,
        tradingSymbol: tradingSymbol,
        tradingTimeSlot: tradingTimeSlot,
        accountType: 'demo',
        maxTradeCount: maxTradeCount // 添加交易次数限制
    };

    const success = AITradingManager.startAITrading(config);

    if (success) {
        console.log('AI交易已通过全局管理器启动:', config);
    }

}

// 停止AI交易 (使用全局管理器)
function stopAITrading() {
    // 使用全局管理器停止AI交易
    AITradingManager.stopAITrading('demo');

    console.log('AI交易已通过全局管理器停止');
    alert('AI交易已停止');
}

// 检查当前时间是否在交易时间段内
function isInTradingTime(timeSlot) {
    const now = new Date();
    const currentTime = now.getHours() * 100 + now.getMinutes(); // 转换为HHMM格式

    const [startTime, endTime] = timeSlot.split('-');
    const [startHour, startMin] = startTime.split(':').map(Number);
    const [endHour, endMin] = endTime.split(':').map(Number);

    const startTimeNum = startHour * 100 + startMin;
    const endTimeNum = endHour * 100 + endMin;

    return currentTime >= startTimeNum && currentTime <= endTimeNum;
}

// 检查交易时间段（在AI交易运行时定期检查）
function checkTradingTime() {
    if (aiTradingActive) {
        const tradingTimeSlot = document.getElementById('tradingTimeSlot').value;

        if (!isInTradingTime(tradingTimeSlot)) {
            console.log('超出交易时间段，自动停止AI交易');
            stopAITrading();
            alert(`已超出交易时间段 (${tradingTimeSlot})，AI交易已自动停止`);
        }
    }
}

// 设置默认货币对
function setAsDefaultSymbol() {
    const selectedSymbol = document.getElementById('aiTradingSymbol').value;

    if (!selectedSymbol) {
        alert('请先选择一个货币对');
        return;
    }

    // 保存到localStorage
    localStorage.setItem('defaultTradingSymbol', selectedSymbol);

    // 显示成功提示
    const symbolText = document.getElementById('aiTradingSymbol').selectedOptions[0].text;
    alert(`已将 ${symbolText} 设为默认货币对`);
}

// 加载默认设置
function loadDefaultSettings() {
    // 加载默认货币对
    const defaultSymbol = localStorage.getItem('defaultTradingSymbol');
    if (defaultSymbol) {
        const symbolSelect = document.getElementById('aiTradingSymbol');
        const option = symbolSelect.querySelector(`option[value="${defaultSymbol}"]`);
        if (option) {
            symbolSelect.value = defaultSymbol;
            console.log('已加载默认货币对:', defaultSymbol);
        }
    }

    // 加载默认风险等级
    const defaultRiskLevel = localStorage.getItem('defaultRiskLevel');
    if (defaultRiskLevel) {
        const riskSelect = document.getElementById('riskLevel');
        const option = riskSelect.querySelector(`option[value="${defaultRiskLevel}"]`);
        if (option) {
            riskSelect.value = defaultRiskLevel;
            console.log('已加载默认风险等级:', defaultRiskLevel);
        }
    }

    // 加载默认交易金额
    const defaultTradeAmount = localStorage.getItem('defaultTradeAmount');
    if (defaultTradeAmount) {
        document.getElementById('maxTradeAmount').value = defaultTradeAmount;
        console.log('已加载默认交易金额:', defaultTradeAmount);
    }
}

// 保存当前设置为默认
function saveCurrentAsDefault() {
    const symbol = document.getElementById('aiTradingSymbol').value;
    const riskLevel = document.getElementById('riskLevel').value;
    const tradeAmount = document.getElementById('maxTradeAmount').value;

    if (symbol) localStorage.setItem('defaultTradingSymbol', symbol);
    if (riskLevel) localStorage.setItem('defaultRiskLevel', riskLevel);
    if (tradeAmount) localStorage.setItem('defaultTradeAmount', tradeAmount);

    alert('当前设置已保存为默认值');
}

// 更新AI运行时间
function updateAIRunningTime() {
    if (!aiStartTime) return;

    const now = new Date();
    const diff = now - aiStartTime;

    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((diff % (1000 * 60)) / 1000);

    const timeString = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    document.getElementById('aiRunningTime').textContent = timeString;
}

// 执行AI交易决策
function executeAITradingDecision(strategyId, riskLevel, maxTradeAmount, tradingSymbol) {
    console.log('执行AI交易决策...', { strategyId, riskLevel, maxTradeAmount, tradingSymbol });

    // 调用AI策略分析API
    fetch('/api/ai-trading/analyze', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            strategy_id: strategyId,
            risk_level: riskLevel,
            max_trade_amount: maxTradeAmount,
            trading_symbol: tradingSymbol,
            account_type: 'demo'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.recommendation) {
            const rec = data.recommendation;

            // 如果AI建议执行交易
            if (rec.action !== 'hold') {
                executeAITrade(rec);
            } else {
                console.log('AI策略建议持有，暂不交易');
            }
        } else {
            console.error('AI分析失败:', data.error);
        }
    })
    .catch(error => {
        console.error('AI交易分析请求失败:', error);
    });
}

// 执行AI推荐的交易
function executeAITrade(recommendation) {
    const tradeData = {
        symbol: recommendation.symbol,
        side: recommendation.action, // 'buy' or 'sell'
        amount: recommendation.amount,
        type: 'market',
        ai_generated: true,
        strategy_id: recommendation.strategy_id
    };

    console.log('执行AI交易:', tradeData);

    // 提交AI生成的交易订单
    fetch('/api/demo-trading/order', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(tradeData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('AI交易执行成功:', data);
            // 刷新交易历史
            loadTradeHistory();
            // 显示成功提示
            if (typeof MateTrade4 !== 'undefined' && MateTrade4.notifications) {
                MateTrade4.notifications.success('AI交易执行成功！');
            }
        } else {
            console.error('AI交易执行失败:', data.error);
            if (typeof MateTrade4 !== 'undefined' && MateTrade4.notifications) {
                MateTrade4.notifications.error('AI交易执行失败: ' + data.error);
            }
        }
    })
    .catch(error => {
        console.error('AI交易执行请求失败:', error);
    });
}

// 页面加载时初始化 (第二阶段 - 在状态恢复后)
document.addEventListener('DOMContentLoaded', function() {
    // 延迟执行，等待第一阶段状态恢复完成
    setTimeout(() => {
        console.log('🔄 开始第二阶段初始化...');

        // 加载AI策略
        loadAIStrategies();

        // 加载默认设置
        loadDefaultSettings();

        // 加载交易历史
        loadTradeHistory();

        // 加载持仓信息
        loadPositions();

        // 初始化AI智能交易为默认启用状态
        initializeAITradingDefault();

        // 添加设置保存按钮事件
        const riskLevelSelect = document.getElementById('riskLevel');
        if (riskLevelSelect) {
            riskLevelSelect.addEventListener('change', function() {
                localStorage.setItem('defaultRiskLevel', this.value);
            });
        }

        const maxTradeAmountInput = document.getElementById('maxTradeAmount');
        if (maxTradeAmountInput) {
            maxTradeAmountInput.addEventListener('change', function() {
                localStorage.setItem('defaultTradeAmount', this.value);
            });
        }

        console.log('✅ 第二阶段初始化完成');
    }, 3000); // 在AI交易状态恢复后执行
});

// 初始化AI智能交易为默认启用状态
function initializeAITradingDefault() {
    // 确保AI智能交易开关为启用状态
    const aiTradingSwitch = document.getElementById('aiTradingSwitch');
    if (aiTradingSwitch) {
        aiTradingSwitch.checked = true;

        // 触发切换事件以更新UI
        toggleAITrading();

        // 更新标题
        const title = document.getElementById('tradingModeTitle');
        if (title) {
            title.textContent = 'AI智能交易';
        }

        console.log('✅ AI智能交易已设置为默认启用状态');
    }
}

// 从AI交易管理器恢复AI交易状态
function restoreAITradingStateFromManager() {
    if (!window.AITradingManager) {
        console.warn('⚠️ AI交易管理器未加载，跳过状态恢复');
        return;
    }

    try {
        const state = AITradingManager.getCurrentState('demo');
        console.log('🔍 检查AI交易管理器状态:', state);

        if (state.active) {
            console.log('✅ 检测到活跃的AI交易，恢复UI状态...');

            // 恢复UI状态
            updateUIForTradingStarted(state);

            // 恢复策略选择
            const strategySelect = document.getElementById('aiStrategySelect');
            if (strategySelect && state.strategyId) {
                // 等待策略加载完成后设置
                setTimeout(() => {
                    strategySelect.value = state.strategyId.toString();
                    console.log('✅ 恢复策略选择:', state.strategyId);
                }, 1000);
            }

            // 恢复其他设置
            if (state.riskLevel) {
                const riskLevelSelect = document.getElementById('riskLevel');
                if (riskLevelSelect) {
                    riskLevelSelect.value = state.riskLevel;
                    console.log('✅ 恢复风险等级:', state.riskLevel);
                }
            }

            if (state.maxTradeAmount) {
                const maxTradeAmountInput = document.getElementById('maxTradeAmount');
                if (maxTradeAmountInput) {
                    maxTradeAmountInput.value = state.maxTradeAmount;
                    console.log('✅ 恢复交易金额:', state.maxTradeAmount);
                }
            }

            if (state.tradingSymbol) {
                const tradingSymbolSelect = document.getElementById('aiTradingSymbol');
                if (tradingSymbolSelect) {
                    tradingSymbolSelect.value = state.tradingSymbol;
                    console.log('✅ 恢复交易货币对:', state.tradingSymbol);
                }
            }

            if (state.tradingTimeSlot) {
                const tradingTimeSlotSelect = document.getElementById('tradingTimeSlot');
                if (tradingTimeSlotSelect) {
                    tradingTimeSlotSelect.value = state.tradingTimeSlot;
                    console.log('✅ 恢复交易时间段:', state.tradingTimeSlot);
                }
            }

            // 恢复交易次数限制设置
            if (state.maxTradeCount) {
                const maxTradeCountSelect = document.getElementById('maxTradeCount');
                if (maxTradeCountSelect) {
                    const value = state.maxTradeCount === null ? 'unlimited' : state.maxTradeCount.toString();
                    maxTradeCountSelect.value = value;
                    updateTradeCountLimitDisplay();
                    console.log('✅ 恢复交易次数限制:', value);
                }
            }

            // 恢复交易次数统计
            if (state.currentRunTradeCount !== undefined) {
                updateAITradeCount(state.currentRunTradeCount);
                console.log('✅ 恢复交易次数统计:', state.currentRunTradeCount);
            }

            // 如果是持仓管理模式，更新UI
            if (state.positionManagementOnly) {
                console.log('🔄 恢复持仓管理模式UI');
                updateUIForPositionManagementMode({
                    currentCount: state.currentRunTradeCount,
                    limit: state.maxTradeCount,
                    positionManagementMode: true
                });
            }

            console.log('🎉 AI交易状态恢复完成');
        } else {
            console.log('ℹ️ 没有活跃的AI交易需要恢复');
        }
    } catch (error) {
        console.error('❌ 恢复AI交易状态失败:', error);
    }
}

// 检查并恢复AI交易状态 (保留原函数名以兼容)
function checkAndRestoreAITradingState() {
    // 现在由restoreAITradingStateFromManager处理
    console.log('🔄 AI交易状态恢复已移至restoreAITradingStateFromManager');
}

// 加载持仓信息
function loadPositions() {
    // 构建请求URL，包含账户ID
    const url = currentAccountId ?
        `/api/demo-trading/positions?account_id=${currentAccountId}` :
        '/api/demo-trading/positions';

    console.log('加载持仓，账户ID:', currentAccountId);

    fetch(url)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const tbody = document.querySelector('#positionsTable tbody');
            tbody.innerHTML = '';

            if (!data.positions || data.positions.length === 0) {
                tbody.innerHTML = '<tr><td colspan="8" class="text-center text-muted">暂无持仓</td></tr>';
                return;
            }

            data.positions.forEach(position => {
                const row = document.createElement('tr');

                // 根据盈亏设置行的颜色
                if (position.current_profit > 0) {
                    row.classList.add('table-success');
                } else if (position.current_profit < 0) {
                    row.classList.add('table-danger');
                }

                // 获取账户显示名称
                let accountDisplay = '未知账户';

                // 优先根据当前选择的账户类型判断
                if (currentAccountType === 'mt5' && currentAccountId) {
                    // MT5账户显示
                    const accountNumber = currentAccountId.replace('mt5_', '');
                    accountDisplay = `MT5-${accountNumber}`;
                } else if (currentAccountType === 'internal' && currentAccountId) {
                    // 内部模拟账户显示
                    accountDisplay = currentAccountId.replace('internal_demo_', '内部模拟-');
                } else if (position.account_type === 'mt5') {
                    // 从持仓数据中获取MT5账户信息
                    accountDisplay = `MT5-${position.trade_id}`.substring(0, 15);
                } else if (position.account_type === 'internal') {
                    // 从持仓数据中获取内部账户信息
                    accountDisplay = '内部模拟';
                } else {
                    // 根据交易ID格式推断（最后的回退方案）
                    if (position.trade_id && position.trade_id.toString().length > 8) {
                        accountDisplay = 'MT5账户';
                    } else {
                        accountDisplay = '内部模拟';
                    }
                }

                console.log(`持仓账户显示: ${accountDisplay} (当前账户类型: ${currentAccountType}, 账户ID: ${currentAccountId})`);

                row.innerHTML = `
                    <td><span class="badge bg-info">${accountDisplay}</span></td>
                    <td><strong>${position.symbol}</strong></td>
                    <td>
                        <span class="badge ${position.trade_type === 'buy' ? 'bg-success' : 'bg-danger'}">
                            ${position.trade_type === 'buy' ? '买入' : '卖出'}
                        </span>
                    </td>
                    <td>${position.volume}</td>
                    <td>$${position.open_price}</td>
                    <td>$${position.current_price}</td>
                    <td>
                        <span class="${position.current_profit > 0 ? 'text-success' : position.current_profit < 0 ? 'text-danger' : 'text-muted'}">
                            ${position.current_profit > 0 ? '+' : ''}$${position.current_profit.toFixed(2)}
                        </span>
                    </td>
                    <td>
                        <button class="btn btn-sm btn-outline-danger" onclick="closePosition(${position.trade_id}, '${position.account_type || 'internal'}')">
                            <i class="fas fa-times"></i> 平仓
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });

            // 更新总盈亏显示
            updateTotalProfit(data.total_profit);
        }
    })
    .catch(error => {
        console.error('加载持仓信息失败:', error);
    });
}

// 平仓操作
function closePosition(tradeId, accountType) {
    // 判断账户类型
    const isM5Account = currentAccountId && currentAccountId.startsWith('mt5_');
    const accountTypeText = isM5Account ? 'MT5客户端' : '内部模拟系统';

    if (!confirm(`确定要在${accountTypeText}中平仓这个持仓吗？`)) {
        return;
    }

    console.log(`平仓请求: ${tradeId}, 账户类型: ${isM5Account ? 'mt5' : 'internal'}`);

    fetch('/api/demo-trading/close-position', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            trade_id: tradeId,
            account_type: isM5Account ? 'mt5' : 'internal'  // 传递账户类型
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const isM5Account = currentAccountId && currentAccountId.startsWith('mt5_');
            const successMsg = isM5Account ?
                `✅ MT5持仓平仓成功！${data.close_price ? `平仓价: $${data.close_price}` : ''}` :
                `💾 内部持仓平仓成功！${data.profit ? `盈亏: ${data.profit > 0 ? '+' : ''}$${data.profit.toFixed(2)}` : ''}`;

            showNotification(successMsg, 'success');
            loadPositions(); // 重新加载持仓
            loadTradeHistory(); // 重新加载交易历史
        } else {
            showNotification(`❌ 平仓失败: ${data.error}`, 'error');
        }
    })
    .catch(error => {
        console.error('平仓失败:', error);
        showNotification('平仓失败，请重试', 'error');
    });
}

// 更新总盈亏显示
function updateTotalProfit(totalProfit) {
    console.log('当前总浮动盈亏:', totalProfit);
}

// 显示通知
function showNotification(message, type = 'info') {
    const alertClass = {
        'success': 'alert-success',
        'error': 'alert-danger',
        'warning': 'alert-warning',
        'info': 'alert-info'
    }[type] || 'alert-info';

    const notification = document.createElement('div');
    notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 3000);
}

// 清空所有持仓
function clearAllPositions(event) {
    try {
        // 检查账户ID
        const accountId = currentAccountId || 'demo_default';

        if (!confirm('⚠️ 确定要清空所有模拟持仓吗？\n\n此操作将：\n- 强制平仓所有持仓\n- 无法撤销\n\n请确认您要继续')) {
            return;
        }

        // 显示加载状态
        let clearBtn = null;
        let originalText = '';

        if (event && event.target) {
            clearBtn = event.target.closest('button');
            if (clearBtn) {
                originalText = clearBtn.innerHTML;
                clearBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 清空中...';
                clearBtn.disabled = true;
            }
        }

        fetch('/api/demo-trading/clear-positions', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                account_id: accountId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(`✅ 成功清空 ${data.cleared_count} 个持仓`, 'success');
                loadPositions(); // 重新加载持仓
                loadTradeHistory(); // 重新加载交易历史
            } else {
                showNotification(`❌ 清空持仓失败: ${data.error}`, 'error');
            }
        })
        .catch(error => {
            console.error('清空持仓失败:', error);
            showNotification('❌ 清空持仓失败，请重试', 'error');
        })
        .finally(() => {
            // 恢复按钮状态
            if (clearBtn) {
                clearBtn.innerHTML = originalText;
                clearBtn.disabled = false;
            }
        });

    } catch (error) {
        console.error('清空持仓异常:', error);
        showNotification('❌ 清空持仓异常，请重试', 'error');
    }
}

// 清空交易历史
function clearTradeHistory(event) {
    // 检查账户ID
    const accountId = currentAccountId || 'demo_default';

    if (!confirm('⚠️ 确定要清空模拟交易历史吗？\n\n此操作将：\n- 删除所有历史交易记录\n- 保留当前持仓\n- 无法撤销\n\n请确认您要继续')) {
        return;
    }

    // 显示加载状态
    const clearBtn = event.target.closest('button');
    const originalText = clearBtn.innerHTML;
    clearBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 清空中...';
    clearBtn.disabled = true;

    fetch('/api/demo-trading/clear-history', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            account_id: accountId
        })
    })
    .then(response => {
        console.log('清空历史响应状态:', response.status);
        console.log('清空历史响应头:', response.headers);
        return response.json();
    })
    .then(data => {
        console.log('清空历史响应数据:', data);
        if (data.success) {
            showNotification(`✅ 成功清空 ${data.cleared_count} 条交易历史`, 'success');
            loadTradeHistory(); // 重新加载交易历史
        } else {
            console.error('清空历史失败，服务器返回:', data);
            showNotification(`❌ 清空历史失败: ${data.error}`, 'error');
        }
    })
    .catch(error => {
        console.error('清空历史网络错误:', error);
        showNotification('❌ 清空历史失败，请重试', 'error');
    })
    .finally(() => {
        // 恢复按钮状态
        clearBtn.innerHTML = originalText;
        clearBtn.disabled = false;
    });
}

// 清空所有数据
function clearAllData(event) {
    // 检查账户ID
    const accountId = currentAccountId || 'demo_default';

    if (!confirm('🚨 确定要清空所有模拟交易数据吗？\n\n此操作将：\n- 强制平仓所有持仓\n- 删除所有交易历史\n- 重置账户到初始状态\n- 无法撤销\n\n⚠️ 这是一个危险操作，请再次确认！')) {
        return;
    }

    // 二次确认
    if (!confirm('🔴 最后确认：您真的要清空所有模拟交易数据吗？\n\n此操作不可撤销！')) {
        return;
    }

    // 显示加载状态
    const clearBtn = event.target.closest('button');
    const originalText = clearBtn.innerHTML;
    clearBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 清空中...';
    clearBtn.disabled = true;

    fetch('/api/demo-trading/clear-all', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            account_id: accountId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(`✅ 成功清空所有数据！\n- 持仓: ${data.positions_cleared} 个\n- 历史: ${data.history_cleared} 条`, 'success');
            loadPositions(); // 重新加载持仓
            loadTradeHistory(); // 重新加载交易历史

            // 重置账户余额显示
            if (currentAccountId) {
                loadAccountInfo(currentAccountId);
            }
        } else {
            showNotification(`❌ 清空数据失败: ${data.error}`, 'error');
        }
    })
    .catch(error => {
        console.error('清空数据失败:', error);
        showNotification('❌ 清空数据失败，请重试', 'error');
    })
    .finally(() => {
        // 恢复按钮状态
        clearBtn.innerHTML = originalText;
        clearBtn.disabled = false;
    });
}

// 根据风险等级自动调整止损止盈
function updateStopLossTakeProfitByRisk() {
    const riskLevel = document.getElementById('riskLevel').value;
    const stopLossInput = document.getElementById('aiStopLossPercent');
    const takeProfitInput = document.getElementById('aiTakeProfitPercent');

    if (!stopLossInput || !takeProfitInput) {
        return;
    }

    // 根据风险等级设置止损止盈
    const riskConfig = {
        'conservative': { stopLoss: 1.0, takeProfit: 1.5 },  // 保守型
        'moderate': { stopLoss: 1.0, takeProfit: 2.0 },      // 稳健型
        'aggressive': { stopLoss: 1.5, takeProfit: 3.0 }     // 激进型
    };

    const config = riskConfig[riskLevel] || riskConfig['moderate'];

    stopLossInput.value = config.stopLoss;
    takeProfitInput.value = config.takeProfit;

    console.log(`✅ 根据风险等级 ${riskLevel} 自动调整: 止损${config.stopLoss}%, 止盈${config.takeProfit}%`);

    // 显示调整通知
    showNotification(`✅ 已根据${getRiskLevelName(riskLevel)}自动调整止损止盈\n\n止损: ${config.stopLoss}%\n止盈: ${config.takeProfit}%`, 'success');
}

// 获取风险等级中文名称
function getRiskLevelName(riskLevel) {
    const names = {
        'conservative': '保守型',
        'moderate': '稳健型',
        'aggressive': '激进型'
    };
    return names[riskLevel] || '稳健型';
}

// 实时保证金计算器
function calculateMargin() {
    const symbol = document.getElementById('marginSymbol').value;
    const lots = parseFloat(document.getElementById('marginLots').value) || 0;
    const price = parseFloat(document.getElementById('marginPrice').value) || 0;
    const leverage = parseFloat(document.getElementById('marginLeverage').value) || 100;

    let contractValue = 0;

    // 根据不同交易品种计算合约价值
    switch(symbol) {
        case 'XAUUSD':
            // 黄金: 1手 = 100盎司
            contractValue = lots * 100 * price;
            break;
        case 'EURUSD':
        case 'GBPUSD':
            // 外汇: 1手 = 100,000基础货币
            contractValue = lots * 100000 * price;
            break;
        case 'USDJPY':
            // 美日: 1手 = 100,000美元
            contractValue = lots * 100000;
            break;
        default:
            contractValue = lots * 100000 * price;
    }

    // 计算保证金
    const margin = contractValue / leverage;

    // 显示结果
    document.getElementById('marginResult').value = margin.toFixed(2);

    // 更新价格输入框的占位符
    updatePricePlaceholder(symbol);
}

// 更新价格输入框的占位符和默认值
function updatePricePlaceholder(symbol) {
    const priceInput = document.getElementById('marginPrice');

    switch(symbol) {
        case 'XAUUSD':
            if (!priceInput.value || priceInput.value == '1.1000') {
                priceInput.value = '3300.00';
            }
            priceInput.placeholder = '如: 3300.00';
            break;
        case 'EURUSD':
            if (!priceInput.value || priceInput.value == '3300.00') {
                priceInput.value = '1.1000';
            }
            priceInput.placeholder = '如: 1.1000';
            break;
        case 'GBPUSD':
            if (!priceInput.value || priceInput.value == '3300.00' || priceInput.value == '1.1000') {
                priceInput.value = '1.3000';
            }
            priceInput.placeholder = '如: 1.3000';
            break;
        case 'USDJPY':
            if (!priceInput.value || priceInput.value == '3300.00' || priceInput.value == '1.1000' || priceInput.value == '1.3000') {
                priceInput.value = '150.00';
            }
            priceInput.placeholder = '如: 150.00';
            break;
    }

    // 重新计算保证金
    calculateMargin();
}

// 页面加载时初始化保证金计算器
document.addEventListener('DOMContentLoaded', function() {
    // 添加品种选择变化事件
    const marginSymbolSelect = document.getElementById('marginSymbol');
    if (marginSymbolSelect) {
        marginSymbolSelect.addEventListener('change', function() {
            updatePricePlaceholder(this.value);
        });

        // 初始计算
        setTimeout(calculateMargin, 100);
    }

    // 初始化图表
    initTradingChart();

    // 监听图表品种变化
    const chartSymbolSelect = document.getElementById('chartSymbol');
    if (chartSymbolSelect) {
        chartSymbolSelect.addEventListener('change', updateChartSymbol);
    }
});

// ==================== 实时价格图表功能 ====================

let currentChartSymbol = 'XAUUSD';
let currentTimeframe = 'M1';
let chartUpdateInterval;
let tradingChart;

// 初始化交易图表
function initTradingChart() {
    // 加载Plotly.js
    if (typeof Plotly === 'undefined') {
        const script = document.createElement('script');
        script.src = 'https://cdn.plot.ly/plotly-3.0.1.min.js';
        script.onload = function() {
            createTradingChart();
            startChartUpdates();
        };
        document.head.appendChild(script);
    } else {
        createTradingChart();
        startChartUpdates();
    }
}

// 创建交易图表
function createTradingChart() {
    const data = generateChartData();
    const traces = [];

    // K线图
    traces.push({
        x: data.map(d => d.time),
        open: data.map(d => d.open),
        high: data.map(d => d.high),
        low: data.map(d => d.low),
        close: data.map(d => d.close),
        type: 'candlestick',
        name: currentChartSymbol,
        increasing: { line: { color: '#00ff88' } },
        decreasing: { line: { color: '#ff4444' } },
        xaxis: 'x',
        yaxis: 'y'
    });

    // 添加移动平均线
    if (document.getElementById('showMA5').checked) {
        traces.push({
            x: data.map(d => d.time),
            y: data.map(d => d.ma5),
            type: 'scatter',
            mode: 'lines',
            name: 'MA5',
            line: { color: '#ff7f0e', width: 1 },
            xaxis: 'x',
            yaxis: 'y'
        });
    }

    if (document.getElementById('showMA20').checked) {
        traces.push({
            x: data.map(d => d.time),
            y: data.map(d => d.ma20),
            type: 'scatter',
            mode: 'lines',
            name: 'MA20',
            line: { color: '#d62728', width: 1 },
            xaxis: 'x',
            yaxis: 'y'
        });
    }

    if (document.getElementById('showMA50').checked) {
        traces.push({
            x: data.map(d => d.time),
            y: data.map(d => d.ma50),
            type: 'scatter',
            mode: 'lines',
            name: 'MA50',
            line: { color: '#9467bd', width: 1 },
            xaxis: 'x',
            yaxis: 'y'
        });
    }

    // 布林带
    if (document.getElementById('showBollinger').checked) {
        traces.push({
            x: data.map(d => d.time),
            y: data.map(d => d.bb_upper),
            type: 'scatter',
            mode: 'lines',
            name: '布林上轨',
            line: { color: '#17becf', width: 1, dash: 'dash' },
            xaxis: 'x',
            yaxis: 'y'
        });

        traces.push({
            x: data.map(d => d.time),
            y: data.map(d => d.bb_lower),
            type: 'scatter',
            mode: 'lines',
            name: '布林下轨',
            line: { color: '#17becf', width: 1, dash: 'dash' },
            fill: 'tonexty',
            fillcolor: 'rgba(23, 190, 207, 0.1)',
            xaxis: 'x',
            yaxis: 'y'
        });
    }

    // 成交量
    if (document.getElementById('showVolume').checked) {
        traces.push({
            x: data.map(d => d.time),
            y: data.map(d => d.volume),
            type: 'bar',
            name: '成交量',
            marker: { color: 'rgba(158,202,225,0.8)' },
            xaxis: 'x',
            yaxis: 'y2'
        });
    }

    // RSI指标
    if (document.getElementById('showRSI').checked) {
        traces.push({
            x: data.map(d => d.time),
            y: data.map(d => d.rsi),
            type: 'scatter',
            mode: 'lines',
            name: 'RSI',
            line: { color: '#8c564b', width: 1 },
            xaxis: 'x',
            yaxis: 'y3'
        });
    }

    // MACD指标
    if (document.getElementById('showMACD').checked) {
        traces.push({
            x: data.map(d => d.time),
            y: data.map(d => d.macd),
            type: 'scatter',
            mode: 'lines',
            name: 'MACD',
            line: { color: '#e377c2', width: 1 },
            xaxis: 'x',
            yaxis: 'y4'
        });

        traces.push({
            x: data.map(d => d.time),
            y: data.map(d => d.macd_signal),
            type: 'scatter',
            mode: 'lines',
            name: 'MACD信号',
            line: { color: '#7f7f7f', width: 1 },
            xaxis: 'x',
            yaxis: 'y4'
        });
    }

    // 计算子图数量和高度
    let subplotCount = 1; // 主图
    if (document.getElementById('showVolume').checked) subplotCount++;
    if (document.getElementById('showRSI').checked) subplotCount++;
    if (document.getElementById('showMACD').checked) subplotCount++;

    const mainHeight = 0.6;
    const subHeight = (1 - mainHeight) / (subplotCount - 1);

    const layout = {
        title: `${currentChartSymbol} ${currentTimeframe}`,
        height: 400,
        margin: { t: 40, r: 10, b: 40, l: 60 },
        showlegend: false,
        xaxis: {
            type: 'date',
            rangeslider: { visible: false },
            domain: [0, 1]
        },
        yaxis: {
            title: '价格',
            domain: [subplotCount > 1 ? subHeight * (subplotCount - 1) : 0, 1]
        },
        template: 'plotly_white',
        dragmode: 'pan'
    };

    // 添加子图y轴
    let currentY = 2;
    if (document.getElementById('showVolume').checked) {
        layout[`yaxis${currentY}`] = {
            title: '成交量',
            domain: [subHeight * (subplotCount - currentY), subHeight * (subplotCount - currentY + 1) - 0.02],
            side: 'right'
        };
        currentY++;
    }

    if (document.getElementById('showRSI').checked) {
        layout[`yaxis${currentY}`] = {
            title: 'RSI',
            domain: [subHeight * (subplotCount - currentY), subHeight * (subplotCount - currentY + 1) - 0.02],
            range: [0, 100]
        };
        currentY++;
    }

    if (document.getElementById('showMACD').checked) {
        layout[`yaxis${currentY}`] = {
            title: 'MACD',
            domain: [0, subHeight - 0.02]
        };
    }

    const config = {
        responsive: true,
        displayModeBar: true,
        modeBarButtonsToRemove: ['pan2d', 'lasso2d', 'select2d'],
        displaylogo: false
    };

    Plotly.newPlot('tradingChart', traces, layout, config);

    // 更新技术分析摘要
    updateTechnicalSummary(data);
    updatePriceInfo(data);
}

// 生成图表数据（已禁用模拟数据）
function generateChartData() {
    console.error('❌ generateChartData: 模拟图表数据已禁用，需要集成真实数据源');
    return [];
}

// 获取基础价格
function getBasePrice(symbol) {
    const basePrices = {
        'XAUUSD': 3300.00,
        'EURUSD': 1.1000,
        'GBPUSD': 1.3000,
        'USDJPY': 150.00,
        'USDCHF': 0.9200,
        'AUDUSD': 0.7500
    };
    return basePrices[symbol] || 1.0000;
}

// 获取波动率
function getVolatility(symbol) {
    const volatilities = {
        'XAUUSD': 5.0,
        'EURUSD': 0.002,
        'GBPUSD': 0.003,
        'USDJPY': 0.5,
        'USDCHF': 0.002,
        'AUDUSD': 0.003
    };
    return volatilities[symbol] || 0.002;
}

// 获取时间框架毫秒数
function getTimeframeMs(timeframe) {
    const timeframes = {
        'M1': 60 * 1000,
        'M5': 5 * 60 * 1000,
        'M15': 15 * 60 * 1000,
        'H1': 60 * 60 * 1000,
        'H4': 4 * 60 * 60 * 1000,
        'D1': 24 * 60 * 60 * 1000
    };
    return timeframes[timeframe] || 60 * 1000;
}

// 更新图表
function updateChart() {
    createTradingChart();
}

// 设置时间框架
function setTimeframe(timeframe) {
    currentTimeframe = timeframe;

    // 更新按钮状态
    document.querySelectorAll('.timeframe-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[data-timeframe="${timeframe}"]`).classList.add('active');

    updateChart();
}

// 更新图表品种
function updateChartSymbol() {
    currentChartSymbol = document.getElementById('chartSymbol').value;
    updateChart();
}

// 开始图表更新
function startChartUpdates() {
    // 清除现有定时器
    if (chartUpdateInterval) {
        clearInterval(chartUpdateInterval);
    }

    // 根据时间框架设置更新频率
    const updateFrequency = currentTimeframe === 'M1' ? 5000 :
                           currentTimeframe === 'M5' ? 15000 :
                           currentTimeframe === 'M15' ? 30000 : 60000;

    chartUpdateInterval = setInterval(() => {
        updateChart();
    }, updateFrequency);
}

// 更新技术分析摘要
function updateTechnicalSummary(data) {
    const latest = data[data.length - 1];
    const previous = data[data.length - 2];

    if (!latest || !previous) return;

    // 移动平均分析
    let maTrend = 'neutral';
    if (latest.ma5 && latest.ma20) {
        if (latest.close > latest.ma5 && latest.ma5 > latest.ma20) {
            maTrend = 'buy';
        } else if (latest.close < latest.ma5 && latest.ma5 < latest.ma20) {
            maTrend = 'sell';
        }
    }

    // 技术指标分析
    let indicatorTrend = 'neutral';
    if (latest.rsi) {
        if (latest.rsi > 70) {
            indicatorTrend = 'sell';
        } else if (latest.rsi < 30) {
            indicatorTrend = 'buy';
        }
    }

    // 总体评价
    let overallTrend = 'neutral';
    if (maTrend === 'buy' && indicatorTrend !== 'sell') {
        overallTrend = 'buy';
    } else if (maTrend === 'sell' && indicatorTrend !== 'buy') {
        overallTrend = 'sell';
    }

    // 更新显示
    const trendTexts = {
        'buy': '买入',
        'sell': '卖出',
        'neutral': '中性'
    };

    const trendColors = {
        'buy': 'bg-success',
        'sell': 'bg-danger',
        'neutral': 'bg-warning'
    };

    document.getElementById('maTrend').textContent = trendTexts[maTrend];
    document.getElementById('maTrend').className = `badge ${trendColors[maTrend]}`;

    document.getElementById('indicatorTrend').textContent = trendTexts[indicatorTrend];
    document.getElementById('indicatorTrend').className = `badge ${trendColors[indicatorTrend]}`;

    document.getElementById('overallTrend').textContent = trendTexts[overallTrend];
    document.getElementById('overallTrend').className = `badge ${trendColors[overallTrend]}`;
}

// 更新价格信息
function updatePriceInfo(data) {
    const latest = data[data.length - 1];
    const previous = data[data.length - 2];

    if (!latest || !previous) return;

    const currentPrice = latest.close;
    const priceChange = currentPrice - previous.close;
    const priceChangePercent = (priceChange / previous.close) * 100;

    // 更新当前价格
    document.getElementById('currentPrice').textContent = currentPrice.toFixed(5);

    // 更新涨跌
    const changeText = priceChange >= 0 ? `+${priceChange.toFixed(5)}` : priceChange.toFixed(5);
    const changePercentText = priceChangePercent >= 0 ? `+${priceChangePercent.toFixed(2)}%` : `${priceChangePercent.toFixed(2)}%`;
    const changeColor = priceChange >= 0 ? 'text-success' : 'text-danger';

    document.getElementById('priceChange').textContent = changeText;
    document.getElementById('priceChange').className = changeColor;

    document.getElementById('priceChangePercent').textContent = changePercentText;
    document.getElementById('priceChangePercent').className = changeColor;

    // 更新支撑阻力位
    const high = Math.max(...data.slice(-20).map(d => d.high));
    const low = Math.min(...data.slice(-20).map(d => d.low));

    document.getElementById('resistanceLevel').textContent = high.toFixed(5);
    document.getElementById('supportLevel').textContent = low.toFixed(5);

    // 更新时间
    document.getElementById('lastUpdate').textContent = new Date().toLocaleTimeString();
}

// 全屏显示
function toggleFullscreen() {
    const chartElement = document.getElementById('tradingChart');
    if (!document.fullscreenElement) {
        chartElement.requestFullscreen().then(() => {
            // 全屏后调整图表大小
            setTimeout(() => {
                Plotly.Plots.resize('tradingChart');
            }, 100);
        });
    } else {
        document.exitFullscreen();
    }
}

// 保存图表
function saveChart() {
    Plotly.downloadImage('tradingChart', {
        format: 'png',
        width: 1200,
        height: 800,
        filename: `${currentChartSymbol}_${currentTimeframe}_chart`
    });
}

</script>
{% endblock %}
