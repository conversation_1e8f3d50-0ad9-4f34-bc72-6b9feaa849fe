{% extends "base.html" %}

{% block page_title %}交易查询{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-search"></i>
                    交易查询与分析
                </h5>
            </div>
            <div class="card-body">
                <!-- 查询条件面板 -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-filter"></i>
                            查询条件
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label">开始时间</label>
                                <input type="date" class="form-control" id="queryStartDate">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">结束时间</label>
                                <input type="date" class="form-control" id="queryEndDate">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">货币对</label>
                                <select class="form-select" id="querySymbol">
                                    <option value="">全部货币对</option>
                                    <optgroup label="主要外汇对">
                                        <option value="EURUSD">EUR/USD</option>
                                        <option value="GBPUSD">GBP/USD</option>
                                        <option value="USDJPY">USD/JPY</option>
                                        <option value="USDCHF">USD/CHF</option>
                                    </optgroup>
                                    <optgroup label="次要外汇对">
                                        <option value="AUDUSD">AUD/USD</option>
                                        <option value="USDCAD">USD/CAD</option>
                                        <option value="NZDUSD">NZD/USD</option>
                                    </optgroup>
                                    <optgroup label="贵金属">
                                        <option value="XAUUSD">XAU/USD (黄金)</option>
                                        <option value="XAGUSD">XAG/USD (白银)</option>
                                    </optgroup>
                                    <optgroup label="加密货币">
                                        <option value="BTCUSD">BTC/USD</option>
                                        <option value="ETHUSD">ETH/USD</option>
                                    </optgroup>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">交易方向</label>
                                <select class="form-select" id="queryDirection">
                                    <option value="">全部方向</option>
                                    <option value="buy">买入</option>
                                    <option value="sell">卖出</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">账户类型</label>
                                <select class="form-select" id="queryAccountType">
                                    <option value="">全部账户</option>
                                    <option value="demo">模拟账户</option>
                                    <option value="real">真实账户</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">盈亏状态</label>
                                <select class="form-select" id="queryProfitStatus">
                                    <option value="">全部状态</option>
                                    <option value="profit">盈利</option>
                                    <option value="loss">亏损</option>
                                    <option value="breakeven">持平</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">最小金额</label>
                                <input type="number" class="form-control" id="queryMinAmount" placeholder="最小交易金额" step="0.01">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">最大金额</label>
                                <input type="number" class="form-control" id="queryMaxAmount" placeholder="最大交易金额" step="0.01">
                            </div>
                        </div>

                        <!-- 新增数据源和查询选项 -->
                        <div class="row mt-3">
                            <div class="col-md-4">
                                <label class="form-label">数据源</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="includeDatabase" checked>
                                    <label class="form-check-label" for="includeDatabase">
                                        <i class="fas fa-database text-primary"></i> 本地数据库
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="includeMT5" checked>
                                    <label class="form-check-label" for="includeMT5">
                                        <i class="fas fa-chart-line text-success"></i> MT5实时数据
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <label for="pageSize" class="form-label">显示数量</label>
                                <select class="form-select" id="pageSize">
                                    <option value="50">50条</option>
                                    <option value="100" selected>100条</option>
                                    <option value="200">200条</option>
                                    <option value="500">500条</option>
                                    <option value="1000">1000条</option>
                                    <option value="all">全部</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">查询范围</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="queryRange" id="queryRecent" value="recent" checked onchange="toggleDateInputs()">
                                    <label class="form-check-label" for="queryRecent">
                                        <i class="fas fa-calendar-alt text-primary"></i>
                                        最近30天
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="queryRange" id="queryCustom" value="custom" onchange="toggleDateInputs()">
                                    <label class="form-check-label" for="queryCustom">
                                        <i class="fas fa-calendar-check text-info"></i>
                                        自定义时间范围
                                    </label>
                                </div>
                                <small class="text-muted" id="dateRangeHint">
                                    <i class="fas fa-info-circle"></i>
                                    当前将查询最近30天的数据
                                </small>
                            </div>
                        </div>

                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="btn-group">
                                    <button class="btn btn-primary" onclick="executeTradeQuery()">
                                        <i class="fas fa-search"></i>
                                        查询
                                    </button>
                                    <button class="btn btn-outline-secondary" onclick="resetTradeQuery()">
                                        <i class="fas fa-undo"></i>
                                        重置
                                    </button>
                                    <button class="btn btn-outline-success" onclick="exportTradeData()">
                                        <i class="fas fa-download"></i>
                                        导出CSV
                                    </button>
                                    <button class="btn btn-outline-info" onclick="showQuickFilters()">
                                        <i class="fas fa-magic"></i>
                                        快速筛选
                                    </button>
                                    <button class="btn btn-outline-warning" onclick="refreshData()">
                                        <i class="fas fa-sync-alt"></i>
                                        刷新数据
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 快速筛选按钮 -->
                        <div class="row mt-3" id="quickFilters" style="display: none;">
                            <div class="col-12">
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-magic"></i> 快速筛选</h6>
                                    <div class="btn-group-sm" role="group">
                                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="setQuickFilter('today')">今日</button>
                                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="setQuickFilter('week')">本周</button>
                                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="setQuickFilter('month')">本月</button>
                                        <button type="button" class="btn btn-outline-success btn-sm" onclick="setQuickFilter('profit')">盈利交易</button>
                                        <button type="button" class="btn btn-outline-danger btn-sm" onclick="setQuickFilter('loss')">亏损交易</button>
                                        <button type="button" class="btn btn-outline-warning btn-sm" onclick="setQuickFilter('gold')">黄金交易</button>
                                        <button type="button" class="btn btn-outline-info btn-sm" onclick="setQuickFilter('large')">大额交易</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 统计信息面板 -->
                <div class="row mb-4" id="queryStats" style="display: none;">
                    <div class="col-12">
                        <div class="card bg-light">
                            <div class="card-header">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-chart-bar"></i>
                                    查询统计
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row text-center">
                                    <div class="col-md-2">
                                        <div class="border-end">
                                            <h4 id="totalTrades" class="text-primary">0</h4>
                                            <small class="text-muted">总交易数</small>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="border-end">
                                            <h4 id="totalProfit" class="text-success">$0.00</h4>
                                            <small class="text-muted">总盈亏</small>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="border-end">
                                            <h4 id="winRate" class="text-info">0%</h4>
                                            <small class="text-muted">胜率</small>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="border-end">
                                            <h4 id="avgProfit">$0.00</h4>
                                            <small class="text-muted">平均盈亏</small>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="border-end">
                                            <h4 id="maxProfit" class="text-success">$0.00</h4>
                                            <small class="text-muted">最大盈利</small>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <h4 id="maxLoss" class="text-danger">$0.00</h4>
                                        <small class="text-muted">最大亏损</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 交易结果表格 -->
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-list"></i>
                                查询结果
                            </h6>
                            <span class="badge bg-secondary" id="resultCount">0 条记录</span>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover" id="queryResultTable">
                                <thead class="table-light">
                                    <tr>
                                        <th>账户</th>
                                        <th>时间</th>
                                        <th>货币对</th>
                                        <th>方向</th>
                                        <th>数量</th>
                                        <th>开仓价</th>
                                        <th>平仓价</th>
                                        <th>盈亏</th>
                                        <th>状态</th>
                                        <th>数据源</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td colspan="10" class="text-center text-muted py-4">
                                            <i class="fas fa-search fa-2x mb-2"></i>
                                            <br>请设置查询条件并点击"查询"按钮
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- 分页 -->
                        <nav id="pagination" style="display: none;">
                            <ul class="pagination justify-content-center">
                                <li class="page-item">
                                    <a class="page-link" href="#" onclick="changePage(-1)">上一页</a>
                                </li>
                                <li class="page-item active">
                                    <span class="page-link" id="currentPage">1</span>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="#" onclick="changePage(1)">下一页</a>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 交易详情模态框 -->
<div class="modal fade" id="tradeDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-info-circle"></i>
                    交易详情
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="tradeDetailContent">
                <!-- 详情内容将通过JavaScript填充 -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 页面加载时设置默认日期
document.addEventListener('DOMContentLoaded', function() {
    // 设置默认查询时间为今天（1天时间范围）
    const today = new Date();
    const todayStr = today.toISOString().split('T')[0];

    document.getElementById('queryEndDate').value = todayStr;
    document.getElementById('queryStartDate').value = todayStr;

    console.log('📅 设置默认查询时间为今天:', todayStr);
});

let currentPage = 1;
let totalPages = 1;
let allTrades = [];

// 执行交易查询
function executeTradeQuery() {
    // 检查数据源选择
    const includeDatabase = document.getElementById('includeDatabase').checked;
    const includeMT5 = document.getElementById('includeMT5').checked;

    if (!includeDatabase && !includeMT5) {
        alert('请至少选择一个数据源');
        return;
    }

    // 处理查询范围
    let startDate = '';
    let endDate = '';

    const queryRange = document.querySelector('input[name="queryRange"]:checked').value;
    if (queryRange === 'recent') {
        // 最近30天
        const now = new Date();
        const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        startDate = thirtyDaysAgo.toISOString().split('T')[0];
        endDate = now.toISOString().split('T')[0];
        console.log('📅 使用最近30天范围:', { startDate, endDate });
    } else {
        // 自定义时间范围
        startDate = document.getElementById('queryStartDate').value;
        endDate = document.getElementById('queryEndDate').value;

        // 如果自定义时间范围为空，默认使用最近7天
        if (!startDate || !endDate) {
            const now = new Date();
            const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
            startDate = startDate || sevenDaysAgo.toISOString().split('T')[0];
            endDate = endDate || now.toISOString().split('T')[0];
            console.log('📅 自定义时间范围为空，使用默认7天范围:', { startDate, endDate });
        } else {
            console.log('📅 使用自定义时间范围:', { startDate, endDate });
        }
    }

    const queryParams = {
        start_date: startDate,
        end_date: endDate,
        symbol: document.getElementById('querySymbol').value,
        direction: document.getElementById('queryDirection').value,
        account_type: document.getElementById('queryAccountType').value,
        profit_status: document.getElementById('queryProfitStatus').value,
        min_amount: document.getElementById('queryMinAmount').value,
        max_amount: document.getElementById('queryMaxAmount').value,
        include_mt5: includeMT5.toString(),
        include_database: includeDatabase.toString(),
        page_size: document.getElementById('pageSize').value
    };

    // 移除空值参数（但保留重要参数：数据源和日期）
    Object.keys(queryParams).forEach(key => {
        if (!queryParams[key] &&
            key !== 'include_mt5' &&
            key !== 'include_database' &&
            key !== 'start_date' &&
            key !== 'end_date') {
            delete queryParams[key];
        }
    });

    console.log('🔍 执行交易查询:', queryParams);

    // 显示加载状态
    const tbody = document.querySelector('#queryResultTable tbody');
    tbody.innerHTML = '<tr><td colspan="11" class="text-center"><i class="fas fa-spinner fa-spin"></i> 查询中...</td></tr>';

    // 构建查询URL
    const queryString = new URLSearchParams(queryParams).toString();
    const url = `/api/trading/query${queryString ? '?' + queryString : ''}`;

    fetch(url)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                allTrades = data.trades;
                displayQueryResults(data.trades, data.statistics);
                showQueryStats(data.statistics);

                // 显示数据来源统计
                const dbCount = data.trades.filter(t => t.source === 'database').length;
                const mt5Count = data.trades.filter(t => t.source === 'mt5').length;

                document.getElementById('resultCount').textContent =
                    `${data.trades.length} 条记录 (数据库: ${dbCount}, MT5: ${mt5Count})`;

                console.log(`✅ 查询成功，总计 ${data.trades.length} 条记录`);
                console.log(`   - 本地数据库: ${dbCount} 条`);
                console.log(`   - MT5数据: ${mt5Count} 条`);
            } else {
                console.error('查询失败:', data.error);
                tbody.innerHTML = `<tr><td colspan="11" class="text-center text-danger">查询失败: ${data.error}</td></tr>`;
            }
        })
        .catch(error => {
            console.error('查询请求失败:', error);
            tbody.innerHTML = `<tr><td colspan="11" class="text-center text-danger">查询请求失败: ${error.message}</td></tr>`;
        });
}

// 显示查询结果
function displayQueryResults(trades, statistics) {
    const tbody = document.querySelector('#queryResultTable tbody');
    tbody.innerHTML = '';

    if (trades && trades.length > 0) {
        trades.forEach(trade => {
            const profitClass = trade.profit >= 0 ? 'text-success' : 'text-danger';
            const directionBadge = trade.direction === 'buy' ? 'bg-success' : 'bg-danger';
            const directionText = trade.direction === 'buy' ? '买入' : '卖出';
            const accountBadge = trade.account_type === 'real' ? 'bg-primary' : 'bg-info';
            const accountText = trade.account_type === 'real' ? '真实' : '模拟';

            // 数据源显示
            const sourceClass = trade.source === 'mt5' ? 'bg-success' : 'bg-primary';
            const sourceText = trade.source === 'mt5' ? 'MT5' : '数据库';
            const sourceIcon = trade.source === 'mt5' ? 'fa-chart-line' : 'fa-database';

            const row = `
                <tr>
                    <td><span class="badge ${accountBadge}">${trade.account_name || accountText}</span></td>
                    <td>${new Date(trade.open_time).toLocaleString('zh-CN')}</td>
                    <td><strong>${trade.symbol}</strong></td>
                    <td><span class="badge ${directionBadge}">${directionText}</span></td>
                    <td>${trade.volume}</td>
                    <td>$${parseFloat(trade.open_price).toFixed(5)}</td>
                    <td>${trade.close_price ? '$' + parseFloat(trade.close_price).toFixed(5) : '-'}</td>
                    <td class="${profitClass}"><strong>$${parseFloat(trade.profit || 0).toFixed(2)}</strong></td>
                    <td><span class="badge ${trade.status === 'closed' ? 'bg-secondary' : 'bg-warning'}">${trade.status === 'closed' ? '已平仓' : '持仓中'}</span></td>
                    <td>
                        <span class="badge ${sourceClass}">
                            <i class="fas ${sourceIcon}"></i>
                            ${sourceText}
                        </span>
                    </td>
                    <td>
                        <button class="btn btn-sm btn-outline-info" onclick="showTradeDetail('${trade.id}')">
                            <i class="fas fa-eye"></i>
                        </button>
                    </td>
                </tr>
            `;
            tbody.innerHTML += row;
        });
    } else {
        tbody.innerHTML = '<tr><td colspan="11" class="text-center text-muted py-4"><i class="fas fa-inbox"></i><br>没有找到符合条件的交易记录</td></tr>';
    }
}

// 显示查询统计信息
function showQueryStats(stats) {
    if (!stats) return;

    document.getElementById('totalTrades').textContent = stats.total_trades || 0;
    document.getElementById('totalProfit').textContent = `$${(stats.total_profit || 0).toFixed(2)}`;
    document.getElementById('totalProfit').className = stats.total_profit >= 0 ? 'text-success' : 'text-danger';
    document.getElementById('winRate').textContent = `${(stats.win_rate || 0).toFixed(1)}%`;
    document.getElementById('avgProfit').textContent = `$${(stats.avg_profit || 0).toFixed(2)}`;
    document.getElementById('avgProfit').className = stats.avg_profit >= 0 ? 'text-success' : 'text-danger';
    document.getElementById('maxProfit').textContent = `$${(stats.max_profit || 0).toFixed(2)}`;
    document.getElementById('maxLoss').textContent = `$${(stats.max_loss || 0).toFixed(2)}`;

    // 显示统计面板
    document.getElementById('queryStats').style.display = 'block';
}

// 重置查询条件
function resetTradeQuery() {
    document.getElementById('queryStartDate').value = '';
    document.getElementById('queryEndDate').value = '';
    document.getElementById('querySymbol').value = '';
    document.getElementById('queryDirection').value = '';
    document.getElementById('queryAccountType').value = '';
    document.getElementById('queryProfitStatus').value = '';
    document.getElementById('queryMinAmount').value = '';
    document.getElementById('queryMaxAmount').value = '';

    // 隐藏统计面板
    document.getElementById('queryStats').style.display = 'none';
    document.getElementById('resultCount').textContent = '0 条记录';

    // 重置表格
    const tbody = document.querySelector('#queryResultTable tbody');
    tbody.innerHTML = '<tr><td colspan="10" class="text-center text-muted py-4"><i class="fas fa-search fa-2x mb-2"></i><br>请设置查询条件并点击"查询"按钮</td></tr>';

    // 重新设置默认日期
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 30);

    document.getElementById('queryEndDate').value = endDate.toISOString().split('T')[0];
    document.getElementById('queryStartDate').value = startDate.toISOString().split('T')[0];
}

// 导出交易数据
function exportTradeData() {
    const queryParams = {
        start_date: document.getElementById('queryStartDate').value,
        end_date: document.getElementById('queryEndDate').value,
        symbol: document.getElementById('querySymbol').value,
        direction: document.getElementById('queryDirection').value,
        account_type: document.getElementById('queryAccountType').value,
        profit_status: document.getElementById('queryProfitStatus').value,
        min_amount: document.getElementById('queryMinAmount').value,
        max_amount: document.getElementById('queryMaxAmount').value,
        export: 'csv'
    };

    // 移除空值参数
    Object.keys(queryParams).forEach(key => {
        if (!queryParams[key]) {
            delete queryParams[key];
        }
    });

    const queryString = new URLSearchParams(queryParams).toString();
    const url = `/api/trading/export${queryString ? '?' + queryString : ''}`;

    // 创建下载链接
    const link = document.createElement('a');
    link.href = url;
    link.download = `trading_history_${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// 显示/隐藏快速筛选
function showQuickFilters() {
    const quickFilters = document.getElementById('quickFilters');
    quickFilters.style.display = quickFilters.style.display === 'none' ? 'block' : 'none';
}

// 快速筛选
function setQuickFilter(type) {
    const today = new Date();
    const startDate = new Date();

    switch(type) {
        case 'today':
            document.getElementById('queryStartDate').value = today.toISOString().split('T')[0];
            document.getElementById('queryEndDate').value = today.toISOString().split('T')[0];
            break;
        case 'week':
            startDate.setDate(today.getDate() - 7);
            document.getElementById('queryStartDate').value = startDate.toISOString().split('T')[0];
            document.getElementById('queryEndDate').value = today.toISOString().split('T')[0];
            break;
        case 'month':
            startDate.setMonth(today.getMonth() - 1);
            document.getElementById('queryStartDate').value = startDate.toISOString().split('T')[0];
            document.getElementById('queryEndDate').value = today.toISOString().split('T')[0];
            break;
        case 'profit':
            document.getElementById('queryProfitStatus').value = 'profit';
            break;
        case 'loss':
            document.getElementById('queryProfitStatus').value = 'loss';
            break;
        case 'gold':
            document.getElementById('querySymbol').value = 'XAUUSD';
            break;
        case 'large':
            document.getElementById('queryMinAmount').value = '0.1';
            break;
    }

    // 自动执行查询
    executeTradeQuery();
}

// 刷新数据
function refreshData() {
    console.log('🔄 刷新交易数据...');
    executeTradeQuery();
}

// 切换日期输入控制
function toggleDateInputs() {
    const queryRange = document.querySelector('input[name="queryRange"]:checked').value;
    const startDateInput = document.getElementById('queryStartDate');
    const endDateInput = document.getElementById('queryEndDate');
    const dateRangeHint = document.getElementById('dateRangeHint');

    if (queryRange === 'recent') {
        // 最近30天模式
        startDateInput.disabled = true;
        endDateInput.disabled = true;
        startDateInput.style.opacity = '0.6';
        endDateInput.style.opacity = '0.6';
        dateRangeHint.innerHTML = '<i class="fas fa-info-circle"></i> 当前将查询最近30天的数据';
        dateRangeHint.className = 'text-muted';
    } else {
        // 自定义时间范围模式
        startDateInput.disabled = false;
        endDateInput.disabled = false;
        startDateInput.style.opacity = '1';
        endDateInput.style.opacity = '1';

        // 检查是否设置了日期
        if (!startDateInput.value || !endDateInput.value) {
            dateRangeHint.innerHTML = '<i class="fas fa-exclamation-triangle"></i> 请设置开始和结束日期，否则将使用默认7天范围';
            dateRangeHint.className = 'text-warning';
        } else {
            const startDate = new Date(startDateInput.value);
            const endDate = new Date(endDateInput.value);
            const daysDiff = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));
            dateRangeHint.innerHTML = `<i class="fas fa-check-circle"></i> 将查询 ${daysDiff + 1} 天的数据 (${startDateInput.value} 到 ${endDateInput.value})`;
            dateRangeHint.className = 'text-success';
        }
    }
}

// 监听日期输入变化
document.addEventListener('DOMContentLoaded', function() {
    const startDateInput = document.getElementById('queryStartDate');
    const endDateInput = document.getElementById('queryEndDate');

    startDateInput.addEventListener('change', toggleDateInputs);
    endDateInput.addEventListener('change', toggleDateInputs);

    // 初始化状态
    toggleDateInputs();
});

// 显示交易详情
function showTradeDetail(tradeId) {
    const trade = allTrades.find(t => t.id == tradeId);
    if (!trade) return;

    const content = `
        <div class="row">
            <div class="col-md-6">
                <h6>基本信息</h6>
                <table class="table table-sm">
                    <tr><td>交易ID:</td><td>${trade.id}</td></tr>
                    <tr><td>账户:</td><td>${trade.account_name}</td></tr>
                    <tr><td>货币对:</td><td><strong>${trade.symbol}</strong></td></tr>
                    <tr><td>方向:</td><td><span class="badge ${trade.direction === 'buy' ? 'bg-success' : 'bg-danger'}">${trade.direction === 'buy' ? '买入' : '卖出'}</span></td></tr>
                    <tr><td>数量:</td><td>${trade.volume}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>价格信息</h6>
                <table class="table table-sm">
                    <tr><td>开仓价:</td><td>$${parseFloat(trade.open_price).toFixed(5)}</td></tr>
                    <tr><td>平仓价:</td><td>${trade.close_price ? '$' + parseFloat(trade.close_price).toFixed(5) : '未平仓'}</td></tr>
                    <tr><td>盈亏:</td><td class="${trade.profit >= 0 ? 'text-success' : 'text-danger'}"><strong>$${parseFloat(trade.profit || 0).toFixed(2)}</strong></td></tr>
                    <tr><td>状态:</td><td><span class="badge ${trade.status === 'closed' ? 'bg-secondary' : 'bg-warning'}">${trade.status === 'closed' ? '已平仓' : '持仓中'}</span></td></tr>
                </table>
            </div>
        </div>
        <div class="row">
            <div class="col-12">
                <h6>时间信息</h6>
                <table class="table table-sm">
                    <tr><td>开仓时间:</td><td>${new Date(trade.open_time).toLocaleString('zh-CN')}</td></tr>
                    <tr><td>平仓时间:</td><td>${trade.close_time ? new Date(trade.close_time).toLocaleString('zh-CN') : '未平仓'}</td></tr>
                    <tr><td>持仓时长:</td><td>${trade.close_time ? calculateDuration(trade.open_time, trade.close_time) : '持仓中'}</td></tr>
                </table>
            </div>
        </div>
    `;

    document.getElementById('tradeDetailContent').innerHTML = content;
    new bootstrap.Modal(document.getElementById('tradeDetailModal')).show();
}

// 计算持仓时长
function calculateDuration(openTime, closeTime) {
    const start = new Date(openTime);
    const end = new Date(closeTime);
    const diff = end - start;

    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

    if (days > 0) {
        return `${days}天 ${hours}小时 ${minutes}分钟`;
    } else if (hours > 0) {
        return `${hours}小时 ${minutes}分钟`;
    } else {
        return `${minutes}分钟`;
    }
}


</script>
{% endblock %}
