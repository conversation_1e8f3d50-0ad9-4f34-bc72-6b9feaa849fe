#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证参数优化功能改进
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def verify_frontend_changes():
    """验证前端代码改进"""
    print("🔍 验证前端代码改进...")
    
    try:
        with open('templates/model_inference.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查表格头部是否包含新字段
        if '<th>盈利单</th>' in content and '<th>亏损单</th>' in content:
            print("✅ 前端表格头部包含盈利单和亏损单字段")
        else:
            print("❌ 前端表格头部缺少盈利单或亏损单字段")
        
        # 检查表格数据是否包含新字段
        if 'result.winning_trades' in content and 'result.losing_trades' in content:
            print("✅ 前端表格数据包含盈利单和亏损单显示")
        else:
            print("❌ 前端表格数据缺少盈利单或亏损单显示")
        
        # 检查导出功能
        if 'exportOptimizationResults' in content and '导出结果' in content:
            print("✅ 前端包含导出功能")
        else:
            print("❌ 前端缺少导出功能")
        
        # 检查排序优化
        if 'resort_only: true' in content:
            print("✅ 前端包含快速排序功能")
        else:
            print("❌ 前端缺少快速排序功能")
            
    except Exception as e:
        print(f"❌ 验证前端代码失败: {e}")

def verify_backend_changes():
    """验证后端代码改进"""
    print("\n🔍 验证后端代码改进...")
    
    try:
        # 检查深度学习服务
        from services.deep_learning_service import deep_learning_service
        
        # 检查新方法
        methods_to_check = [
            '_calculate_all_scores',
            'resort_optimization_results', 
            'export_optimization_results_csv',
            '_get_risk_preference_name'
        ]
        
        for method in methods_to_check:
            if hasattr(deep_learning_service, method):
                print(f"✅ 深度学习服务包含 {method} 方法")
            else:
                print(f"❌ 深度学习服务缺少 {method} 方法")
        
        # 检查路由文件
        with open('routes.py', 'r', encoding='utf-8') as f:
            routes_content = f.read()
        
        if 'resort_only' in routes_content:
            print("✅ 路由支持 resort_only 参数")
        else:
            print("❌ 路由缺少 resort_only 参数支持")
        
        if '/api/deep-learning/export-optimization-results' in routes_content:
            print("✅ 路由包含导出API端点")
        else:
            print("❌ 路由缺少导出API端点")
        
        if 'make_response' in routes_content:
            print("✅ 路由导入了 make_response")
        else:
            print("❌ 路由缺少 make_response 导入")
            
    except Exception as e:
        print(f"❌ 验证后端代码失败: {e}")

def verify_data_structure():
    """验证数据结构改进"""
    print("\n🔍 验证数据结构改进...")
    
    try:
        with open('services/deep_learning_service.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否包含盈利亏损单数字段
        if "'winning_trades':" in content and "'losing_trades':" in content:
            print("✅ 优化结果数据结构包含盈利亏损单数字段")
        else:
            print("❌ 优化结果数据结构缺少盈利亏损单数字段")
        
        # 检查是否包含所有得分字段
        if "'all_scores':" in content:
            print("✅ 优化结果数据结构包含所有得分字段")
        else:
            print("❌ 优化结果数据结构缺少所有得分字段")
            
    except Exception as e:
        print(f"❌ 验证数据结构失败: {e}")

def main():
    """主验证函数"""
    print("🧪 开始验证参数优化功能改进...")
    print("=" * 50)
    
    verify_frontend_changes()
    verify_backend_changes()
    verify_data_structure()
    
    print("\n" + "=" * 50)
    print("🎉 验证完成!")
    print("\n📋 改进功能总结:")
    print("1. ✅ 增加盈利亏损单数字段 - 在参数组合排名列表中显示盈利单和亏损单数")
    print("2. ✅ 优化排序逻辑避免重复执行 - 切换排序时使用预计算的得分快速重排")
    print("3. ✅ 增加结果导出下载功能 - 支持CSV格式导出所有参数组合和统计数据")
    
    print("\n💡 使用方法:")
    print("1. 进入模型推理页面")
    print("2. 配置参数并执行参数优化")
    print("3. 查看结果表格中的盈利单和亏损单列")
    print("4. 切换排序方式体验快速重排功能")
    print("5. 点击导出按钮下载详细结果")

if __name__ == "__main__":
    main()
