#!/usr/bin/env python3
"""
重启深度学习训练
"""

import requests
import json
import time

def login_session():
    """创建登录会话"""
    session = requests.Session()
    
    try:
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post('http://127.0.0.1:5000/login', data=login_data)
        return session if response.status_code == 200 else None
            
    except Exception as e:
        print(f"❌ 登录失败: {e}")
        return None

def start_optimized_training():
    """启动优化的训练配置"""
    
    print("🚀 启动优化的深度学习训练")
    print("=" * 50)
    
    session = login_session()
    if not session:
        print("❌ 无法登录")
        return None
    
    # 使用优化的配置，避免卡住
    config = {
        'model_name': f'optimized_training_{int(time.time())}',
        'model_type': 'LSTM',  # 使用稳定的LSTM
        'symbol': 'XAUUSD',
        'timeframe': 'H1',
        'epochs': 50,  # 减少轮次
        'batch_size': 16,  # 适中的批次大小
        'learning_rate': 0.001,
        'validation_split': 0.2,
        'sequence_length': 30,  # 适中的序列长度
        'features': ['close', 'volume'],
        'early_stopping': True,  # 启用早停
        'patience': 15,  # 合理的耐心值
        'min_epochs': 10,  # 最少训练轮次
        'use_gpu': True,
        'save_checkpoints': True
    }
    
    print(f"📝 优化的训练配置:")
    for key, value in config.items():
        print(f"   {key}: {value}")
    
    try:
        print(f"\n🚀 启动训练...")
        
        response = session.post(
            'http://127.0.0.1:5000/api/deep-learning/start-training',
            json=config,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                task_id = result.get('task_id')
                print(f"✅ 训练启动成功!")
                print(f"   任务ID: {task_id}")
                return task_id
            else:
                print(f"❌ 训练启动失败: {result.get('error')}")
                return None
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 启动训练失败: {e}")
        return None

def monitor_training_health(task_id, duration=300):
    """监控训练健康状态"""
    
    print(f"\n📊 监控训练健康状态 (任务: {task_id})")
    print("=" * 60)
    
    session = login_session()
    if not session:
        print("❌ 无法登录")
        return False
    
    start_time = time.time()
    last_progress = -1
    last_update_time = time.time()
    health_checks = []
    
    print(f"🔄 开始健康监控 (时长: {duration//60}分钟):")
    
    for i in range(duration // 5):  # 每5秒检查一次
        try:
            current_time = time.time()
            elapsed = current_time - start_time
            
            # 获取训练进度
            response = session.get(f'http://127.0.0.1:5000/api/deep-learning/training-progress/{task_id}')
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get('success'):
                    progress_data = result['progress']
                    
                    current_status = progress_data.get('status', 'unknown')
                    current_progress = progress_data.get('progress', 0)
                    current_epoch = progress_data.get('epoch', 0)
                    train_loss = progress_data.get('train_loss')
                    val_loss = progress_data.get('val_loss')
                    
                    # 检查进度是否有更新
                    if current_progress != last_progress:
                        last_progress = current_progress
                        last_update_time = current_time
                        print(f"   [{elapsed:.0f}s] 📈 进度更新: {current_progress}% (轮次: {current_epoch})")
                        
                        if train_loss is not None:
                            print(f"   [{elapsed:.0f}s] 📉 损失: 训练={train_loss:.4f}, 验证={val_loss:.4f if val_loss else 'N/A'}")
                    
                    # 健康检查
                    time_since_update = current_time - last_update_time
                    is_healthy = time_since_update < 120  # 2分钟内有更新认为健康
                    
                    health_checks.append({
                        'time': elapsed,
                        'healthy': is_healthy,
                        'progress': current_progress,
                        'status': current_status
                    })
                    
                    # 如果超过2分钟没有进度更新，发出警告
                    if time_since_update > 120:
                        print(f"   [{elapsed:.0f}s] ⚠️ 警告: {time_since_update:.0f}秒无进度更新")
                    
                    # 检查训练状态
                    if current_status == 'completed':
                        print(f"   [{elapsed:.0f}s] 🎉 训练完成!")
                        break
                    elif current_status == 'failed':
                        print(f"   [{elapsed:.0f}s] ❌ 训练失败!")
                        break
                
                else:
                    print(f"   [{elapsed:.0f}s] ❌ API错误: {result.get('error')}")
            else:
                print(f"   [{elapsed:.0f}s] ❌ HTTP错误: {response.status_code}")
            
            time.sleep(5)  # 每5秒检查一次
            
        except Exception as e:
            print(f"   [{elapsed:.0f}s] ❌ 监控异常: {e}")
            time.sleep(5)
    
    # 分析健康状态
    print(f"\n📊 训练健康分析:")
    healthy_count = sum(1 for check in health_checks if check['healthy'])
    total_checks = len(health_checks)
    health_rate = (healthy_count / total_checks * 100) if total_checks > 0 else 0
    
    print(f"   健康检查次数: {total_checks}")
    print(f"   健康次数: {healthy_count}")
    print(f"   健康率: {health_rate:.1f}%")
    
    if health_rate > 80:
        print(f"   ✅ 训练运行健康")
        return True
    elif health_rate > 50:
        print(f"   ⚠️ 训练偶有卡顿")
        return True
    else:
        print(f"   ❌ 训练可能有问题")
        return False

def main():
    """主函数"""
    
    print("🔄 深度学习训练重启和监控")
    print("=" * 80)
    
    print("📋 重启原因分析:")
    print("• 之前的训练任务已卡住超过5分钟")
    print("• 任务已自动清理并标记为失败")
    print("• GPU状态正常，内存使用率0%")
    print("• 可以安全重新启动训练")
    
    # 启动优化的训练
    task_id = start_optimized_training()
    
    if task_id:
        # 监控训练健康状态
        is_healthy = monitor_training_health(task_id, duration=300)  # 监控5分钟
        
        print(f"\n📋 重启结果")
        print("=" * 80)
        
        if is_healthy:
            print(f"🎉 训练重启成功!")
            print(f"✅ 任务ID: {task_id}")
            print(f"✅ 训练运行健康")
            print(f"✅ 使用了优化的配置参数")
            
            print(f"\n💡 优化措施:")
            print(f"• 减少了训练轮次 (50轮)")
            print(f"• 使用适中的批次大小 (16)")
            print(f"• 启用早停机制 (耐心值15)")
            print(f"• 设置最少训练轮次 (10轮)")
            print(f"• 使用稳定的LSTM模型")
            
        else:
            print(f"⚠️ 训练重启但可能仍有问题")
            print(f"💡 建议:")
            print(f"• 检查应用程序控制台日志")
            print(f"• 考虑重启应用程序")
            print(f"• 尝试更小的配置参数")
            
    else:
        print(f"\n❌ 训练重启失败")
        print(f"💡 建议:")
        print(f"• 检查应用程序状态")
        print(f"• 重启应用程序")
        print(f"• 检查系统资源")
    
    print(f"\n🎯 预防训练卡住的建议")
    print("=" * 80)
    
    print(f"📊 配置建议:")
    print(f"• 批次大小: 8-32 (根据GPU内存调整)")
    print(f"• 序列长度: 20-60 (不要过长)")
    print(f"• 训练轮次: 50-100 (启用早停)")
    print(f"• 早停耐心值: 10-20 (防止过拟合)")
    
    print(f"\n🔧 监控建议:")
    print(f"• 定期检查训练进度")
    print(f"• 关注GPU内存使用")
    print(f"• 监控损失值变化")
    print(f"• 设置合理的训练时间预期")
    
    print(f"\n⚠️ 如果再次卡住:")
    print(f"• 运行 check_stuck_training.py 诊断")
    print(f"• 考虑使用更小的模型配置")
    print(f"• 检查系统资源和网络连接")
    print(f"• 必要时重启整个应用程序")

if __name__ == '__main__':
    main()
