#!/usr/bin/env python3
"""
诊断深度学习训练启动问题
"""

import sqlite3
import requests
import json
import time
import os
from datetime import datetime, timedelta

def login_session():
    """创建登录会话"""
    session = requests.Session()
    
    try:
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post('http://127.0.0.1:5000/login', data=login_data)
        return session if response.status_code == 200 else None
            
    except Exception as e:
        return None

def check_recent_training_tasks():
    """检查最近的训练任务"""
    
    print("🔍 检查最近的训练任务")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 获取最近24小时的训练任务
        cursor.execute("""
            SELECT id, model_id, status, progress, current_epoch, total_epochs,
                   train_loss, val_loss, logs, created_at, started_at, completed_at, updated_at,
                   (julianday('now') - julianday(created_at)) * 24 * 60 as minutes_ago
            FROM training_tasks 
            WHERE created_at > datetime('now', '-24 hours')
            ORDER BY created_at DESC 
            LIMIT 10
        """)
        
        tasks = cursor.fetchall()
        
        if not tasks:
            print("❌ 最近24小时内没有训练任务")
            return None
        
        print(f"📊 找到 {len(tasks)} 个最近的训练任务:")
        
        stuck_tasks = []
        
        for i, task in enumerate(tasks, 1):
            (task_id, model_id, status, progress, current_epoch, total_epochs,
             train_loss, val_loss, logs, created_at, started_at, completed_at, 
             updated_at, minutes_ago) = task
            
            print(f"\n🔹 任务 {i}: {task_id}")
            print(f"   模型ID: {model_id}")
            print(f"   状态: {status}")
            print(f"   进度: {progress}%")
            print(f"   轮次: {current_epoch}/{total_epochs}")
            print(f"   创建时间: {created_at} ({minutes_ago:.1f}分钟前)")
            print(f"   开始时间: {started_at}")
            print(f"   更新时间: {updated_at}")
            
            if train_loss is not None:
                print(f"   训练损失: {train_loss:.4f}")
            if val_loss is not None:
                print(f"   验证损失: {val_loss:.4f}")
            
            if logs:
                try:
                    log_data = json.loads(logs)
                    if 'error' in log_data:
                        print(f"   ❌ 错误: {log_data['error']}")
                except:
                    print(f"   📝 日志: {logs[:100]}...")
            
            # 检查是否是卡住的任务
            if status == 'running':
                if updated_at:
                    try:
                        updated_time = datetime.fromisoformat(updated_at.replace('Z', '+00:00'))
                        now = datetime.now()
                        minutes_since_update = (now - updated_time).total_seconds() / 60
                        
                        if minutes_since_update > 5:  # 超过5分钟没更新
                            print(f"   ⚠️ 可能卡住: {minutes_since_update:.1f}分钟未更新")
                            stuck_tasks.append(task_id)
                        else:
                            print(f"   ✅ 正常运行: {minutes_since_update:.1f}分钟前更新")
                    except Exception as e:
                        print(f"   ❌ 时间解析错误: {e}")
                        stuck_tasks.append(task_id)
                else:
                    print(f"   ❌ 没有更新时间记录")
                    stuck_tasks.append(task_id)
        
        conn.close()
        
        if stuck_tasks:
            print(f"\n⚠️ 发现 {len(stuck_tasks)} 个可能卡住的任务:")
            for task_id in stuck_tasks:
                print(f"   - {task_id}")
        
        return tasks[0] if tasks else None
        
    except Exception as e:
        print(f"❌ 检查训练任务失败: {e}")
        return None

def check_training_data_availability():
    """检查训练数据可用性"""
    
    print("\n📊 检查训练数据可用性")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 检查历史数据表
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name LIKE '%history%'
        """)
        
        history_tables = cursor.fetchall()
        
        if history_tables:
            print(f"✅ 找到历史数据表:")
            for table in history_tables:
                table_name = table[0]
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                print(f"   - {table_name}: {count} 条记录")
        else:
            print("❌ 没有找到历史数据表")
        
        # 检查MT5数据
        try:
            import MetaTrader5 as mt5
            
            if mt5.initialize():
                print(f"✅ MT5连接正常")
                
                # 尝试获取一些数据
                rates = mt5.copy_rates_from_pos("XAUUSD", mt5.TIMEFRAME_H1, 0, 100)
                
                if rates is not None and len(rates) > 0:
                    print(f"✅ MT5数据可用: {len(rates)} 条XAUUSD H1数据")
                else:
                    print(f"❌ MT5数据获取失败")
                
                mt5.shutdown()
            else:
                print(f"❌ MT5连接失败")
                
        except ImportError:
            print(f"❌ MT5模块未安装")
        except Exception as e:
            print(f"❌ MT5检查失败: {e}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 数据可用性检查失败: {e}")

def test_simple_training():
    """测试简单的训练启动"""
    
    print("\n🧪 测试简单训练启动")
    print("=" * 60)
    
    session = login_session()
    if not session:
        print("❌ 登录失败")
        return None
    
    try:
        # 使用最简单的配置启动训练
        training_config = {
            'model_name': f'diagnostic_test_{int(time.time())}',
            'model_type': 'LSTM',
            'symbol': 'XAUUSD',
            'timeframe': 'H1',
            'epochs': 1,  # 只训练1轮
            'batch_size': 16,  # 小批次
            'learning_rate': 0.01,
            'validation_split': 0.2,
            'sequence_length': 10,  # 短序列
            'features': ['close', 'volume']  # 简单特征
        }
        
        print(f"📝 训练配置:")
        for key, value in training_config.items():
            print(f"   {key}: {value}")
        
        print(f"\n🚀 启动训练...")
        
        response = session.post(
            'http://127.0.0.1:5000/api/deep-learning/start-training',
            json=training_config,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        print(f"📡 响应状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                task_id = result.get('task_id')
                print(f"✅ 训练启动成功!")
                print(f"   任务ID: {task_id}")
                
                # 立即检查任务状态
                time.sleep(2)
                check_task_immediate_status(session, task_id)
                
                return task_id
            else:
                print(f"❌ 训练启动失败: {result.get('error')}")
                return None
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   错误详情: {error_data}")
            except:
                print(f"   响应内容: {response.text[:200]}...")
            return None
            
    except Exception as e:
        print(f"❌ 测试训练启动失败: {e}")
        return None

def check_task_immediate_status(session, task_id):
    """检查任务的即时状态"""
    
    print(f"\n🔍 检查任务即时状态: {task_id}")
    print("-" * 40)
    
    try:
        # 检查数据库状态
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT status, progress, current_epoch, total_epochs, logs, created_at, updated_at
            FROM training_tasks 
            WHERE id = ?
        """, (task_id,))
        
        task = cursor.fetchone()
        
        if task:
            status, progress, current_epoch, total_epochs, logs, created_at, updated_at = task
            
            print(f"📊 数据库状态:")
            print(f"   状态: {status}")
            print(f"   进度: {progress}%")
            print(f"   轮次: {current_epoch}/{total_epochs}")
            print(f"   创建: {created_at}")
            print(f"   更新: {updated_at}")
            
            if logs:
                try:
                    log_data = json.loads(logs)
                    print(f"   日志: {log_data}")
                except:
                    print(f"   日志: {logs}")
        else:
            print(f"❌ 数据库中找不到任务")
        
        conn.close()
        
        # 检查API状态
        response = session.get(f'http://127.0.0.1:5000/api/deep-learning/training-progress/{task_id}')
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                progress_data = result['progress']
                print(f"\n📡 API状态:")
                print(f"   状态: {progress_data.get('status')}")
                print(f"   进度: {progress_data.get('progress')}%")
                print(f"   轮次: {progress_data.get('epoch')}/{progress_data.get('total_epochs')}")
            else:
                print(f"\n❌ API错误: {result.get('error')}")
        else:
            print(f"\n❌ API请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 检查即时状态失败: {e}")

def check_application_logs():
    """检查应用程序日志"""
    
    print(f"\n📝 检查应用程序日志")
    print("=" * 60)
    
    # 检查是否有日志文件
    log_files = []
    
    for filename in os.listdir('.'):
        if filename.endswith('.log') or 'log' in filename.lower():
            log_files.append(filename)
    
    if log_files:
        print(f"✅ 找到日志文件:")
        for log_file in log_files:
            print(f"   - {log_file}")
            
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    
                # 显示最后20行
                print(f"\n📄 {log_file} 最后20行:")
                for line in lines[-20:]:
                    if 'error' in line.lower() or 'exception' in line.lower():
                        print(f"   ❌ {line.strip()}")
                    elif 'training' in line.lower():
                        print(f"   🔄 {line.strip()}")
                    else:
                        print(f"   📝 {line.strip()}")
                        
            except Exception as e:
                print(f"   ❌ 读取日志失败: {e}")
    else:
        print(f"❌ 没有找到日志文件")
        print(f"💡 建议检查控制台输出或应用程序日志配置")

def main():
    """主函数"""
    
    print("🔧 深度学习训练启动问题诊断")
    print("=" * 80)
    
    # 检查最近的训练任务
    latest_task = check_recent_training_tasks()
    
    # 检查训练数据可用性
    check_training_data_availability()
    
    # 测试简单训练启动
    test_task_id = test_simple_training()
    
    # 检查应用程序日志
    check_application_logs()
    
    print(f"\n📋 诊断总结")
    print("=" * 80)
    
    print(f"🔍 可能的问题原因:")
    print(f"1. 训练数据准备失败 - 检查MT5连接和历史数据")
    print(f"2. GPU内存不足 - 虽然显示8GB可用，但可能有其他限制")
    print(f"3. 模型初始化失败 - 检查PyTorch和CUDA兼容性")
    print(f"4. 数据预处理错误 - 检查特征工程和数据格式")
    print(f"5. 训练循环异常 - 检查训练代码中的异常处理")
    
    print(f"\n💡 建议的解决步骤:")
    print(f"1. 检查应用程序控制台输出中的错误信息")
    print(f"2. 尝试使用更小的批次大小和更短的序列长度")
    print(f"3. 确认PyTorch和CUDA版本兼容性")
    print(f"4. 检查训练数据是否充足（至少需要几百条记录）")
    print(f"5. 考虑先使用CPU模式测试训练流程")
    
    if test_task_id:
        print(f"\n🎯 测试任务已创建: {test_task_id}")
        print(f"   请在模型训练页面查看此任务的详细状态")
        print(f"   或等待几分钟后再次运行此诊断脚本")

if __name__ == '__main__':
    main()
