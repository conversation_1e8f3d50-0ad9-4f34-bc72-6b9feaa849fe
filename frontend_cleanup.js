
// 在浏览器控制台中执行以下代码来清理前端缓存

// 1. 清理交易统计数据
tradingStatistics = {
    todayTrades: 0,
    currentPositions: 0,
    totalProfit: 0
};

// 2. 更新显示
document.getElementById('todayTrades').textContent = '0';
document.getElementById('currentPositions').textContent = '0';

// 3. 隐藏持仓详情
const positionSection = document.getElementById('positionDetailsSection');
if (positionSection) {
    positionSection.style.display = 'none';
}

// 4. 清空持仓卡片容器
const cardsContainer = document.getElementById('positionCardsContainer');
if (cardsContainer) {
    cardsContainer.innerHTML = '<div class="col-12 text-center text-muted py-4" id="noPositionsMessage"><i class="fas fa-inbox fa-3x mb-3"></i><p>暂无持仓</p></div>';
}

// 5. 强制刷新交易统计
updateTradingStatistics();

console.log('✅ 前端数据已清理');
