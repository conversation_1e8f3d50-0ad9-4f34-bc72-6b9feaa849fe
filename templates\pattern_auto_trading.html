{% extends "base.html" %}

{% block page_title %}形态监测自动交易{% endblock %}

{% block content %}
<div class="row">
    <!-- 左侧：形态监测信号 -->
    <div class="col-lg-6 mb-4">
        <!-- 形态监测配置 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line text-info"></i>
                    形态监测配置
                </h5>
            </div>
            <div class="card-body">
                <!-- 交易品种选择 -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label">监测品种</label>
                        <select class="form-select" id="monitoringSymbol">
                            <option value="XAUUSD">黄金 (XAU/USD)</option>
                            <option value="EURUSD">欧美 (EUR/USD)</option>
                            <option value="GBPUSD">英美 (GBP/USD)</option>
                            <option value="USDJPY">美日 (USD/JPY)</option>
                            <option value="AUDUSD">澳美 (AUD/USD)</option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">置信度阈值</label>
                        <div class="d-flex align-items-center">
                            <input type="range" class="form-range me-2" id="confidenceThreshold" 
                                   min="0.3" max="0.9" step="0.1" value="0.7">
                            <span id="confidenceValue" class="badge bg-primary">70%</span>
                        </div>
                    </div>
                </div>

                <!-- 时间周期选择 -->
                <div class="mb-3">
                    <label class="form-label">监测时间周期 <small class="text-muted">(可多选)</small></label>
                    <div class="timeframe-selector">
                        <button class="timeframe-btn" data-timeframe="5m" onclick="toggleTimeframe('5m')">5分钟</button>
                        <button class="timeframe-btn active" data-timeframe="15m" onclick="toggleTimeframe('15m')">15分钟</button>
                        <button class="timeframe-btn" data-timeframe="30m" onclick="toggleTimeframe('30m')">30分钟</button>
                        <button class="timeframe-btn" data-timeframe="1h" onclick="toggleTimeframe('1h')">1小时</button>
                        <button class="timeframe-btn" data-timeframe="4h" onclick="toggleTimeframe('4h')">4小时</button>
                    </div>
                </div>

                <!-- 监测控制 -->
                <div class="d-flex gap-2 align-items-center">
                    <button class="btn btn-success" id="startMonitoringBtn" onclick="startPatternMonitoring()">
                        <i class="fas fa-play"></i>
                        开始监测
                    </button>
                    <button class="btn btn-danger" id="stopMonitoringBtn" onclick="stopPatternMonitoring()" disabled>
                        <i class="fas fa-stop"></i>
                        停止监测
                    </button>
                    <button class="btn btn-outline-info" onclick="refreshPatternSignals()">
                        <i class="fas fa-refresh"></i>
                        刷新信号
                    </button>
                    <div class="ms-2">
                        <span class="badge bg-secondary" id="monitoringStatus">已停止</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 实时形态信号 -->
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-signal text-warning"></i>
                        实时形态信号
                    </h5>
                    <span class="badge bg-info" id="signalCount">0 个信号</span>
                </div>
            </div>
            <div class="card-body">
                <div id="patternSignalsList" class="pattern-signals-container">
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-search fa-2x mb-2"></i>
                        <p>暂无形态信号，请开始监测</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 右侧：自动交易配置和状态 -->
    <div class="col-lg-6 mb-4">
        <!-- MT5连接状态 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-plug text-primary"></i>
                    MT5连接状态
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info" id="mt5ConnectionStatus">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <i class="fas fa-plug"></i>
                            <strong>MT5连接状态</strong>
                        </div>
                        <div>
                            <button class="btn btn-sm btn-outline-primary" onclick="autoConnectMT5()">
                                <i class="fas fa-sync"></i>
                                自动连接MT5
                            </button>
                        </div>
                    </div>
                    <small class="d-block mt-2" id="mt5StatusText">点击自动连接以获取MT5客户端账户信息</small>
                </div>
            </div>
        </div>

        <!-- 自动交易配置 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-robot text-success"></i>
                    自动交易配置
                </h5>
            </div>
            <div class="card-body">
                <!-- 交易参数 -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label">交易手数区间</label>
                        <div class="row">
                            <div class="col-6">
                                <input type="number" class="form-control" id="minTradeVolume"
                                       value="0.01" min="0.01" max="1.0" step="0.01" placeholder="最小">
                            </div>
                            <div class="col-6">
                                <input type="number" class="form-control" id="maxTradeVolume"
                                       value="0.05" min="0.01" max="1.0" step="0.01" placeholder="最大">
                            </div>
                        </div>
                        <small class="text-muted">系统将根据信号强度在此区间内选择手数</small>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">最大同时持仓</label>
                        <input type="number" class="form-control" id="maxPositions"
                               value="10" min="1" max="20" step="1">
                        <small class="text-muted">默认最大同时持仓10个</small>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label">止损点数</label>
                        <input type="number" class="form-control" id="stopLossPips" 
                               value="50" min="10" max="200" step="5">
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">止盈点数</label>
                        <input type="number" class="form-control" id="takeProfitPips" 
                               value="100" min="20" max="500" step="10">
                    </div>
                </div>

                <!-- 信号过滤 -->
                <div class="mb-3">
                    <label class="form-label">信号过滤条件</label>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="filterHighConfidence" checked>
                        <label class="form-check-label" for="filterHighConfidence">
                            只交易高置信度信号 (≥70%)
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="filterMultiTimeframe">
                        <label class="form-check-label" for="filterMultiTimeframe">
                            需要多时间周期确认
                        </label>
                    </div>
                </div>

                <!-- 自动交易控制 -->
                <div class="d-flex gap-2 align-items-center">
                    <button class="btn btn-success" id="startAutoTradingBtn" onclick="startAutoTrading()">
                        <i class="fas fa-play"></i>
                        启动自动交易
                    </button>
                    <button class="btn btn-danger" id="stopAutoTradingBtn" onclick="stopAutoTrading()" disabled>
                        <i class="fas fa-stop"></i>
                        停止自动交易
                    </button>
                    <div class="ms-2">
                        <span class="badge bg-secondary" id="autoTradingStatus">已停止</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 交易状态 -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie text-info"></i>
                    交易状态统计
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-4">
                        <h6 class="text-muted mb-1">今日信号</h6>
                        <h5 class="mb-0" id="todaySignals">
                            <span class="text-info">0</span>
                        </h5>
                    </div>
                    <div class="col-md-4">
                        <h6 class="text-muted mb-1">执行交易</h6>
                        <h5 class="mb-0" id="executedTrades">
                            <span class="text-primary">0</span>
                        </h5>
                    </div>
                    <div class="col-md-4">
                        <h6 class="text-muted mb-1">当前持仓</h6>
                        <h5 class="mb-0" id="currentPositions">
                            <span class="text-warning">0</span>
                        </h5>
                    </div>
                </div>

                <hr>

                <div class="row text-center">
                    <div class="col-md-6">
                        <h6 class="text-muted mb-1">今日盈亏</h6>
                        <h5 class="mb-0" id="todayPnL">
                            <span class="text-success">$0.00</span>
                        </h5>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-muted mb-1">胜率</h6>
                        <h5 class="mb-0" id="winRate">
                            <span class="text-info">0%</span>
                        </h5>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 当前持仓 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list text-primary"></i>
                        当前持仓 <small class="text-muted">(形态信号交易)</small>
                    </h5>
                    <button class="btn btn-outline-danger btn-sm" onclick="closeAllPatternPositions()">
                        <i class="fas fa-times"></i>
                        一键平仓
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="positionsTable">
                        <thead>
                            <tr>
                                <th>时间</th>
                                <th>品种</th>
                                <th>方向</th>
                                <th>手数</th>
                                <th>开仓价</th>
                                <th>当前价</th>
                                <th>止损</th>
                                <th>止盈</th>
                                <th>盈亏</th>
                                <th>形态信号</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="11" class="text-center text-muted">暂无持仓</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 交易历史 -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-history text-secondary"></i>
                        形态信号交易历史 <small class="text-muted">(仅显示当天数据)</small>
                    </h5>
                    <button class="btn btn-outline-info btn-sm" onclick="syncTradingStatus()">
                        <i class="fas fa-sync"></i>
                        同步状态
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="historyTable">
                        <thead>
                            <tr>
                                <th>时间</th>
                                <th>品种</th>
                                <th>方向</th>
                                <th>手数</th>
                                <th>开仓价</th>
                                <th>平仓价</th>
                                <th>盈亏</th>
                                <th>形态信号</th>
                                <th>置信度</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="10" class="text-center text-muted">今日暂无交易记录</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.timeframe-selector {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.timeframe-btn {
    padding: 0.5rem 1rem;
    border: 1px solid #dee2e6;
    background: white;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    min-width: 70px;
    text-align: center;
}

.timeframe-btn.active {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

.timeframe-btn:hover {
    background: #f8f9fa;
    border-color: #007bff;
}

.timeframe-btn.active:hover {
    background: #0056b3;
}

.pattern-signals-container {
    max-height: 400px;
    overflow-y: auto;
}

.pattern-signal {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    background: white;
    transition: all 0.3s ease;
    cursor: pointer;
}

.pattern-signal:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.pattern-signal.bullish {
    border-left: 4px solid #28a745;
}

.pattern-signal.bearish {
    border-left: 4px solid #dc3545;
}

.signal-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;
}

.signal-indicator.bullish {
    background-color: #28a745;
}

.signal-indicator.bearish {
    background-color: #dc3545;
}
</style>

<script>
// 全局变量
let monitoringActive = false;
let autoTradingActive = false;
let currentTimeframes = ['15m'];
let patternSignals = [];
let mt5Connected = false;
let monitoringInterval = null;
let statsInterval = null;

// 状态持久化键名
const STATE_KEYS = {
    MONITORING_ACTIVE: 'pattern_monitoring_active',
    AUTO_TRADING_ACTIVE: 'pattern_auto_trading_active',
    TIMEFRAMES: 'pattern_timeframes',
    SYMBOL: 'pattern_symbol',
    CONFIDENCE: 'pattern_confidence',
    MIN_TRADE_VOLUME: 'pattern_min_trade_volume',
    MAX_TRADE_VOLUME: 'pattern_max_trade_volume',
    MAX_POSITIONS: 'pattern_max_positions',
    STOP_LOSS_PIPS: 'pattern_stop_loss_pips',
    TAKE_PROFIT_PIPS: 'pattern_take_profit_pips',
    FILTER_HIGH_CONFIDENCE: 'pattern_filter_high_confidence',
    FILTER_MULTI_TIMEFRAME: 'pattern_filter_multi_timeframe'
};

// 状态保存函数
function saveState() {
    try {
        localStorage.setItem(STATE_KEYS.MONITORING_ACTIVE, monitoringActive);
        localStorage.setItem(STATE_KEYS.AUTO_TRADING_ACTIVE, autoTradingActive);
        localStorage.setItem(STATE_KEYS.TIMEFRAMES, JSON.stringify(currentTimeframes));
        localStorage.setItem(STATE_KEYS.SYMBOL, document.getElementById('monitoringSymbol').value);
        localStorage.setItem(STATE_KEYS.CONFIDENCE, document.getElementById('confidenceThreshold').value);
        localStorage.setItem(STATE_KEYS.MIN_TRADE_VOLUME, document.getElementById('minTradeVolume').value);
        localStorage.setItem(STATE_KEYS.MAX_TRADE_VOLUME, document.getElementById('maxTradeVolume').value);
        localStorage.setItem(STATE_KEYS.MAX_POSITIONS, document.getElementById('maxPositions').value);
        localStorage.setItem(STATE_KEYS.STOP_LOSS_PIPS, document.getElementById('stopLossPips').value);
        localStorage.setItem(STATE_KEYS.TAKE_PROFIT_PIPS, document.getElementById('takeProfitPips').value);
        localStorage.setItem(STATE_KEYS.FILTER_HIGH_CONFIDENCE, document.getElementById('filterHighConfidence').checked);
        localStorage.setItem(STATE_KEYS.FILTER_MULTI_TIMEFRAME, document.getElementById('filterMultiTimeframe').checked);

        console.log('💾 状态已保存');
    } catch (error) {
        console.error('❌ 状态保存失败:', error);
    }
}

// 状态恢复函数
function restoreState() {
    try {
        // 恢复基本配置
        const savedSymbol = localStorage.getItem(STATE_KEYS.SYMBOL);
        if (savedSymbol) {
            document.getElementById('monitoringSymbol').value = savedSymbol;
        }

        const savedConfidence = localStorage.getItem(STATE_KEYS.CONFIDENCE);
        if (savedConfidence) {
            document.getElementById('confidenceThreshold').value = savedConfidence;
            updateConfidenceDisplay();
        }

        const savedMinVolume = localStorage.getItem(STATE_KEYS.MIN_TRADE_VOLUME);
        if (savedMinVolume) {
            document.getElementById('minTradeVolume').value = savedMinVolume;
        }

        const savedMaxVolume = localStorage.getItem(STATE_KEYS.MAX_TRADE_VOLUME);
        if (savedMaxVolume) {
            document.getElementById('maxTradeVolume').value = savedMaxVolume;
        }

        const savedMaxPositions = localStorage.getItem(STATE_KEYS.MAX_POSITIONS);
        if (savedMaxPositions) {
            document.getElementById('maxPositions').value = savedMaxPositions;
        }

        const savedStopLoss = localStorage.getItem(STATE_KEYS.STOP_LOSS_PIPS);
        if (savedStopLoss) {
            document.getElementById('stopLossPips').value = savedStopLoss;
        }

        const savedTakeProfit = localStorage.getItem(STATE_KEYS.TAKE_PROFIT_PIPS);
        if (savedTakeProfit) {
            document.getElementById('takeProfitPips').value = savedTakeProfit;
        }

        const savedFilterHigh = localStorage.getItem(STATE_KEYS.FILTER_HIGH_CONFIDENCE);
        if (savedFilterHigh !== null) {
            document.getElementById('filterHighConfidence').checked = savedFilterHigh === 'true';
        }

        const savedFilterMulti = localStorage.getItem(STATE_KEYS.FILTER_MULTI_TIMEFRAME);
        if (savedFilterMulti !== null) {
            document.getElementById('filterMultiTimeframe').checked = savedFilterMulti === 'true';
        }

        // 恢复时间周期
        const savedTimeframes = localStorage.getItem(STATE_KEYS.TIMEFRAMES);
        if (savedTimeframes) {
            currentTimeframes = JSON.parse(savedTimeframes);
            updateTimeframeUI();
        }

        // 恢复运行状态
        const savedMonitoring = localStorage.getItem(STATE_KEYS.MONITORING_ACTIVE);
        const savedAutoTrading = localStorage.getItem(STATE_KEYS.AUTO_TRADING_ACTIVE);

        console.log('📂 恢复状态检查:', {
            savedMonitoring: savedMonitoring,
            savedAutoTrading: savedAutoTrading
        });

        if (savedMonitoring === 'true') {
            // 延迟恢复监测状态，等待MT5连接
            setTimeout(() => {
                console.log('🔄 尝试恢复监测状态, MT5连接状态:', mt5Connected);
                restoreMonitoringState();
            }, 3000);
        }

        if (savedAutoTrading === 'true') {
            // 延迟恢复自动交易状态
            setTimeout(() => {
                console.log('🔄 尝试恢复自动交易状态, MT5连接:', mt5Connected, '监测状态:', monitoringActive);
                restoreAutoTradingState();
            }, 5000);
        }

        console.log('📂 状态已恢复');
    } catch (error) {
        console.error('❌ 状态恢复失败:', error);
    }
}

// 恢复监测状态
function restoreMonitoringState() {
    console.log('🔄 开始恢复监测状态, MT5连接状态:', mt5Connected);

    // 不再检查MT5连接状态，直接恢复UI状态
    monitoringActive = true;
    document.getElementById('startMonitoringBtn').disabled = true;
    document.getElementById('stopMonitoringBtn').disabled = false;

    // 更新状态指示器
    document.getElementById('monitoringStatus').textContent = '运行中';
    document.getElementById('monitoringStatus').className = 'badge bg-success';

    // 开始信号轮询
    startSignalPolling();

    console.log('🔍 监测状态已恢复');
    showNotification('形态监测已自动恢复运行', 'success');
}

// 恢复自动交易状态
function restoreAutoTradingState() {
    console.log('🔄 开始恢复自动交易状态, MT5连接:', mt5Connected, '监测状态:', monitoringActive);

    // 只检查监测状态，不检查MT5连接
    if (!monitoringActive) {
        console.log('⚠️ 监测未启动，无法恢复自动交易状态');
        return;
    }

    autoTradingActive = true;
    document.getElementById('startAutoTradingBtn').disabled = true;
    document.getElementById('stopAutoTradingBtn').disabled = false;
    document.getElementById('autoTradingStatus').textContent = '运行中';
    document.getElementById('autoTradingStatus').className = 'badge bg-success';

    console.log('🤖 自动交易状态已恢复');
    showNotification('自动交易已自动恢复运行', 'success');
}

// 更新时间周期UI
function updateTimeframeUI() {
    document.querySelectorAll('.timeframe-btn').forEach(btn => {
        const timeframe = btn.getAttribute('data-timeframe');
        if (currentTimeframes.includes(timeframe)) {
            btn.classList.add('active');
        } else {
            btn.classList.remove('active');
        }
    });
}

// 显示通知
function showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    // 3秒后自动移除
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 3000);
}

// 检查并恢复状态
function checkAndRestoreState() {
    try {
        const savedMonitoring = localStorage.getItem(STATE_KEYS.MONITORING_ACTIVE);
        const savedAutoTrading = localStorage.getItem(STATE_KEYS.AUTO_TRADING_ACTIVE);

        // 检查监测状态
        if (savedMonitoring === 'true' && !monitoringActive) {
            console.log('🔄 检测到监测状态丢失，尝试恢复...');
            restoreMonitoringState();
        }

        // 检查自动交易状态
        if (savedAutoTrading === 'true' && !autoTradingActive && monitoringActive) {
            console.log('🔄 检测到自动交易状态丢失，尝试恢复...');
            restoreAutoTradingState();
        }

    } catch (error) {
        console.error('❌ 状态检查失败:', error);
    }
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 形态监测自动交易页面已加载');

    // 初始化置信度滑块
    updateConfidenceDisplay();

    // 恢复保存的状态
    restoreState();

    // 尝试自动连接MT5
    setTimeout(() => {
        autoConnectMT5();
    }, 1000);

    // 定期更新统计信息和持仓
    statsInterval = setInterval(() => {
        if (autoTradingActive || monitoringActive) {
            updateTradingStats();
            loadCurrentPositions();
            loadTradingHistory();
        }
    }, 30000); // 每30秒更新一次

    // 定期保存状态
    setInterval(() => {
        saveState();
    }, 10000); // 每10秒保存一次状态

    // 定期检查状态恢复
    setInterval(() => {
        checkAndRestoreState();
    }, 15000); // 每15秒检查一次状态

    // 初始加载数据
    setTimeout(() => {
        // 先同步状态，再加载数据
        syncTradingStatus();
        setTimeout(() => {
            loadCurrentPositions();
            loadTradingHistory();
            updateTradingStats();
        }, 1000);
    }, 2000);
});

// 页面卸载时保存状态
window.addEventListener('beforeunload', function() {
    saveState();
    console.log('📤 页面卸载，状态已保存');
});

// 页面隐藏时保存状态
document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
        saveState();
        console.log('👁️ 页面隐藏，状态已保存');
    }
});

// 更新置信度显示
function updateConfidenceDisplay() {
    const slider = document.getElementById('confidenceThreshold');
    const display = document.getElementById('confidenceValue');
    const value = Math.round(slider.value * 100);
    display.textContent = value + '%';
}

// 置信度滑块事件
document.getElementById('confidenceThreshold').addEventListener('input', updateConfidenceDisplay);

// 切换时间周期选择
function toggleTimeframe(timeframe) {
    const index = currentTimeframes.indexOf(timeframe);
    const btn = document.querySelector(`[data-timeframe="${timeframe}"]`);

    if (index > -1) {
        if (currentTimeframes.length > 1) {
            currentTimeframes.splice(index, 1);
            btn.classList.remove('active');
        } else {
            showNotification('至少需要选择一个时间周期', 'warning');
            return;
        }
    } else {
        currentTimeframes.push(timeframe);
        btn.classList.add('active');
    }

    // 保存状态
    saveState();

    console.log('当前选中的时间周期:', currentTimeframes);
}

// 自动连接MT5
function autoConnectMT5() {
    const statusText = document.getElementById('mt5StatusText');
    statusText.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 正在连接MT5...';
    
    fetch('/api/mt5/auto-connect', {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            mt5Connected = true;
            statusText.innerHTML = `
                <i class="fas fa-check-circle text-success"></i>
                已连接: ${data.account_info.name} (余额: $${data.account_info.balance.toFixed(2)})
            `;
            console.log('✅ MT5连接成功');
        } else {
            mt5Connected = false;
            statusText.innerHTML = `
                <i class="fas fa-exclamation-triangle text-danger"></i>
                连接失败: ${data.error}
            `;
            console.error('❌ MT5连接失败:', data.error);
        }
    })
    .catch(error => {
        mt5Connected = false;
        statusText.innerHTML = `
            <i class="fas fa-exclamation-triangle text-danger"></i>
            连接异常: ${error.message}
        `;
        console.error('❌ MT5连接异常:', error);
    });
}

// 开始形态监测
function startPatternMonitoring() {
    if (!mt5Connected) {
        alert('请先连接MT5');
        return;
    }

    const symbol = document.getElementById('monitoringSymbol').value;
    const confidence = document.getElementById('confidenceThreshold').value;

    console.log('🔍 开始形态监测:', {
        symbol: symbol,
        timeframes: currentTimeframes,
        confidence: confidence
    });

    monitoringActive = true;
    document.getElementById('startMonitoringBtn').disabled = true;
    document.getElementById('stopMonitoringBtn').disabled = false;
    document.getElementById('monitoringStatus').textContent = '运行中';
    document.getElementById('monitoringStatus').className = 'badge bg-success';

    // 保存状态
    saveState();

    // 开始定期获取形态信号
    startSignalPolling();

    showNotification('形态监测已启动', 'success');
}

// 停止形态监测
function stopPatternMonitoring() {
    monitoringActive = false;
    document.getElementById('startMonitoringBtn').disabled = false;
    document.getElementById('stopMonitoringBtn').disabled = true;
    document.getElementById('monitoringStatus').textContent = '已停止';
    document.getElementById('monitoringStatus').className = 'badge bg-secondary';

    // 清除监测间隔
    if (monitoringInterval) {
        clearTimeout(monitoringInterval);
        monitoringInterval = null;
    }

    // 保存状态
    saveState();

    console.log('⏹️ 停止形态监测');
    showNotification('形态监测已停止', 'warning');
}

// 获取选择的交易品种
function getSelectedTradingSymbols() {
    const checkboxes = document.querySelectorAll('input[name="tradingSymbols"]:checked');
    const selectedSymbols = [];
    checkboxes.forEach(checkbox => {
        selectedSymbols.push(checkbox.value);
    });
    return selectedSymbols;
}

// 开始信号轮询
function startSignalPolling() {
    if (!monitoringActive) return;

    // 立即获取一次信号
    refreshPatternSignals();

    // 每30秒获取一次新信号
    monitoringInterval = setTimeout(() => {
        if (monitoringActive) {
            startSignalPolling();
        }
    }, 30000);
}

// 刷新形态信号
function refreshPatternSignals() {
    console.log('🔄 刷新形态信号...');
    
    // 这里应该调用形态监测API获取最新信号
    // 暂时使用模拟数据
    const mockSignals = generateMockSignals();
    updateSignalsList(mockSignals);
}

// 时间周期优先级映射（数值越大优先级越高）
const TIMEFRAME_PRIORITY = {
    '1m': 1,
    '5m': 2,
    '15m': 3,
    '30m': 4,
    '1h': 5,
    '4h': 6,
    '1d': 7
};

// 生成模拟信号数据
function generateMockSignals() {
    const patterns = ['双底', '头肩顶', '上升三角形', '下降楔形', '矩形整理'];

    // 获取当前选择的交易品种
    const selectedSymbols = getSelectedTradingSymbols();
    if (selectedSymbols.length === 0) {
        console.log('⚠️ 未选择任何交易品种');
        return [];
    }

    const signals = [];

    // 系统严禁生成模拟交易信号，必须使用真实的技术分析结果
    console.error('❌ 系统严禁生成模拟交易信号，请使用基于MT5真实数据的技术分析');

    // 返回空信号数组，强制使用真实数据源
    return [];

    console.log(`📊 为选择的品种 [${selectedSymbols.join(', ')}] 生成了 ${signals.length} 个信号`);
    return signals;
}

// 信号冲突处理：优先选择时间周期长的信号
function resolveSignalConflicts(signals) {
    if (signals.length <= 1) return signals;

    // 按品种分组
    const signalsBySymbol = {};
    signals.forEach(signal => {
        if (!signalsBySymbol[signal.symbol]) {
            signalsBySymbol[signal.symbol] = [];
        }
        signalsBySymbol[signal.symbol].push(signal);
    });

    const resolvedSignals = [];

    // 对每个品种处理信号冲突
    Object.entries(signalsBySymbol).forEach(([symbol, symbolSignals]) => {
        if (symbolSignals.length === 1) {
            resolvedSignals.push(symbolSignals[0]);
            return;
        }

        // 检查是否有相反的信号
        const bullishSignals = symbolSignals.filter(s => s.pattern_type === 'bullish');
        const bearishSignals = symbolSignals.filter(s => s.pattern_type === 'bearish');

        if (bullishSignals.length > 0 && bearishSignals.length > 0) {
            // 有冲突信号，选择时间周期最长的
            console.log(`⚠️ 检测到 ${symbol} 的冲突信号，应用时间周期优先级规则`);

            const allConflictSignals = [...bullishSignals, ...bearishSignals];
            const highestPrioritySignal = allConflictSignals.reduce((prev, current) => {
                const prevPriority = TIMEFRAME_PRIORITY[prev.timeframe] || 0;
                const currentPriority = TIMEFRAME_PRIORITY[current.timeframe] || 0;
                return currentPriority > prevPriority ? current : prev;
            });

            resolvedSignals.push(highestPrioritySignal);

            showNotification(
                `${symbol}: 时间周期 ${highestPrioritySignal.timeframe} 信号优先级更高，已选择该信号`,
                'info'
            );
        } else {
            // 没有冲突，选择置信度最高的
            const bestSignal = symbolSignals.reduce((prev, current) => {
                return current.confidence > prev.confidence ? current : prev;
            });
            resolvedSignals.push(bestSignal);
        }
    });

    return resolvedSignals;
}

// 更新信号列表
function updateSignalsList(signals) {
    // 应用信号冲突解决逻辑
    const resolvedSignals = resolveSignalConflicts(signals);
    patternSignals = resolvedSignals;

    const container = document.getElementById('patternSignalsList');
    const countBadge = document.getElementById('signalCount');

    countBadge.textContent = resolvedSignals.length + ' 个信号';

    if (resolvedSignals.length === 0) {
        container.innerHTML = `
            <div class="text-center text-muted py-4">
                <i class="fas fa-search fa-2x mb-2"></i>
                <p>暂无形态信号</p>
            </div>
        `;
        return;
    }
    
    const signalsHtml = resolvedSignals.map(signal => {
        const signalClass = signal.pattern_type === 'bullish' ? 'bullish' : 'bearish';
        const directionIcon = signal.pattern_type === 'bullish' ? 'fas fa-arrow-up text-success' : 'fas fa-arrow-down text-danger';
        const confidenceClass = signal.confidence >= 0.7 ? 'success' : signal.confidence >= 0.5 ? 'warning' : 'danger';
        
        return `
            <div class="pattern-signal ${signalClass}" onclick="executeSignalTrade('${signal.id}')">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h6 class="mb-1">
                            <span class="signal-indicator ${signalClass}"></span>
                            ${signal.pattern_name}
                            <span class="badge bg-primary ms-1">${signal.symbol}</span>
                            <span class="badge bg-secondary ms-1">${signal.timeframe}</span>
                            <i class="${directionIcon} ms-2"></i>
                        </h6>
                        <small class="text-muted">${signal.timestamp}</small>
                    </div>
                    <div class="text-end">
                        <div class="fw-bold">$${signal.price.toFixed(2)}</div>
                        <span class="badge bg-${confidenceClass}">${Math.round(signal.confidence * 100)}%</span>
                    </div>
                </div>
                <div class="mt-2">
                    <small class="text-muted">
                        ${signal.pattern_type === 'bullish' ? '看涨信号' : '看跌信号'} - 
                        点击执行交易
                    </small>
                </div>
            </div>
        `;
    }).join('');
    
    container.innerHTML = signalsHtml;
}

// 启动自动交易
function startAutoTrading() {
    if (!mt5Connected) {
        alert('请先连接MT5');
        return;
    }

    if (!monitoringActive) {
        alert('请先启动形态监测');
        return;
    }

    autoTradingActive = true;
    document.getElementById('startAutoTradingBtn').disabled = true;
    document.getElementById('stopAutoTradingBtn').disabled = false;
    document.getElementById('autoTradingStatus').textContent = '运行中';
    document.getElementById('autoTradingStatus').className = 'badge bg-success';

    // 保存状态
    saveState();

    console.log('🤖 自动交易已启动');
    showNotification('自动交易已启动，将根据形态信号自动执行交易', 'success');
}

// 停止自动交易
function stopAutoTrading() {
    autoTradingActive = false;
    document.getElementById('startAutoTradingBtn').disabled = false;
    document.getElementById('stopAutoTradingBtn').disabled = true;
    document.getElementById('autoTradingStatus').textContent = '已停止';
    document.getElementById('autoTradingStatus').className = 'badge bg-secondary';

    // 保存状态
    saveState();

    console.log('⏹️ 自动交易已停止');
    showNotification('自动交易已停止', 'warning');
}

// 根据信号强度计算交易手数
function calculateTradeVolume(signal) {
    const minVolume = parseFloat(document.getElementById('minTradeVolume').value) || 0.01;
    const maxVolume = parseFloat(document.getElementById('maxTradeVolume').value) || 0.05;

    // 根据置信度计算手数：置信度越高，手数越大
    const confidence = signal.confidence;
    const volumeRange = maxVolume - minVolume;
    const calculatedVolume = minVolume + (confidence - 0.5) * volumeRange / 0.5;

    // 确保在范围内
    const finalVolume = Math.max(minVolume, Math.min(maxVolume, calculatedVolume));

    // 保留两位小数
    return Math.round(finalVolume * 100) / 100;
}

// 执行信号交易
function executeSignalTrade(signalId) {
    if (!autoTradingActive) {
        alert('请先启动自动交易');
        return;
    }

    const signal = patternSignals.find(s => s.id == signalId);
    if (!signal) {
        alert('信号不存在');
        return;
    }

    // 计算交易手数
    const volume = calculateTradeVolume(signal);

    console.log('📈 执行信号交易:', signal, '手数:', volume);

    // 构建交易请求
    const tradeRequest = {
        signal_id: signalId,
        signal_data: signal,
        volume: volume
    };

    // 发送交易请求
    fetch('/api/pattern-auto-trading/execute', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(tradeRequest)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(`交易已执行: ${signal.pattern_name} ${volume}手`, 'success');
            loadCurrentPositions();
            loadTradingHistory();
            updateTradingStats();
        } else {
            showNotification('交易执行失败: ' + data.error, 'danger');
        }
    })
    .catch(error => {
        showNotification('交易请求失败: ' + error.message, 'danger');
    });
}

// 更新交易统计
function updateTradingStats() {
    console.log('📊 更新交易统计数据...');

    fetch('/api/pattern-auto-trading/stats')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const stats = data.stats;

                // 更新统计显示
                document.getElementById('todaySignals').innerHTML = `<span class="text-info">${stats.today_signals}</span>`;
                document.getElementById('executedTrades').innerHTML = `<span class="text-primary">${stats.executed_trades}</span>`;
                document.getElementById('currentPositions').innerHTML = `<span class="text-warning">${stats.current_positions}</span>`;

                // 盈亏显示（根据正负显示不同颜色）
                const pnlClass = stats.today_pnl >= 0 ? 'text-success' : 'text-danger';
                document.getElementById('todayPnL').innerHTML = `<span class="${pnlClass}">$${stats.today_pnl}</span>`;

                document.getElementById('winRate').innerHTML = `<span class="text-info">${stats.win_rate}%</span>`;

                console.log('✅ 统计数据更新成功:', stats);
            } else {
                console.error('❌ 获取统计数据失败:', data.error);
            }
        })
        .catch(error => {
            console.error('❌ 统计数据请求失败:', error);
        });
}

// 加载当前持仓
function loadCurrentPositions() {
    if (!mt5Connected) {
        console.log('MT5未连接，无法加载持仓');
        return;
    }

    fetch('/api/pattern-auto-trading/positions')
        .then(response => response.json())
        .then(data => {
            const tbody = document.querySelector('#positionsTable tbody');
            tbody.innerHTML = '';

            if (!data.success || data.positions.length === 0) {
                tbody.innerHTML = '<tr><td colspan="11" class="text-center text-muted">暂无持仓</td></tr>';
            } else {
                data.positions.forEach(position => {
                    const directionClass = position.type === 'buy' ? 'text-success' : 'text-danger';
                    const directionText = position.type === 'buy' ? '买入' : '卖出';
                    const pnlClass = position.profit >= 0 ? 'text-success' : 'text-danger';

                    const row = `
                        <tr>
                            <td>${new Date(position.open_time).toLocaleString('zh-CN')}</td>
                            <td><strong>${position.symbol}</strong></td>
                            <td><span class="${directionClass}">${directionText}</span></td>
                            <td>${position.volume}</td>
                            <td>${position.open_price.toFixed(5)}</td>
                            <td>${position.current_price.toFixed(5)}</td>
                            <td>${position.stop_loss || '-'}</td>
                            <td>${position.take_profit || '-'}</td>
                            <td><span class="${pnlClass}">$${position.profit.toFixed(2)}</span></td>
                            <td><span class="badge bg-info">${position.pattern_signal || '未知'}</span></td>
                            <td>
                                <button class="btn btn-sm btn-outline-danger" onclick="closePosition('${position.ticket}')">
                                    <i class="fas fa-times"></i>
                                </button>
                            </td>
                        </tr>
                    `;
                    tbody.innerHTML += row;
                });
            }
        })
        .catch(error => {
            console.error('加载持仓失败:', error);
        });
}

// 加载交易历史
function loadTradingHistory() {
    console.log('📊 加载交易历史...');

    fetch('/api/pattern-auto-trading/history')
        .then(response => response.json())
        .then(data => {
            const tbody = document.querySelector('#historyTable tbody');
            tbody.innerHTML = '';

            if (!data.success || data.trades.length === 0) {
                tbody.innerHTML = '<tr><td colspan="10" class="text-center text-muted">今日暂无交易记录</td></tr>';
            } else {
                data.trades.forEach(trade => {
                    const directionClass = trade.type === 'buy' ? 'text-success' : 'text-danger';
                    const directionText = trade.type === 'buy' ? '买入' : '卖出';
                    const pnlClass = trade.profit >= 0 ? 'text-success' : 'text-danger';
                    const statusClass = trade.status === 'closed' ? 'success' : 'warning';
                    const statusText = trade.status === 'closed' ? '已平仓' : '持仓中';

                    const row = `
                        <tr>
                            <td>${new Date(trade.open_time).toLocaleString('zh-CN')}</td>
                            <td><strong>${trade.symbol}</strong></td>
                            <td><span class="${directionClass}">${directionText}</span></td>
                            <td>${trade.volume}</td>
                            <td>${trade.open_price.toFixed(5)}</td>
                            <td>${trade.close_price ? trade.close_price.toFixed(5) : '-'}</td>
                            <td><span class="${pnlClass}">$${trade.profit.toFixed(2)}</span></td>
                            <td><span class="badge bg-info">${trade.pattern_signal || '未知'}</span></td>
                            <td><span class="badge bg-primary">${Math.round(trade.confidence * 100)}%</span></td>
                            <td><span class="badge bg-${statusClass}">${statusText}</span></td>
                        </tr>
                    `;
                    tbody.innerHTML += row;
                });

                console.log(`✅ 加载了 ${data.trades.length} 条交易记录`);
            }
        })
        .catch(error => {
            console.error('❌ 加载交易历史失败:', error);
        });
}

// 同步交易状态
function syncTradingStatus() {
    console.log('🔄 同步交易状态...');

    fetch('/api/pattern-auto-trading/sync-status', {
        method: 'POST'
    })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log(`✅ 同步完成: ${data.message}`);
                if (data.updated_count > 0) {
                    showNotification(`同步完成: 更新了 ${data.updated_count} 个交易状态`, 'success');
                    // 重新加载数据
                    loadTradingHistory();
                    loadCurrentPositions();
                    updateTradingStats();
                }
            } else {
                console.error('❌ 同步失败:', data.error);
                showNotification('同步交易状态失败: ' + data.error, 'danger');
            }
        })
        .catch(error => {
            console.error('❌ 同步请求失败:', error);
            showNotification('同步请求失败: ' + error.message, 'danger');
        });
}

// 平仓单个持仓
function closePosition(ticket) {
    if (!mt5Connected) {
        alert('请先连接MT5');
        return;
    }

    const confirmed = confirm(`确认要平掉订单 ${ticket} 吗？`);
    if (confirmed) {
        fetch('/api/pattern-auto-trading/close-position', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                ticket: ticket
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('持仓已平仓', 'success');
                loadCurrentPositions();
                loadTradingHistory();
                updateTradingStats();
            } else {
                showNotification('平仓失败: ' + data.error, 'danger');
            }
        })
        .catch(error => {
            showNotification('平仓请求失败: ' + error.message, 'danger');
        });
    }
}

// 一键平仓
function closeAllPatternPositions() {
    if (!mt5Connected) {
        alert('请先连接MT5');
        return;
    }

    const confirmed = confirm('确认要平掉所有形态信号交易的持仓吗？');
    if (confirmed) {
        fetch('/api/pattern-auto-trading/close-all-positions', {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(`已平仓 ${data.closed_count} 个持仓`, 'success');
                loadCurrentPositions();
                loadTradingHistory();
                updateTradingStats();
            } else {
                showNotification('一键平仓失败: ' + data.error, 'danger');
            }
        })
        .catch(error => {
            showNotification('一键平仓请求失败: ' + error.message, 'danger');
        });
    }
}
</script>
{% endblock %}
