# 数据准备和模型训练分离功能使用指南

## 🎯 功能概述

为了解决推理模型训练进度无变化的问题，我们将数据准备和模型训练分离为两个独立的阶段：

1. **数据准备阶段**: 获取历史数据，计算技术指标，创建训练序列，存储到本地缓存
2. **模型训练阶段**: 从缓存加载数据，开始神经网络训练

## 🔧 技术实现

### 后端修改

#### 1. 深度学习服务 (`services/deep_learning_service.py`)
- ✅ 新增 `start_data_preparation()` - 启动数据准备阶段
- ✅ 新增 `start_model_training()` - 启动模型训练阶段  
- ✅ 新增 `_prepare_data_async()` - 异步数据准备
- ✅ 修改 `_train_model_async()` - 支持跳过数据准备
- ✅ 修改原有 `start_training()` - 保持向后兼容

#### 2. API路由 (`routes.py`)
- ✅ 新增 `/api/deep-learning/start-data-preparation` - 启动数据准备
- ✅ 新增 `/api/deep-learning/start-model-training/<task_id>` - 启动模型训练

#### 3. 数据库状态
- ✅ 新增 `data_preparation` - 数据准备中
- ✅ 新增 `data_ready` - 数据准备完成，可以开始训练

### 前端修改

#### 1. 按钮界面 (`templates/model_training.html`)
- ✅ 原"开始训练"按钮 → "开始数据准备"按钮
- ✅ 新增"开始模型训练"按钮（数据准备完成后显示）

#### 2. 进度显示
- ✅ 数据准备阶段显示详细进度信息
- ✅ 数据准备完成后显示成功提示和训练按钮
- ✅ 支持数据获取、特征计算、序列创建等各阶段显示

#### 3. JavaScript功能
- ✅ 分离的表单提交处理
- ✅ 新增 `showModelTrainingButton()` 函数
- ✅ 新增 `resetButtonStates()` 函数

## 📋 使用流程

### 1. 启动数据准备
1. 在模型训练页面配置训练参数
2. 点击"**开始数据准备**"按钮
3. 系统开始获取历史数据和计算技术指标
4. 实时显示数据准备进度

### 2. 数据准备阶段显示
- 📊 **数据获取**: 显示品种、时间框架、天数
- 📈 **特征计算**: 显示计算的技术指标
- 🔢 **序列创建**: 显示序列长度和特征维度
- ✂️ **数据分割**: 显示训练集和验证集信息

### 3. 数据准备完成
- ✅ 显示"数据准备完成"成功提示
- 🔄 "开始数据准备"按钮变为绿色"数据准备完成"
- 🚀 显示"开始模型训练"按钮

### 4. 启动模型训练
1. 点击"**开始模型训练**"按钮
2. 系统从缓存加载数据
3. 开始神经网络训练
4. 显示训练进度和损失值

## 🎯 解决的问题

### 1. 训练进度卡住
- **原因**: 数据获取阶段耗时过长，用户看不到进度
- **解决**: 分离数据准备，实时显示数据获取进度

### 2. 用户体验差
- **原因**: 长时间无反馈，不知道系统在做什么
- **解决**: 详细的阶段提示和进度显示

### 3. 调试困难
- **原因**: 数据准备和训练混在一起，难以定位问题
- **解决**: 分离后可以独立测试每个阶段

## 📊 状态流转

```
pending → data_preparation → data_ready → running → completed/failed/stopped
```

- `pending`: 任务创建
- `data_preparation`: 数据准备中
- `data_ready`: 数据准备完成，等待开始训练
- `running`: 模型训练中
- `completed/failed/stopped`: 训练结束

## 🔍 监控和调试

### 1. 数据准备监控
- 查看数据获取进度
- 检查技术指标计算
- 验证数据缓存状态

### 2. 训练监控  
- 实时损失值显示
- 训练轮次进度
- GPU使用状态

### 3. 日志信息
每个阶段都有详细的日志记录，便于问题排查。

## 💡 最佳实践

### 1. 数据准备
- 建议先用较小的数据范围测试（如30天）
- 确保MT5连接稳定
- 检查数据缓存是否正常

### 2. 模型训练
- 数据准备完成后再开始训练
- 监控GPU使用情况
- 设置合适的早停参数

### 3. 错误处理
- 如果数据准备失败，检查MT5连接
- 如果训练失败，检查GPU状态
- 可以重置表单重新开始

## 🚀 向后兼容

原有的"开始训练"API仍然可用，会自动执行完整流程（数据准备+模型训练）。新的分离功能是可选的，用户可以选择使用。

## ✅ 验证结果

- ✅ 所有新功能测试通过
- ✅ API路由正常工作
- ✅ 前端界面正确显示
- ✅ 数据准备任务成功启动
- ✅ 状态流转正常

---

**实施完成时间**: 2025-07-30  
**功能状态**: ✅ 已完成并测试通过
