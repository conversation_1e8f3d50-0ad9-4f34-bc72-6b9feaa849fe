/**
 * 全局状态管理器
 * 解决页面切换导致状态丢失的问题
 */

class GlobalStateManager {
    constructor() {
        this.storageKey = 'MateTrade4_GlobalState';
        this.state = this.loadState();
        this.listeners = {};
        
        // 定期保存状态
        setInterval(() => {
            this.saveState();
        }, 5000); // 每5秒保存一次
        
        // 页面卸载时保存状态
        window.addEventListener('beforeunload', () => {
            this.saveState();
        });
    }

    /**
     * 加载状态
     */
    loadState() {
        try {
            const saved = localStorage.getItem(this.storageKey);
            return saved ? JSON.parse(saved) : this.getDefaultState();
        } catch (error) {
            console.error('加载全局状态失败:', error);
            return this.getDefaultState();
        }
    }

    /**
     * 获取默认状态
     */
    getDefaultState() {
        return {
            // MT5连接状态
            mt5Connection: {
                connected: false,
                accountInfo: null,
                lastConnectTime: null
            },
            
            // 模拟交易状态
            demoTrading: {
                selectedAccountType: null,
                selectedAccountId: null,
                selectedAccountInfo: null,
                aiTradingEnabled: false,
                aiStrategyId: null,
                tradingSymbol: 'EURUSD',
                tradingTimeSlot: 'all'
            },
            
            // 真实交易状态
            realTrading: {
                selectedAccountId: null,
                selectedAccountInfo: null,
                aiTradingEnabled: false
            },
            
            // AI智能交易状态
            aiTrading: {
                demo: {
                    active: false,
                    strategyId: null,
                    symbol: 'EURUSD',
                    timeSlot: 'all',
                    startTime: null
                },
                real: {
                    active: false,
                    strategyId: null,
                    symbol: 'EURUSD',
                    timeSlot: 'all',
                    startTime: null
                }
            },
            
            // 止盈止损设置
            stopLossTakeProfit: {
                enabled: true,
                stopLossPercent: 2.0,
                takeProfitPercent: 5.0,
                autoAdjust: true
            }
        };
    }

    /**
     * 保存状态
     */
    saveState() {
        try {
            localStorage.setItem(this.storageKey, JSON.stringify(this.state));
        } catch (error) {
            console.error('保存全局状态失败:', error);
        }
    }

    /**
     * 获取状态
     */
    getState(path = null) {
        if (!path) return this.state;
        
        const keys = path.split('.');
        let current = this.state;
        
        for (const key of keys) {
            if (current && typeof current === 'object' && key in current) {
                current = current[key];
            } else {
                return null;
            }
        }
        
        return current;
    }

    /**
     * 设置状态
     */
    setState(path, value) {
        const keys = path.split('.');
        let current = this.state;
        
        // 导航到目标位置
        for (let i = 0; i < keys.length - 1; i++) {
            const key = keys[i];
            if (!current[key] || typeof current[key] !== 'object') {
                current[key] = {};
            }
            current = current[key];
        }
        
        // 设置值
        const lastKey = keys[keys.length - 1];
        const oldValue = current[lastKey];
        current[lastKey] = value;
        
        // 触发监听器
        this.notifyListeners(path, value, oldValue);
        
        // 立即保存重要状态
        if (this.isImportantState(path)) {
            this.saveState();
        }
    }

    /**
     * 判断是否为重要状态（需要立即保存）
     */
    isImportantState(path) {
        const importantPaths = [
            'demoTrading.selectedAccountId',
            'demoTrading.aiTradingEnabled',
            'mt5Connection.connected',
            'aiTrading.demo.active',
            'aiTrading.real.active'
        ];
        return importantPaths.includes(path);
    }

    /**
     * 添加状态监听器
     */
    addListener(path, callback) {
        if (!this.listeners[path]) {
            this.listeners[path] = [];
        }
        this.listeners[path].push(callback);
    }

    /**
     * 移除状态监听器
     */
    removeListener(path, callback) {
        if (this.listeners[path]) {
            this.listeners[path] = this.listeners[path].filter(cb => cb !== callback);
        }
    }

    /**
     * 通知监听器
     */
    notifyListeners(path, newValue, oldValue) {
        if (this.listeners[path]) {
            this.listeners[path].forEach(callback => {
                try {
                    callback(newValue, oldValue, path);
                } catch (error) {
                    console.error('状态监听器执行失败:', error);
                }
            });
        }
    }

    /**
     * 重置状态
     */
    resetState(path = null) {
        if (path) {
            this.setState(path, this.getDefaultState()[path]);
        } else {
            this.state = this.getDefaultState();
            this.saveState();
        }
    }

    /**
     * MT5连接状态管理
     */
    setMT5Connected(connected, accountInfo = null) {
        this.setState('mt5Connection.connected', connected);
        this.setState('mt5Connection.accountInfo', accountInfo);
        this.setState('mt5Connection.lastConnectTime', connected ? Date.now() : null);
    }

    getMT5ConnectionState() {
        return this.getState('mt5Connection');
    }

    /**
     * 模拟交易状态管理
     */
    setDemoTradingAccount(accountType, accountId, accountInfo = null) {
        this.setState('demoTrading.selectedAccountType', accountType);
        this.setState('demoTrading.selectedAccountId', accountId);
        this.setState('demoTrading.selectedAccountInfo', accountInfo);
    }

    getDemoTradingState() {
        return this.getState('demoTrading');
    }

    /**
     * AI交易状态管理
     */
    setAITradingState(type, active, config = {}) {
        const basePath = `aiTrading.${type}`;
        this.setState(`${basePath}.active`, active);
        
        if (active) {
            this.setState(`${basePath}.startTime`, Date.now());
            if (config.strategyId) this.setState(`${basePath}.strategyId`, config.strategyId);
            if (config.symbol) this.setState(`${basePath}.symbol`, config.symbol);
            if (config.timeSlot) this.setState(`${basePath}.timeSlot`, config.timeSlot);
        }
    }

    getAITradingState(type) {
        return this.getState(`aiTrading.${type}`);
    }

    /**
     * 止盈止损设置管理
     */
    setStopLossTakeProfitSettings(settings) {
        Object.keys(settings).forEach(key => {
            this.setState(`stopLossTakeProfit.${key}`, settings[key]);
        });
    }

    getStopLossTakeProfitSettings() {
        return this.getState('stopLossTakeProfit');
    }

    /**
     * 调试方法
     */
    debugState() {
        console.log('当前全局状态:', this.state);
    }

    /**
     * 清除所有状态
     */
    clearAllState() {
        localStorage.removeItem(this.storageKey);
        this.state = this.getDefaultState();
    }
}

// 创建全局实例
window.GlobalStateManager = new GlobalStateManager();

// 调试用
window.debugGlobalState = () => {
    window.GlobalStateManager.debugState();
};

console.log('✅ 全局状态管理器已初始化');
