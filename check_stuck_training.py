#!/usr/bin/env python3
"""
检查卡住的深度学习训练任务
"""

import sqlite3
import json
import requests
from datetime import datetime, timed<PERSON>ta

def check_stuck_training_tasks():
    """检查卡住的训练任务"""
    
    print("🔍 检查卡住的深度学习训练任务")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 查找所有running状态的任务
        cursor.execute("""
            SELECT id, model_id, status, progress, current_epoch, total_epochs,
                   train_loss, val_loss, logs, created_at, started_at, updated_at
            FROM training_tasks 
            WHERE status = 'running'
            ORDER BY created_at DESC
        """)
        
        running_tasks = cursor.fetchall()
        
        if not running_tasks:
            print("✅ 没有发现running状态的任务")
            conn.close()
            return []
        
        print(f"📊 发现 {len(running_tasks)} 个running状态的任务:")
        
        stuck_tasks = []
        current_time = datetime.now()
        
        for task in running_tasks:
            (task_id, model_id, status, progress, current_epoch, total_epochs,
             train_loss, val_loss, logs, created_at, started_at, updated_at) = task
            
            print(f"\n🔹 任务: {task_id}")
            print(f"   模型ID: {model_id}")
            print(f"   状态: {status}")
            print(f"   进度: {progress}%")
            print(f"   轮次: {current_epoch}/{total_epochs}")
            print(f"   创建时间: {created_at}")
            print(f"   开始时间: {started_at}")
            print(f"   更新时间: {updated_at}")
            
            # 检查是否卡住
            if updated_at:
                try:
                    last_update = datetime.fromisoformat(updated_at.replace('Z', '+00:00').replace('+00:00', ''))
                    time_since_update = current_time - last_update
                    
                    print(f"   距离上次更新: {time_since_update}")
                    
                    # 如果超过5分钟没有更新，认为是卡住了
                    if time_since_update > timedelta(minutes=5):
                        print(f"   ⚠️ 任务可能卡住了 (超过5分钟无更新)")
                        stuck_tasks.append(task_id)
                    else:
                        print(f"   ✅ 任务正常运行中")
                        
                except Exception as e:
                    print(f"   ❌ 时间解析错误: {e}")
                    stuck_tasks.append(task_id)
            else:
                print(f"   ⚠️ 没有更新时间记录")
                stuck_tasks.append(task_id)
            
            # 显示日志信息
            if logs:
                try:
                    log_data = json.loads(logs)
                    stage = log_data.get('stage', 'unknown')
                    message = log_data.get('message', '')
                    print(f"   当前阶段: {stage}")
                    if message:
                        print(f"   消息: {message}")
                    if 'error' in log_data:
                        print(f"   错误: {log_data['error']}")
                except:
                    print(f"   原始日志: {logs[:100]}...")
        
        conn.close()
        return stuck_tasks
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return []

def check_training_processes():
    """检查训练进程状态"""
    
    print(f"\n🔄 检查训练进程状态")
    print("=" * 60)
    
    try:
        # 检查应用程序日志
        print("💡 建议检查以下内容:")
        print("1. 应用程序控制台是否有错误信息")
        print("2. GPU内存是否耗尽")
        print("3. 系统内存使用情况")
        print("4. 网络连接是否正常")
        print("5. MT5连接状态")
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")

def fix_stuck_tasks(stuck_task_ids):
    """修复卡住的任务"""
    
    if not stuck_task_ids:
        print(f"\n✅ 没有需要修复的卡住任务")
        return
    
    print(f"\n🔧 修复卡住的任务")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        for task_id in stuck_task_ids:
            print(f"🔧 修复任务: {task_id}")
            
            # 将卡住的任务标记为失败
            cursor.execute("""
                UPDATE training_tasks
                SET status = 'failed', 
                    completed_at = ?,
                    updated_at = ?,
                    logs = json_set(COALESCE(logs, '{}'), '$.error', '任务卡住，已自动清理')
                WHERE id = ?
            """, (datetime.now(), datetime.now(), task_id))
            
            print(f"   ✅ 任务已标记为失败")
        
        conn.commit()
        conn.close()
        
        print(f"✅ 已修复 {len(stuck_task_ids)} 个卡住的任务")
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")

def test_training_api():
    """测试训练API是否正常"""
    
    print(f"\n🧪 测试训练API状态")
    print("=" * 60)
    
    try:
        # 登录
        session = requests.Session()
        
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post('http://127.0.0.1:5000/login', data=login_data)
        if response.status_code != 200:
            print("❌ 登录失败")
            return False
        
        print("✅ 登录成功")
        
        # 测试GPU状态API
        response = session.get('http://127.0.0.1:5000/api/deep-learning/gpu-status')
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                gpu_status = result.get('gpu_status', {})
                print(f"✅ GPU状态API正常")
                print(f"   GPU可用: {gpu_status.get('gpu_available')}")
                print(f"   GPU内存: {gpu_status.get('memory_used', 0):.1f}GB / {gpu_status.get('memory_total', 0):.1f}GB")
                
                # 检查GPU内存是否耗尽
                memory_usage = gpu_status.get('memory_usage_percent', 0)
                if memory_usage > 90:
                    print(f"⚠️ GPU内存使用率过高: {memory_usage}%")
                else:
                    print(f"✅ GPU内存使用正常: {memory_usage}%")
                    
            else:
                print(f"❌ GPU状态API错误: {result.get('error')}")
        else:
            print(f"❌ GPU状态API请求失败: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False

def provide_solutions():
    """提供解决方案"""
    
    print(f"\n💡 解决方案建议")
    print("=" * 60)
    
    print(f"🔧 立即可以尝试的解决方案:")
    print(f"1. 清理卡住的任务 (本脚本已自动处理)")
    print(f"2. 重启应用程序")
    print(f"3. 检查GPU内存使用情况")
    print(f"4. 检查系统内存是否充足")
    
    print(f"\n🎯 预防措施:")
    print(f"1. 使用更小的批次大小 (batch_size)")
    print(f"2. 减少序列长度 (sequence_length)")
    print(f"3. 启用早停机制，避免过长训练")
    print(f"4. 定期监控训练进度")
    
    print(f"\n⚠️ 如果问题持续:")
    print(f"1. 检查应用程序控制台的详细错误日志")
    print(f"2. 重启整个系统")
    print(f"3. 检查CUDA和PyTorch安装")
    print(f"4. 尝试使用CPU模式训练")

def main():
    """主函数"""
    
    print("🚨 深度学习训练卡住问题诊断和修复")
    print("=" * 80)
    
    # 检查卡住的任务
    stuck_tasks = check_stuck_training_tasks()
    
    # 检查进程状态
    check_training_processes()
    
    # 测试API状态
    api_ok = test_training_api()
    
    # 修复卡住的任务
    fix_stuck_tasks(stuck_tasks)
    
    # 提供解决方案
    provide_solutions()
    
    print(f"\n📋 诊断总结")
    print("=" * 80)
    
    if stuck_tasks:
        print(f"🔧 发现并修复了 {len(stuck_tasks)} 个卡住的任务")
        print(f"✅ 卡住的任务已标记为失败，可以重新启动训练")
    else:
        print(f"✅ 没有发现明显卡住的任务")
    
    if api_ok:
        print(f"✅ 训练API状态正常")
    else:
        print(f"⚠️ 训练API可能有问题")
    
    print(f"\n🎯 下一步建议:")
    if stuck_tasks:
        print(f"1. 卡住的任务已清理，可以重新启动训练")
        print(f"2. 建议使用更小的批次大小和序列长度")
        print(f"3. 启用早停机制，设置合理的耐心值")
    else:
        print(f"1. 检查当前训练任务的具体状态")
        print(f"2. 查看应用程序控制台的实时日志")
        print(f"3. 如果确实卡住，可以手动停止并重新开始")

if __name__ == '__main__':
    main()
