#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SN加仓策略管理器
负责SN加仓策略的执行状态管理和持续监控
"""

import json
import time
import threading
from datetime import datetime
from typing import Dict, List, Optional
import MetaTrader5 as mt5
from services.mt5_service import mt5_service

class SNStrategyManager:
    """SN加仓策略管理器"""
    
    def __init__(self):
        self.active_strategies = {}  # 活跃的SN策略
        self.monitoring_thread = None
        self.is_monitoring = False
        self.lock = threading.Lock()
    
    def start_sn_strategy(self, user_id: int, config: dict) -> dict:
        """启动SN加仓策略"""
        try:
            strategy_id = f"sn_{user_id}_{int(time.time())}"
            
            # 创建策略状态
            strategy_state = {
                'id': strategy_id,
                'user_id': user_id,
                'config': config,
                'status': 'initializing',
                'start_time': datetime.now().isoformat(),
                'positions': [],
                'start_time': datetime.now().isoformat(),  # 添加开始时间
                'groups': {
                    'group1': {'status': 'pending', 'orders': [], 'profit_closed': False},
                    'group2': {'status': 'pending', 'orders': [], 'profit_closed': False},
                    'group3': {'status': 'pending', 'orders': [], 'profit_closed': False}
                },
                'total_profit': 0.0,
                'group2_profit_level': None,  # 第2组盈利水平，用于特殊规则
                'special_rule_triggered': False
            }
            
            # 执行SN加仓订单
            execution_result = self._execute_sn_orders(strategy_state)
            
            if execution_result['success']:
                strategy_state['status'] = 'active'
                strategy_state['positions'] = execution_result['positions']
                
                # 保存到活跃策略
                with self.lock:
                    self.active_strategies[strategy_id] = strategy_state
                
                # 启动监控
                self._start_monitoring()
                
                print(f"✅ SN策略 {strategy_id} 启动成功")

                # 计算总订单数和总手数
                total_orders = len(execution_result['positions'])
                total_volume = sum(pos['volume'] for pos in execution_result['positions'])

                return {
                    'success': True,
                    'strategy_id': strategy_id,
                    'message': 'SN加仓策略启动成功',
                    'positions': execution_result['positions'],
                    'executed_orders': execution_result['positions'],  # 兼容前端
                    'total_orders': total_orders,
                    'total_volume': total_volume
                }
            else:
                print(f"❌ SN策略执行失败: {execution_result['error']}")
                return {
                    'success': False,
                    'error': execution_result['error']
                }
                
        except Exception as e:
            print(f"❌ 启动SN策略异常: {e}")
            return {
                'success': False,
                'error': f'启动SN策略失败: {str(e)}'
            }
    
    def _execute_sn_orders(self, strategy_state: dict) -> dict:
        """执行SN加仓订单"""
        try:
            config = strategy_state['config']
            symbol = config['trading_symbol']
            direction = config['trading_direction']

            print(f"🔍 开始执行SN加仓订单，品种: {symbol}, 方向: {direction}")

            # 严格检查MT5连接状态
            if not mt5_service.connected:
                print(f"❌ MT5未连接，尝试重新连接...")
                if not mt5_service.connect():
                    error_msg = 'MT5连接失败，无法执行真实交易。请检查MT5软件是否正常运行并已登录。'
                    print(f"❌ {error_msg}")
                    return {'success': False, 'error': error_msg}
                else:
                    print(f"✅ MT5重新连接成功")

            # 验证MT5账户状态
            account_info = mt5_service.get_account_info()
            if not account_info:
                error_msg = 'MT5账户信息获取失败，无法执行真实交易。请确保MT5已正确登录。'
                print(f"❌ {error_msg}")
                return {'success': False, 'error': error_msg}

            print(f"✅ MT5账户验证成功")
            print(f"   账户: {account_info.get('login', 'N/A')}")
            print(f"   余额: ${account_info.get('balance', 0):.2f}")
            print(f"   净值: ${account_info.get('equity', 0):.2f}")
            
            # SN加仓策略的3组配置 - 使用前端配置的参数
            # 从前端获取实际配置参数，如果没有则使用默认值
            frontend_params = strategy_state.get('config', {}).get('parameters', [
                {'group': '第1组', 'orders': [1, 2], 'volume': 0.01, 'stopLoss': 200, 'takeProfit': 800},
                {'group': '第2组', 'orders': [3, 4], 'volume': 0.01, 'stopLoss': 250, 'takeProfit': 1200},
                {'group': '第3组', 'orders': [5, 6], 'volume': 0.01, 'stopLoss': 400, 'takeProfit': 2000}
            ])

            groups = []
            for i, param in enumerate(frontend_params):
                groups.append({
                    'name': param['group'],
                    'group_key': f'group{i+1}',
                    'orders': param['orders'],
                    'stop_loss': param['stopLoss'],  # 使用前端配置的止损点数
                    'take_profit': param['takeProfit'],  # 使用前端配置的止盈点数
                    'volume': param['volume']
                })
            
            # 获取当前价格
            tick = mt5_service.get_symbol_tick(symbol)
            if not tick:
                return {'success': False, 'error': '无法获取当前价格'}
            
            current_price = tick['ask'] if direction == 'up' else tick['bid']
            executed_positions = []
            
            # 执行所有订单
            for group in groups:
                group_positions = []
                
                for order_num in group['orders']:
                    # 计算止损止盈价格 - 修正点值计算
                    # XAUUSD: 1点 = 0.01美元, 外汇: 1点 = 0.0001
                    point_value = 0.01 if 'XAU' in symbol else 0.0001

                    print(f"🔍 计算订单{order_num}止损止盈:")
                    print(f"   当前价格: {current_price}")
                    print(f"   点值: {point_value}")
                    print(f"   止损点数: {group['stop_loss']}点")
                    print(f"   止盈点数: {group['take_profit']}点")

                    if direction == 'up':  # 买入
                        stop_loss = current_price - (group['stop_loss'] * point_value)
                        take_profit = current_price + (group['take_profit'] * point_value)
                        order_type = mt5.ORDER_TYPE_BUY
                    else:  # 卖出
                        stop_loss = current_price + (group['stop_loss'] * point_value)
                        take_profit = current_price - (group['take_profit'] * point_value)
                        order_type = mt5.ORDER_TYPE_SELL

                    print(f"   计算结果: 止损={stop_loss:.2f}, 止盈={take_profit:.2f}")
                    
                    # 发送订单
                    print(f"📤 发送订单{order_num}: {symbol} {direction} {group['volume']}手")
                    print(f"   入场价: {current_price}, 止损: {stop_loss}, 止盈: {take_profit}")

                    result = mt5_service.send_order(
                        symbol=symbol,
                        order_type=order_type,
                        volume=group['volume'],
                        price=None,  # 市价单
                        sl=stop_loss,
                        tp=take_profit,
                        comment=f"SN-{group['name']}-{order_num}"
                    )

                    print(f"📋 订单{order_num}结果: {result}")

                    if result['success']:
                        position = {
                            'order_id': order_num,
                            'group': group['name'],
                            'group_key': group['group_key'],
                            'direction': direction,
                            'volume': group['volume'],
                            'entry_price': current_price,
                            'stop_loss': stop_loss,
                            'take_profit': take_profit,
                            'mt5_order_id': result.get('order_id'),
                            'status': 'active',
                            'open_time': datetime.now().isoformat()
                        }
                        
                        group_positions.append(position)
                        executed_positions.append(position)
                        
                        print(f"✅ 订单{order_num}执行成功: {direction} {group['volume']}手 @ {current_price}")
                    else:
                        error_detail = result.get('error', '未知错误')
                        print(f"❌ 订单{order_num}执行失败: {error_detail}")

                        # 真实交易失败时，立即停止执行并返回错误
                        error_msg = f"SN加仓策略执行失败：订单{order_num}无法执行 - {error_detail}。" \
                                   f"已执行的订单数: {len(executed_positions)}。" \
                                   f"请检查MT5连接、账户余额、交易权限等。"

                        print(f"🛑 策略执行中止: {error_msg}")

                        return {
                            'success': False,
                            'error': error_msg,
                            'partial_execution': True,
                            'executed_orders': len(executed_positions),
                            'failed_order': order_num,
                            'executed_positions': executed_positions
                        }
                
                # 更新组状态
                strategy_state['groups'][group['group_key']]['orders'] = group_positions
                strategy_state['groups'][group['group_key']]['status'] = 'active'
            
            return {
                'success': True,
                'positions': executed_positions
            }
            
        except Exception as e:
            print(f"❌ 执行SN订单异常: {e}")
            return {
                'success': False,
                'error': f'执行订单失败: {str(e)}'
            }
    
    def _start_monitoring(self):
        """启动监控线程"""
        if not self.is_monitoring:
            self.is_monitoring = True
            self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
            self.monitoring_thread.start()
            print("✅ SN策略监控已启动")
    
    def _monitoring_loop(self):
        """监控循环"""
        while self.is_monitoring:
            try:
                with self.lock:
                    strategies_to_monitor = list(self.active_strategies.values())
                
                for strategy in strategies_to_monitor:
                    self._monitor_strategy(strategy)
                
                time.sleep(5)  # 每5秒检查一次
                
            except Exception as e:
                print(f"❌ 监控循环异常: {e}")
                time.sleep(10)
    
    def _monitor_strategy(self, strategy: dict):
        """监控单个策略"""
        try:
            strategy_id = strategy['id']
            
            # 检查持仓状态
            self._check_positions_status(strategy)
            
            # 检查第2组特殊规则
            self._check_group2_special_rule(strategy)
            
            # 更新总盈亏
            self._update_total_profit(strategy)
            
        except Exception as e:
            print(f"❌ 监控策略 {strategy.get('id')} 异常: {e}")
    
    def _check_positions_status(self, strategy: dict):
        """检查持仓状态"""
        # 这里应该检查MT5中的实际持仓状态
        # 判断是否触发止损或止盈
        pass
    
    def _check_group2_special_rule(self, strategy: dict):
        """检查第2组特殊规则"""
        # 如果第2组实现盈利自动平仓，但趋势未达到第3组止盈点，
        # 又返回到第2组止盈点时，对第3组执行平仓
        pass
    
    def _update_total_profit(self, strategy: dict):
        """更新总盈亏"""
        # 计算当前总盈亏
        pass
    
    def get_strategy_status(self, strategy_id: str) -> Optional[dict]:
        """获取策略状态"""
        with self.lock:
            return self.active_strategies.get(strategy_id)
    
    def stop_strategy(self, strategy_id: str) -> dict:
        """停止策略"""
        try:
            with self.lock:
                if strategy_id in self.active_strategies:
                    strategy = self.active_strategies[strategy_id]
                    strategy['status'] = 'stopped'
                    strategy['stop_time'] = datetime.now().isoformat()
                    
                    # 平掉所有持仓
                    self._close_all_positions(strategy)
                    
                    # 从活跃策略中移除
                    del self.active_strategies[strategy_id]
                    
                    print(f"✅ SN策略 {strategy_id} 已停止")
                    return {'success': True, 'message': '策略已停止'}
                else:
                    return {'success': False, 'error': '策略不存在'}
                    
        except Exception as e:
            print(f"❌ 停止策略异常: {e}")
            return {'success': False, 'error': str(e)}
    
    def _close_all_positions(self, strategy: dict):
        """平掉策略的所有持仓"""
        try:
            for position in strategy['positions']:
                if position['status'] == 'active':
                    # 这里应该调用MT5平仓
                    print(f"平仓订单 {position['order_id']}")
                    position['status'] = 'closed'
                    
        except Exception as e:
            print(f"❌ 平仓异常: {e}")
    
    def get_all_active_strategies(self) -> List[dict]:
        """获取所有活跃策略"""
        with self.lock:
            return list(self.active_strategies.values())

# 全局SN策略管理器实例
sn_strategy_manager = SNStrategyManager()
