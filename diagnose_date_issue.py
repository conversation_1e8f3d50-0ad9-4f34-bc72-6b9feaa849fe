#!/usr/bin/env python3
"""
诊断AI模型训练中的日期设置问题
"""

import sys
import os
import json
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_date_range_logic():
    """测试日期范围逻辑"""
    
    print("🔍 诊断AI模型训练日期设置问题")
    print("=" * 70)
    
    # 导入深度学习服务
    from services.deep_learning_service import deep_learning_service
    
    # 测试用例：模拟用户选择结束日期为2025/07/01
    test_cases = [
        {
            'name': '用户选择结束日期2025/07/01，训练365天',
            'data_config': {
                'mode': 'days',
                'training_days': 365,
                'end_date': '2025-07-01',  # 用户选择的结束日期
                'symbol': 'XAUUSD',
                'timeframe': '1h'
            },
            'expected_end': '2025-07-01'
        },
        {
            'name': '用户选择日期范围模式',
            'data_config': {
                'mode': 'range',
                'start_date': '2024-07-01',
                'end_date': '2025-07-01',
                'symbol': 'XAUUSD',
                'timeframe': '1h'
            },
            'expected_end': '2025-07-01'
        },
        {
            'name': '用户没有选择结束日期（应该使用当前日期）',
            'data_config': {
                'mode': 'days',
                'training_days': 365,
                'symbol': 'XAUUSD',
                'timeframe': '1h'
            },
            'expected_end': datetime.now().strftime('%Y-%m-%d')
        }
    ]
    
    print("📊 测试日期范围计算逻辑:")
    print("-" * 50)
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n{i}. {case['name']}")
        print(f"   输入配置: {json.dumps(case['data_config'], indent=6, ensure_ascii=False)}")
        
        try:
            # 测试 _get_date_range_info 方法
            date_range = deep_learning_service._get_date_range_info(case['data_config'])
            
            print(f"   计算结果:")
            print(f"      开始日期: {date_range['start_date']}")
            print(f"      结束日期: {date_range['end_date']}")
            print(f"      训练天数: {date_range['days']}")
            print(f"      模式: {date_range['mode']}")
            
            # 检查结果是否符合预期
            if date_range['end_date'] == case['expected_end']:
                print(f"   ✅ 结果正确")
            else:
                print(f"   ❌ 结果错误!")
                print(f"      期望结束日期: {case['expected_end']}")
                print(f"      实际结束日期: {date_range['end_date']}")
                
        except Exception as e:
            print(f"   ❌ 计算失败: {e}")
    
    return True

def test_training_days_calculation():
    """测试训练天数计算"""
    
    print(f"\n📊 测试训练天数计算逻辑:")
    print("-" * 50)
    
    from services.deep_learning_service import deep_learning_service
    
    test_cases = [
        {
            'name': '天数模式 - 有结束日期',
            'data_config': {
                'mode': 'days',
                'training_days': 365,
                'end_date': '2025-07-01'
            },
            'fallback_config': {},
            'expected': 365
        },
        {
            'name': '范围模式',
            'data_config': {
                'mode': 'range',
                'start_date': '2024-07-01',
                'end_date': '2025-07-01'
            },
            'fallback_config': {},
            'expected': 366  # 2024是闰年
        },
        {
            'name': '预设模式 - 1年',
            'data_config': {
                'mode': 'preset',
                'preset_range': '1year'
            },
            'fallback_config': {},
            'expected': 365
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n{i}. {case['name']}")
        print(f"   输入: {json.dumps(case['data_config'], ensure_ascii=False)}")
        
        try:
            result = deep_learning_service._calculate_training_days(
                case['data_config'], 
                case['fallback_config']
            )
            
            print(f"   计算结果: {result}天")
            print(f"   期望结果: {case['expected']}天")
            
            if result == case['expected']:
                print(f"   ✅ 计算正确")
            else:
                print(f"   ❌ 计算错误!")
                
        except Exception as e:
            print(f"   ❌ 计算失败: {e}")

def simulate_training_config():
    """模拟完整的训练配置"""
    
    print(f"\n🧪 模拟完整训练配置:")
    print("-" * 50)
    
    # 模拟前端发送的配置
    frontend_config = {
        'model_name': 'XAU-H1-Test',
        'model_type': 'lstm',
        'symbol': 'XAUUSD',
        'timeframe': '1h',
        'data_config': {
            'mode': 'days',
            'training_days': 365,
            'end_date': '2025-07-01',  # 用户选择的结束日期
            'symbol': 'XAUUSD',
            'timeframe': '1h'
        },
        'sequence_length': 60,
        'hidden_size': 128,
        'num_layers': 2,
        'epochs': 100,
        'features': {
            'price': True,
            'volume': True,
            'technical': True,
            'time': True
        }
    }
    
    print("前端配置:")
    print(json.dumps(frontend_config, indent=2, ensure_ascii=False))
    
    from services.deep_learning_service import deep_learning_service
    
    try:
        # 测试日期范围计算
        data_config = frontend_config['data_config']
        date_range = deep_learning_service._get_date_range_info(data_config)
        
        print(f"\n后端计算的日期范围:")
        print(f"  开始日期: {date_range['start_date']}")
        print(f"  结束日期: {date_range['end_date']}")
        print(f"  训练天数: {date_range['days']}")
        
        # 检查是否正确使用了用户选择的结束日期
        user_end_date = data_config.get('end_date')
        calculated_end_date = date_range['end_date']
        
        if user_end_date == calculated_end_date:
            print(f"\n✅ 日期设置正确!")
            print(f"   用户选择: {user_end_date}")
            print(f"   系统使用: {calculated_end_date}")
        else:
            print(f"\n❌ 日期设置错误!")
            print(f"   用户选择: {user_end_date}")
            print(f"   系统使用: {calculated_end_date}")
            print(f"   这就是问题所在!")
            
    except Exception as e:
        print(f"❌ 模拟失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")

def main():
    """主函数"""
    
    print("🔧 AI模型训练日期设置问题诊断")
    print("=" * 80)
    
    try:
        # 测试日期范围逻辑
        test_date_range_logic()
        
        # 测试训练天数计算
        test_training_days_calculation()
        
        # 模拟完整训练配置
        simulate_training_config()
        
        print(f"\n📊 诊断结果")
        print("=" * 80)
        
        print("🔍 问题分析:")
        print("• 检查前端是否正确传递end_date")
        print("• 检查后端_get_date_range_info方法的逻辑")
        print("• 验证不同模式下的日期处理")
        
        print(f"\n💡 可能的解决方案:")
        print("• 确保前端正确设置data_config.end_date")
        print("• 修复后端日期范围计算逻辑")
        print("• 添加更多的日志输出用于调试")
        print("• 统一日期格式和处理逻辑")
        
    except Exception as e:
        print(f"❌ 诊断失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")

if __name__ == '__main__':
    main()
