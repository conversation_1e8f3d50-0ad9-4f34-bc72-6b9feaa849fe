#!/usr/bin/env python3
"""
多时间周期形态分析服务
为Web界面提供多时间周期的形态分析功能
"""

import os
import sys
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional
import pandas as pd
import numpy as np

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from services.mt5_pattern_analyzer import MT5PatternAnalyzer
from services.pattern_visualizer import PatternVisualizer
from services.data_converter import DataConverter

logger = logging.getLogger(__name__)

class MultiTimeframePatternService:
    """多时间周期形态分析服务"""
    
    def __init__(self):
        self.analyzer = MT5PatternAnalyzer()
        self.visualizer = PatternVisualizer()
        self.timeframes = ['5m', '15m', '30m', '1h']
        self.analysis_cache = {}
        
    def analyze_all_timeframes(self, symbol: str, bars: int = 500,
                             confidence_threshold: float = 0.6,
                             timeframes: List[str] = None) -> Dict:
        """分析所有时间周期"""
        # 使用传入的时间周期，如果没有则使用默认的
        if timeframes is None:
            timeframes = self.timeframes

        logger.info(f"开始多时间周期分析: {symbol}, 时间周期: {timeframes}")

        results = {
            'symbol': symbol,
            'timestamp': datetime.now().isoformat(),
            'timeframes': {},
            'summary': {},
            'best_opportunities': []
        }
        
        # 连接MT5
        if not self.analyzer.connect_mt5():
            error_msg = "无法连接MT5，请确保：1) MT5终端已启动 2) 已登录交易账户 3) 允许DLL导入"
            logger.error(error_msg)
            return {'error': error_msg}
        
        all_patterns = []
        
        for timeframe in timeframes:
            try:
                logger.info(f"分析 {timeframe} 时间周期...")
                
                # 获取数据
                df = self.analyzer.get_historical_data(symbol, timeframe, bars)
                if df is None:
                    logger.warning(f"无法获取{timeframe}数据")
                    continue
                
                # 识别形态
                patterns = self._identify_all_patterns(df)
                
                # 过滤低置信度形态
                filtered_patterns = [p for p in patterns if p.get('confidence', 0) >= confidence_threshold]
                
                # 保存结果
                timeframe_result = {
                    'data_points': len(df),
                    'total_patterns': len(patterns),
                    'high_confidence_patterns': len(filtered_patterns),
                    'patterns': filtered_patterns,
                    'bullish_count': len([p for p in filtered_patterns if p.get('direction') == 'bullish']),
                    'bearish_count': len([p for p in filtered_patterns if p.get('direction') == 'bearish']),
                    'timeframe_info': self._get_timeframe_info(timeframe, bars)
                }
                
                results['timeframes'][timeframe] = timeframe_result
                all_patterns.extend(filtered_patterns)
                
                logger.info(f"{timeframe}: 发现{len(filtered_patterns)}个高置信度形态")
                
            except Exception as e:
                logger.error(f"分析{timeframe}时出错: {e}")
                continue
        
        # 生成综合分析
        results['summary'] = self._generate_summary(results['timeframes'])
        results['best_opportunities'] = self._find_best_opportunities(all_patterns)
        
        # 保存分析结果
        self._save_analysis_results(symbol, results)
        
        logger.info(f"多时间周期分析完成: 总共{len(all_patterns)}个形态")
        return results
    
    def _identify_all_patterns(self, df: pd.DataFrame) -> List[Dict]:
        """识别所有形态类型"""
        patterns = []

        try:
            # 头肩形态
            hs_top = self.analyzer.identify_head_shoulders_top(df)
            hs_bottom = self.analyzer.identify_head_shoulders_bottom(df)
            patterns.extend(hs_top)
            patterns.extend(hs_bottom)

            # 双顶双底
            double_top = self.analyzer.identify_double_top(df)
            double_bottom = self.analyzer.identify_double_bottom(df)
            patterns.extend(double_top)
            patterns.extend(double_bottom)

            # 三角形
            triangles = self.analyzer.identify_triangles(df)
            patterns.extend(triangles)

            # 楔形
            wedges = self.analyzer.identify_wedges(df)
            patterns.extend(wedges)

        except Exception as e:
            logger.error(f"形态识别出错: {e}")

        return patterns
    
    def _get_timeframe_info(self, timeframe: str, bars: int) -> Dict:
        """获取时间周期信息"""
        timeframe_map = {
            '5m': {'name': '5分钟', 'minutes': 5, 'suitable_for': '超短线交易'},
            '15m': {'name': '15分钟', 'minutes': 15, 'suitable_for': '短线交易'},
            '30m': {'name': '30分钟', 'minutes': 30, 'suitable_for': '短线交易'},
            '1h': {'name': '1小时', 'minutes': 60, 'suitable_for': '日内交易'}
        }
        
        info = timeframe_map.get(timeframe, {})
        if info:
            total_minutes = bars * info['minutes']
            total_hours = total_minutes / 60
            total_days = total_hours / 24
            
            info.update({
                'bars': bars,
                'total_hours': round(total_hours, 1),
                'total_days': round(total_days, 1),
                'coverage': f"约{total_days:.1f}天"
            })
        
        return info
    
    def _generate_summary(self, timeframes: Dict) -> Dict:
        """生成综合分析摘要"""
        summary = {
            'total_patterns': 0,
            'total_bullish': 0,
            'total_bearish': 0,
            'best_timeframe': None,
            'market_sentiment': 'neutral',
            'confidence_distribution': {},
            'pattern_type_distribution': {}
        }
        
        pattern_types = {}
        confidence_levels = {'high': 0, 'medium': 0, 'low': 0}
        
        for tf, data in timeframes.items():
            summary['total_patterns'] += data['high_confidence_patterns']
            summary['total_bullish'] += data['bullish_count']
            summary['total_bearish'] += data['bearish_count']
            
            # 统计形态类型
            for pattern in data['patterns']:
                pattern_name = pattern.get('pattern_name', pattern.get('pattern_type', 'unknown'))
                pattern_types[pattern_name] = pattern_types.get(pattern_name, 0) + 1
                
                # 统计置信度分布
                confidence = pattern.get('confidence', 0)
                if confidence >= 0.8:
                    confidence_levels['high'] += 1
                elif confidence >= 0.6:
                    confidence_levels['medium'] += 1
                else:
                    confidence_levels['low'] += 1
        
        # 确定市场情绪
        if summary['total_bullish'] > summary['total_bearish'] * 1.2:
            summary['market_sentiment'] = 'bullish'
        elif summary['total_bearish'] > summary['total_bullish'] * 1.2:
            summary['market_sentiment'] = 'bearish'
        
        # 找出最佳时间周期
        best_tf = max(timeframes.keys(), 
                     key=lambda x: timeframes[x]['high_confidence_patterns'])
        summary['best_timeframe'] = best_tf
        
        summary['confidence_distribution'] = confidence_levels
        summary['pattern_type_distribution'] = pattern_types
        
        return summary
    
    def _find_best_opportunities(self, patterns: List[Dict], limit: int = 10) -> List[Dict]:
        """找出最佳交易机会"""
        # 按置信度排序
        sorted_patterns = sorted(patterns, 
                               key=lambda x: x.get('confidence', 0), 
                               reverse=True)
        
        best_opportunities = []
        for pattern in sorted_patterns[:limit]:
            opportunity = {
                'pattern_type': pattern.get('pattern_type'),
                'pattern_name': pattern.get('pattern_name'),
                'direction': pattern.get('direction'),
                'confidence': pattern.get('confidence'),
                'entry_point': pattern.get('entry_point'),
                'stop_loss': pattern.get('stop_loss'),
                'take_profit': pattern.get('take_profit'),
                'risk_reward_ratio': self._calculate_risk_reward(pattern),
                'recommendation': self._get_recommendation(pattern)
            }
            best_opportunities.append(opportunity)
        
        return best_opportunities
    
    def _calculate_risk_reward(self, pattern: Dict) -> Optional[float]:
        """计算风险回报比"""
        try:
            entry = pattern.get('entry_point')
            stop_loss = pattern.get('stop_loss')
            take_profit = pattern.get('take_profit')
            
            if not all([entry, stop_loss, take_profit]):
                return None
            
            risk = abs(entry - stop_loss)
            reward = abs(take_profit - entry)
            
            if risk > 0:
                return round(reward / risk, 2)
        except:
            pass
        
        return None
    
    def _get_recommendation(self, pattern: Dict) -> str:
        """获取交易建议"""
        confidence = pattern.get('confidence', 0)
        direction = pattern.get('direction', 'neutral')
        
        if confidence >= 0.8:
            strength = "强烈"
        elif confidence >= 0.7:
            strength = "建议"
        else:
            strength = "谨慎"
        
        if direction == 'bullish':
            return f"{strength}看涨"
        elif direction == 'bearish':
            return f"{strength}看跌"
        else:
            return "观望"
    
    def _save_analysis_results(self, symbol: str, results: Dict):
        """保存分析结果"""
        try:
            # 创建保存目录
            save_dir = 'pattern_analysis_results'
            os.makedirs(save_dir, exist_ok=True)
            
            # 生成文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{save_dir}/multi_timeframe_{symbol}_{timestamp}.json"
            
            # 使用数据转换器处理numpy类型
            cleaned_results = DataConverter.convert_analysis_results(results)

            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(cleaned_results, f, ensure_ascii=False, indent=2)
            
            logger.info(f"分析结果已保存: {filename}")
            
        except Exception as e:
            logger.error(f"保存分析结果失败: {e}")
    
    def get_saved_analyses(self, symbol: str = None) -> List[Dict]:
        """获取已保存的分析结果"""
        try:
            save_dir = 'pattern_analysis_results'
            if not os.path.exists(save_dir):
                return []
            
            analyses = []
            for filename in os.listdir(save_dir):
                if filename.endswith('.json'):
                    if symbol and symbol not in filename:
                        continue
                    
                    filepath = os.path.join(save_dir, filename)
                    try:
                        with open(filepath, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                        
                        analyses.append({
                            'filename': filename,
                            'symbol': data.get('symbol'),
                            'timestamp': data.get('timestamp'),
                            'total_patterns': data.get('summary', {}).get('total_patterns', 0),
                            'filepath': filepath
                        })
                    except Exception as e:
                        logger.warning(f"读取文件{filename}失败: {e}")
            
            # 按时间排序
            analyses.sort(key=lambda x: x['timestamp'], reverse=True)
            return analyses
            
        except Exception as e:
            logger.error(f"获取已保存分析失败: {e}")
            return []
    
    def load_analysis(self, filepath: str) -> Optional[Dict]:
        """加载指定的分析结果"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"加载分析文件失败: {e}")
            return None

def main():
    """测试函数"""
    service = MultiTimeframePatternService()
    
    # 测试分析
    results = service.analyze_all_timeframes('XAUUSD', bars=300, confidence_threshold=0.6)
    
    if 'error' not in results:
        print(f"✅ 分析完成!")
        print(f"总形态数: {results['summary']['total_patterns']}")
        print(f"看涨形态: {results['summary']['total_bullish']}")
        print(f"看跌形态: {results['summary']['total_bearish']}")
        print(f"最佳时间周期: {results['summary']['best_timeframe']}")
        print(f"市场情绪: {results['summary']['market_sentiment']}")
    else:
        print(f"❌ 分析失败: {results['error']}")

if __name__ == "__main__":
    main()
