#!/usr/bin/env python3
"""
策略配置服务 - 管理不同策略的参数配置
"""

import json
import logging
from typing import Dict, Any, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

class StrategyConfigService:
    """策略配置服务"""
    
    def __init__(self):
        # 默认策略参数配置
        self.default_configs = {
            # 趋势跟踪策略
            'trend_following': {
                'stop_loss_percent': 0.008,  # 0.8%
                'take_profit_percent': 0.016,  # 1.6%
                'max_positions': 3,
                'signal_threshold': 0.7,
                'description': '趋势跟踪策略 - 适合趋势明显的市场'
            },
            
            # 均值回归策略
            'mean_reversion': {
                'stop_loss_percent': 0.006,  # 0.6%
                'take_profit_percent': 0.012,  # 1.2%
                'max_positions': 5,
                'signal_threshold': 0.75,
                'description': '均值回归策略 - 适合震荡市场'
            },
            
            # 突破策略
            'breakout': {
                'stop_loss_percent': 0.010,  # 1.0%
                'take_profit_percent': 0.020,  # 2.0%
                'max_positions': 2,
                'signal_threshold': 0.8,
                'description': '突破策略 - 适合波动较大的市场'
            },
            
            # 高频策略
            'high_frequency': {
                'stop_loss_percent': 0.004,  # 0.4%
                'take_profit_percent': 0.008,  # 0.8%
                'max_positions': 8,
                'signal_threshold': 0.6,
                'description': '高频策略 - 适合短期交易'
            },
            
            # 保守策略
            'conservative': {
                'stop_loss_percent': 0.005,  # 0.5%
                'take_profit_percent': 0.010,  # 1.0%
                'max_positions': 2,
                'signal_threshold': 0.75,  # 恢复到更保守的阈值，确保信号质量
                'description': '保守策略 - 风险较低'
            },
            
            # 激进策略
            'aggressive': {
                'stop_loss_percent': 0.015,  # 1.5%
                'take_profit_percent': 0.030,  # 3.0%
                'max_positions': 5,
                'signal_threshold': 0.65,
                'description': '激进策略 - 高风险高收益'
            }
        }
        
        # 根据策略名称模式匹配配置
        self.strategy_patterns = {
            'trend': 'trend_following',
            'mean': 'mean_reversion',
            'reversion': 'mean_reversion',
            'breakout': 'breakout',
            'break': 'breakout',
            'hf': 'high_frequency',
            'high': 'high_frequency',
            'conservative': 'conservative',
            'safe': 'conservative',
            'aggressive': 'aggressive',
            'risk': 'aggressive'
        }
    
    def get_strategy_config(self, strategy_id: str, strategy_name: str = None) -> Dict[str, Any]:
        """
        根据策略ID和名称获取配置
        
        Args:
            strategy_id: 策略ID
            strategy_name: 策略名称
            
        Returns:
            策略配置字典
        """
        try:
            # 首先尝试从策略名称匹配
            if strategy_name:
                config_type = self._match_strategy_type(strategy_name.lower())
                if config_type:
                    config = self.default_configs[config_type].copy()
                    config['config_type'] = config_type
                    config['matched_by'] = 'name'
                    logger.info(f"✅ 策略 {strategy_name} 匹配到配置类型: {config_type}")
                    return config
            
            # 从策略ID匹配
            config_type = self._match_strategy_type(strategy_id.lower())
            if config_type:
                config = self.default_configs[config_type].copy()
                config['config_type'] = config_type
                config['matched_by'] = 'id'
                logger.info(f"✅ 策略ID {strategy_id} 匹配到配置类型: {config_type}")
                return config
            
            # 使用默认配置（趋势跟踪）
            config = self.default_configs['trend_following'].copy()
            config['config_type'] = 'trend_following'
            config['matched_by'] = 'default'
            logger.info(f"⚠️ 策略 {strategy_id} 使用默认配置: trend_following")
            return config
            
        except Exception as e:
            logger.error(f"❌ 获取策略配置失败: {e}")
            # 返回最保守的配置
            config = self.default_configs['conservative'].copy()
            config['config_type'] = 'conservative'
            config['matched_by'] = 'error_fallback'
            return config
    
    def _match_strategy_type(self, text: str) -> Optional[str]:
        """根据文本匹配策略类型"""
        for pattern, config_type in self.strategy_patterns.items():
            if pattern in text:
                return config_type
        return None
    
    def get_all_config_types(self) -> Dict[str, Dict[str, Any]]:
        """获取所有配置类型"""
        return self.default_configs.copy()
    
    def validate_config(self, config: Dict[str, Any]) -> bool:
        """验证配置参数的合理性"""
        try:
            # 检查必要字段
            required_fields = ['stop_loss_percent', 'take_profit_percent', 'max_positions', 'signal_threshold']
            for field in required_fields:
                if field not in config:
                    logger.error(f"❌ 配置缺少必要字段: {field}")
                    return False
            
            # 检查数值范围
            if not (0.001 <= config['stop_loss_percent'] <= 0.05):  # 0.1% - 5%
                logger.error(f"❌ 止损百分比超出合理范围: {config['stop_loss_percent']}")
                return False
            
            if not (0.002 <= config['take_profit_percent'] <= 0.10):  # 0.2% - 10%
                logger.error(f"❌ 止盈百分比超出合理范围: {config['take_profit_percent']}")
                return False
            
            if not (1 <= config['max_positions'] <= 10):
                logger.error(f"❌ 最大持仓数超出合理范围: {config['max_positions']}")
                return False
            
            if not (0.5 <= config['signal_threshold'] <= 1.0):
                logger.error(f"❌ 信号阈值超出合理范围: {config['signal_threshold']}")
                return False
            
            # 检查止盈是否大于止损
            if config['take_profit_percent'] <= config['stop_loss_percent']:
                logger.error(f"❌ 止盈百分比应大于止损百分比")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 验证配置失败: {e}")
            return False
    
    def create_custom_config(self, base_type: str, custom_params: Dict[str, Any]) -> Dict[str, Any]:
        """创建自定义配置"""
        try:
            if base_type not in self.default_configs:
                base_type = 'trend_following'
            
            # 从基础配置开始
            config = self.default_configs[base_type].copy()
            
            # 应用自定义参数
            for key, value in custom_params.items():
                if key in config:
                    config[key] = value
            
            # 验证配置
            if self.validate_config(config):
                config['config_type'] = f"custom_{base_type}"
                config['matched_by'] = 'custom'
                config['created_at'] = datetime.now().isoformat()
                logger.info(f"✅ 创建自定义配置成功: {base_type} + {custom_params}")
                return config
            else:
                logger.error(f"❌ 自定义配置验证失败")
                return self.default_configs[base_type].copy()
                
        except Exception as e:
            logger.error(f"❌ 创建自定义配置失败: {e}")
            return self.default_configs['conservative'].copy()

# 全局策略配置服务实例
strategy_config_service = StrategyConfigService()
