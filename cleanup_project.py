#!/usr/bin/env python3
"""
项目清理脚本 - 清理测试文件和临时文件
"""

import os
import shutil
import glob
from pathlib import Path

def get_files_to_clean():
    """获取需要清理的文件列表"""
    
    # 需要清理的文件模式
    patterns_to_clean = [
        # 测试文件
        'test_*.py',
        'test_*.html',
        'test_*.json',
        'test_*.csv',
        'test_*.png',
        'test_*.txt',
        'test_*.bat',
        
        # 临时文件和日志
        '*.log',
        'pattern_analysis.log',
        'network_diagnosis_*.txt',
        'comprehensive_data_test_*.csv',
        'mt5_test_results_*.csv',
        'proxy_test_results_*.csv',
        'xauusd_data_test_results_*.csv',
        'xauusd_mt5_data_*.csv',
        
        # 文档和说明文件（保留README.md）
        '*_SUMMARY.md',
        '*_FIX*.md',
        '*_GUIDE.md',
        '*_REPORT.md',
        '*_ANALYSIS*.md',
        '*_IMPLEMENTED.md',
        '*_COMPLETED.md',
        '*_UPDATE.md',
        '*_OPTIMIZATION*.md',
        '*_ENHANCEMENT*.md',
        '*_IMPROVEMENT*.md',
        '*_TROUBLESHOOTING.md',
        '*_SOLUTION.md',
        '*_FIXES*.md',
        '*_FEATURE*.md',
        '*_INTEGRATION*.md',
        '*_MIGRATION*.md',
        '*_DIAGNOSTIC*.md',
        '*_ERROR*.md',
        '*_ISSUE*.md',
        '*_CHANGELOG*.md',
        '*_UPGRADE*.md',
        '*_NOTES.md',
        
        # HTML测试和修复文件
        '*_fix_summary.html',
        '*_fixes_summary.html',
        '*_test_*.html',
        
        # 特定的临时文件
        'analyze_*.py',
        'debug_*.py',
        'diagnose_*.py',
        'fix_*.py',
        'check_*.py',
        'quick_*.py',
        'simple_*.py',
        'final_*.py',
        'force_*.py',
        'create_*_table.py',
        'migrate_*.py',
        'init_*_db.py',
        'add_*.py',
        'update_*.py',
        'merge_*.py',
        'restart_*.py',
        'configure_*.py',
        'find_*.py',
        'sync_*.py',
        'complete_*.py',
        'integrated_*.py',
        'balanced_*.py',
        'ultra_*.py',
        'smart_*.py',
        'standalone_*.py',
        'signal_*.py',
        'trade_*.py',
        'margin_*.py',
        'project_*.py',
        'windows_*.py',
        
        # 批处理文件
        '*.bat',
        
        # 配置和连接文件
        'mt4_*.py',
        'mt5_*.py',
        'config_*.json',
        'mt5_integration_test.json',
        '连接*.TXT',
        
        # 备份文件
        'trading_system_backup_*.db',
        'backup_*',
        
        # 其他临时文件
        'app_simple.py',
        'deploy.py',
        'new_gold_strategy.py',
        'pattern_recognition.py',
        'pattern_test_data.py',
        'real_pattern_analyzer.py',
        'requirements.txt'  # 如果有的话，保留主要的
    ]
    
    # 需要清理的目录
    directories_to_clean = [
        'pattern_analysis_results',
        'backup_mt4_*',
        '__pycache__'
    ]
    
    return patterns_to_clean, directories_to_clean

def should_preserve_file(file_path):
    """判断是否应该保留文件"""
    
    # 保留的重要文件
    preserve_files = [
        'README.md',
        'requirements.txt',
        'app.py',
        'routes.py',
        'models.py',
        'start.py',
        'init_database.py',
        'create_trades_table.py',  # 刚创建的重要文件
        'cleanup_project.py',  # 当前脚本
        'test_gpu_training.py'  # 刚创建的诊断脚本
    ]
    
    # 保留的重要目录
    preserve_dirs = [
        'templates',
        'static',
        'services',
        'config',
        'instance',
        'models',
        'deep_learning_models'
    ]
    
    file_name = os.path.basename(file_path)
    
    # 检查是否是保留文件
    if file_name in preserve_files:
        return True
    
    # 检查是否在保留目录中
    for preserve_dir in preserve_dirs:
        if preserve_dir in file_path:
            return True
    
    return False

def clean_project():
    """清理项目文件"""
    
    print("🧹 开始清理项目文件...")
    print("=" * 60)
    
    patterns_to_clean, directories_to_clean = get_files_to_clean()
    
    cleaned_files = []
    cleaned_dirs = []
    preserved_files = []
    
    # 清理文件
    print("📁 扫描需要清理的文件...")
    
    for pattern in patterns_to_clean:
        matching_files = glob.glob(pattern)
        
        for file_path in matching_files:
            if os.path.isfile(file_path):
                if should_preserve_file(file_path):
                    preserved_files.append(file_path)
                    print(f"  🔒 保留: {file_path}")
                else:
                    try:
                        os.remove(file_path)
                        cleaned_files.append(file_path)
                        print(f"  🗑️ 删除: {file_path}")
                    except Exception as e:
                        print(f"  ❌ 删除失败: {file_path} - {e}")
    
    # 清理目录
    print("\n📂 扫描需要清理的目录...")
    
    for dir_pattern in directories_to_clean:
        matching_dirs = glob.glob(dir_pattern)
        
        for dir_path in matching_dirs:
            if os.path.isdir(dir_path):
                if should_preserve_file(dir_path):
                    preserved_files.append(dir_path)
                    print(f"  🔒 保留目录: {dir_path}")
                else:
                    try:
                        shutil.rmtree(dir_path)
                        cleaned_dirs.append(dir_path)
                        print(f"  🗑️ 删除目录: {dir_path}")
                    except Exception as e:
                        print(f"  ❌ 删除目录失败: {dir_path} - {e}")
    
    # 清理空的 __pycache__ 目录
    print("\n🔍 清理 __pycache__ 目录...")
    for root, dirs, files in os.walk('.'):
        for dir_name in dirs:
            if dir_name == '__pycache__':
                pycache_path = os.path.join(root, dir_name)
                try:
                    shutil.rmtree(pycache_path)
                    cleaned_dirs.append(pycache_path)
                    print(f"  🗑️ 删除: {pycache_path}")
                except Exception as e:
                    print(f"  ❌ 删除失败: {pycache_path} - {e}")
    
    # 统计结果
    print("\n" + "=" * 60)
    print("📊 清理结果统计")
    print("=" * 60)
    
    print(f"✅ 删除文件: {len(cleaned_files)} 个")
    print(f"✅ 删除目录: {len(cleaned_dirs)} 个")
    print(f"🔒 保留文件: {len(preserved_files)} 个")
    
    if cleaned_files:
        print(f"\n📋 删除的文件 ({len(cleaned_files)} 个):")
        for i, file_path in enumerate(cleaned_files[:20], 1):  # 只显示前20个
            print(f"  {i:2d}. {file_path}")
        if len(cleaned_files) > 20:
            print(f"  ... 还有 {len(cleaned_files) - 20} 个文件")
    
    if cleaned_dirs:
        print(f"\n📋 删除的目录 ({len(cleaned_dirs)} 个):")
        for i, dir_path in enumerate(cleaned_dirs, 1):
            print(f"  {i:2d}. {dir_path}")
    
    # 显示剩余的重要文件
    print(f"\n📋 保留的重要文件结构:")
    important_files = [
        'app.py',
        'routes.py', 
        'models.py',
        'start.py',
        'README.md',
        'templates/',
        'services/',
        'static/',
        'instance/'
    ]
    
    for file_path in important_files:
        if os.path.exists(file_path):
            if os.path.isdir(file_path):
                file_count = len([f for f in os.listdir(file_path) if os.path.isfile(os.path.join(file_path, f))])
                print(f"  📂 {file_path} ({file_count} 个文件)")
            else:
                file_size = os.path.getsize(file_path) / 1024  # KB
                print(f"  📄 {file_path} ({file_size:.1f} KB)")
    
    print(f"\n🎉 项目清理完成！")
    print(f"💾 释放的空间: 约 {(len(cleaned_files) + len(cleaned_dirs)) * 10} KB")
    print(f"📁 项目结构更加整洁，便于维护和部署")

def confirm_cleanup():
    """确认是否执行清理"""
    
    print("⚠️  项目文件清理确认")
    print("=" * 60)
    print("此操作将删除以下类型的文件:")
    print("• 测试文件 (test_*.py, test_*.html 等)")
    print("• 临时文件和日志文件")
    print("• 文档和说明文件 (保留 README.md)")
    print("• 调试和修复脚本")
    print("• 备份文件和缓存目录")
    print("• 批处理文件和配置文件")
    print()
    print("保留的重要文件:")
    print("• 核心应用文件 (app.py, routes.py, models.py 等)")
    print("• 模板和静态文件 (templates/, static/)")
    print("• 服务模块 (services/)")
    print("• 数据库文件 (instance/)")
    print("• README.md 和其他重要配置")
    print()
    
    response = input("确认执行清理操作？(y/N): ").lower().strip()
    
    return response in ['y', 'yes', '是', '确认']

def main():
    """主函数"""
    
    print("🧹 MateTrade4 项目清理工具")
    print("=" * 60)
    
    if not confirm_cleanup():
        print("❌ 用户取消操作")
        return
    
    try:
        clean_project()
    except KeyboardInterrupt:
        print("\n❌ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 清理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
