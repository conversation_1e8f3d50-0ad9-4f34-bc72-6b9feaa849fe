{% extends "base.html" %}

{% block page_title %}MT5连接管理{% endblock %}

{% block extra_css %}
<style>
.connection-status {
    transition: all 0.3s ease;
}
</style>
{% endblock %}

{% block content %}
<div class="row">
    <!-- MT5连接状态 -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-plug"></i>
                    MT5连接状态
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-4">
                    <div class="connection-status" id="connectionStatus">
                        <i class="fas fa-circle text-danger fa-2x"></i>
                        <h5 class="mt-2">未连接</h5>
                        <p class="text-muted">请配置并连接到MT5终端</p>
                    </div>
                </div>

                <!-- 连接模式选择 -->
                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="connectionMode" id="autoMode" value="auto" checked>
                        <label class="form-check-label" for="autoMode">
                            <strong>自动检测连接</strong>
                            <small class="text-muted d-block">系统自动检测MT4/MT5并连接</small>
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="connectionMode" id="manualMode" value="manual">
                        <label class="form-check-label" for="manualMode">
                            <strong>手动配置连接</strong>
                            <small class="text-muted d-block">手动输入账户信息</small>
                        </label>
                    </div>
                </div>

                <form id="mt5ConnectionForm">
                    <!-- 手动配置区域 -->
                    <div id="manualConfig" style="display: none;">
                        <div class="mb-3">
                            <label class="form-label">账户号码</label>
                            <input type="number" class="form-control" id="loginNumber">
                        </div>

                        <div class="mb-3">
                            <label class="form-label">密码</label>
                            <input type="password" class="form-control" id="password">
                        </div>

                        <div class="mb-3">
                            <label class="form-label">服务器</label>
                            <select class="form-select" id="server">
                                <option value="">选择服务器</option>
                                <option value="Demo-Server">演示服务器</option>
                                <option value="MetaQuotes-Demo">MetaQuotes-Demo</option>
                                <option value="ICMarkets-Live01">ICMarkets-Live01</option>
                                <option value="ICMarkets-Demo">ICMarkets-Demo</option>
                                <option value="FXCM-USDDemo01">FXCM-USDDemo01</option>
                                <option value="FXCM-Real">FXCM-Real</option>
                            </select>
                        </div>
                    </div>

                    <!-- 自动检测说明 -->
                    <div id="autoConfig">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <strong>自动检测模式</strong><br>
                            系统将自动检测您的MT4/MT5安装并尝试连接：
                            <ul class="mb-0 mt-2">
                                <li>检测EC Markets MT4终端</li>
                                <li>如果MT4可用且已配置，将连接真实MT4</li>
                                <li>如果MT4不可用，将启用演示模式</li>
                                <li>无需填写任何账户信息即可测试</li>
                            </ul>
                        </div>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary" id="connectBtn">
                            <i class="fas fa-plug"></i>
                            <span id="connectBtnText">自动检测并连接</span>
                        </button>
                        <button type="button" class="btn btn-outline-danger" id="disconnectBtn"
                                onclick="disconnectMT5()" style="display: none;">
                            <i class="fas fa-unlink"></i>
                            断开连接
                        </button>
                        <button type="button" class="btn btn-warning" id="reconnectBtn"
                                onclick="forceReconnect()" style="display: none;">
                            <i class="fas fa-redo"></i>
                            强制重连
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 账户信息 -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user-circle"></i>
                    账户信息
                </h5>
            </div>
            <div class="card-body">
                <!-- 连接状态监控 -->
                <div id="connectionMonitor" style="display: none;">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-heartbeat"></i> 连接监控状态</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <small class="text-muted">监控状态:</small>
                                <span id="monitorStatus" class="badge bg-secondary">未知</span>
                            </div>
                            <div class="col-md-6">
                                <small class="text-muted">重连尝试:</small>
                                <span id="reconnectAttempts">0/5</span>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-md-6">
                                <small class="text-muted">最后连接:</small>
                                <span id="lastConnection">-</span>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="autoReconnectSwitch" checked>
                                    <label class="form-check-label" for="autoReconnectSwitch">
                                        <small>自动重连</small>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="accountInfo" style="display: none;">
                    <div class="row">
                        <div class="col-6 mb-3">
                            <label class="text-muted">账户号码</label>
                            <div class="fw-bold" id="accountLogin">-</div>
                        </div>
                        <div class="col-6 mb-3">
                            <label class="text-muted">交易模式</label>
                            <div class="fw-bold" id="tradeMode">-</div>
                        </div>
                        <div class="col-6 mb-3">
                            <label class="text-muted">余额</label>
                            <div class="fw-bold profit-positive" id="accountBalance">-</div>
                        </div>
                        <div class="col-6 mb-3">
                            <label class="text-muted">净值</label>
                            <div class="fw-bold" id="accountEquity">-</div>
                        </div>
                        <div class="col-6 mb-3">
                            <label class="text-muted">已用保证金</label>
                            <div class="fw-bold" id="accountMargin">-</div>
                        </div>
                        <div class="col-6 mb-3">
                            <label class="text-muted">可用保证金</label>
                            <div class="fw-bold" id="freeMargin">-</div>
                        </div>
                        <div class="col-6 mb-3">
                            <label class="text-muted">保证金比例</label>
                            <div class="fw-bold" id="marginLevel">-</div>
                        </div>
                        <div class="col-6 mb-3">
                            <label class="text-muted">浮动盈亏</label>
                            <div class="fw-bold" id="accountProfit">-</div>
                        </div>
                        <div class="col-12 mb-3">
                            <label class="text-muted">经纪商</label>
                            <div class="fw-bold" id="company">-</div>
                        </div>
                        <div class="col-12">
                            <label class="text-muted">服务器</label>
                            <div class="fw-bold" id="serverName">-</div>
                        </div>
                    </div>
                </div>

                <div id="noAccountInfo" class="text-center text-muted">
                    <i class="fas fa-info-circle fa-2x mb-2"></i>
                    <p>请先连接MT5以查看账户信息</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 交易品种和持仓信息 -->
<div class="row">
    <!-- 可用交易品种 -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list"></i>
                    可用交易品种
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <input type="text" class="form-control" id="symbolFilter"
                           placeholder="搜索交易品种...">
                </div>

                <div id="symbolsList" style="max-height: 400px; overflow-y: auto;">
                    <div class="text-center text-muted">
                        <i class="fas fa-search fa-2x mb-2"></i>
                        <p>请先连接MT5以获取交易品种</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 当前持仓 -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-coins"></i>
                    当前持仓
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm" id="positionsTable">
                        <thead>
                            <tr>
                                <th>品种</th>
                                <th>类型</th>
                                <th>手数</th>
                                <th>开仓价</th>
                                <th>当前价</th>
                                <th>盈亏</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="7" class="text-center text-muted">暂无持仓</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 快速交易面板 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt"></i>
                    快速交易
                </h5>
            </div>
            <div class="card-body">
                <form id="quickTradeForm" class="row g-3">
                    <div class="col-md-2">
                        <label class="form-label">交易品种</label>
                        <select class="form-select" id="tradeSymbol" required>
                            <option value="">选择品种</option>
                        </select>
                    </div>

                    <div class="col-md-2">
                        <label class="form-label">交易类型</label>
                        <select class="form-select" id="tradeType" required>
                            <option value="buy">买入</option>
                            <option value="sell">卖出</option>
                        </select>
                    </div>

                    <div class="col-md-2">
                        <label class="form-label">手数</label>
                        <input type="number" class="form-control" id="tradeVolume"
                               value="0.01" min="0.01" step="0.01" required>
                    </div>

                    <div class="col-md-2">
                        <label class="form-label">止损</label>
                        <input type="number" class="form-control" id="tradeStopLoss"
                               step="0.00001" placeholder="可选">
                    </div>

                    <div class="col-md-2">
                        <label class="form-label">止盈</label>
                        <input type="number" class="form-control" id="tradeTakeProfit"
                               step="0.00001" placeholder="可选">
                    </div>

                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <button type="submit" class="btn btn-success w-100">
                            <i class="fas fa-paper-plane"></i>
                            下单
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- 引入全局状态管理器 -->
<script src="{{ url_for('static', filename='js/global_state_manager.js') }}"></script>

<script>
let isConnected = false;
let accountData = null;

// 全局错误捕获
window.addEventListener('error', function(e) {
    console.error('🚨 JavaScript错误:', e.error);
    console.error('🚨 错误位置:', e.filename, ':', e.lineno, ':', e.colno);
});

// 未处理的Promise错误捕获
window.addEventListener('unhandledrejection', function(e) {
    console.error('🚨 未处理的Promise错误:', e.reason);
});

// 页面加载完成后检查连接状态
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔄 MT5连接页面加载完成');

    // 等待全局状态管理器加载
    setTimeout(() => {
        if (window.GlobalStateManager) {
            console.log('✅ 全局状态管理器已加载');
            restoreMT5ConnectionState();
        } else {
            console.warn('⚠️ 全局状态管理器未加载，跳过状态恢复');
        }

        checkConnectionStatus();
        setupEventListeners();

        // 设置自动重连开关事件监听
        const autoReconnectSwitch = document.getElementById('autoReconnectSwitch');
        if (autoReconnectSwitch) {
            autoReconnectSwitch.addEventListener('change', toggleAutoReconnect);
        }

        console.log('✅ MT5连接页面初始化完成');
    }, 100);
});

// 恢复MT5连接状态
function restoreMT5ConnectionState() {
    if (!window.GlobalStateManager) {
        console.warn('全局状态管理器未加载');
        return;
    }

    try {
        const mt5State = window.GlobalStateManager.getMT5ConnectionState();
        console.log('恢复MT5连接状态:', mt5State);

        if (mt5State && mt5State.connected && mt5State.accountInfo) {
            // 恢复连接状态显示
            updateConnectionStatus(true, mt5State.accountInfo);
            updateAccountInfo(mt5State.accountInfo);
            console.log('✅ MT5连接状态已恢复');
        } else {
            console.log('ℹ️ 没有需要恢复的MT5连接状态');
        }
    } catch (error) {
        console.error('❌ 恢复MT5连接状态失败:', error);
    }
}

// 保存MT5连接状态
function saveMT5ConnectionState(connected, accountInfo = null) {
    if (window.GlobalStateManager) {
        try {
            window.GlobalStateManager.setMT5Connected(connected, accountInfo);
            console.log('✅ MT5连接状态已保存:', { connected, accountInfo });
        } catch (error) {
            console.error('❌ 保存MT5连接状态失败:', error);
        }
    } else {
        console.warn('⚠️ 全局状态管理器不可用，无法保存状态');
    }
}

// 强制重连
function forceReconnect() {
    const button = document.getElementById('reconnectBtn');
    const originalText = button.innerHTML;

    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 重连中...';
    button.disabled = true;

    fetch('/api/mt5/force-reconnect', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('已触发强制重连，请稍候...', 'info');
            // 延迟检查连接状态
            setTimeout(() => {
                checkConnectionStatus();
            }, 3000);
        } else {
            showNotification('重连失败: ' + data.error, 'error');
        }
    })
    .catch(error => {
        console.error('重连请求失败:', error);
        showNotification('重连请求失败', 'error');
    })
    .finally(() => {
        button.innerHTML = originalText;
        button.disabled = false;
    });
}

// 切换自动重连
function toggleAutoReconnect() {
    const switchElement = document.getElementById('autoReconnectSwitch');
    const enabled = switchElement.checked;

    fetch('/api/mt5/auto-reconnect', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ enabled: enabled })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
        } else {
            showNotification('设置失败: ' + data.error, 'error');
            // 恢复开关状态
            switchElement.checked = !enabled;
        }
    })
    .catch(error => {
        console.error('设置自动重连失败:', error);
        showNotification('设置失败', 'error');
        switchElement.checked = !enabled;
    });
}

// 更新连接监控状态
function updateConnectionMonitor(status) {
    const monitorDiv = document.getElementById('connectionMonitor');
    const monitorStatus = document.getElementById('monitorStatus');
    const reconnectAttempts = document.getElementById('reconnectAttempts');
    const lastConnection = document.getElementById('lastConnection');
    const autoReconnectSwitch = document.getElementById('autoReconnectSwitch');

    if (status && status.reconnect_service_active) {
        monitorDiv.style.display = 'block';

        // 更新监控状态
        if (status.monitoring) {
            monitorStatus.textContent = '监控中';
            monitorStatus.className = 'badge bg-success';
        } else {
            monitorStatus.textContent = '未监控';
            monitorStatus.className = 'badge bg-secondary';
        }

        // 更新重连尝试次数
        reconnectAttempts.textContent = `${status.reconnect_attempts || 0}/${status.max_attempts || 5}`;

        // 更新最后连接时间
        if (status.last_successful_connection) {
            const date = new Date(status.last_successful_connection);
            lastConnection.textContent = date.toLocaleString('zh-CN');
        } else {
            lastConnection.textContent = '-';
        }

        // 更新自动重连开关
        autoReconnectSwitch.checked = status.auto_reconnect_enabled !== false;
    } else {
        monitorDiv.style.display = 'none';
    }
}

// 定期检查连接状态
let connectionStatusInterval = null;

function startConnectionStatusMonitoring() {
    if (connectionStatusInterval) {
        clearInterval(connectionStatusInterval);
    }

    connectionStatusInterval = setInterval(() => {
        fetch('/api/mt5/connection-status')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateConnectionMonitor(data.status);

                // 如果连接断开，显示重连按钮
                const reconnectBtn = document.getElementById('reconnectBtn');
                if (!data.status.connected && isConnected) {
                    reconnectBtn.style.display = 'inline-block';
                    showNotification('检测到MT5连接断开', 'warning');
                } else if (data.status.connected && !isConnected) {
                    reconnectBtn.style.display = 'none';
                    showNotification('MT5连接已恢复', 'success');
                }

                // 更新全局连接状态
                isConnected = data.status.connected;
            }
        })
        .catch(error => {
            console.debug('连接状态检查失败:', error);
        });
    }, 15000); // 每15秒检查一次
}

function stopConnectionStatusMonitoring() {
    if (connectionStatusInterval) {
        clearInterval(connectionStatusInterval);
        connectionStatusInterval = null;
    }
}

// 显示通知消息
function showNotification(message, type = 'info') {
    // 尝试使用MateTrade4通知系统
    if (window.MateTrade4 && MateTrade4.notifications) {
        if (type === 'success') {
            MateTrade4.notifications.success(message);
        } else if (type === 'error') {
            MateTrade4.notifications.error(message);
        } else {
            MateTrade4.notifications.info(message);
        }
    } else {
        // 回退到浏览器alert
        alert(message);
    }
}

// 设置事件监听器
function setupEventListeners() {
    console.log('🔧 设置MT5连接页面事件监听器...');

    // MT5连接表单
    const mt5Form = document.getElementById('mt5ConnectionForm');
    if (mt5Form) {
        mt5Form.addEventListener('submit', function(e) {
            e.preventDefault();
            console.log('📝 MT5连接表单提交事件触发');
            connectMT5();
        });
        console.log('✅ MT5连接表单事件监听器已设置');
    } else {
        console.error('❌ 找不到MT5连接表单元素');
    }

    // 连接按钮直接点击事件（备用）
    const connectBtn = document.getElementById('connectBtn');
    if (connectBtn) {
        connectBtn.addEventListener('click', function(e) {
            console.log('🔘 连接按钮点击事件触发');
            // 如果按钮类型不是submit，直接调用连接函数
            if (connectBtn.type !== 'submit') {
                e.preventDefault();
                connectMT5();
            }
        });
        console.log('✅ 连接按钮点击事件监听器已设置');
    } else {
        console.error('❌ 找不到连接按钮元素');
    }

    // 快速交易表单
    const quickTradeForm = document.getElementById('quickTradeForm');
    if (quickTradeForm) {
        quickTradeForm.addEventListener('submit', function(e) {
            e.preventDefault();
            sendQuickOrder();
        });
        console.log('✅ 快速交易表单事件监听器已设置');
    }

    // 品种搜索
    const symbolFilter = document.getElementById('symbolFilter');
    if (symbolFilter) {
        symbolFilter.addEventListener('input', function() {
            filterSymbols(this.value);
        });
        console.log('✅ 品种搜索事件监听器已设置');
    }

    // 连接模式切换
    const connectionModeRadios = document.querySelectorAll('input[name="connectionMode"]');
    if (connectionModeRadios.length > 0) {
        connectionModeRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                console.log('🔄 连接模式切换:', this.value);
                toggleConnectionMode(this.value);
            });
        });
        console.log('✅ 连接模式切换事件监听器已设置');
    } else {
        console.error('❌ 找不到连接模式选择元素');
    }

    console.log('🎉 所有事件监听器设置完成');

    // 测试按钮点击功能
    testButtonClick();
}

// 测试按钮点击功能
function testButtonClick() {
    const connectBtn = document.getElementById('connectBtn');
    if (connectBtn) {
        console.log('🧪 测试连接按钮状态:');
        console.log('   - 按钮存在:', !!connectBtn);
        console.log('   - 按钮类型:', connectBtn.type);
        console.log('   - 按钮禁用状态:', connectBtn.disabled);
        console.log('   - 按钮文本:', connectBtn.textContent.trim());

        // 添加临时测试点击事件
        connectBtn.addEventListener('click', function(e) {
            console.log('🧪 测试点击事件触发 - 事件类型:', e.type);
            console.log('🧪 测试点击事件触发 - 目标元素:', e.target.tagName);
        });
    } else {
        console.error('❌ 测试失败: 找不到连接按钮');
    }
}

// 切换连接模式
function toggleConnectionMode(mode) {
    const manualConfig = document.getElementById('manualConfig');
    const autoConfig = document.getElementById('autoConfig');
    const connectBtnText = document.getElementById('connectBtnText');

    if (mode === 'manual') {
        manualConfig.style.display = 'block';
        autoConfig.style.display = 'none';
        connectBtnText.textContent = '连接MT5';

        // 恢复必填验证
        document.getElementById('loginNumber').required = true;
        document.getElementById('password').required = true;
        document.getElementById('server').required = true;
    } else {
        manualConfig.style.display = 'none';
        autoConfig.style.display = 'block';
        connectBtnText.textContent = '自动检测并连接';

        // 移除必填验证
        document.getElementById('loginNumber').required = false;
        document.getElementById('password').required = false;
        document.getElementById('server').required = false;
    }
}

// 检查连接状态
function checkConnectionStatus() {
    // 这里可以添加检查MT5连接状态的API调用
    updateConnectionStatus(false);
}

// 连接MT5
function connectMT5() {
    try {
        console.log('🔗 connectMT5函数被调用');
        console.log('🔗 开始连接MT5...');

        const connectionModeElement = document.querySelector('input[name="connectionMode"]:checked');
        if (!connectionModeElement) {
            console.error('❌ 找不到选中的连接模式');
            showNotification('请选择连接模式', 'error');
            return;
        }

        const connectionMode = connectionModeElement.value;
        console.log('连接模式:', connectionMode);

    let requestData = {};

    if (connectionMode === 'manual') {
        const loginNumber = document.getElementById('loginNumber').value;
        const password = document.getElementById('password').value;
        const server = document.getElementById('server').value;

        console.log('手动连接参数:', { loginNumber, server });

        if (!loginNumber || !password || !server) {
            showNotification('请填写完整的连接信息', 'error');
            return;
        }

        requestData = {
            login: parseInt(loginNumber),
            password: password,
            server: server
        };
    } else {
        // 自动检测模式，发送空数据让后端自动检测
        console.log('自动检测模式');
        requestData = {};
    }

    const connectBtn = document.getElementById('connectBtn');
    const originalText = connectBtn.innerHTML;
    connectBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> ' + (connectionMode === 'auto' ? '检测中...' : '连接中...');
    connectBtn.disabled = true;

    console.log('发送连接请求:', requestData);

    fetch('/api/mt5/connect', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
    })
    .then(response => {
        console.log('连接响应状态:', response.status);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('连接响应数据:', data);

        if (data.success) {
            console.log('✅ MT5连接成功');
            updateConnectionStatus(true, data.account_info);
            updateAccountInfo(data.account_info);
            loadSymbols();
            loadPositions();

            // 保存MT5连接状态
            saveMT5ConnectionState(true, data.account_info);

            // 启动连接状态监控
            startConnectionStatusMonitoring();

            // 根据连接类型显示不同的成功消息
            if (data.account_info && data.account_info.mt4_mode) {
                showNotification('🎉 成功连接到MT4终端！', 'success');
            } else if (data.account_info && data.account_info.demo_mode) {
                showNotification('✅ 演示模式已启用 (MT4未配置或不可用)', 'success');
            } else {
                showNotification('✅ MT5连接成功！', 'success');
            }
        } else {
            console.error('❌ MT5连接失败:', data.error);
            showNotification('连接失败: ' + data.error, 'error');
            // 保存失败状态
            saveMT5ConnectionState(false, null);
        }
    })
    .catch(error => {
        console.error('❌ 连接请求异常:', error);
        showNotification('连接请求失败: ' + error.message, 'error');
        // 保存失败状态
        saveMT5ConnectionState(false, null);
    })
    .finally(() => {
        console.log('连接请求完成，恢复按钮状态');
        connectBtn.innerHTML = originalText;
        connectBtn.disabled = false;
    });

    } catch (error) {
        console.error('❌ connectMT5函数执行异常:', error);
        showNotification('连接函数执行失败: ' + error.message, 'error');
    }
}

// 断开MT5连接
function disconnectMT5() {
    fetch('/api/mt5/disconnect', {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateConnectionStatus(false);
            clearAccountInfo();
            clearSymbols();
            clearPositions();

            // 停止连接状态监控
            stopConnectionStatusMonitoring();

            // 保存断开连接状态
            saveMT5ConnectionState(false, null);

            MateTrade4.notifications.success('MT5连接已断开');
        } else {
            MateTrade4.notifications.error('断开连接失败: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        MateTrade4.notifications.error('断开连接请求失败');
    });
}

// 更新连接状态
function updateConnectionStatus(connected, accountInfo = null) {
    isConnected = connected;
    const statusDiv = document.getElementById('connectionStatus');
    const connectBtn = document.getElementById('connectBtn');
    const disconnectBtn = document.getElementById('disconnectBtn');

    if (connected) {
        let connectionType = 'MT5终端';
        let statusClass = 'text-success';
        let statusIcon = 'fas fa-circle';

        if (accountInfo) {
            if (accountInfo.mt4_mode) {
                connectionType = 'MT4终端 (EC Markets)';
                statusIcon = 'fas fa-check-circle';
            } else if (accountInfo.demo_mode) {
                connectionType = '演示模式';
                statusClass = 'text-warning';
                statusIcon = 'fas fa-play-circle';
            }
        }

        statusDiv.innerHTML = `
            <i class="${statusIcon} ${statusClass} fa-2x"></i>
            <h5 class="mt-2 ${statusClass}">已连接</h5>
            <p class="text-muted">${connectionType}连接正常</p>
        `;
        connectBtn.style.display = 'none';
        disconnectBtn.style.display = 'block';
    } else {
        statusDiv.innerHTML = `
            <i class="fas fa-circle text-danger fa-2x"></i>
            <h5 class="mt-2">未连接</h5>
            <p class="text-muted">请点击连接按钮开始</p>
        `;
        connectBtn.style.display = 'block';
        disconnectBtn.style.display = 'none';
    }
}

// 更新账户信息
function updateAccountInfo(accountInfo) {
    if (!accountInfo || accountInfo.error) {
        return;
    }

    accountData = accountInfo;

    document.getElementById('accountLogin').textContent = accountInfo.login || '-';
    document.getElementById('tradeMode').textContent = accountInfo.trade_mode || '-';
    document.getElementById('accountBalance').textContent = MateTrade4.utils.formatCurrency(accountInfo.balance);
    document.getElementById('accountEquity').textContent = MateTrade4.utils.formatCurrency(accountInfo.equity);
    document.getElementById('accountMargin').textContent = MateTrade4.utils.formatCurrency(accountInfo.margin);
    document.getElementById('freeMargin').textContent = MateTrade4.utils.formatCurrency(accountInfo.free_margin);
    document.getElementById('marginLevel').textContent = accountInfo.margin_level ? accountInfo.margin_level.toFixed(2) + '%' : '-';
    document.getElementById('accountProfit').textContent = MateTrade4.utils.formatCurrency(accountInfo.profit);
    document.getElementById('company').textContent = accountInfo.company || '-';
    document.getElementById('serverName').textContent = accountInfo.server || '-';

    // 设置盈亏颜色
    const profitElement = document.getElementById('accountProfit');
    profitElement.className = 'fw-bold ' + MateTrade4.utils.getProfitClass(accountInfo.profit);

    document.getElementById('accountInfo').style.display = 'block';
    document.getElementById('noAccountInfo').style.display = 'none';
}

// 清除账户信息
function clearAccountInfo() {
    accountData = null;
    document.getElementById('accountInfo').style.display = 'none';
    document.getElementById('noAccountInfo').style.display = 'block';
}

// 加载交易品种
function loadSymbols() {
    fetch('/api/mt5/symbols')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displaySymbols(data.symbols);
            populateTradeSymbols(data.symbols);
        } else {
            MateTrade4.notifications.error('获取交易品种失败: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        MateTrade4.notifications.error('获取交易品种请求失败');
    });
}

// 显示交易品种
function displaySymbols(symbols) {
    const symbolsList = document.getElementById('symbolsList');
    symbolsList.innerHTML = '';

    symbols.forEach(symbol => {
        // 处理symbol对象，提取name和description
        const symbolName = symbol.name || symbol;
        const symbolDesc = symbol.description || '';

        const div = document.createElement('div');
        div.className = 'symbol-item p-2 border-bottom';
        div.innerHTML = `
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <span class="fw-bold">${symbolName}</span>
                    ${symbolDesc ? `<br><small class="text-muted">${symbolDesc}</small>` : ''}
                </div>
                <button class="btn btn-sm btn-outline-primary" onclick="viewSymbolChart('${symbolName}')">
                    <i class="fas fa-chart-line"></i>
                </button>
            </div>
        `;
        symbolsList.appendChild(div);
    });
}

// 填充交易品种下拉框
function populateTradeSymbols(symbols) {
    const select = document.getElementById('tradeSymbol');
    select.innerHTML = '<option value="">选择品种</option>';

    symbols.forEach(symbol => {
        // 处理symbol对象，提取name
        const symbolName = symbol.name || symbol;
        const symbolDesc = symbol.description || '';

        const option = document.createElement('option');
        option.value = symbolName;
        option.textContent = symbolDesc ? `${symbolName} - ${symbolDesc}` : symbolName;
        select.appendChild(option);
    });
}

// 清除交易品种
function clearSymbols() {
    document.getElementById('symbolsList').innerHTML = `
        <div class="text-center text-muted">
            <i class="fas fa-search fa-2x mb-2"></i>
            <p>请先连接MT5以获取交易品种</p>
        </div>
    `;

    document.getElementById('tradeSymbol').innerHTML = '<option value="">选择品种</option>';
}

// 过滤交易品种
function filterSymbols(query) {
    const items = document.querySelectorAll('.symbol-item');
    items.forEach(item => {
        const symbol = item.querySelector('.fw-bold').textContent;
        if (symbol.toLowerCase().includes(query.toLowerCase())) {
            item.style.display = 'block';
        } else {
            item.style.display = 'none';
        }
    });
}

// 查看品种图表
function viewSymbolChart(symbol) {
    // 跳转到图表页面
    window.open(`/charts?symbol=${symbol}`, '_blank');
}

// 加载持仓
function loadPositions() {
    fetch('/api/mt5/positions')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayPositions(data.positions);
        } else {
            MateTrade4.notifications.error('获取持仓失败: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        MateTrade4.notifications.error('获取持仓请求失败');
    });
}

// 显示持仓
function displayPositions(positions) {
    const tbody = document.querySelector('#positionsTable tbody');
    tbody.innerHTML = '';

    if (positions.length === 0) {
        tbody.innerHTML = '<tr><td colspan="7" class="text-center text-muted">暂无持仓</td></tr>';
        return;
    }

    positions.forEach(position => {
        const row = `
            <tr>
                <td>${position.symbol}</td>
                <td><span class="badge bg-${position.type === '买入' ? 'success' : 'danger'}">${position.type}</span></td>
                <td>${position.volume}</td>
                <td>${position.price_open.toFixed(5)}</td>
                <td>${position.price_current.toFixed(5)}</td>
                <td class="${MateTrade4.utils.getProfitClass(position.profit)}">
                    ${MateTrade4.utils.formatCurrency(position.profit)}
                </td>
                <td>
                    <button class="btn btn-sm btn-outline-danger" onclick="closePosition(${position.ticket})">
                        平仓
                    </button>
                </td>
            </tr>
        `;
        tbody.innerHTML += row;
    });
}

// 清除持仓
function clearPositions() {
    const tbody = document.querySelector('#positionsTable tbody');
    tbody.innerHTML = '<tr><td colspan="7" class="text-center text-muted">暂无持仓</td></tr>';
}

// 平仓
function closePosition(ticket) {
    if (confirm('确定要平仓吗？')) {
        fetch(`/api/mt5/close-position/${ticket}`, {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                MateTrade4.notifications.success('平仓成功！');
                loadPositions();
                // 刷新账户信息
                if (accountData) {
                    // 这里可以重新获取账户信息
                }
            } else {
                MateTrade4.notifications.error('平仓失败: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            MateTrade4.notifications.error('平仓请求失败');
        });
    }
}

// 发送快速订单
function sendQuickOrder() {
    if (!isConnected) {
        MateTrade4.notifications.error('请先连接MT5');
        return;
    }

    const symbol = document.getElementById('tradeSymbol').value;
    const type = document.getElementById('tradeType').value;
    const volume = parseFloat(document.getElementById('tradeVolume').value);
    const stopLoss = document.getElementById('tradeStopLoss').value;
    const takeProfit = document.getElementById('tradeTakeProfit').value;

    if (!symbol || !type || !volume) {
        MateTrade4.notifications.error('请填写完整的交易信息');
        return;
    }

    const orderData = {
        symbol: symbol,
        type: type,
        volume: volume,
        sl: stopLoss ? parseFloat(stopLoss) : 0,
        tp: takeProfit ? parseFloat(takeProfit) : 0,
        comment: 'Quick trade from web'
    };

    fetch('/api/mt5/send-order', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(orderData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            MateTrade4.notifications.success('订单发送成功！');
            document.getElementById('quickTradeForm').reset();
            loadPositions();
        } else {
            MateTrade4.notifications.error('订单发送失败: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        MateTrade4.notifications.error('订单发送请求失败');
    });
}

// 定时刷新数据
setInterval(function() {
    if (isConnected) {
        loadPositions();
        // 可以添加更多需要定时刷新的数据
    }
}, 30000); // 每30秒刷新一次
</script>
{% endblock %}
