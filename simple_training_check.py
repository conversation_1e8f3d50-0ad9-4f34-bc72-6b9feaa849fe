#!/usr/bin/env python3
"""
简单的训练状态检查和修复
"""

import sqlite3
import sys
from datetime import datetime, timedelta

def main():
    print("🔍 AI推理模型训练状态检查")
    print("=" * 60)
    
    try:
        # 连接数据库
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 检查训练任务表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='training_tasks'")
        if not cursor.fetchone():
            print("❌ 训练任务表不存在")
            conn.close()
            return
        
        print("✅ 数据库连接成功")
        
        # 检查所有训练任务
        cursor.execute("""
            SELECT id, name, symbol, timeframe, status, progress, current_epoch, total_epochs, 
                   created_at, updated_at
            FROM training_tasks 
            ORDER BY updated_at DESC 
            LIMIT 10
        """)
        
        all_tasks = cursor.fetchall()
        
        if not all_tasks:
            print("📊 没有找到任何训练任务")
            conn.close()
            return
        
        print(f"📊 找到 {len(all_tasks)} 个训练任务:")
        print()
        
        running_tasks = []
        stuck_tasks = []
        
        for i, task in enumerate(all_tasks, 1):
            task_id, name, symbol, timeframe, status, progress, epoch, total_epochs, created_at, updated_at = task
            
            print(f"{i}. 任务: {name}")
            print(f"   ID: {task_id}")
            print(f"   品种: {symbol} {timeframe}")
            print(f"   状态: {status}")
            print(f"   进度: {progress}%")
            print(f"   轮次: {epoch}/{total_epochs}")
            print(f"   创建: {created_at}")
            print(f"   更新: {updated_at}")
            
            # 检查运行状态
            if status in ['running', 'pending']:
                running_tasks.append(task_id)
                
                # 检查是否卡住
                if updated_at:
                    try:
                        last_update = datetime.fromisoformat(updated_at.replace('Z', '+00:00').replace('+00:00', ''))
                        now = datetime.now()
                        time_diff = now - last_update
                        
                        if time_diff > timedelta(minutes=5):
                            print(f"   ⚠️ 可能卡住 (已 {time_diff} 没有更新)")
                            stuck_tasks.append(task_id)
                        else:
                            print(f"   ✅ 正常 (最后更新: {time_diff} 前)")
                    except Exception as e:
                        print(f"   ❓ 时间解析错误: {e}")
                        stuck_tasks.append(task_id)
            
            print("-" * 40)
        
        # 处理卡住的任务
        if stuck_tasks:
            print(f"\n⚠️ 发现 {len(stuck_tasks)} 个可能卡住的任务")
            
            choice = input("是否要重置这些任务? (y/n): ").lower().strip()
            
            if choice == 'y':
                print("🔄 重置卡住的任务...")
                
                for task_id in stuck_tasks:
                    cursor.execute("""
                        UPDATE training_tasks
                        SET status = 'pending',
                            progress = 0,
                            current_epoch = 0,
                            train_loss = 0,
                            val_loss = 0,
                            updated_at = ?
                        WHERE id = ?
                    """, (datetime.now().isoformat(), task_id))
                    
                    print(f"   ✅ 重置任务: {task_id[:8]}...")
                
                conn.commit()
                print(f"✅ 已重置 {len(stuck_tasks)} 个任务")
                print("💡 请在前端页面重新开始训练")
            else:
                print("⏭️ 跳过重置")
        
        elif running_tasks:
            print(f"\n✅ 发现 {len(running_tasks)} 个正常运行的任务")
            print("💡 训练正在正常进行")
        
        else:
            print("\n✅ 当前没有活跃的训练任务")
            print("💡 可以在前端页面开始新的训练")
        
        # 显示最近的模型
        print(f"\n📋 最近的深度学习模型:")
        cursor.execute("""
            SELECT id, name, symbol, timeframe, status, created_at
            FROM deep_learning_models 
            ORDER BY created_at DESC 
            LIMIT 5
        """)
        
        models = cursor.fetchall()
        for model in models:
            model_id, name, symbol, timeframe, status, created_at = model
            print(f"   {name} | {symbol} {timeframe} | {status} | {created_at}")
        
        conn.close()
        
        print(f"\n🎯 检查完成")
        print("=" * 60)
        print("💡 如果训练仍然卡住:")
        print("1. 重启应用程序: python app.py")
        print("2. 在前端页面重新开始训练")
        print("3. 监控训练进度是否正常更新")
        print("4. 如果仍有问题，考虑减少模型复杂度")
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
