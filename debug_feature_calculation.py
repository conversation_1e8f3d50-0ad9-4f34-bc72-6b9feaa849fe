#!/usr/bin/env python3
"""
调试特征计算问题
"""

import numpy as np
import MetaTrader5 as mt5
from datetime import datetime, <PERSON><PERSON><PERSON>

def debug_mt5_data_structure():
    """调试MT5数据结构"""
    
    print("🔍 调试MT5数据结构")
    print("=" * 60)
    
    try:
        # 初始化MT5
        if not mt5.initialize():
            print("❌ MT5初始化失败")
            return None
        
        print("✅ MT5初始化成功")
        
        # 获取少量数据进行分析
        rates = mt5.copy_rates_from_pos("XAUUSD", mt5.TIMEFRAME_H1, 0, 10)
        
        if rates is None:
            print("❌ 无法获取数据")
            mt5.shutdown()
            return None
        
        print(f"📊 获取到 {len(rates)} 条数据")
        print(f"数据类型: {type(rates)}")
        print(f"数据形状: {rates.shape if hasattr(rates, 'shape') else 'N/A'}")
        print(f"数据dtype: {rates.dtype if hasattr(rates, 'dtype') else 'N/A'}")
        
        # 显示数据结构
        if len(rates) > 0:
            print(f"\n📋 第一条记录:")
            first_record = rates[0]
            print(f"   类型: {type(first_record)}")
            print(f"   内容: {first_record}")
            
            # 如果是结构化数组，显示字段名
            if hasattr(rates, 'dtype') and rates.dtype.names:
                print(f"\n📝 字段名: {rates.dtype.names}")
                for i, field in enumerate(rates.dtype.names):
                    print(f"   {i}: {field} = {first_record[field]}")
            
            # 尝试不同的索引方式
            print(f"\n🧪 测试索引方式:")
            
            try:
                # 方式1: 直接索引
                print(f"   rates[0]: {rates[0]}")
            except Exception as e:
                print(f"   ❌ rates[0] 失败: {e}")
            
            try:
                # 方式2: 字段索引
                if hasattr(rates, 'dtype') and rates.dtype.names:
                    for field in ['time', 'open', 'high', 'low', 'close', 'tick_volume', 'spread', 'real_volume']:
                        if field in rates.dtype.names:
                            print(f"   rates['{field}'][0]: {rates[field][0]}")
            except Exception as e:
                print(f"   ❌ 字段索引失败: {e}")
            
            try:
                # 方式3: 转换为普通数组
                if hasattr(rates, 'dtype') and rates.dtype.names:
                    # 提取OHLCV数据
                    ohlcv_data = np.column_stack([
                        rates['open'],
                        rates['high'], 
                        rates['low'],
                        rates['close'],
                        rates['tick_volume'] if 'tick_volume' in rates.dtype.names else rates['real_volume']
                    ])
                    print(f"   转换后形状: {ohlcv_data.shape}")
                    print(f"   转换后类型: {ohlcv_data.dtype}")
                    print(f"   第一行: {ohlcv_data[0]}")
            except Exception as e:
                print(f"   ❌ 数组转换失败: {e}")
        
        mt5.shutdown()
        return rates
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        if 'mt5' in locals():
            mt5.shutdown()
        return None

def test_feature_calculation_logic():
    """测试特征计算逻辑"""
    
    print(f"\n🧪 测试特征计算逻辑")
    print("=" * 60)
    
    # 创建模拟的OHLCV数据
    np.random.seed(42)
    n_samples = 100
    
    # 模拟价格数据 [open, high, low, close, volume]
    base_price = 2650.0
    price_changes = np.random.normal(0, 0.01, n_samples)
    
    prices = [base_price]
    for change in price_changes:
        new_price = prices[-1] * (1 + change)
        prices.append(new_price)
    
    prices = np.array(prices[1:])
    
    # 生成OHLCV数据
    ohlcv_data = []
    for i, close in enumerate(prices):
        if i == 0:
            open_price = base_price
        else:
            open_price = prices[i-1]
        
        volatility = abs(np.random.normal(0, 0.005))
        high = max(open_price, close) * (1 + volatility)
        low = min(open_price, close) * (1 - volatility)
        volume = np.random.randint(100, 1000)
        
        ohlcv_data.append([open_price, high, low, close, volume])
    
    price_data = np.array(ohlcv_data)
    
    print(f"📊 模拟数据:")
    print(f"   形状: {price_data.shape}")
    print(f"   类型: {price_data.dtype}")
    print(f"   前3行:\n{price_data[:3]}")
    
    # 测试特征提取
    feature_list = ['close', 'volume']
    
    print(f"\n🔧 测试特征提取:")
    
    try:
        # 价格数据映射 [open, high, low, close, volume]
        price_map = {
            'open': 0,
            'high': 1, 
            'low': 2,
            'close': 3,
            'volume': 4
        }
        
        features = []
        
        for feature_name in feature_list:
            if feature_name in price_map:
                col_idx = price_map[feature_name]
                print(f"   提取特征 {feature_name} (列 {col_idx})")
                
                if col_idx < price_data.shape[1]:
                    feature_data = price_data[:, col_idx]
                    print(f"     原始数据形状: {feature_data.shape}")
                    print(f"     前5个值: {feature_data[:5]}")
                    
                    # 标准化
                    if feature_data.std() > 1e-10:
                        normalized_feature = (feature_data - feature_data.mean()) / feature_data.std()
                    else:
                        normalized_feature = feature_data - feature_data.mean()
                    
                    print(f"     标准化后形状: {normalized_feature.shape}")
                    print(f"     标准化后前5个值: {normalized_feature[:5]}")
                    
                    features.append(normalized_feature.reshape(-1, 1))
                    print(f"     ✅ 特征 {feature_name} 提取成功")
                else:
                    print(f"     ❌ 列索引 {col_idx} 超出范围")
            else:
                print(f"   ❌ 不支持的特征: {feature_name}")
        
        if features:
            result = np.concatenate(features, axis=1)
            print(f"\n✅ 特征合并成功:")
            print(f"   最终形状: {result.shape}")
            print(f"   最终类型: {result.dtype}")
            print(f"   前3行:\n{result[:3]}")
            return True
        else:
            print(f"\n❌ 没有成功提取任何特征")
            return False
            
    except Exception as e:
        print(f"❌ 特征计算失败: {e}")
        import traceback
        print(f"详细错误:\n{traceback.format_exc()}")
        return False

def main():
    """主函数"""
    
    print("🔧 特征计算问题调试")
    print("=" * 80)
    
    # 调试MT5数据结构
    mt5_data = debug_mt5_data_structure()
    
    # 测试特征计算逻辑
    logic_ok = test_feature_calculation_logic()
    
    print(f"\n📋 调试总结")
    print("=" * 80)
    
    print(f"🔍 发现的问题:")
    if mt5_data is not None:
        print(f"✅ MT5数据获取正常")
        if hasattr(mt5_data, 'dtype') and mt5_data.dtype.names:
            print(f"✅ MT5返回结构化数组，字段: {mt5_data.dtype.names}")
        else:
            print(f"⚠️ MT5数据格式可能不是预期的结构化数组")
    else:
        print(f"❌ MT5数据获取失败")
    
    if logic_ok:
        print(f"✅ 特征计算逻辑正常")
    else:
        print(f"❌ 特征计算逻辑有问题")
    
    print(f"\n💡 可能的问题原因:")
    print(f"1. MT5返回的是结构化数组，不能直接用[:, col_idx]索引")
    print(f"2. 需要先转换为普通NumPy数组")
    print(f"3. 字段名可能与预期不同")
    print(f"4. 数据类型转换问题")
    
    print(f"\n🔧 建议的修复方案:")
    print(f"1. 在特征计算前先检查数据类型")
    print(f"2. 如果是结构化数组，先转换为普通数组")
    print(f"3. 添加详细的调试日志")
    print(f"4. 增加异常处理和数据验证")

if __name__ == '__main__':
    main()
