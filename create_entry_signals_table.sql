-- 创建入场信号表
CREATE TABLE IF NOT EXISTS entry_signals (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    signal_type VARCHAR(50) NOT NULL,
    symbol VARCHAR(20) NOT NULL,
    action VARCHAR(10) NOT NULL,
    strength FLOAT,
    confidence FLOAT,
    signal_price FLOAT,
    entry_price FLOAT,
    is_executed BOOLEAN DEFAULT FALSE,
    execution_result VARCHAR(50),
    execution_reason TEXT,
    trade_id VARCHAR(100),
    market_analysis TEXT,
    signal_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    execution_time DATETIME,
    FOREIGN KEY (user_id) REFERENCES user (id)
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_entry_signals_user_id ON entry_signals(user_id);
CREATE INDEX IF NOT EXISTS idx_entry_signals_signal_time ON entry_signals(signal_time);
CREATE INDEX IF NOT EXISTS idx_entry_signals_symbol ON entry_signals(symbol);

-- 验证表是否创建成功
SELECT name FROM sqlite_master WHERE type='table' AND name='entry_signals';
