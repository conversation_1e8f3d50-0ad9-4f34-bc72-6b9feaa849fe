#!/usr/bin/env python3
"""
回测验证服务 - 验证AI策略在历史数据上的表现
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import logging
import MetaTrader5 as mt5

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BacktestService:
    """回测验证服务"""
    
    def __init__(self):
        self.results_cache = {}  # 缓存回测结果
    
    def run_backtest(self, strategy_id: str, symbol: str = 'XAUUSD',
                    timeframe: str = '1h', days: int = 30,
                    initial_balance: float = 10000.0,
                    lot_size: float = 0.01,
                    custom_config: Dict[str, Any] = None,
                    allow_auto_connect: bool = False) -> Dict[str, Any]:
        """运行策略回测"""
        try:
            logger.info(f"🔄 开始回测策略 {strategy_id}...")
            logger.info(f"   参数: {symbol} | {timeframe} | {days}天 | ${initial_balance} | {lot_size}手")

            # 获取历史数据 - 传递连接控制参数
            historical_data = self.get_historical_data(symbol, timeframe, days, allow_auto_connect)
            if historical_data is None:
                return {
                    'success': False,
                    'error': 'MT5连接失败或无法获取历史数据。请确保MT5终端正在运行并已登录，或在回测参数中启用自动连接。'
                }

            if len(historical_data) < 50:
                return {
                    'success': False,
                    'error': f'历史数据不足({len(historical_data)}条)，需要至少50条数据进行回测'
                }
            
            # 加载AI策略
            from services.ai_strategy_loader import ai_strategy_loader
            strategy = ai_strategy_loader.load_strategy_model(strategy_id)
            if not strategy:
                return {'success': False, 'error': '无法加载策略'}

            # 获取策略配置
            from services.strategy_config_service import strategy_config_service
            if custom_config:
                # 使用自定义配置
                strategy_config = strategy_config_service.create_custom_config(
                    custom_config.get('base_type', 'trend_following'),
                    custom_config
                )
            else:
                # 根据策略自动选择配置
                strategy_config = strategy_config_service.get_strategy_config(
                    strategy_id, strategy.get('name', '')
                )

            logger.info(f"📊 使用策略配置: {strategy_config['config_type']}")
            logger.info(f"   止损: {strategy_config['stop_loss_percent']*100:.1f}% | 止盈: {strategy_config['take_profit_percent']*100:.1f}%")
            logger.info(f"   最大持仓: {strategy_config['max_positions']} | 信号阈值: {strategy_config['signal_threshold']}")

            # 执行回测
            backtest_results = self.execute_backtest(
                strategy, historical_data, initial_balance, lot_size, strategy_config
            )
            
            # 计算性能指标
            performance_metrics = self.calculate_performance_metrics(
                backtest_results, initial_balance
            )
            
            # 生成回测报告
            report = self.generate_backtest_report(
                strategy, backtest_results, performance_metrics, 
                symbol, timeframe, days
            )
            
            logger.info(f"✅ 回测完成，总收益: {performance_metrics['total_return']:.2f}%")
            
            # 确保所有必要字段都存在
            safe_results = {
                'initial_balance': backtest_results.get('initial_balance', initial_balance),
                'final_balance': backtest_results.get('final_balance', initial_balance),
                'final_equity': backtest_results.get('final_equity', initial_balance),
                'trades': backtest_results.get('trades', []),
                'equity_curve': backtest_results.get('equity_curve', [initial_balance]),
                'total_trades': backtest_results.get('total_trades', 0)
            }

            return {
                'success': True,
                'strategy_id': strategy_id,
                'symbol': symbol,
                'timeframe': timeframe,
                'days': days,
                'results': safe_results,
                'performance': performance_metrics,
                'report': report
            }
            
        except Exception as e:
            logger.error(f"❌ 回测失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def get_historical_data(self, symbol: str, timeframe: str, days: int,
                           allow_auto_connect: bool = False) -> Optional[pd.DataFrame]:
        """
        获取历史数据 - 仅使用MT5真实数据

        Args:
            symbol: 交易品种
            timeframe: 时间框架
            days: 天数
            allow_auto_connect: 是否允许自动连接MT5
        """
        try:
            # 检查MT5连接 - 严格模式，不允许自动连接
            if not self.check_mt5_connection(allow_auto_connect=allow_auto_connect):
                logger.error("❌ MT5连接检查失败，拒绝获取数据")
                logger.error("   回测必须使用真实的MT5连接和数据")
                logger.error("   请确保MT5终端正在运行并已登录")
                return None

            # 时间框架映射
            timeframe_map = {
                '1m': mt5.TIMEFRAME_M1,
                '5m': mt5.TIMEFRAME_M5,
                '15m': mt5.TIMEFRAME_M15,
                '30m': mt5.TIMEFRAME_M30,
                '1h': mt5.TIMEFRAME_H1,
                '4h': mt5.TIMEFRAME_H4,
                '1d': mt5.TIMEFRAME_D1
            }

            mt5_timeframe = timeframe_map.get(timeframe, mt5.TIMEFRAME_H1)

            # 计算需要的数据点数
            points_per_day = {
                '1m': 1440, '5m': 288, '15m': 96,
                '30m': 48, '1h': 24, '4h': 6, '1d': 1
            }
            total_points = days * points_per_day.get(timeframe, 24)

            # 获取历史数据
            rates = mt5.copy_rates_from_pos(symbol, mt5_timeframe, 0, total_points)

            if rates is None or len(rates) == 0:
                error_info = mt5.last_error()
                logger.error(f"❌ 无法获取 {symbol} 的历史数据")
                logger.error(f"   MT5错误代码: {error_info}")
                logger.error(f"   请检查: 1) MT5是否正常连接 2) 符号是否正确 3) 是否有数据权限")
                return None

            # 转换为DataFrame
            df = pd.DataFrame(rates)
            df['time'] = pd.to_datetime(df['time'], unit='s')
            df = df.sort_values('time').reset_index(drop=True)

            logger.info(f"✅ 获取MT5真实历史数据: {len(df)} 条记录")
            logger.info(f"   时间范围: {df['time'].min()} 到 {df['time'].max()}")
            logger.info(f"   价格范围: ${df['close'].min():.2f} - ${df['close'].max():.2f}")
            return df

        except Exception as e:
            logger.error(f"❌ 获取历史数据失败: {e}")
            return None

    def check_mt5_connection(self, allow_auto_connect: bool = False) -> bool:
        """
        检查MT5连接状态

        Args:
            allow_auto_connect: 是否允许自动连接MT5
        """
        try:
            logger.info("🔍 检查MT5连接状态...")

            # 检查MT5是否已初始化
            account_info = mt5.account_info()
            if account_info is None:
                if allow_auto_connect:
                    logger.info("🔄 MT5未初始化，尝试自动连接...")

                    # 尝试初始化MT5
                    if not mt5.initialize():
                        error_info = mt5.last_error()
                        logger.error(f"❌ MT5自动连接失败: {error_info}")
                        logger.error("   请确保: 1) MT5终端正在运行 2) 已登录交易账户")
                        return False

                    # 再次检查账户信息
                    account_info = mt5.account_info()
                    if account_info is None:
                        error_info = mt5.last_error()
                        logger.error(f"❌ 无法获取MT5账户信息: {error_info}")
                        logger.error("   请检查: 1) 账户是否已登录 2) 网络连接是否正常")
                        return False

                    logger.info("✅ MT5自动连接成功")
                else:
                    logger.error("❌ MT5未连接，且不允许自动连接")
                    logger.error("   用户需要手动启动并连接MT5终端")
                    logger.error("   如需自动连接，请在回测参数中启用")
                    return False

            # 验证连接质量 - 尝试获取一条测试数据
            test_rates = mt5.copy_rates_from_pos('XAUUSD', mt5.TIMEFRAME_H1, 0, 1)
            if test_rates is None or len(test_rates) == 0:
                logger.error("❌ MT5连接异常，无法获取测试数据")
                logger.error("   可能原因: 1) 网络连接问题 2) 服务器维护 3) 数据权限问题")
                return False

            # 验证连接质量
            logger.info(f"✅ MT5连接正常且数据可用")
            logger.info(f"   账户: {account_info.login}")
            logger.info(f"   服务器: {account_info.server}")
            logger.info(f"   余额: ${account_info.balance:.2f}")
            logger.info(f"   测试数据: XAUUSD @ ${test_rates[0]['close']:.2f}")

            return True

        except Exception as e:
            logger.error(f"❌ 检查MT5连接异常: {e}")
            return False


    
    def execute_backtest(self, strategy: Dict[str, Any], data: pd.DataFrame,
                        initial_balance: float, lot_size: float,
                        strategy_config: Dict[str, Any]) -> Dict[str, Any]:
        """执行回测"""
        try:
            from services.ai_strategy_loader import ai_strategy_loader
            
            # 初始化回测状态
            balance = initial_balance
            equity = initial_balance
            positions = []
            trades = []
            equity_curve = [initial_balance]
            
            # 遍历历史数据
            for i in range(50, len(data)):  # 从第50个数据点开始，确保有足够的历史数据
                current_data = data.iloc[:i+1]
                current_time = current_data.iloc[-1]['time']
                current_price = current_data.iloc[-1]['close']
                
                # 生成交易信号
                signal = ai_strategy_loader.predict_signal(strategy['id'], current_data)

                # 调试信息
                if i % 24 == 0:  # 每24小时打印一次状态
                    logger.info(f"📊 回测进度: {i}/{len(data)} ({(i/len(data)*100):.1f}%), 当前价格: {current_price:.2f}, 持仓数: {len(positions)}, 已完成交易: {len(trades)}")
                    logger.info(f"📊 信号阈值: {strategy_config['signal_threshold']}, 最近信号: {'有' if signal else '无'}")

                # 详细的信号调试信息
                if signal:
                    logger.info(f"🔍 AI信号详情: action={signal['action']}, confidence={signal.get('confidence', 0):.3f}, threshold={strategy_config['signal_threshold']}")

                # 应用信号阈值过滤
                if signal and signal.get('confidence', 0) >= strategy_config['signal_threshold']:
                    logger.info(f"🎯 生成交易信号: {signal['action']} @ {current_price:.2f} (置信度: {signal.get('confidence', 0):.2f})")
                elif signal:
                    logger.info(f"⚠️ 信号置信度不足: {signal.get('confidence', 0):.2f} < {strategy_config['signal_threshold']}")
                    signal = None  # 过滤掉低置信度信号
                else:
                    if i % 48 == 0:  # 每48小时打印一次无信号状态
                        logger.info(f"📊 AI策略未生成信号 (数据点: {i})")
                
                # 处理现有持仓
                for pos in positions[:]:  # 使用切片复制列表
                    # 计算当前盈亏
                    if pos['type'] == 'buy':
                        pnl = (current_price - pos['entry_price']) * pos['volume'] * 100
                    else:
                        pnl = (pos['entry_price'] - current_price) * pos['volume'] * 100
                    
                    pos['current_pnl'] = pnl
                    
                    # 检查止盈止损
                    should_close = False
                    close_reason = ''
                    
                    if pos['type'] == 'buy':
                        if current_price <= pos['stop_loss']:
                            should_close = True
                            close_reason = 'stop_loss'
                        elif current_price >= pos['take_profit']:
                            should_close = True
                            close_reason = 'take_profit'
                    else:
                        if current_price >= pos['stop_loss']:
                            should_close = True
                            close_reason = 'stop_loss'
                        elif current_price <= pos['take_profit']:
                            should_close = True
                            close_reason = 'take_profit'
                    
                    # 平仓
                    if should_close:
                        balance += pnl
                        
                        trade_record = {
                            'entry_time': pos['entry_time'],
                            'exit_time': current_time,
                            'type': pos['type'],
                            'volume': pos['volume'],
                            'entry_price': pos['entry_price'],
                            'exit_price': current_price,
                            'take_profit': pos['take_profit'],
                            'stop_loss': pos['stop_loss'],
                            'pnl': pnl,
                            'close_reason': close_reason
                        }
                        trades.append(trade_record)
                        positions.remove(pos)
                
                # 处理新信号
                if signal and len(positions) < strategy_config['max_positions']:
                    entry_price = current_price

                    # 使用动态止盈止损参数
                    stop_loss_percent = strategy_config['stop_loss_percent']
                    take_profit_percent = strategy_config['take_profit_percent']

                    if signal['action'] == 'buy':
                        stop_loss = entry_price * (1 - stop_loss_percent)
                        take_profit = entry_price * (1 + take_profit_percent)
                    else:
                        stop_loss = entry_price * (1 + stop_loss_percent)
                        take_profit = entry_price * (1 - take_profit_percent)

                    logger.info(f"📈 开仓: {signal['action']} @ {entry_price:.2f}, 止损: {stop_loss:.2f}, 止盈: {take_profit:.2f}")
                    
                    # 开仓
                    position = {
                        'entry_time': current_time,
                        'type': signal['action'],
                        'volume': lot_size,
                        'entry_price': entry_price,
                        'stop_loss': stop_loss,
                        'take_profit': take_profit,
                        'current_pnl': 0.0
                    }
                    positions.append(position)
                
                # 计算当前权益
                total_pnl = sum(pos['current_pnl'] for pos in positions)
                equity = balance + total_pnl
                equity_curve.append(equity)
            
            # 平仓所有剩余持仓
            final_price = data.iloc[-1]['close']
            final_time = data.iloc[-1]['time']
            
            for pos in positions:
                if pos['type'] == 'buy':
                    pnl = (final_price - pos['entry_price']) * pos['volume'] * 100
                else:
                    pnl = (pos['entry_price'] - final_price) * pos['volume'] * 100
                
                balance += pnl
                
                trade_record = {
                    'entry_time': pos['entry_time'],
                    'exit_time': final_time,
                    'type': pos['type'],
                    'volume': pos['volume'],
                    'entry_price': pos['entry_price'],
                    'exit_price': final_price,
                    'take_profit': pos['take_profit'],
                    'stop_loss': pos['stop_loss'],
                    'pnl': pnl,
                    'close_reason': 'backtest_end'
                }
                trades.append(trade_record)
            
            return {
                'initial_balance': initial_balance,
                'final_balance': balance,
                'final_equity': balance,
                'trades': trades,
                'equity_curve': equity_curve,
                'total_trades': len(trades)
            }
            
        except Exception as e:
            logger.error(f"❌ 执行回测失败: {e}")
            return {}

    def calculate_performance_metrics(self, results: Dict[str, Any],
                                    initial_balance: float) -> Dict[str, Any]:
        """计算性能指标"""
        try:
            trades = results.get('trades', [])
            equity_curve = results.get('equity_curve', [initial_balance])
            final_balance = results.get('final_balance', initial_balance)

            if not trades:
                return {
                    'total_return': 0.0,
                    'total_trades': 0,
                    'win_rate': 0.0,
                    'profit_factor': 0.0,
                    'max_drawdown': 0.0,
                    'sharpe_ratio': 0.0
                }

            # 基本指标
            total_return = ((final_balance - initial_balance) / initial_balance) * 100
            total_trades = len(trades)

            # 盈亏统计
            winning_trades = [t for t in trades if t['pnl'] > 0]
            losing_trades = [t for t in trades if t['pnl'] < 0]

            win_rate = (len(winning_trades) / total_trades) * 100 if total_trades > 0 else 0

            total_profit = sum(t['pnl'] for t in winning_trades)
            total_loss = abs(sum(t['pnl'] for t in losing_trades))
            profit_factor = total_profit / total_loss if total_loss > 0 else float('inf')

            # 最大回撤
            peak = initial_balance
            max_drawdown = 0.0

            for equity in equity_curve:
                if equity > peak:
                    peak = equity
                drawdown = ((peak - equity) / peak) * 100
                if drawdown > max_drawdown:
                    max_drawdown = drawdown

            # 夏普比率（简化计算）
            if len(equity_curve) > 1:
                returns = np.diff(equity_curve) / equity_curve[:-1]
                sharpe_ratio = np.mean(returns) / np.std(returns) * np.sqrt(252) if np.std(returns) > 0 else 0
            else:
                sharpe_ratio = 0

            # 确保所有数值都是有效的数字
            average_win = round(total_profit / len(winning_trades), 2) if winning_trades else 0.0
            average_loss = round(total_loss / len(losing_trades), 2) if losing_trades else 0.0

            return {
                'total_return': round(total_return, 2) if not np.isnan(total_return) else 0.0,
                'total_trades': total_trades,
                'winning_trades': len(winning_trades),
                'losing_trades': len(losing_trades),
                'win_rate': round(win_rate, 2) if not np.isnan(win_rate) else 0.0,
                'profit_factor': round(profit_factor, 2) if not np.isinf(profit_factor) and not np.isnan(profit_factor) else 0.0,
                'max_drawdown': round(max_drawdown, 2) if not np.isnan(max_drawdown) else 0.0,
                'sharpe_ratio': round(sharpe_ratio, 2) if not np.isnan(sharpe_ratio) else 0.0,
                'total_profit': round(total_profit, 2) if not np.isnan(total_profit) else 0.0,
                'total_loss': round(total_loss, 2) if not np.isnan(total_loss) else 0.0,
                'average_win': average_win,
                'average_loss': average_loss
            }

        except Exception as e:
            logger.error(f"❌ 计算性能指标失败: {e}")
            return {}

    def generate_backtest_report(self, strategy: Dict[str, Any], results: Dict[str, Any],
                               performance: Dict[str, Any], symbol: str,
                               timeframe: str, days: int) -> Dict[str, Any]:
        """生成回测报告"""
        try:
            report = {
                'strategy_info': {
                    'name': strategy['name'],
                    'id': strategy['id'],
                    'timeframe': strategy['timeframe']
                },
                'backtest_settings': {
                    'symbol': symbol,
                    'timeframe': timeframe,
                    'period_days': days,
                    'initial_balance': results.get('initial_balance', 10000)
                },
                'performance_summary': performance,
                'trade_analysis': {
                    'total_trades': len(results.get('trades', [])),
                    'profitable_trades': performance.get('winning_trades', 0),
                    'losing_trades': performance.get('losing_trades', 0),
                    'win_rate': f"{performance.get('win_rate', 0):.2f}%",
                    'profit_factor': performance.get('profit_factor', 0)
                },
                'risk_metrics': {
                    'max_drawdown': f"{performance.get('max_drawdown', 0):.2f}%",
                    'sharpe_ratio': performance.get('sharpe_ratio', 0)
                },
                'generated_at': datetime.now().isoformat()
            }

            return report

        except Exception as e:
            logger.error(f"❌ 生成回测报告失败: {e}")
            return {}

# 全局回测服务实例
backtest_service = BacktestService()
