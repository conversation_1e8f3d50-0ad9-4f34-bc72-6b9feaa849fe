#!/usr/bin/env python3
"""
深度学习训练进度卡住问题的综合修复方案
"""

import sqlite3
import json
import time
import requests
from datetime import datetime
import sys
import os
import threading

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def login_session():
    """登录并获取会话"""
    session = requests.Session()
    
    try:
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post('http://127.0.0.1:5000/login', data=login_data)
        
        if response.status_code == 200 and 'dashboard' in response.url:
            print("✅ 登录成功")
            return session
        else:
            print(f"❌ 登录失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return None

def analyze_training_issue():
    """分析训练问题"""
    print("\n🔍 分析训练问题")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 查找所有运行中的训练任务
        cursor.execute("""
            SELECT id, model_id, status, progress, current_epoch, total_epochs,
                   train_loss, val_loss, logs, created_at, updated_at,
                   (julianday('now') - julianday(updated_at)) * 24 * 60 as minutes_since_update
            FROM training_tasks 
            WHERE status = 'running'
            ORDER BY created_at DESC 
        """)
        
        running_tasks = cursor.fetchall()
        
        if not running_tasks:
            print("✅ 没有运行中的训练任务")
            conn.close()
            return []
        
        print(f"📋 发现 {len(running_tasks)} 个运行中的训练任务:")
        
        stuck_tasks = []
        
        for task in running_tasks:
            (task_id, model_id, status, progress, current_epoch, total_epochs,
             train_loss, val_loss, logs, created_at, updated_at, minutes_since_update) = task
            
            print(f"\n🔹 任务: {task_id[:8]}...")
            print(f"   进度: {progress}%")
            print(f"   轮次: {current_epoch}/{total_epochs}")
            print(f"   距离上次更新: {minutes_since_update:.1f} 分钟")
            
            # 分析日志信息
            if logs:
                try:
                    log_data = json.loads(logs)
                    stage = log_data.get('stage', 'unknown')
                    message = log_data.get('message', '')
                    print(f"   阶段: {stage}")
                    print(f"   消息: {message}")
                    
                    if 'error' in log_data:
                        print(f"   ❌ 错误: {log_data['error']}")
                except:
                    print(f"   日志: {logs[:100]}...")
            
            # 判断是否卡住
            is_stuck = False
            if minutes_since_update > 10:  # 超过10分钟无更新
                print(f"   ⚠️ 长时间无更新 (超过10分钟)")
                is_stuck = True
            elif progress == 25.0 and current_epoch == 0:
                print(f"   ⚠️ 卡在模型训练开始阶段")
                is_stuck = True
            elif progress < 30 and minutes_since_update > 5:
                print(f"   ⚠️ 数据准备阶段卡住")
                is_stuck = True
            
            if is_stuck:
                stuck_tasks.append(task_id)
        
        conn.close()
        return stuck_tasks
        
    except Exception as e:
        print(f"❌ 分析训练问题失败: {e}")
        return []

def force_stop_all_training():
    """强制停止所有训练任务"""
    print("\n🛑 强制停止所有训练任务")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 更新所有运行中的任务为停止状态
        cursor.execute("""
            UPDATE training_tasks 
            SET status = 'stopped', updated_at = ?
            WHERE status IN ('running', 'pending')
        """, (datetime.now().isoformat(),))
        
        affected_rows = cursor.rowcount
        conn.commit()
        conn.close()
        
        print(f"✅ 强制停止了 {affected_rows} 个训练任务")
        
        # 清理训练控制状态
        try:
            from services.deep_learning_service import DeepLearningService
            dl_service = DeepLearningService()
            
            if hasattr(dl_service, 'training_control'):
                old_count = len(dl_service.training_control)
                dl_service.training_control.clear()
                print(f"✅ 清理了 {old_count} 个训练控制状态")
        except Exception as e:
            print(f"⚠️ 清理训练控制状态失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 强制停止训练失败: {e}")
        return False

def check_system_resources():
    """检查系统资源"""
    print("\n📊 检查系统资源")
    print("=" * 50)
    
    try:
        import psutil
        import torch
        
        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=1)
        print(f"CPU使用率: {cpu_percent}%")
        
        # 内存使用率
        memory = psutil.virtual_memory()
        print(f"内存使用率: {memory.percent}% ({memory.used / 1024**3:.1f}GB / {memory.total / 1024**3:.1f}GB)")
        
        # GPU信息
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            print(f"GPU数量: {gpu_count}")
            
            for i in range(gpu_count):
                gpu_name = torch.cuda.get_device_name(i)
                gpu_memory_allocated = torch.cuda.memory_allocated(i) / 1024**3
                gpu_memory_reserved = torch.cuda.memory_reserved(i) / 1024**3
                
                print(f"GPU {i}: {gpu_name}")
                print(f"   已分配内存: {gpu_memory_allocated:.2f}GB")
                print(f"   已保留内存: {gpu_memory_reserved:.2f}GB")
                
                # 清理GPU内存
                torch.cuda.empty_cache()
                print(f"   ✅ 已清理GPU内存缓存")
        else:
            print("❌ CUDA不可用")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查系统资源失败: {e}")
        return False

def test_simple_training():
    """测试简单的训练配置"""
    print("\n🧪 测试简单的训练配置")
    print("=" * 50)
    
    session = login_session()
    if not session:
        return False
    
    # 使用最简单的配置
    simple_config = {
        'symbol': 'XAUUSD',
        'timeframe': 'H1',
        'days': 7,  # 只用7天数据
        'epochs': 5,  # 只训练5轮
        'batch_size': 8,  # 很小的批次
        'learning_rate': 0.001,
        'sequence_length': 10,  # 很短的序列
        'hidden_size': 32,  # 很小的隐藏层
        'num_layers': 1,  # 只有1层
        'dropout': 0.1,
        'early_stopping': True,
        'patience': 3,
        'validation_split': 0.2,
        'use_gpu': True
    }
    
    print("📊 简单训练配置:")
    for key, value in simple_config.items():
        print(f"   {key}: {value}")
    
    try:
        response = session.post(
            'http://127.0.0.1:5000/api/deep-learning/start-training',
            json=simple_config,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                task_id = result.get('task_id')
                print(f"✅ 简单训练启动成功!")
                print(f"   任务ID: {task_id}")
                
                # 监控60秒
                print(f"\n📊 监控简单训练 (60秒):")
                
                progress_updates = 0
                last_progress = 0
                
                for i in range(12):  # 12次检查，每次5秒
                    time.sleep(5)
                    
                    try:
                        progress_response = session.get(f'http://127.0.0.1:5000/api/deep-learning/training-progress/{task_id}')
                        
                        if progress_response.status_code == 200:
                            progress_result = progress_response.json()
                            
                            if progress_result.get('success'):
                                progress_data = progress_result['progress']
                                progress = progress_data.get('progress', 0)
                                epoch = progress_data.get('epoch', 0)
                                status = progress_data.get('status', 'unknown')
                                
                                print(f"[{(i+1)*5:2d}s] 进度: {progress}%, 轮次: {epoch}, 状态: {status}")
                                
                                # 检查进度更新
                                if progress != last_progress:
                                    progress_updates += 1
                                    print(f"       ✅ 进度更新 (总更新: {progress_updates})")
                                
                                last_progress = progress
                                
                                # 如果训练完成
                                if status in ['completed', 'failed', 'stopped']:
                                    print(f"       🏁 训练结束: {status}")
                                    break
                                    
                            else:
                                print(f"[{(i+1)*5:2d}s] ❌ API错误: {progress_result.get('error')}")
                        else:
                            print(f"[{(i+1)*5:2d}s] ❌ HTTP错误: {progress_response.status_code}")
                            
                    except Exception as e:
                        print(f"[{(i+1)*5:2d}s] ❌ 监控异常: {e}")
                
                print(f"\n📈 简单训练测试结果:")
                print(f"   进度更新次数: {progress_updates}")
                
                if progress_updates >= 2:
                    print(f"   ✅ 训练进度正常更新")
                    return True
                else:
                    print(f"   ❌ 训练进度更新异常")
                    return False
                
            else:
                print(f"❌ 简单训练启动失败: {result.get('error')}")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text[:200]}...")
            return False
            
    except Exception as e:
        print(f"❌ 测试简单训练失败: {e}")
        return False

def main():
    print("🔧 深度学习训练进度卡住问题 - 综合修复")
    print("=" * 60)
    
    # 步骤1: 分析训练问题
    stuck_tasks = analyze_training_issue()
    
    # 步骤2: 强制停止所有训练
    if not force_stop_all_training():
        print("❌ 强制停止训练失败")
        return
    
    # 步骤3: 检查系统资源
    check_system_resources()
    
    # 步骤4: 等待系统稳定
    print("\n⏳ 等待系统稳定...")
    time.sleep(5)
    
    # 步骤5: 测试简单训练
    if test_simple_training():
        print("\n✅ 修复成功！训练功能已恢复正常")
        print("💡 建议:")
        print("1. 使用较小的数据集和模型配置")
        print("2. 监控GPU内存使用情况")
        print("3. 如果问题再次出现，考虑重启应用程序")
    else:
        print("\n❌ 修复失败，需要进一步诊断")
        print("💡 进一步修复建议:")
        print("1. 重启应用程序: python app.py")
        print("2. 检查MT5连接状态")
        print("3. 检查GPU驱动和CUDA环境")
        print("4. 考虑使用CPU模式训练")

if __name__ == '__main__':
    main()
