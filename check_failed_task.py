#!/usr/bin/env python3
"""
检查失败任务的详细信息
"""

import sqlite3
import json

def check_latest_failed_task():
    """检查最新失败任务的详细信息"""
    
    print("🔍 检查最新失败任务的详细信息")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 获取最新的失败任务
        cursor.execute("""
            SELECT id, model_id, status, progress, current_epoch, total_epochs,
                   train_loss, val_loss, logs, created_at, started_at, completed_at, updated_at
            FROM training_tasks 
            WHERE status = 'failed'
            ORDER BY created_at DESC 
            LIMIT 1
        """)
        
        task = cursor.fetchone()
        
        if not task:
            print("❌ 没有找到失败的任务")
            conn.close()
            return
        
        (task_id, model_id, status, progress, current_epoch, total_epochs,
         train_loss, val_loss, logs, created_at, started_at, completed_at, updated_at) = task
        
        print(f"📊 最新失败任务详情:")
        print(f"   任务ID: {task_id}")
        print(f"   模型ID: {model_id}")
        print(f"   状态: {status}")
        print(f"   进度: {progress}%")
        print(f"   轮次: {current_epoch}/{total_epochs}")
        print(f"   训练损失: {train_loss}")
        print(f"   验证损失: {val_loss}")
        print(f"   创建时间: {created_at}")
        print(f"   开始时间: {started_at}")
        print(f"   完成时间: {completed_at}")
        print(f"   更新时间: {updated_at}")
        
        if logs:
            print(f"\n📝 详细日志信息:")
            try:
                log_data = json.loads(logs)
                
                # 格式化显示日志
                for key, value in log_data.items():
                    if key == 'error':
                        print(f"   ❌ 错误: {value}")
                    elif key == 'stage':
                        print(f"   🔄 阶段: {value}")
                    elif key == 'message':
                        print(f"   💬 消息: {value}")
                    else:
                        print(f"   📊 {key}: {value}")
                        
            except json.JSONDecodeError:
                print(f"   原始日志: {logs}")
        else:
            print(f"\n📝 没有日志信息")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")

def check_application_logs():
    """检查应用程序日志"""
    
    print(f"\n📋 检查应用程序最新日志")
    print("=" * 60)
    
    # 检查应用程序终端输出
    print("💡 建议检查应用程序控制台输出中的错误信息")
    print("💡 特别关注以下类型的错误:")
    print("   • MT5连接错误")
    print("   • 数据获取错误")
    print("   • GPU/CUDA错误")
    print("   • 内存不足错误")
    print("   • 模型初始化错误")

def analyze_progress_pattern():
    """分析进度模式"""
    
    print(f"\n📈 分析进度更新模式")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 获取最近几个任务的进度模式
        cursor.execute("""
            SELECT id, status, progress, logs, created_at, updated_at
            FROM training_tasks 
            ORDER BY created_at DESC 
            LIMIT 5
        """)
        
        tasks = cursor.fetchall()
        
        print(f"📊 最近5个任务的进度模式:")
        
        for i, task in enumerate(tasks, 1):
            task_id, status, progress, logs, created_at, updated_at = task
            
            print(f"\n🔹 任务 {i}: {task_id[:8]}...")
            print(f"   状态: {status}")
            print(f"   进度: {progress}%")
            print(f"   创建: {created_at}")
            print(f"   更新: {updated_at}")
            
            if logs:
                try:
                    log_data = json.loads(logs)
                    stage = log_data.get('stage', 'unknown')
                    message = log_data.get('message', '')
                    print(f"   阶段: {stage}")
                    if message:
                        print(f"   消息: {message[:50]}...")
                except:
                    pass
        
        conn.close()
        
        print(f"\n💡 进度模式分析:")
        print(f"• 如果多个任务都在15%失败，可能是数据获取阶段的问题")
        print(f"• 如果进度能到25%+，说明数据准备基本正常")
        print(f"• 如果进度能到30%+，说明模型训练开始了")
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")

def suggest_fixes():
    """建议修复方案"""
    
    print(f"\n🔧 建议的修复方案")
    print("=" * 60)
    
    print(f"🎯 根据当前情况，建议按以下顺序检查:")
    
    print(f"\n1. 📊 数据获取问题:")
    print(f"   • 检查MT5是否正常连接")
    print(f"   • 确认XAUUSD品种数据可用")
    print(f"   • 验证历史数据权限")
    print(f"   • 尝试手动获取少量数据测试")
    
    print(f"\n2. 🔧 代码逻辑问题:")
    print(f"   • 检查数据准备函数的异常处理")
    print(f"   • 验证特征计算逻辑")
    print(f"   • 确认序列创建过程")
    print(f"   • 检查数据形状和类型")
    
    print(f"\n3. 🎮 GPU/环境问题:")
    print(f"   • 验证PyTorch CUDA兼容性")
    print(f"   • 检查GPU内存是否充足")
    print(f"   • 尝试CPU模式训练")
    print(f"   • 检查依赖包版本")
    
    print(f"\n4. 📝 日志增强:")
    print(f"   • 添加更详细的错误日志")
    print(f"   • 在关键步骤添加调试信息")
    print(f"   • 记录数据形状和统计信息")
    print(f"   • 捕获并记录所有异常")

def main():
    """主函数"""
    
    print("🔧 失败任务详细分析")
    print("=" * 80)
    
    # 检查最新失败任务
    check_latest_failed_task()
    
    # 分析进度模式
    analyze_progress_pattern()
    
    # 检查应用程序日志
    check_application_logs()
    
    # 建议修复方案
    suggest_fixes()
    
    print(f"\n📋 总结")
    print("=" * 80)
    
    print(f"✅ 已修复的功能:")
    print(f"• 训练进度更新机制正常工作")
    print(f"• 数据准备过程能够开始")
    print(f"• 前端页面能显示进度变化")
    print(f"• GPU状态API正常响应")
    
    print(f"\n⚠️ 仍需解决的问题:")
    print(f"• 训练在数据准备阶段失败")
    print(f"• 需要查看具体的错误信息")
    print(f"• 可能需要调整数据获取逻辑")
    
    print(f"\n🎯 下一步行动:")
    print(f"1. 查看应用程序控制台的详细错误日志")
    print(f"2. 检查MT5数据获取的具体问题")
    print(f"3. 根据错误信息进行针对性修复")
    print(f"4. 测试更简单的配置参数")

if __name__ == '__main__':
    main()
