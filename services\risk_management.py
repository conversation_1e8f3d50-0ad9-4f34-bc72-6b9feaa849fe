#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
风险管理服务
专业的风险控制和资金管理工具
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
import logging
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class RiskManagement:
    """风险管理类"""
    
    def __init__(self):
        self.max_risk_per_trade = 0.02  # 单笔交易最大风险2%（恢复保守设置）
        self.max_daily_loss = 0.05      # 日最大亏损5%（恢复保守设置）
        self.max_drawdown = 0.15        # 最大回撤15%（更保守的设置）
        self.max_correlation = 0.7      # 最大相关性70%（恢复原设置）
        
    def calculate_position_size(self, account_balance: float, risk_amount: float, 
                              entry_price: float, stop_loss: float, 
                              symbol_info: Dict = None) -> Dict:
        """
        计算仓位大小
        
        Args:
            account_balance: 账户余额
            risk_amount: 风险金额或风险百分比
            entry_price: 入场价格
            stop_loss: 止损价格
            symbol_info: 交易品种信息
            
        Returns:
            Dict: 包含仓位大小和相关信息的字典
        """
        try:
            # 如果风险金额小于1，视为百分比
            if risk_amount < 1:
                risk_dollars = account_balance * risk_amount
            else:
                risk_dollars = risk_amount
            
            # 计算每点价值
            if symbol_info:
                point_value = symbol_info.get('point', 0.0001)
                contract_size = symbol_info.get('contract_size', 100000)
            else:
                point_value = 0.0001
                contract_size = 100000
            
            # 计算止损点数
            stop_loss_points = abs(entry_price - stop_loss)
            
            # 计算仓位大小
            if stop_loss_points > 0:
                position_size = risk_dollars / (stop_loss_points * contract_size)
            else:
                position_size = 0
            
            # 计算相关指标
            risk_reward_ratio = 0
            if stop_loss_points > 0:
                # 假设止盈是止损的2倍
                take_profit_points = stop_loss_points * 2
                risk_reward_ratio = take_profit_points / stop_loss_points
            
            return {
                'position_size': round(position_size, 2),
                'risk_dollars': risk_dollars,
                'stop_loss_points': stop_loss_points,
                'risk_reward_ratio': risk_reward_ratio,
                'max_loss': risk_dollars,
                'recommended': True if position_size > 0 else False
            }
            
        except Exception as e:
            logger.error(f"计算仓位大小失败: {e}")
            return {'error': str(e)}
    
    def calculate_risk_metrics(self, trades: List[Dict]) -> Dict:
        """
        计算风险指标
        
        Args:
            trades: 交易记录列表
            
        Returns:
            Dict: 风险指标字典
        """
        try:
            if not trades:
                return {'error': '没有交易数据'}
            
            # 转换为DataFrame
            df = pd.DataFrame(trades)
            
            # 计算收益率
            if 'profit' in df.columns and 'initial_balance' in df.columns:
                df['return'] = df['profit'] / df['initial_balance']
            else:
                df['return'] = 0
            
            # 基本统计
            total_trades = len(df)
            winning_trades = len(df[df['profit'] > 0])
            losing_trades = len(df[df['profit'] < 0])
            
            win_rate = winning_trades / total_trades if total_trades > 0 else 0
            
            # 平均盈利和亏损
            avg_win = df[df['profit'] > 0]['profit'].mean() if winning_trades > 0 else 0
            avg_loss = df[df['profit'] < 0]['profit'].mean() if losing_trades > 0 else 0
            
            # 盈亏比
            profit_factor = abs(avg_win / avg_loss) if avg_loss != 0 else 0
            
            # 最大连续亏损
            max_consecutive_losses = self._calculate_max_consecutive_losses(df['profit'])
            
            # 回撤分析
            cumulative_returns = (1 + df['return']).cumprod()
            drawdown = self._calculate_drawdown(cumulative_returns)
            max_drawdown = drawdown.min()
            
            # 夏普比率
            if df['return'].std() != 0:
                sharpe_ratio = df['return'].mean() / df['return'].std() * np.sqrt(252)
            else:
                sharpe_ratio = 0
            
            # VaR (Value at Risk) 95%置信度
            var_95 = np.percentile(df['return'], 5) if len(df) > 0 else 0
            
            return {
                'total_trades': total_trades,
                'winning_trades': winning_trades,
                'losing_trades': losing_trades,
                'win_rate': round(win_rate, 4),
                'avg_win': round(avg_win, 2),
                'avg_loss': round(avg_loss, 2),
                'profit_factor': round(profit_factor, 2),
                'max_consecutive_losses': max_consecutive_losses,
                'max_drawdown': round(max_drawdown, 4),
                'sharpe_ratio': round(sharpe_ratio, 2),
                'var_95': round(var_95, 4),
                'total_profit': round(df['profit'].sum(), 2),
                'avg_return': round(df['return'].mean(), 4)
            }
            
        except Exception as e:
            logger.error(f"计算风险指标失败: {e}")
            return {'error': str(e)}
    
    def _calculate_max_consecutive_losses(self, profits: pd.Series) -> int:
        """计算最大连续亏损次数"""
        consecutive_losses = 0
        max_consecutive = 0
        
        for profit in profits:
            if profit < 0:
                consecutive_losses += 1
                max_consecutive = max(max_consecutive, consecutive_losses)
            else:
                consecutive_losses = 0
                
        return max_consecutive
    
    def _calculate_drawdown(self, cumulative_returns: pd.Series) -> pd.Series:
        """计算回撤"""
        peak = cumulative_returns.expanding().max()
        drawdown = (cumulative_returns - peak) / peak
        return drawdown
    
    def check_correlation_risk(self, positions: List[Dict], 
                             correlation_matrix: pd.DataFrame = None) -> Dict:
        """
        检查相关性风险
        
        Args:
            positions: 当前持仓列表
            correlation_matrix: 相关性矩阵
            
        Returns:
            Dict: 相关性风险分析结果
        """
        try:
            if not positions:
                return {'risk_level': 'low', 'message': '无持仓'}
            
            # 提取持仓品种
            symbols = [pos['symbol'] for pos in positions]
            
            # 如果没有提供相关性矩阵，使用默认值
            if correlation_matrix is None:
                correlation_matrix = self._get_default_correlation_matrix(symbols)
            
            # 计算组合相关性风险
            high_correlation_pairs = []
            
            for i, symbol1 in enumerate(symbols):
                for j, symbol2 in enumerate(symbols[i+1:], i+1):
                    if symbol1 in correlation_matrix.index and symbol2 in correlation_matrix.columns:
                        corr = correlation_matrix.loc[symbol1, symbol2]
                        if abs(corr) > self.max_correlation:
                            high_correlation_pairs.append({
                                'symbol1': symbol1,
                                'symbol2': symbol2,
                                'correlation': round(corr, 3)
                            })
            
            # 评估风险等级
            if len(high_correlation_pairs) == 0:
                risk_level = 'low'
                message = '持仓相关性风险较低'
            elif len(high_correlation_pairs) <= 2:
                risk_level = 'medium'
                message = f'发现{len(high_correlation_pairs)}对高相关性持仓'
            else:
                risk_level = 'high'
                message = f'发现{len(high_correlation_pairs)}对高相关性持仓，风险较高'
            
            return {
                'risk_level': risk_level,
                'message': message,
                'high_correlation_pairs': high_correlation_pairs,
                'total_positions': len(positions)
            }
            
        except Exception as e:
            logger.error(f"检查相关性风险失败: {e}")
            return {'error': str(e)}
    
    def _get_default_correlation_matrix(self, symbols: List[str]) -> pd.DataFrame:
        """获取默认相关性矩阵"""
        # 创建一个简单的相关性矩阵
        n = len(symbols)
        corr_matrix = np.eye(n)  # 对角线为1
        
        # 添加一些假设的相关性
        for i in range(n):
            for j in range(i+1, n):
                # 同类货币对相关性较高
                if self._is_same_currency_group(symbols[i], symbols[j]):
                    corr_matrix[i][j] = corr_matrix[j][i] = 0.8
                else:
                    corr_matrix[i][j] = corr_matrix[j][i] = 0.3
        
        return pd.DataFrame(corr_matrix, index=symbols, columns=symbols)
    
    def _is_same_currency_group(self, symbol1: str, symbol2: str) -> bool:
        """判断是否为同类货币对"""
        # 简单的分组逻辑
        major_pairs = ['EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF']
        commodity_pairs = ['AUDUSD', 'NZDUSD', 'USDCAD']
        
        if symbol1 in major_pairs and symbol2 in major_pairs:
            return True
        if symbol1 in commodity_pairs and symbol2 in commodity_pairs:
            return True
        
        return False
    
    def calculate_kelly_criterion(self, win_rate: float, avg_win: float, avg_loss: float) -> float:
        """
        计算凯利公式最优仓位
        
        Args:
            win_rate: 胜率
            avg_win: 平均盈利
            avg_loss: 平均亏损
            
        Returns:
            float: 最优仓位比例
        """
        try:
            if avg_loss == 0:
                return 0
            
            # 凯利公式: f = (bp - q) / b
            # 其中 b = avg_win/avg_loss, p = win_rate, q = 1-win_rate
            b = abs(avg_win / avg_loss)
            p = win_rate
            q = 1 - win_rate
            
            kelly_fraction = (b * p - q) / b
            
            # 限制最大仓位为25%
            kelly_fraction = max(0, min(kelly_fraction, 0.25))
            
            return round(kelly_fraction, 4)
            
        except Exception as e:
            logger.error(f"计算凯利公式失败: {e}")
            return 0
    
    def generate_risk_report(self, account_info: Dict, positions: List[Dict], 
                           trades: List[Dict]) -> Dict:
        """
        生成风险报告
        
        Args:
            account_info: 账户信息
            positions: 当前持仓
            trades: 历史交易
            
        Returns:
            Dict: 风险报告
        """
        try:
            report = {
                'timestamp': datetime.now().isoformat(),
                'account_summary': {},
                'position_analysis': {},
                'risk_metrics': {},
                'recommendations': []
            }
            
            # 账户摘要
            if account_info:
                report['account_summary'] = {
                    'balance': account_info.get('balance', 0),
                    'equity': account_info.get('equity', 0),
                    'margin': account_info.get('margin', 0),
                    'free_margin': account_info.get('free_margin', 0),
                    'margin_level': account_info.get('margin_level', 0)
                }
            
            # 持仓分析
            if positions:
                total_exposure = sum([abs(pos.get('volume', 0) * pos.get('price_open', 0)) 
                                    for pos in positions])
                report['position_analysis'] = {
                    'total_positions': len(positions),
                    'total_exposure': total_exposure,
                    'correlation_risk': self.check_correlation_risk(positions)
                }
            
            # 风险指标
            if trades:
                report['risk_metrics'] = self.calculate_risk_metrics(trades)
            
            # 生成建议
            recommendations = []
            
            # 检查保证金水平（调整为更宽松的阈值）
            margin_level = account_info.get('margin_level', 0) if account_info else 0
            if margin_level < 100:  # 从200%调整为100%
                recommendations.append({
                    'type': 'warning',
                    'message': f'保证金水平过低 ({margin_level:.1f}%)，建议减少持仓'
                })
            
            # 检查持仓数量（调整为更宽松的阈值）
            if len(positions) > 20:  # 从10调整为20
                recommendations.append({
                    'type': 'info',
                    'message': '持仓数量较多，建议关注相关性风险'
                })
            
            # 检查回撤（调整为更宽松的阈值）
            if 'max_drawdown' in report.get('risk_metrics', {}):
                max_dd = report['risk_metrics']['max_drawdown']
                if max_dd < -0.25:  # 从-15%调整为-25%
                    recommendations.append({
                        'type': 'warning',
                        'message': f'最大回撤过大 ({max_dd:.1%})，建议调整策略'
                    })
            
            report['recommendations'] = recommendations
            
            return report
            
        except Exception as e:
            logger.error(f"生成风险报告失败: {e}")
            return {'error': str(e)}

# 全局风险管理实例
risk_manager = RiskManagement()
