#!/usr/bin/env python3
"""
AI训练监控和管理工具
实现了完整的训练监控、异常检测和自动恢复机制
"""

import os
import sys
import json
import time
import sqlite3
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import argparse

# 添加服务路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from services.deep_learning_service import DeepLearningService
    import psutil
    import torch
except ImportError as e:
    print(f"❌ 导入依赖失败: {e}")
    print("请确保已安装所需依赖: pip install torch psutil")
    sys.exit(1)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('ai_training_monitor.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class AITrainingMonitor:
    """AI训练监控器"""
    
    def __init__(self):
        self.db_path = 'trading_system.db'
        self.dl_service = DeepLearningService()
        
    def show_system_status(self):
        """显示系统状态"""
        print("\n" + "="*80)
        print("🖥️  AI训练系统状态监控")
        print("="*80)
        
        # 获取系统状态
        status = self.dl_service.get_system_status()
        
        print(f"📅 时间: {status.get('timestamp', 'N/A')}")
        print(f"🔧 设备: {status.get('device', 'N/A')}")
        print(f"🐍 PyTorch: {'✅' if status.get('torch_available') else '❌'}")
        print(f"🧠 TensorFlow: {'✅' if status.get('tensorflow_available') else '❌'}")
        
        # 系统资源
        monitor = status.get('system_monitor', {})
        if monitor:
            print(f"\n💻 系统资源:")
            print(f"   CPU使用率: {monitor.get('cpu_percent', 0):.1f}%")
            print(f"   内存使用率: {monitor.get('memory_percent', 0):.1f}%")
            print(f"   可用内存: {monitor.get('memory_available_gb', 0):.2f}GB")
            
            gpu_info = monitor.get('gpu_info', {})
            if gpu_info:
                print(f"   GPU内存已分配: {gpu_info.get('memory_allocated', 0):.2f}GB")
                print(f"   GPU内存已保留: {gpu_info.get('memory_reserved', 0):.2f}GB")
        
        # 任务统计
        task_stats = status.get('task_statistics', {})
        if task_stats:
            print(f"\n📊 训练任务统计:")
            for status_name, count in task_stats.items():
                emoji = {'running': '🏃', 'completed': '✅', 'failed': '❌', 'pending': '⏳'}.get(status_name, '📋')
                print(f"   {emoji} {status_name}: {count}")
        
        # 活跃任务
        active_tasks = status.get('active_tasks', [])
        if active_tasks:
            print(f"\n🏃 活跃训练任务:")
            for task in active_tasks:
                print(f"   📋 {task['task_id'][:8]}... - 进度: {task['progress']:.1f}% - 轮次: {task['current_epoch']}")
                print(f"      最后更新: {task['last_update']}")
    
    def show_training_details(self, task_id: str):
        """显示训练详情"""
        print(f"\n📋 训练任务详情: {task_id}")
        print("="*60)
        
        # 获取任务进度
        progress = self.dl_service.get_training_progress(task_id)
        if not progress:
            print("❌ 未找到训练任务")
            return
        
        print(f"状态: {progress.get('status', 'N/A')}")
        print(f"进度: {progress.get('progress', 0):.1f}%")
        print(f"当前轮次: {progress.get('current_epoch', 0)}/{progress.get('total_epochs', 0)}")
        print(f"训练损失: {progress.get('train_loss', 0):.4f}")
        print(f"验证损失: {progress.get('val_loss', 0):.4f}")
        print(f"最佳损失: {progress.get('best_loss', 0):.4f}")
        print(f"最后更新: {progress.get('updated_at', 'N/A')}")
        
        # 获取训练指标历史
        metrics = self.dl_service.get_training_metrics(task_id)
        if metrics:
            print(f"\n📈 最近10个训练指标:")
            for metric in metrics[-10:]:
                timestamp = metric.get('timestamp', 'N/A')
                epoch = metric.get('epoch', 0)
                train_loss = metric.get('train_loss', 0)
                val_loss = metric.get('val_loss', 0)
                progress = metric.get('progress', 0)
                print(f"   {timestamp} - Epoch {epoch} - 进度: {progress:.1f}% - 训练损失: {train_loss:.4f} - 验证损失: {val_loss:.4f}")
    
    def check_stuck_tasks(self):
        """检查卡死的任务"""
        print("\n🔍 检查卡死的训练任务")
        print("="*50)
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 查找长时间无更新的运行中任务
            cursor.execute('''
                SELECT id, progress, current_epoch, updated_at,
                       (julianday('now') - julianday(updated_at)) * 24 * 60 as minutes_since_update
                FROM training_tasks 
                WHERE status = 'running'
                AND datetime(updated_at) < datetime('now', '-5 minutes')
                ORDER BY updated_at ASC
            ''')
            
            stuck_tasks = cursor.fetchall()
            
            if not stuck_tasks:
                print("✅ 没有发现卡死的训练任务")
            else:
                print(f"⚠️ 发现 {len(stuck_tasks)} 个可能卡死的任务:")
                for task_id, progress, epoch, updated_at, minutes in stuck_tasks:
                    print(f"   📋 {task_id[:8]}... - 进度: {progress:.1f}% - 轮次: {epoch}")
                    print(f"      最后更新: {updated_at} ({minutes:.1f}分钟前)")
                    
                    # 提供恢复选项
                    print(f"      建议操作: python ai_training_monitor.py --restart-task {task_id}")
            
            conn.close()
            
        except Exception as e:
            logger.error(f"检查卡死任务失败: {e}")
    
    def restart_task(self, task_id: str):
        """重启卡死的任务"""
        print(f"\n🔄 重启训练任务: {task_id}")
        print("="*50)
        
        try:
            # 这里可以实现任务重启逻辑
            # 目前只是标记任务为失败，用户可以重新启动
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                UPDATE training_tasks 
                SET status = 'failed', 
                    logs = ?, 
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (json.dumps({
                'restart_reason': 'Task appeared to be stuck',
                'restart_time': datetime.now().isoformat(),
                'message': '任务因长时间无响应被重置，请重新启动训练'
            }), task_id))
            
            conn.commit()
            conn.close()
            
            print(f"✅ 任务已重置为失败状态，请在界面中重新启动训练")
            
        except Exception as e:
            logger.error(f"重启任务失败: {e}")
    
    def cleanup_old_data(self, days: int = 30):
        """清理旧数据"""
        print(f"\n🧹 清理 {days} 天前的旧数据")
        print("="*40)
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 清理旧的训练任务
            cursor.execute('''
                DELETE FROM training_tasks 
                WHERE status IN ('completed', 'failed')
                AND datetime(updated_at) < datetime('now', '-{} days')
            '''.format(days))
            
            deleted_tasks = cursor.rowcount
            
            conn.commit()
            conn.close()
            
            print(f"✅ 清理了 {deleted_tasks} 个旧训练任务")
            
            # 清理旧的指标文件
            metrics_dir = os.path.join('deep_learning_models', 'metrics')
            if os.path.exists(metrics_dir):
                cleaned_files = 0
                cutoff_time = time.time() - (days * 24 * 3600)
                
                for filename in os.listdir(metrics_dir):
                    filepath = os.path.join(metrics_dir, filename)
                    if os.path.getmtime(filepath) < cutoff_time:
                        os.remove(filepath)
                        cleaned_files += 1
                
                print(f"✅ 清理了 {cleaned_files} 个旧指标文件")
            
        except Exception as e:
            logger.error(f"清理旧数据失败: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='AI训练监控和管理工具')
    parser.add_argument('--status', action='store_true', help='显示系统状态')
    parser.add_argument('--details', type=str, help='显示指定任务的详细信息')
    parser.add_argument('--check-stuck', action='store_true', help='检查卡死的任务')
    parser.add_argument('--restart-task', type=str, help='重启指定的卡死任务')
    parser.add_argument('--cleanup', type=int, default=30, help='清理N天前的旧数据')
    parser.add_argument('--monitor', action='store_true', help='持续监控模式')
    
    args = parser.parse_args()
    
    monitor = AITrainingMonitor()
    
    if args.status:
        monitor.show_system_status()
    elif args.details:
        monitor.show_training_details(args.details)
    elif args.check_stuck:
        monitor.check_stuck_tasks()
    elif args.restart_task:
        monitor.restart_task(args.restart_task)
    elif args.cleanup:
        monitor.cleanup_old_data(args.cleanup)
    elif args.monitor:
        print("🔄 启动持续监控模式 (按Ctrl+C退出)")
        try:
            while True:
                monitor.show_system_status()
                monitor.check_stuck_tasks()
                print(f"\n⏰ 等待60秒后下次检查...")
                time.sleep(60)
        except KeyboardInterrupt:
            print("\n👋 监控已停止")
    else:
        # 默认显示状态
        monitor.show_system_status()
        monitor.check_stuck_tasks()

if __name__ == "__main__":
    main()
