#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断MT5数据获取问题
分析AI推理模型训练中的数据获取故障
"""

import sys
import os
import time
from datetime import datetime, timedelta

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_mt5_installation():
    """检查MT5库安装"""
    print("🔍 检查MT5库安装...")
    try:
        import MetaTrader5 as mt5
        print(f"✅ MT5库已安装，版本: {mt5.__version__}")
        return True
    except ImportError:
        print("❌ MT5库未安装")
        print("💡 请运行: pip install MetaTrader5")
        return False

def check_mt5_terminal():
    """检查MT5终端状态"""
    print("\n🔍 检查MT5终端状态...")
    try:
        import MetaTrader5 as mt5
        
        # 尝试初始化
        if not mt5.initialize():
            error = mt5.last_error()
            print(f"❌ MT5终端连接失败: {error}")
            print("💡 请检查:")
            print("   1. MT5终端是否正在运行")
            print("   2. MT5是否已登录账户")
            print("   3. 专家顾问设置是否正确")
            return False
        
        # 检查账户信息
        account_info = mt5.account_info()
        if account_info is None:
            error = mt5.last_error()
            print(f"❌ 无法获取账户信息: {error}")
            mt5.shutdown()
            return False
        
        print(f"✅ MT5终端连接成功")
        print(f"   账户: {account_info.login}")
        print(f"   服务器: {account_info.server}")
        print(f"   余额: {account_info.balance}")
        print(f"   公司: {account_info.company}")
        
        mt5.shutdown()
        return True
        
    except Exception as e:
        print(f"❌ MT5终端检查失败: {e}")
        return False

def test_symbol_availability():
    """测试交易品种可用性"""
    print("\n🔍 测试交易品种可用性...")
    try:
        import MetaTrader5 as mt5
        
        if not mt5.initialize():
            print("❌ MT5初始化失败")
            return False
        
        symbol = "XAUUSD"
        
        # 检查品种信息
        symbol_info = mt5.symbol_info(symbol)
        if symbol_info is None:
            print(f"❌ 品种 {symbol} 不可用")
            print("💡 请检查:")
            print("   1. 品种名称是否正确")
            print("   2. 经纪商是否提供该品种")
            print("   3. 市场观察窗口是否已添加该品种")
            mt5.shutdown()
            return False
        
        print(f"✅ 品种 {symbol} 可用")
        print(f"   描述: {symbol_info.description}")
        print(f"   点值: {symbol_info.point}")
        print(f"   最小手数: {symbol_info.volume_min}")
        print(f"   最大手数: {symbol_info.volume_max}")
        
        # 检查品种是否可见
        if not symbol_info.visible:
            print(f"⚠️ 品种 {symbol} 不可见，尝试添加到市场观察...")
            if mt5.symbol_select(symbol, True):
                print(f"✅ 成功添加 {symbol} 到市场观察")
            else:
                print(f"❌ 无法添加 {symbol} 到市场观察")
                mt5.shutdown()
                return False
        
        mt5.shutdown()
        return True
        
    except Exception as e:
        print(f"❌ 品种可用性检查失败: {e}")
        return False

def test_data_retrieval():
    """测试数据获取"""
    print("\n🔍 测试数据获取...")
    try:
        import MetaTrader5 as mt5
        
        if not mt5.initialize():
            print("❌ MT5初始化失败")
            return False
        
        symbol = "XAUUSD"
        
        # 确保品种可见
        mt5.symbol_select(symbol, True)
        
        # 测试1: 获取最近数据
        print("\n📊 测试1: 获取最近100条H1数据...")
        rates = mt5.copy_rates_from_pos(symbol, mt5.TIMEFRAME_H1, 0, 100)
        if rates is not None and len(rates) > 0:
            print(f"✅ 成功获取 {len(rates)} 条数据")
            print(f"   最新时间: {datetime.fromtimestamp(rates[-1]['time'])}")
            print(f"   最新价格: {rates[-1]['close']:.5f}")
        else:
            print("❌ 无法获取最近数据")
            error = mt5.last_error()
            print(f"   错误: {error}")
        
        # 测试2: 获取5分钟数据
        print("\n📊 测试2: 获取最近100条5M数据...")
        rates = mt5.copy_rates_from_pos(symbol, mt5.TIMEFRAME_M5, 0, 100)
        if rates is not None and len(rates) > 0:
            print(f"✅ 成功获取 {len(rates)} 条5M数据")
            print(f"   最新时间: {datetime.fromtimestamp(rates[-1]['time'])}")
        else:
            print("❌ 无法获取5M数据")
            error = mt5.last_error()
            print(f"   错误: {error}")
        
        # 测试3: 获取时间范围数据（模拟训练数据获取）
        print("\n📊 测试3: 获取时间范围数据（模拟训练场景）...")
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)  # 最近30天
        
        print(f"   时间范围: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")
        
        rates = mt5.copy_rates_range(symbol, mt5.TIMEFRAME_M5, start_date, end_date)
        if rates is not None and len(rates) > 0:
            print(f"✅ 成功获取 {len(rates)} 条时间范围数据")
            print(f"   开始时间: {datetime.fromtimestamp(rates[0]['time'])}")
            print(f"   结束时间: {datetime.fromtimestamp(rates[-1]['time'])}")
        else:
            print("❌ 无法获取时间范围数据")
            error = mt5.last_error()
            print(f"   错误: {error}")
        
        # 测试4: 测试训练中使用的具体时间范围
        print("\n📊 测试4: 测试训练使用的时间范围...")
        train_end = datetime(2025, 6, 1)  # 根据日志中的时间
        train_start = datetime(2024, 6, 1)
        
        print(f"   训练时间范围: {train_start.strftime('%Y-%m-%d')} 到 {train_end.strftime('%Y-%m-%d')}")
        
        rates = mt5.copy_rates_range(symbol, mt5.TIMEFRAME_M5, train_start, train_end)
        if rates is not None and len(rates) > 0:
            print(f"✅ 成功获取训练时间范围数据: {len(rates)} 条")
            print(f"   数据密度: {len(rates) / 365:.1f} 条/天")
        else:
            print("❌ 无法获取训练时间范围数据")
            error = mt5.last_error()
            print(f"   错误: {error}")
            
            # 尝试更短的时间范围
            print("\n🔄 尝试更短的时间范围（最近7天）...")
            short_start = datetime.now() - timedelta(days=7)
            short_end = datetime.now()
            
            rates = mt5.copy_rates_range(symbol, mt5.TIMEFRAME_M5, short_start, short_end)
            if rates is not None and len(rates) > 0:
                print(f"✅ 短时间范围成功: {len(rates)} 条")
            else:
                print("❌ 短时间范围也失败")
        
        mt5.shutdown()
        return True
        
    except Exception as e:
        print(f"❌ 数据获取测试失败: {e}")
        return False

def test_mt5_service():
    """测试MT5服务"""
    print("\n🔍 测试MT5服务...")
    try:
        from services.mt5_service import mt5_service
        
        # 检查连接状态
        if not mt5_service.is_connected():
            print("🔄 MT5服务未连接，尝试连接...")
            if not mt5_service.connect():
                print("❌ MT5服务连接失败")
                return False
        
        print("✅ MT5服务连接成功")
        
        # 测试获取历史数据
        print("📊 测试MT5服务数据获取...")
        data = mt5_service.get_historical_data("XAUUSD", "5m", 100)
        
        if data and len(data) > 0:
            print(f"✅ MT5服务数据获取成功: {len(data)} 条")
            return True
        else:
            print("❌ MT5服务数据获取失败")
            return False
            
    except Exception as e:
        print(f"❌ MT5服务测试失败: {e}")
        return False

def analyze_training_issue():
    """分析训练问题"""
    print("\n🔍 分析训练数据获取问题...")
    
    # 根据日志分析问题
    print("📋 根据日志分析:")
    print("1. MT5重新连接成功 ✅")
    print("2. 时间框架转换正常: 5m -> 5 ✅")
    print("3. 数据范围: 2024-06-01 到 2025-06-01 (1年数据)")
    print("4. 连接超时设置: 5秒")
    print("5. 重试3次都获取到空数据 ❌")
    
    print("\n🔍 可能的问题原因:")
    print("1. 📅 时间范围问题:")
    print("   - 2025-06-01 是未来时间，可能没有数据")
    print("   - 建议使用当前时间作为结束时间")
    
    print("2. 🕐 市场时间问题:")
    print("   - 可能在市场关闭时间获取数据")
    print("   - 周末或节假日可能没有数据")
    
    print("3. 📊 数据权限问题:")
    print("   - 经纪商可能限制历史数据访问")
    print("   - 需要检查账户类型和数据权限")
    
    print("4. 🔗 连接稳定性问题:")
    print("   - 网络连接不稳定")
    print("   - MT5服务器响应慢")
    
    print("5. ⚙️ 配置问题:")
    print("   - MT5专家顾问设置")
    print("   - 防火墙或安全软件阻止")

def main():
    """主诊断函数"""
    print("🔧 MT5数据获取问题诊断")
    print("=" * 60)
    
    # 步骤1: 检查基础环境
    if not check_mt5_installation():
        return
    
    # 步骤2: 检查MT5终端
    if not check_mt5_terminal():
        return
    
    # 步骤3: 检查交易品种
    if not test_symbol_availability():
        return
    
    # 步骤4: 测试数据获取
    if not test_data_retrieval():
        return
    
    # 步骤5: 测试MT5服务
    test_mt5_service()
    
    # 步骤6: 分析训练问题
    analyze_training_issue()
    
    print("\n" + "=" * 60)
    print("🎯 诊断完成!")
    print("\n💡 建议解决方案:")
    print("1. 修改训练数据时间范围，使用当前时间作为结束时间")
    print("2. 检查市场开放时间，避免在休市时获取数据")
    print("3. 增加数据获取的重试间隔和超时时间")
    print("4. 添加更详细的错误日志和诊断信息")
    print("5. 考虑使用备用数据源或缓存机制")

if __name__ == "__main__":
    main()
