#!/usr/bin/env python3
"""
MT4配置指导工具
帮助用户正确配置MT4以支持Python连接
"""

import os
import subprocess
import time

def show_mt4_configuration_guide():
    """显示MT4配置指导"""
    print("⚙️  MT4配置指导")
    print("=" * 60)
    
    print("🎯 您的MT4路径:")
    print("   C:\\Program Files (x86)\\EC Markets MetaTrader 4 Terminal\\terminal.exe")
    print("   这是EC Markets经纪商的MT4版本")
    
    print("\n📋 必需的配置步骤:")
    print("1. 🔧 配置专家顾问设置")
    print("2. 🔑 确保账户已登录")
    print("3. 🌐 检查网络连接")
    print("4. 🔄 重启MT4终端")
    
    print("\n" + "="*60)
    print("🔧 步骤1: 配置专家顾问设置")
    print("="*60)
    print("在MT4终端中:")
    print("1. 点击菜单 '工具' -> '选项'")
    print("2. 切换到 '专家顾问' 选项卡")
    print("3. ✅ 勾选 '允许自动交易'")
    print("4. ✅ 勾选 '允许DLL导入'")
    print("5. ✅ 勾选 '允许导入外部专家顾问'")
    print("6. ✅ 勾选 '允许实时自动交易' (如果有)")
    print("7. 点击 '确定' 保存设置")
    
    print("\n" + "="*60)
    print("🔑 步骤2: 确保账户登录")
    print("="*60)
    print("1. 检查MT4右下角的连接状态")
    print("2. 应该显示绿色的连接指示器")
    print("3. 如果未登录:")
    print("   - 点击 '文件' -> '登录到交易账户'")
    print("   - 输入您的账户信息")
    print("   - 选择正确的服务器")
    
    print("\n" + "="*60)
    print("🌐 步骤3: 检查网络连接")
    print("="*60)
    print("1. 确保MT4可以正常连接到服务器")
    print("2. 检查防火墙设置")
    print("3. 确保没有代理软件干扰")
    
    print("\n" + "="*60)
    print("🔄 步骤4: 重启MT4终端")
    print("="*60)
    print("1. 完全关闭MT4终端")
    print("2. 等待5秒")
    print("3. 重新启动MT4")
    print("4. 确保所有设置都已保存")

def create_mt4_test_script():
    """创建MT4测试脚本"""
    test_script = '''#!/usr/bin/env python3
"""
EC Markets MT4连接测试
专门针对您的MT4安装进行测试
"""

import MetaTrader5 as mt5
import time

def test_ec_markets_mt4():
    """测试EC Markets MT4连接"""
    print("🧪 EC Markets MT4连接测试")
    print("=" * 50)
    
    mt4_path = r"C:\\Program Files (x86)\\EC Markets MetaTrader 4 Terminal\\terminal.exe"
    
    print(f"MT4路径: {mt4_path}")
    print("正在尝试连接...")
    
    try:
        # 尝试初始化
        if not mt5.initialize(mt4_path):
            error = mt5.last_error()
            print(f"❌ 初始化失败: {error}")
            
            if error[0] == -10003:
                print("\\n💡 解决方案:")
                print("1. 确保MT4终端正在运行")
                print("2. 检查专家顾问设置:")
                print("   - 工具 -> 选项 -> 专家顾问")
                print("   - 勾选 '允许自动交易' 和 '允许DLL导入'")
                print("3. 重启MT4终端")
                print("4. 以管理员身份运行此脚本")
            
            return False
        
        print("✅ MT4初始化成功!")
        
        # 获取终端信息
        terminal_info = mt5.terminal_info()
        if terminal_info:
            print(f"终端名称: {terminal_info.name}")
            print(f"终端版本: {terminal_info.build}")
            print(f"终端路径: {terminal_info.path}")
        
        # 获取账户信息
        account_info = mt5.account_info()
        if account_info:
            print(f"✅ 账户已登录:")
            print(f"   账户: {account_info.login}")
            print(f"   服务器: {account_info.server}")
            print(f"   余额: {account_info.balance} {account_info.currency}")
            print(f"   净值: {account_info.equity} {account_info.currency}")
        else:
            print("⚠️  未登录账户，请在MT4中登录")
        
        # 获取交易品种
        symbols = mt5.symbols_get()
        if symbols:
            visible_symbols = [s.name for s in symbols if s.visible]
            print(f"✅ 可用交易品种: {len(visible_symbols)} 个")
            if visible_symbols:
                print(f"   示例: {', '.join(visible_symbols[:5])}")
        
        # 测试价格数据
        if symbols and len([s for s in symbols if s.visible]) > 0:
            test_symbol = [s.name for s in symbols if s.visible][0]
            rates = mt5.copy_rates_from_pos(test_symbol, mt5.TIMEFRAME_H1, 0, 10)
            if rates is not None:
                print(f"✅ 价格数据获取成功: {test_symbol}")
                print(f"   最新价格: {rates[-1]['close']}")
            else:
                print(f"❌ 无法获取 {test_symbol} 价格数据")
        
        # 关闭连接
        mt5.shutdown()
        print("\\n🎉 MT4连接测试完成!")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        return False

if __name__ == "__main__":
    test_ec_markets_mt4()
'''
    
    with open('test_ec_markets_mt4.py', 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print(f"\n📄 已创建专用测试脚本: test_ec_markets_mt4.py")

def create_mt4_restart_script():
    """创建MT4重启脚本"""
    restart_script = '''@echo off
echo 重启EC Markets MT4终端...
echo.

REM 关闭现有的MT4进程
echo 正在关闭MT4进程...
taskkill /F /IM terminal.exe >nul 2>&1

REM 等待5秒
echo 等待5秒...
timeout /t 5 /nobreak >nul

REM 启动MT4
echo 启动MT4终端...
if exist "C:\\Program Files (x86)\\EC Markets MetaTrader 4 Terminal\\terminal.exe" (
    start "" "C:\\Program Files (x86)\\EC Markets MetaTrader 4 Terminal\\terminal.exe"
    echo ✅ MT4已启动
) else (
    echo ❌ 未找到MT4可执行文件
)

echo.
echo 📋 请在MT4中检查以下设置:
echo 1. 工具 -^> 选项 -^> 专家顾问
echo 2. ✅ 勾选 "允许自动交易"
echo 3. ✅ 勾选 "允许DLL导入"
echo 4. ✅ 勾选 "允许导入外部专家顾问"
echo 5. 确保账户已登录
echo.
echo 配置完成后，运行: python test_ec_markets_mt4.py
echo.
pause
'''
    
    with open('restart_mt4.bat', 'w', encoding='utf-8') as f:
        f.write(restart_script)
    
    print(f"📄 已创建重启脚本: restart_mt4.bat")

def main():
    """主函数"""
    print("🔧 MT4配置指导工具")
    print("专门针对您的EC Markets MT4")
    print("=" * 60)
    
    # 显示配置指导
    show_mt4_configuration_guide()
    
    # 创建测试脚本
    create_mt4_test_script()
    
    # 创建重启脚本
    create_mt4_restart_script()
    
    print("\n" + "=" * 60)
    print("🚀 下一步操作:")
    print("1. 📋 按照上述指导配置MT4设置")
    print("2. 🔄 运行 restart_mt4.bat 重启MT4")
    print("3. 🧪 运行 test_ec_markets_mt4.py 测试连接")
    print("4. ✅ 如果测试成功，即可在MateTrade4中使用")
    
    print("\n💡 重要提示:")
    print("- EC Markets是正规的外汇经纪商")
    print("- 确保您有有效的交易账户")
    print("- 专家顾问设置是连接成功的关键")
    print("- 如果仍有问题，可联系EC Markets技术支持")

if __name__ == "__main__":
    main()
