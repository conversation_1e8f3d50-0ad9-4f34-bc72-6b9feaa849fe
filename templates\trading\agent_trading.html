{% extends "base.html" %}

{% block page_title %}智能体交易{% endblock %}

{% block content %}
<div class="row">
    <!-- 策略管理 -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-robot"></i>
                    智能体交易策略
                </h5>
            </div>
            <div class="card-body">
                <button class="btn btn-primary btn-sm mb-3" data-bs-toggle="modal" data-bs-target="#createStrategyModal">
                    <i class="fas fa-plus"></i>
                    创建新策略
                </button>

                <div id="strategiesList">
                    <div class="text-center text-muted">
                        <i class="fas fa-spinner fa-spin"></i>
                        加载中...
                    </div>
                </div>
            </div>
        </div>

        <!-- 活跃会话 -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-play-circle"></i>
                    活跃交易会话
                </h5>
            </div>
            <div class="card-body">
                <div id="activeSessionsList">
                    <div class="text-center text-muted">暂无活跃会话</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 交易控制面板 -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-cogs"></i>
                    智能体交易控制面板
                </h5>
            </div>
            <div class="card-body">
                <div id="tradingControlPanel">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        请先创建或选择一个智能体交易策略
                    </div>
                </div>
            </div>
        </div>

        <!-- 实时决策日志 -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-brain"></i>
                    AI决策日志
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="decisionsTable">
                        <thead>
                            <tr>
                                <th>时间</th>
                                <th>货币对</th>
                                <th>决策</th>
                                <th>信心度</th>
                                <th>理由</th>
                                <th>执行结果</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="6" class="text-center text-muted">暂无决策记录</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 性能评估 -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line"></i>
                    性能评估
                </h5>
            </div>
            <div class="card-body">
                <div id="performanceMetrics">
                    <div class="text-center text-muted">暂无性能数据</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 创建策略模态框 -->
<div class="modal fade" id="createStrategyModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">创建智能体交易策略</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createStrategyForm">
                    <div class="mb-3">
                        <label class="form-label">策略名称 *</label>
                        <input type="text" class="form-control" id="strategyName" required>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">策略描述</label>
                        <textarea class="form-control" id="strategyDescription" rows="3"></textarea>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">用户交易策略和心得 *</label>
                        <textarea class="form-control" id="userStrategy" rows="4"
                                  placeholder="请描述您的交易理念、偏好的技术指标、风险控制方法等..."></textarea>
                        <small class="text-muted">这将作为AI决策的重要参考</small>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">风险承受度</label>
                                <select class="form-select" id="riskTolerance">
                                    <option value="conservative">保守型</option>
                                    <option value="moderate" selected>稳健型</option>
                                    <option value="aggressive">激进型</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">最大日交易次数</label>
                                <input type="number" class="form-control" id="maxDailyTrades" value="30" min="1" max="50">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">最大持仓规模 ($)</label>
                                <input type="number" class="form-control" id="maxPositionSize" value="1000" min="100" step="100">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">交易时间段</label>
                                <select class="form-select" id="tradingHours">
                                    <option value="9:00-12:00">上午时段 (9:00-12:00)</option>
                                    <option value="13:00-16:00">下午时段 (13:00-16:00)</option>
                                    <option value="9:00-18:00" selected>全天时段 (9:00-18:00)</option>
                                    <option value="24小时">24小时交易</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">交易货币对</label>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="EURUSD" id="symbol_EURUSD" checked>
                                    <label class="form-check-label" for="symbol_EURUSD">EUR/USD</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="GBPUSD" id="symbol_GBPUSD">
                                    <label class="form-check-label" for="symbol_GBPUSD">GBP/USD</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="USDJPY" id="symbol_USDJPY">
                                    <label class="form-check-label" for="symbol_USDJPY">USD/JPY</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="AUDUSD" id="symbol_AUDUSD">
                                    <label class="form-check-label" for="symbol_AUDUSD">AUD/USD</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="USDCAD" id="symbol_USDCAD">
                                    <label class="form-check-label" for="symbol_USDCAD">USD/CAD</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="XAUUSD" id="symbol_XAUUSD">
                                    <label class="form-check-label" for="symbol_XAUUSD">XAU/USD</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">AI模型配置</label>
                        <select class="form-select" id="aiModel">
                            <option value="">请选择AI模型</option>
                            <!-- 动态加载系统配置的AI模型 -->
                        </select>
                        <small class="text-muted">选择用于交易决策的AI模型（来自系统设置）</small>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">交易模式</label>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="tradingModeSwitch" checked>
                            <label class="form-check-label" for="tradingModeSwitch">
                                <span id="tradingModeLabel">模拟交易</span>
                            </label>
                        </div>
                        <small class="text-muted" id="tradingModeDescription">使用虚拟资金进行交易，无真实风险</small>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">关联AI策略 (可选)</label>
                        <select class="form-select" id="aiStrategyId">
                            <option value="">无关联策略</option>
                            <!-- 动态加载AI策略列表 -->
                        </select>
                        <small class="text-muted">选择已训练的AI策略作为参考</small>
                    </div>

                    <!-- 止损止盈设置 -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">智能止损比例 (%)</label>
                                <input type="number" class="form-control" id="stopLossPercent"
                                       value="2.0" min="0.5" max="10" step="0.1">
                                <small class="text-muted">AI将根据市场波动智能调整</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">智能止盈比例 (%)</label>
                                <input type="number" class="form-control" id="takeProfitPercent"
                                       value="5.0" min="1" max="20" step="0.1">
                                <small class="text-muted">AI将根据趋势强度智能调整</small>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="dynamicStopLoss" checked>
                            <label class="form-check-label" for="dynamicStopLoss">
                                启用动态止损止盈
                            </label>
                        </div>
                        <small class="text-muted">AI将根据市场条件动态调整止损止盈水平</small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="createStrategy()">创建策略</button>
            </div>
        </div>
    </div>
</div>

<script>
// 全局变量
let currentStrategy = null;
let currentSession = null;
let decisionInterval = null;

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    loadStrategies();
    loadSessions();
    loadAIModels();
    loadTrainedAIStrategies();

    // 设置交易模式开关事件
    setupTradingModeSwitch();

    // 定期刷新活跃会话
    setInterval(loadSessions, 30000); // 30秒刷新一次
});

// 加载策略列表
function loadStrategies() {
    fetch('/api/agent-trading/strategies')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayStrategies(data.strategies);
        } else {
            console.error('加载策略失败:', data.error);
        }
    })
    .catch(error => {
        console.error('加载策略失败:', error);
    });
}

// 显示策略列表
function displayStrategies(strategies) {
    const container = document.getElementById('strategiesList');

    if (strategies.length === 0) {
        container.innerHTML = '<div class="text-center text-muted">暂无策略</div>';
        return;
    }

    let html = '';
    strategies.forEach(strategy => {
        const statusBadge = strategy.is_active ?
            '<span class="badge bg-success">运行中</span>' :
            '<span class="badge bg-secondary">未激活</span>';

        html += `
            <div class="card mb-2 strategy-card" data-strategy-id="${strategy.id}">
                <div class="card-body p-3">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h6 class="card-title mb-1">${strategy.name}</h6>
                            <small class="text-muted">${strategy.description || '无描述'}</small>
                        </div>
                        ${statusBadge}
                    </div>
                    <div class="mt-2">
                        <small class="text-muted">
                            风险: ${strategy.risk_tolerance} |
                            货币对: ${strategy.trading_symbols.join(', ')}
                        </small>
                    </div>
                    <div class="mt-1">
                        <small class="text-info">
                            <i class="fas fa-shield-alt"></i> 止损: ${strategy.stop_loss_percent || '2.0'}% |
                            <i class="fas fa-bullseye"></i> 止盈: ${strategy.take_profit_percent || '5.0'}%
                            ${strategy.dynamic_stop_loss ? ' | <i class="fas fa-magic"></i> 智能调整' : ''}
                        </small>
                    </div>
                    <div class="mt-2 d-flex gap-2 flex-wrap">
                        <button class="btn btn-sm btn-outline-primary" onclick="selectStrategy(${strategy.id})">
                            <i class="fas fa-check"></i> 选择策略
                        </button>
                        ${!strategy.is_active ?
                            `<button class="btn btn-sm btn-success" onclick="startTrading(${strategy.id})">
                                <i class="fas fa-play"></i> 启动交易
                            </button>` :
                            `<button class="btn btn-sm btn-danger" onclick="stopTrading(${strategy.id})">
                                <i class="fas fa-stop"></i> 停止交易
                            </button>`
                        }
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteStrategy(${strategy.id})" title="删除策略">
                            <i class="fas fa-trash"></i> 删除
                        </button>
                    </div>
                </div>
            </div>
        `;
    });

    container.innerHTML = html;
}

// 创建策略
function createStrategy() {
    const formData = {
        name: document.getElementById('strategyName').value,
        description: document.getElementById('strategyDescription').value,
        user_strategy: document.getElementById('userStrategy').value,
        risk_tolerance: document.getElementById('riskTolerance').value,
        max_daily_trades: parseInt(document.getElementById('maxDailyTrades').value),
        max_position_size: parseFloat(document.getElementById('maxPositionSize').value),
        trading_hours: document.getElementById('tradingHours').value,
        trading_mode: document.getElementById('tradingModeSwitch').checked ? 'demo' : 'real',
        ai_strategy_id: document.getElementById('aiStrategyId').value || null,
        ai_model_config: {
            model: document.getElementById('aiModel').value
        },
        // 止损止盈设置
        stop_loss_percent: parseFloat(document.getElementById('stopLossPercent').value),
        take_profit_percent: parseFloat(document.getElementById('takeProfitPercent').value),
        dynamic_stop_loss: document.getElementById('dynamicStopLoss').checked
    };

    // 获取选中的交易货币对
    const symbols = [];
    document.querySelectorAll('input[type="checkbox"]:checked').forEach(checkbox => {
        symbols.push(checkbox.value);
    });
    formData.trading_symbols = symbols;

    if (!formData.name || !formData.user_strategy) {
        alert('请填写策略名称和用户交易策略');
        return;
    }

    if (symbols.length === 0) {
        alert('请至少选择一个交易货币对');
        return;
    }

    fetch('/api/agent-trading/strategies', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('策略创建成功！');
            document.getElementById('createStrategyForm').reset();
            bootstrap.Modal.getInstance(document.getElementById('createStrategyModal')).hide();
            loadStrategies();
        } else {
            alert('创建失败: ' + data.error);
        }
    })
    .catch(error => {
        console.error('创建策略失败:', error);
        alert('创建失败，请重试');
    });
}



// 加载交易会话
function loadSessions() {
    fetch('/api/agent-trading/sessions')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayActiveSessions(data.sessions.filter(s => s.status === 'active'));
        }
    })
    .catch(error => {
        console.error('加载会话失败:', error);
    });
}

// 显示活跃会话
function displayActiveSessions(sessions) {
    const container = document.getElementById('activeSessionsList');

    if (sessions.length === 0) {
        container.innerHTML = '<div class="text-center text-muted">暂无活跃会话</div>';
        return;
    }

    let html = '';
    sessions.forEach(session => {
        const profitColor = session.total_profit >= 0 ? 'text-success' : 'text-danger';
        const profitSign = session.total_profit >= 0 ? '+' : '';

        html += `
            <div class="card mb-2">
                <div class="card-body p-3">
                    <h6 class="card-title mb-1">${session.strategy_name}</h6>
                    <div class="row">
                        <div class="col-6">
                            <small class="text-muted">交易次数</small>
                            <div class="fw-bold">${session.total_trades}</div>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">总盈亏</small>
                            <div class="fw-bold ${profitColor}">
                                ${profitSign}$${session.total_profit.toFixed(2)}
                            </div>
                        </div>
                    </div>
                    <div class="mt-2">
                        <button class="btn btn-sm btn-primary" onclick="selectSession(${session.id})">
                            查看详情
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="stopSession(${session.id})">
                            停止
                        </button>
                    </div>
                </div>
            </div>
        `;
    });

    container.innerHTML = html;
}

// 选择策略
function selectStrategy(strategyId) {
    currentStrategy = strategyId;

    // 高亮选中的策略
    document.querySelectorAll('.strategy-card').forEach(card => {
        card.classList.remove('border-primary');
    });
    document.querySelector(`[data-strategy-id="${strategyId}"]`).classList.add('border-primary');

    // 显示控制面板
    displayTradingControlPanel(strategyId);
}

// 显示交易控制面板
function displayTradingControlPanel(strategyId) {
    const panel = document.getElementById('tradingControlPanel');

    // 获取策略信息
    fetch(`/api/agent-trading/strategies/${strategyId}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const strategy = data.strategy;

            panel.innerHTML = `
                <!-- 智能止盈止损设置显示 -->
                <div class="card border-info mb-3">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-shield-alt"></i> 当前策略止盈止损设置
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="text-center">
                                    <div class="text-danger">
                                        <i class="fas fa-arrow-down fa-2x"></i>
                                    </div>
                                    <h5 class="text-danger">${strategy.stop_loss_percent || 2.0}%</h5>
                                    <small class="text-muted">智能止损</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <div class="text-success">
                                        <i class="fas fa-arrow-up fa-2x"></i>
                                    </div>
                                    <h5 class="text-success">${strategy.take_profit_percent || 5.0}%</h5>
                                    <small class="text-muted">智能止盈</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <div class="text-primary">
                                        <i class="fas fa-robot fa-2x"></i>
                                    </div>
                                    <h5 class="text-primary">${strategy.dynamic_stop_loss ? '启用' : '禁用'}</h5>
                                    <small class="text-muted">动态调整</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <button class="btn btn-outline-secondary btn-sm" onclick="editStopLossSettings(${strategyId})">
                                        <i class="fas fa-edit"></i> 修改设置
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="mt-3">
                            <div class="alert alert-light">
                                <small>
                                    <i class="fas fa-info-circle text-info"></i>
                                    <strong>AI智能调整：</strong>
                                    ${strategy.dynamic_stop_loss ?
                                        'AI将根据市场波动率、趋势强度和货币对特性动态调整止损止盈水平' :
                                        '使用固定比例的止损止盈设置'
                                    }
                                </small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-title">手动执行决策</h6>
                                <p class="card-text">立即执行一次AI交易决策</p>
                                <button class="btn btn-primary" onclick="executeDecision()">
                                    <i class="fas fa-brain"></i>
                                    执行AI决策
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-title">性能评估</h6>
                                <p class="card-text">评估当前策略的交易表现</p>
                                <button class="btn btn-info" onclick="evaluatePerformance()">
                                    <i class="fas fa-chart-line"></i>
                                    评估表现
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="alert alert-info mt-3">
                    <i class="fas fa-info-circle"></i>
                    <strong>智能体交易说明：</strong>
                    <ul class="mb-0 mt-2">
                        <li>AI将结合您的交易策略、市场分析和历史表现做出决策</li>
                        <li>系统会根据上述设置自动计算并应用止盈止损</li>
                        <li>动态调整功能可根据实时市场条件优化风险控制</li>
                        <li>建议定期评估表现，优化策略参数</li>
                    </ul>
                </div>
            `;
        }
    })
    .catch(error => {
        console.error('获取策略信息失败:', error);
        panel.innerHTML = `
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i>
                无法加载策略详情，请重试
            </div>
        `;
    });
}

// 启动交易
function startTrading(strategyId) {
    if (!confirm('确定要启动智能体交易吗？系统将根据AI分析自动执行交易决策。')) {
        return;
    }

    fetch('/api/agent-trading/start', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            strategy_id: strategyId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('智能体交易已启动！');
            currentSession = data.session_id;
            loadStrategies();
            loadSessions();

            // 开始定期执行决策
            startDecisionLoop();
        } else {
            alert('启动失败: ' + data.error);
        }
    })
    .catch(error => {
        console.error('启动交易失败:', error);
        alert('启动失败，请重试');
    });
}

// 停止交易
function stopTrading(strategyId) {
    // 首先找到对应的会话ID
    fetch('/api/agent-trading/sessions')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const activeSession = data.sessions.find(s =>
                s.status === 'active' &&
                s.strategy_name.includes('智能体交易')
            );

            if (activeSession) {
                stopSession(activeSession.id);
            } else {
                alert('未找到活跃的交易会话');
            }
        }
    });
}

// 停止会话
function stopSession(sessionId) {
    if (!confirm('确定要停止这个交易会话吗？')) {
        return;
    }

    fetch('/api/agent-trading/stop', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            session_id: sessionId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('交易会话已停止');
            loadStrategies();
            loadSessions();

            // 停止决策循环
            if (decisionInterval) {
                clearInterval(decisionInterval);
                decisionInterval = null;
            }
        } else {
            alert('停止失败: ' + data.error);
        }
    })
    .catch(error => {
        console.error('停止会话失败:', error);
        alert('停止失败，请重试');
    });
}

// 选择会话
function selectSession(sessionId) {
    currentSession = sessionId;
    displayTradingControlPanel(null);
}

// 执行决策
function executeDecision() {
    if (!currentSession) {
        alert('请先选择一个活跃的交易会话');
        return;
    }

    const button = event.target;
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 执行中...';

    fetch('/api/agent-trading/execute', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            session_id: currentSession
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayDecisionResult(data);
            loadSessions(); // 刷新会话状态
        } else {
            alert('执行失败: ' + data.error);
        }
    })
    .catch(error => {
        console.error('执行决策失败:', error);
        alert('执行失败，请重试');
    })
    .finally(() => {
        button.disabled = false;
        button.innerHTML = '<i class="fas fa-brain"></i> 执行AI决策';
    });
}

// 显示决策结果
function displayDecisionResult(data) {
    const tbody = document.querySelector('#decisionsTable tbody');

    if (data.decisions && data.decisions.decisions) {
        tbody.innerHTML = ''; // 清空现有内容

        data.decisions.decisions.forEach(decision => {
            const execution = data.executions.find(e => e.symbol === decision.symbol);

            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${new Date().toLocaleTimeString()}</td>
                <td><strong>${decision.symbol}</strong></td>
                <td>
                    <span class="badge ${decision.action === 'buy' ? 'bg-success' : decision.action === 'sell' ? 'bg-danger' : 'bg-secondary'}">
                        ${decision.action === 'buy' ? '买入' : decision.action === 'sell' ? '卖出' : '持有'}
                    </span>
                </td>
                <td>
                    <div class="progress" style="height: 20px;">
                        <div class="progress-bar" style="width: ${decision.confidence * 100}%">
                            ${(decision.confidence * 100).toFixed(0)}%
                        </div>
                    </div>
                </td>
                <td><small>${decision.reasoning}</small></td>
                <td>
                    ${execution ?
                        `<span class="badge ${execution.success ? 'bg-success' : 'bg-danger'}">
                            ${execution.success ? '成功' : '失败'}
                        </span>` :
                        '<span class="badge bg-secondary">未执行</span>'
                    }
                </td>
            `;
            tbody.appendChild(row);
        });
    }
}

// 评估表现
function evaluatePerformance() {
    if (!currentSession) {
        alert('请先选择一个交易会话');
        return;
    }

    fetch('/api/agent-trading/evaluate', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            session_id: currentSession
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayPerformanceMetrics(data.evaluation);
        } else {
            alert('评估失败: ' + data.error);
        }
    })
    .catch(error => {
        console.error('评估表现失败:', error);
        alert('评估失败，请重试');
    });
}

// 显示性能指标
function displayPerformanceMetrics(metrics) {
    const container = document.getElementById('performanceMetrics');

    const winRate = (metrics.win_rate * 100).toFixed(1);
    const profitFactor = metrics.profit_factor === Infinity ? '∞' : metrics.profit_factor.toFixed(2);
    const maxDrawdown = (metrics.max_drawdown * 100).toFixed(1);

    container.innerHTML = `
        <div class="row">
            <div class="col-md-3">
                <div class="text-center">
                    <div class="h4 ${metrics.total_profit >= 0 ? 'text-success' : 'text-danger'}">
                        ${metrics.total_profit >= 0 ? '+' : ''}$${metrics.total_profit.toFixed(2)}
                    </div>
                    <small class="text-muted">总盈亏</small>
                </div>
            </div>
            <div class="col-md-3">
                <div class="text-center">
                    <div class="h4 text-info">${winRate}%</div>
                    <small class="text-muted">胜率</small>
                </div>
            </div>
            <div class="col-md-3">
                <div class="text-center">
                    <div class="h4 text-primary">${profitFactor}</div>
                    <small class="text-muted">盈亏比</small>
                </div>
            </div>
            <div class="col-md-3">
                <div class="text-center">
                    <div class="h4 text-warning">${maxDrawdown}%</div>
                    <small class="text-muted">最大回撤</small>
                </div>
            </div>
        </div>

        <div class="row mt-3">
            <div class="col-md-4">
                <small class="text-muted">总交易次数</small>
                <div class="fw-bold">${metrics.total_trades}</div>
            </div>
            <div class="col-md-4">
                <small class="text-muted">平均每笔盈亏</small>
                <div class="fw-bold ${metrics.avg_profit_per_trade >= 0 ? 'text-success' : 'text-danger'}">
                    $${metrics.avg_profit_per_trade.toFixed(2)}
                </div>
            </div>
            <div class="col-md-4">
                <small class="text-muted">浮动盈亏</small>
                <div class="fw-bold ${metrics.floating_pnl >= 0 ? 'text-success' : 'text-danger'}">
                    ${metrics.floating_pnl >= 0 ? '+' : ''}$${metrics.floating_pnl.toFixed(2)}
                </div>
            </div>
        </div>
    `;
}

// 开始决策循环
function startDecisionLoop() {
    if (decisionInterval) {
        clearInterval(decisionInterval);
    }

    // 每5分钟执行一次决策
    decisionInterval = setInterval(() => {
        if (currentSession) {
            executeDecision();
        }
    }, 300000); // 5分钟
}

// 设置交易模式开关
function setupTradingModeSwitch() {
    const tradingModeSwitch = document.getElementById('tradingModeSwitch');
    const tradingModeLabel = document.getElementById('tradingModeLabel');
    const tradingModeDescription = document.getElementById('tradingModeDescription');

    tradingModeSwitch.addEventListener('change', function() {
        if (this.checked) {
            // 模拟交易模式
            tradingModeLabel.textContent = '模拟交易';
            tradingModeDescription.textContent = '使用虚拟资金进行交易，无真实风险';
            tradingModeLabel.className = 'form-check-label text-success';
        } else {
            // 真实交易模式
            tradingModeLabel.textContent = '真实交易';
            tradingModeDescription.textContent = '使用真实资金进行交易，请谨慎操作';
            tradingModeLabel.className = 'form-check-label text-danger';
        }
    });
}

// 加载系统配置的AI模型
function loadAIModels() {
    fetch('/api/ai-models/list')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const select = document.getElementById('aiModel');
            select.innerHTML = '<option value="">请选择AI模型</option>';

            if (data.models && data.models.length > 0) {
                // 按来源分组显示
                const systemModels = data.models.filter(m => m.source === 'system_default');
                const userModels = data.models.filter(m => m.source === 'user_config');

                // 添加系统默认模型
                if (systemModels.length > 0) {
                    const systemGroup = document.createElement('optgroup');
                    systemGroup.label = '系统配置模型';

                    systemModels.forEach(model => {
                        const option = document.createElement('option');
                        option.value = model.id;
                        option.textContent = `${model.name} (${model.provider})`;
                        if (model.status === 'active') {
                            option.textContent += ' ✓';
                        }
                        systemGroup.appendChild(option);
                    });

                    select.appendChild(systemGroup);
                }

                // 添加用户配置模型
                if (userModels.length > 0) {
                    const userGroup = document.createElement('optgroup');
                    userGroup.label = '用户配置模型';

                    userModels.forEach(model => {
                        const option = document.createElement('option');
                        option.value = model.id;
                        option.textContent = `${model.name} (${model.provider})`;
                        if (model.status === 'active') {
                            option.textContent += ' ✓';
                        }
                        userGroup.appendChild(option);
                    });

                    select.appendChild(userGroup);
                }

                console.log(`✅ 加载了 ${data.models.length} 个AI模型 (系统: ${systemModels.length}, 用户: ${userModels.length})`);
            } else {
                console.warn('⚠️ 未找到AI模型配置，使用默认选项');
                loadDefaultAIModels();
            }
        } else {
            console.error('加载AI模型失败:', data.error);
            // 如果API不存在，使用默认选项
            loadDefaultAIModels();
        }
    })
    .catch(error => {
        console.error('加载AI模型失败:', error);
        // 如果请求失败，使用默认选项
        loadDefaultAIModels();
    });
}

// 加载默认AI模型选项
function loadDefaultAIModels() {
    const select = document.getElementById('aiModel');
    select.innerHTML = `
        <option value="">请选择AI模型</option>
        <option value="deepseek_v3">DeepSeek V3</option>
        <option value="openai_gpt4">OpenAI GPT-4</option>
        <option value="claude_3">Claude 3</option>
        <option value="qwen_max">通义千问</option>
        <option value="gemini_pro">Gemini Pro</option>
    `;
}

// 加载已训练的AI策略
function loadTrainedAIStrategies() {
    fetch('/api/ai-strategies/trained')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const select = document.getElementById('aiStrategyId');
            select.innerHTML = '<option value="">无关联策略</option>';

            if (data.strategies && data.strategies.length > 0) {
                data.strategies.forEach(strategy => {
                    const option = document.createElement('option');
                    option.value = strategy.id;

                    // 构建显示文本
                    let displayText = strategy.name;
                    if (strategy.ai_model) {
                        displayText += ` (${strategy.ai_model})`;
                    }
                    if (strategy.accuracy) {
                        displayText += ` - 准确率: ${(strategy.accuracy * 100).toFixed(1)}%`;
                    }
                    if (strategy.status === 'completed') {
                        displayText += ' ✓';
                    }

                    option.textContent = displayText;
                    option.title = strategy.description || ''; // 添加悬停提示
                    select.appendChild(option);
                });

                console.log(`✅ 加载了 ${data.strategies.length} 个已训练的AI策略`);
            } else {
                // 添加提示选项
                const option = document.createElement('option');
                option.value = '';
                option.textContent = '暂无已训练的AI策略，请先到AI训练页面训练策略';
                option.disabled = true;
                select.appendChild(option);

                console.log('ℹ️ 暂无已训练的AI策略');
            }
        } else {
            console.error('加载训练策略失败:', data.error);

            // 显示错误提示
            const select = document.getElementById('aiStrategyId');
            const option = document.createElement('option');
            option.value = '';
            option.textContent = '加载AI策略失败，请刷新页面重试';
            option.disabled = true;
            select.appendChild(option);
        }
    })
    .catch(error => {
        console.error('加载训练策略失败:', error);

        // 显示网络错误提示
        const select = document.getElementById('aiStrategyId');
        const option = document.createElement('option');
        option.value = '';
        option.textContent = '网络错误，无法加载AI策略';
        option.disabled = true;
        select.appendChild(option);
    });
}

// 删除策略
function deleteStrategy(strategyId) {
    if (!confirm('⚠️ 确定要删除这个交易策略吗？\n\n此操作将：\n- 永久删除策略配置\n- 停止相关的交易活动\n- 无法撤销\n\n请确认您要继续')) {
        return;
    }

    // 二次确认
    if (!confirm('🔴 最后确认：您真的要删除这个策略吗？\n\n删除后无法恢复！')) {
        return;
    }

    fetch(`/api/agent-trading/strategies/${strategyId}`, {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('✅ 策略删除成功', 'success');
            loadStrategies(); // 重新加载策略列表
        } else {
            showNotification(`❌ 删除失败: ${data.error}`, 'error');
        }
    })
    .catch(error => {
        console.error('删除策略失败:', error);
        showNotification('❌ 删除策略失败，请重试', 'error');
    });
}

// 修改止盈止损设置
function editStopLossSettings(strategyId) {
    // 获取当前策略信息
    fetch(`/api/agent-trading/strategies/${strategyId}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const strategy = data.strategy;

            // 创建修改对话框
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-shield-alt"></i> 修改止盈止损设置
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="mb-3">
                                <label class="form-label">
                                    <i class="fas fa-arrow-down text-danger"></i> 智能止损比例 (%)
                                </label>
                                <input type="number" class="form-control" id="editStopLossPercent"
                                       value="${strategy.stop_loss_percent || 2.0}" min="0.5" max="10" step="0.1">
                                <small class="text-muted">建议范围: 1-5%</small>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">
                                    <i class="fas fa-arrow-up text-success"></i> 智能止盈比例 (%)
                                </label>
                                <input type="number" class="form-control" id="editTakeProfitPercent"
                                       value="${strategy.take_profit_percent || 5.0}" min="1" max="20" step="0.1">
                                <small class="text-muted">建议范围: 3-10%</small>
                            </div>

                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="editDynamicStopLoss"
                                           ${strategy.dynamic_stop_loss ? 'checked' : ''}>
                                    <label class="form-check-label" for="editDynamicStopLoss">
                                        <i class="fas fa-robot"></i> 启用AI动态调整
                                    </label>
                                </div>
                                <small class="text-muted">AI将根据市场条件动态调整止损止盈水平</small>
                            </div>

                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i>
                                <strong>说明：</strong>
                                <ul class="mb-0 mt-2">
                                    <li>止损比例：当亏损达到此比例时自动平仓</li>
                                    <li>止盈比例：当盈利达到此比例时自动平仓</li>
                                    <li>动态调整：AI根据波动率和趋势智能调整参数</li>
                                </ul>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary" onclick="saveStopLossSettings(${strategyId})">
                                <i class="fas fa-save"></i> 保存设置
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            const modalInstance = new bootstrap.Modal(modal);
            modalInstance.show();

            // 模态框关闭时移除DOM元素
            modal.addEventListener('hidden.bs.modal', function() {
                document.body.removeChild(modal);
            });
        }
    })
    .catch(error => {
        console.error('获取策略信息失败:', error);
        alert('获取策略信息失败，请重试');
    });
}

// 保存止盈止损设置
function saveStopLossSettings(strategyId) {
    const stopLossPercent = parseFloat(document.getElementById('editStopLossPercent').value);
    const takeProfitPercent = parseFloat(document.getElementById('editTakeProfitPercent').value);
    const dynamicStopLoss = document.getElementById('editDynamicStopLoss').checked;

    if (stopLossPercent < 0.5 || stopLossPercent > 10) {
        alert('止损比例必须在0.5%-10%之间');
        return;
    }

    if (takeProfitPercent < 1 || takeProfitPercent > 20) {
        alert('止盈比例必须在1%-20%之间');
        return;
    }

    const updateData = {
        stop_loss_percent: stopLossPercent,
        take_profit_percent: takeProfitPercent,
        dynamic_stop_loss: dynamicStopLoss
    };

    fetch(`/api/agent-trading/strategies/${strategyId}/stop-loss`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 关闭模态框
            const modal = document.querySelector('.modal.show');
            if (modal) {
                bootstrap.Modal.getInstance(modal).hide();
            }

            showNotification('✅ 止盈止损设置已更新', 'success');

            // 刷新控制面板显示
            displayTradingControlPanel(strategyId);
        } else {
            alert('保存失败: ' + data.error);
        }
    })
    .catch(error => {
        console.error('保存设置失败:', error);
        alert('保存失败，请重试');
    });
}

</script>
{% endblock %}
