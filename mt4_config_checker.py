#!/usr/bin/env python3
"""
MT4配置状态检查工具
详细检查MT4专家顾问配置状态
"""

import os
import winreg
import configparser
from pathlib import Path

def check_mt4_expert_settings():
    """检查MT4专家顾问设置"""
    print("🔍 检查MT4专家顾问配置")
    print("=" * 50)
    
    # EC Markets MT4的可能配置路径
    possible_paths = [
        os.path.expanduser(r"~\AppData\Roaming\MetaQuotes\Terminal"),
        r"C:\Program Files (x86)\EC Markets MetaTrader 4 Terminal\config",
        r"C:\Program Files (x86)\EC Markets MetaTrader 4 Terminal\MQL4",
    ]
    
    config_found = False
    
    for base_path in possible_paths:
        if os.path.exists(base_path):
            print(f"✅ 找到配置目录: {base_path}")
            
            # 查找配置文件
            for root, dirs, files in os.walk(base_path):
                for file in files:
                    if file.lower() in ['terminal.ini', 'common.ini', 'experts.ini']:
                        config_file = os.path.join(root, file)
                        print(f"   配置文件: {config_file}")
                        
                        try:
                            # 尝试读取配置文件
                            with open(config_file, 'r', encoding='utf-8', errors='ignore') as f:
                                content = f.read()
                                
                            # 检查关键设置
                            if 'AllowDllImport' in content:
                                print(f"   找到DLL导入设置")
                                config_found = True
                            if 'AllowLiveTrading' in content:
                                print(f"   找到自动交易设置")
                                config_found = True
                                
                        except Exception as e:
                            print(f"   无法读取配置文件: {e}")
    
    if not config_found:
        print("⚠️  未找到专家顾问配置文件")
        print("   这可能意味着需要手动配置")
    
    return config_found

def check_mt4_registry():
    """检查MT4注册表设置"""
    print("\n🔍 检查MT4注册表设置")
    print("=" * 50)
    
    registry_keys = [
        r"SOFTWARE\MetaQuotes\MetaTrader 4",
        r"SOFTWARE\WOW6432Node\MetaQuotes\MetaTrader 4",
        r"SOFTWARE\Classes\mt4file",
    ]
    
    found_settings = False
    
    for key_path in registry_keys:
        try:
            key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, key_path)
            print(f"✅ 找到注册表项: {key_path}")
            
            # 尝试读取一些值
            try:
                install_path = winreg.QueryValueEx(key, "InstallPath")[0]
                print(f"   安装路径: {install_path}")
                found_settings = True
            except:
                pass
            
            winreg.CloseKey(key)
            
        except Exception as e:
            continue
    
    if not found_settings:
        print("⚠️  未找到MT4注册表设置")
    
    return found_settings

def provide_detailed_configuration_guide():
    """提供详细的配置指导"""
    print("\n📋 详细配置指导")
    print("=" * 50)
    
    print("🎯 确保MT4专家顾问正确配置:")
    print()
    print("步骤1: 打开MT4专家顾问设置")
    print("1. 在EC Markets MT4中，点击菜单栏 '工具'")
    print("2. 选择 '选项' (Options)")
    print("3. 在弹出的窗口中，点击 '专家顾问' 选项卡")
    print()
    
    print("步骤2: 配置专家顾问选项")
    print("确保以下选项都已勾选:")
    print("✅ 允许自动交易 (Allow automated trading)")
    print("✅ 允许DLL导入 (Allow DLL imports)")
    print("✅ 允许导入外部专家顾问 (Allow imports of external experts)")
    print("✅ 允许实时自动交易 (Allow live automated trading) - 如果有此选项")
    print()
    
    print("步骤3: 保存设置")
    print("1. 点击 '确定' 按钮保存设置")
    print("2. 设置会立即生效，但建议重启MT4")
    print()
    
    print("步骤4: 重启MT4终端")
    print("1. 完全关闭MT4终端")
    print("2. 等待5-10秒")
    print("3. 重新启动MT4")
    print("4. 确保所有设置都已保存")
    print()
    
    print("步骤5: 验证设置")
    print("1. 重新打开 工具 -> 选项 -> 专家顾问")
    print("2. 确认所有选项仍然勾选")
    print("3. 如果设置丢失，重复步骤2-4")

def create_mt4_restart_helper():
    """创建MT4重启助手脚本"""
    restart_script = '''@echo off
echo 🔄 MT4重启助手
echo 帮助您正确重启MT4以应用专家顾问设置
echo ========================================

echo.
echo 📋 重启前检查:
echo 1. 确保已在MT4中配置专家顾问设置
echo 2. 确保已点击"确定"保存设置
echo.

pause

echo.
echo 🛑 正在关闭MT4...
taskkill /F /IM terminal.exe >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ MT4进程已关闭
) else (
    echo ℹ️  没有找到运行中的MT4进程
)

echo.
echo ⏳ 等待10秒确保完全关闭...
timeout /t 10 /nobreak >nul

echo.
echo 🚀 重新启动MT4...
if exist "C:\\Program Files (x86)\\EC Markets MetaTrader 4 Terminal\\terminal.exe" (
    start "" "C:\\Program Files (x86)\\EC Markets MetaTrader 4 Terminal\\terminal.exe"
    echo ✅ MT4已重新启动
) else (
    echo ❌ 未找到MT4可执行文件
    pause
    exit /b 1
)

echo.
echo 📋 重启后请执行:
echo 1. 等待MT4完全加载
echo 2. 检查专家顾问设置是否保持
echo 3. 运行连接测试: python mt4_reconnection_test.py
echo.

echo ✅ 重启完成!
pause
'''
    
    with open('mt4_restart_helper.bat', 'w', encoding='utf-8') as f:
        f.write(restart_script)
    
    print(f"\n📄 已创建MT4重启助手: mt4_restart_helper.bat")
    print("   双击运行此脚本可以正确重启MT4")

def check_common_issues():
    """检查常见问题"""
    print("\n🔍 检查常见问题")
    print("=" * 50)
    
    issues_found = []
    
    # 检查Windows版本
    import platform
    windows_version = platform.platform()
    print(f"Windows版本: {windows_version}")
    
    if "Windows-10" in windows_version or "Windows-11" in windows_version:
        print("✅ Windows版本兼容")
    else:
        print("⚠️  Windows版本可能影响兼容性")
        issues_found.append("Windows版本")
    
    # 检查Python版本
    python_version = platform.python_version()
    print(f"Python版本: {python_version}")
    
    if python_version.startswith('3.'):
        print("✅ Python版本兼容")
    else:
        print("⚠️  Python版本可能不兼容")
        issues_found.append("Python版本")
    
    # 检查MetaTrader5库
    try:
        import MetaTrader5 as mt5
        print(f"✅ MetaTrader5库已安装")
    except ImportError:
        print("❌ MetaTrader5库未安装")
        issues_found.append("MetaTrader5库")
    
    if issues_found:
        print(f"\n⚠️  发现 {len(issues_found)} 个潜在问题:")
        for issue in issues_found:
            print(f"   - {issue}")
    else:
        print(f"\n✅ 未发现明显的兼容性问题")
    
    return len(issues_found) == 0

def main():
    """主函数"""
    print("🔧 MT4配置状态检查工具")
    print("详细分析MT4专家顾问配置问题")
    print("=" * 60)
    
    # 检查配置文件
    config_ok = check_mt4_expert_settings()
    
    # 检查注册表
    registry_ok = check_mt4_registry()
    
    # 检查常见问题
    compatibility_ok = check_common_issues()
    
    # 提供配置指导
    provide_detailed_configuration_guide()
    
    # 创建重启助手
    create_mt4_restart_helper()
    
    print("\n" + "=" * 60)
    print("📋 检查总结:")
    
    if config_ok and registry_ok and compatibility_ok:
        print("✅ 系统配置看起来正常")
        print("🔧 问题可能在于MT4专家顾问设置")
    else:
        print("⚠️  发现一些配置问题")
        print("🔧 请按照上述指导进行配置")
    
    print("\n🎯 推荐操作顺序:")
    print("1. 按照详细指导配置MT4专家顾问设置")
    print("2. 运行 mt4_restart_helper.bat 重启MT4")
    print("3. 运行 python mt4_reconnection_test.py 重新测试")
    print("4. 如果仍然失败，使用MateTrade4演示模式")

if __name__ == "__main__":
    main()
