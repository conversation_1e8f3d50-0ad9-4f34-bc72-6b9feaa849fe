#!/usr/bin/env python3
"""
修复MT5数据获取问题
"""

import MetaTrader5 as mt5
import pandas as pd
from datetime import datetime, timedelta
import numpy as np

def test_mt5_connection():
    """测试MT5连接"""
    
    print("🔍 测试MT5连接")
    print("=" * 50)
    
    try:
        # 初始化MT5
        if not mt5.initialize():
            print("❌ MT5初始化失败")
            print(f"错误代码: {mt5.last_error()}")
            return False
        
        print("✅ MT5初始化成功")
        
        # 获取账户信息
        account_info = mt5.account_info()
        if account_info:
            print(f"✅ 账户信息获取成功")
            print(f"   账户: {account_info.login}")
            print(f"   服务器: {account_info.server}")
            print(f"   余额: {account_info.balance}")
        else:
            print("⚠️ 无法获取账户信息")
        
        # 测试获取品种信息
        symbol_info = mt5.symbol_info("XAUUSD")
        if symbol_info:
            print(f"✅ XAUUSD品种信息获取成功")
            print(f"   点差: {symbol_info.spread}")
            print(f"   最小手数: {symbol_info.volume_min}")
        else:
            print("❌ 无法获取XAUUSD品种信息")
            return False
        
        # 测试获取历史数据
        print(f"\n📊 测试历史数据获取:")
        
        # 获取最近100个H1数据
        rates = mt5.copy_rates_from_pos("XAUUSD", mt5.TIMEFRAME_H1, 0, 100)
        
        if rates is not None and len(rates) > 0:
            print(f"✅ 成功获取 {len(rates)} 条H1数据")
            
            # 显示最新几条数据
            df = pd.DataFrame(rates)
            df['time'] = pd.to_datetime(df['time'], unit='s')
            
            print(f"📈 最新5条数据:")
            for i in range(min(5, len(df))):
                row = df.iloc[-(i+1)]
                print(f"   {row['time']}: O={row['open']:.2f}, H={row['high']:.2f}, L={row['low']:.2f}, C={row['close']:.2f}")
            
            return True
        else:
            print("❌ 无法获取历史数据")
            print(f"错误代码: {mt5.last_error()}")
            return False
            
    except Exception as e:
        print(f"❌ MT5连接测试失败: {e}")
        return False
    finally:
        mt5.shutdown()

def test_data_preparation():
    """测试数据准备过程"""
    
    print(f"\n🔧 测试数据准备过程")
    print("=" * 50)
    
    try:
        if not mt5.initialize():
            print("❌ MT5初始化失败")
            return False
        
        # 模拟训练数据准备
        symbol = "XAUUSD"
        timeframe = mt5.TIMEFRAME_H1
        
        print(f"📊 准备 {symbol} 的训练数据...")
        
        # 获取足够的历史数据（至少1000条）
        rates = mt5.copy_rates_from_pos(symbol, timeframe, 0, 1000)
        
        if rates is None or len(rates) < 100:
            print(f"❌ 历史数据不足: {len(rates) if rates is not None else 0} 条")
            return False
        
        print(f"✅ 获取到 {len(rates)} 条历史数据")
        
        # 转换为DataFrame
        df = pd.DataFrame(rates)
        df['time'] = pd.to_datetime(df['time'], unit='s')
        
        # 检查数据质量
        print(f"📈 数据质量检查:")
        print(f"   时间范围: {df['time'].min()} 到 {df['time'].max()}")
        print(f"   价格范围: {df['close'].min():.2f} - {df['close'].max():.2f}")
        print(f"   是否有缺失值: {df.isnull().sum().sum()}")
        
        # 准备特征
        features = ['close', 'volume']
        available_features = [f for f in features if f in df.columns]
        
        print(f"   可用特征: {available_features}")
        
        if len(available_features) == 0:
            print(f"❌ 没有可用的特征列")
            return False
        
        # 创建序列数据
        sequence_length = 10
        
        print(f"🔄 创建序列数据 (长度: {sequence_length})...")
        
        # 准备特征数据
        feature_data = df[available_features].values
        
        # 标准化
        from sklearn.preprocessing import StandardScaler
        scaler = StandardScaler()
        feature_data_scaled = scaler.fit_transform(feature_data)
        
        # 创建序列
        X, y = [], []
        
        for i in range(sequence_length, len(feature_data_scaled)):
            X.append(feature_data_scaled[i-sequence_length:i])
            y.append(feature_data_scaled[i, 0])  # 预测收盘价
        
        X = np.array(X)
        y = np.array(y)
        
        print(f"✅ 序列数据创建成功:")
        print(f"   X形状: {X.shape}")
        print(f"   y形状: {y.shape}")
        
        if len(X) < 50:
            print(f"⚠️ 训练样本较少: {len(X)} 个")
        else:
            print(f"✅ 训练样本充足: {len(X)} 个")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据准备测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False
    finally:
        mt5.shutdown()

def create_mock_training_data():
    """创建模拟训练数据（用于测试）"""
    
    print(f"\n🎭 创建模拟训练数据")
    print("=" * 50)
    
    try:
        # 创建模拟的OHLCV数据
        np.random.seed(42)  # 确保可重复
        
        n_samples = 1000
        base_price = 2650.0
        
        # 生成价格序列
        price_changes = np.random.normal(0, 0.01, n_samples)
        prices = [base_price]
        
        for change in price_changes:
            new_price = prices[-1] * (1 + change)
            prices.append(new_price)
        
        prices = np.array(prices[1:])  # 去掉初始价格
        
        # 生成OHLCV数据
        data = []
        for i, close in enumerate(prices):
            # 生成开盘价（基于前一个收盘价）
            if i == 0:
                open_price = base_price
            else:
                open_price = prices[i-1]
            
            # 生成高低价
            volatility = abs(np.random.normal(0, 0.005))
            high = max(open_price, close) * (1 + volatility)
            low = min(open_price, close) * (1 - volatility)
            
            # 生成成交量
            volume = np.random.randint(100, 1000)
            
            data.append({
                'time': datetime.now() - timedelta(hours=n_samples-i),
                'open': open_price,
                'high': high,
                'low': low,
                'close': close,
                'volume': volume
            })
        
        df = pd.DataFrame(data)
        
        print(f"✅ 创建了 {len(df)} 条模拟数据")
        print(f"   时间范围: {df['time'].min()} 到 {df['time'].max()}")
        print(f"   价格范围: {df['close'].min():.2f} - {df['close'].max():.2f}")
        
        # 保存到CSV文件用于测试
        df.to_csv('mock_training_data.csv', index=False)
        print(f"✅ 模拟数据已保存到 mock_training_data.csv")
        
        return df
        
    except Exception as e:
        print(f"❌ 创建模拟数据失败: {e}")
        return None

def main():
    """主函数"""
    
    print("🔧 MT5数据获取问题诊断和修复")
    print("=" * 80)
    
    # 测试MT5连接
    mt5_ok = test_mt5_connection()
    
    if mt5_ok:
        # 测试数据准备
        data_ok = test_data_preparation()
        
        if data_ok:
            print(f"\n🎉 MT5数据获取正常!")
            print(f"✅ 连接正常")
            print(f"✅ 数据获取正常")
            print(f"✅ 数据准备正常")
            
            print(f"\n💡 建议:")
            print(f"• 现在可以尝试重新启动训练")
            print(f"• 如果仍然失败，可能是其他环境问题")
        else:
            print(f"\n⚠️ MT5连接正常但数据准备失败")
            print(f"💡 可能的原因:")
            print(f"• 数据格式问题")
            print(f"• 特征处理错误")
            print(f"• 序列创建逻辑问题")
    else:
        print(f"\n❌ MT5连接有问题")
        print(f"💡 建议的解决方案:")
        print(f"• 检查MT5是否正在运行")
        print(f"• 确认MT5账户已登录")
        print(f"• 检查XAUUSD品种是否可用")
        print(f"• 尝试重启MT5")
        
        # 创建模拟数据作为备选方案
        print(f"\n🎭 创建模拟数据作为备选方案:")
        mock_data = create_mock_training_data()
        
        if mock_data is not None:
            print(f"✅ 可以使用模拟数据进行训练测试")
    
    print(f"\n📋 总结")
    print("=" * 80)
    
    print(f"🔧 已修复的问题:")
    print(f"✅ task_id 未定义错误")
    print(f"✅ 训练进度更新机制")
    print(f"✅ 训练任务启动流程")
    
    print(f"\n🎯 当前状态:")
    if mt5_ok:
        print(f"✅ MT5连接正常，可以获取真实数据")
    else:
        print(f"❌ MT5连接有问题，建议使用模拟数据测试")
    
    print(f"\n🚀 下一步:")
    print(f"1. 如果MT5正常，直接在页面上重新启动训练")
    print(f"2. 如果MT5有问题，先解决连接问题")
    print(f"3. 可以使用最简单的配置进行测试")
    print(f"4. 成功后逐步增加复杂度")

if __name__ == '__main__':
    main()
