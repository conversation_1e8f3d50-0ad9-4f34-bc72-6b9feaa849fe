#!/usr/bin/env python3
"""
创建trades表 - 修复策略交易服务的数据库错误
"""

import sqlite3
import os
from datetime import datetime

def create_trades_table():
    """创建trades表"""
    db_path = 'trading_system.db'
    
    try:
        print("🔄 连接数据库...")
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查表是否已存在
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='trades'
        """)
        
        if cursor.fetchone():
            print("✅ trades表已存在")
            
            # 显示表结构
            cursor.execute("PRAGMA table_info(trades)")
            columns = cursor.fetchall()
            print("\n📋 当前表结构:")
            for col in columns:
                print(f"  - {col[1]}: {col[2]} {'(主键)' if col[5] else ''}")
        else:
            print("📊 创建trades表...")
            
            # 创建trades表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS trades (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL DEFAULT 1,
                    symbol VARCHAR(20) NOT NULL,
                    action VARCHAR(10) NOT NULL,
                    volume REAL NOT NULL,
                    entry_price REAL NOT NULL,
                    stop_loss REAL,
                    take_profit REAL,
                    close_price REAL,
                    profit REAL DEFAULT 0.0,
                    status VARCHAR(20) DEFAULT 'open',
                    strategy_type VARCHAR(50),
                    strategy_name VARCHAR(100),
                    comment VARCHAR(200),
                    mt5_order_id BIGINT,
                    open_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                    close_time DATETIME,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            print("✅ trades表创建成功")
            
            # 显示新表结构
            cursor.execute("PRAGMA table_info(trades)")
            columns = cursor.fetchall()
            print("\n📋 新表结构:")
            for col in columns:
                print(f"  - {col[1]}: {col[2]} {'(主键)' if col[5] else ''}")
        
        # 创建索引以提高查询性能
        print("\n🔧 创建索引...")
        
        indexes = [
            ("idx_trades_user_id", "user_id"),
            ("idx_trades_symbol", "symbol"),
            ("idx_trades_status", "status"),
            ("idx_trades_strategy_type", "strategy_type"),
            ("idx_trades_created_at", "created_at")
        ]
        
        for index_name, column in indexes:
            try:
                cursor.execute(f"CREATE INDEX IF NOT EXISTS {index_name} ON trades ({column})")
                print(f"  ✅ 索引 {index_name} 创建成功")
            except Exception as e:
                print(f"  ⚠️ 索引 {index_name} 创建失败: {e}")
        
        conn.commit()
        
        # 验证表创建
        cursor.execute("SELECT COUNT(*) FROM trades")
        count = cursor.fetchone()[0]
        print(f"\n📊 trades表记录数: {count}")
        
        conn.close()
        
        print("\n✅ trades表初始化完成！")
        return True
        
    except Exception as e:
        print(f"❌ 创建trades表失败: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

def verify_database_structure():
    """验证数据库结构"""
    db_path = 'trading_system.db'
    
    try:
        print("\n🔍 验证数据库结构...")
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取所有表
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name NOT LIKE 'sqlite_%'
            ORDER BY name
        """)
        
        tables = cursor.fetchall()
        print(f"\n📋 数据库中的表 ({len(tables)} 个):")
        
        for table in tables:
            table_name = table[0]
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            print(f"  - {table_name}: {count} 条记录")
        
        # 检查关键表是否存在
        required_tables = ['user', 'strategy', 'trades']
        missing_tables = []
        
        table_names = [t[0] for t in tables]
        for required_table in required_tables:
            if required_table not in table_names:
                missing_tables.append(required_table)
        
        if missing_tables:
            print(f"\n⚠️ 缺少以下关键表: {', '.join(missing_tables)}")
        else:
            print(f"\n✅ 所有关键表都存在")
        
        conn.close()
        return len(missing_tables) == 0
        
    except Exception as e:
        print(f"❌ 验证数据库结构失败: {e}")
        return False

def insert_sample_trade():
    """插入示例交易记录"""
    db_path = 'trading_system.db'
    
    try:
        print("\n📝 插入示例交易记录...")
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查是否已有记录
        cursor.execute("SELECT COUNT(*) FROM trades")
        count = cursor.fetchone()[0]
        
        if count > 0:
            print(f"✅ trades表已有 {count} 条记录，跳过示例数据插入")
        else:
            # 插入示例记录
            sample_trade = (
                1,  # user_id
                'XAUUSD',  # symbol
                'buy',  # action
                0.01,  # volume
                2650.50,  # entry_price
                2640.00,  # stop_loss
                2670.00,  # take_profit
                None,  # close_price
                0.0,  # profit
                'open',  # status
                'strategy',  # strategy_type
                'AI_Test_Strategy',  # strategy_name
                'AI_TEST_TRADE',  # comment
                None,  # mt5_order_id
                datetime.now().isoformat(),  # open_time
                None,  # close_time
                datetime.now().isoformat(),  # created_at
                datetime.now().isoformat()   # updated_at
            )
            
            cursor.execute("""
                INSERT INTO trades (
                    user_id, symbol, action, volume, entry_price,
                    stop_loss, take_profit, close_price, profit, status,
                    strategy_type, strategy_name, comment, mt5_order_id,
                    open_time, close_time, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, sample_trade)
            
            conn.commit()
            print("✅ 示例交易记录插入成功")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 插入示例交易记录失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("策略交易系统 - trades表创建")
    print("=" * 60)
    
    # 检查数据库文件是否存在
    db_path = 'trading_system.db'
    if not os.path.exists(db_path):
        print(f"⚠️ 数据库文件不存在: {db_path}")
        print("   请先运行 init_database.py 创建基础数据库")
        return False
    
    print(f"✅ 数据库文件存在: {db_path}")
    
    # 创建trades表
    if create_trades_table():
        print("\n" + "=" * 30)
        print("验证数据库结构")
        print("=" * 30)
        verify_database_structure()
        
        print("\n" + "=" * 30)
        print("插入示例数据")
        print("=" * 30)
        insert_sample_trade()
        
        print("\n🎉 trades表初始化完成！")
        print("现在策略交易服务可以正常保存交易记录了。")
        return True
    else:
        print("\n❌ trades表初始化失败")
        return False

if __name__ == '__main__':
    main()
