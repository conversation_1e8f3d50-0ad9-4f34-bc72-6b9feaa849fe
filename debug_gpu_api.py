#!/usr/bin/env python3
"""
调试GPU API返回格式
"""

import requests
import json

def debug_gpu_api():
    """调试GPU状态API"""
    
    print("🔍 调试GPU状态API")
    print("=" * 50)
    
    session = requests.Session()
    
    try:
        # 登录
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post('http://127.0.0.1:5000/login', data=login_data)
        if response.status_code != 200:
            print("❌ 登录失败")
            return
        
        print("✅ 登录成功")
        
        # 测试GPU状态API
        response = session.get('http://127.0.0.1:5000/api/deep-learning/gpu-status')
        
        print(f"📊 API响应状态: {response.status_code}")
        print(f"📊 响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"📊 完整响应内容:")
                print(json.dumps(result, indent=2, ensure_ascii=False))
                
                print(f"\n🔍 数据结构分析:")
                print(f"   success: {result.get('success')}")
                print(f"   error: {result.get('error')}")
                
                if 'gpu_status' in result:
                    gpu_status = result['gpu_status']
                    print(f"   gpu_status类型: {type(gpu_status)}")
                    print(f"   gpu_status内容: {gpu_status}")
                    
                    if isinstance(gpu_status, dict):
                        print(f"   gpu_status字段:")
                        for key, value in gpu_status.items():
                            print(f"     {key}: {value} ({type(value)})")
                else:
                    print(f"   ❌ 缺少gpu_status字段")
                
                # 检查其他可能的字段
                for key in result.keys():
                    if key not in ['success', 'error', 'gpu_status']:
                        print(f"   其他字段 {key}: {result[key]}")
                        
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
                print(f"原始响应: {response.text}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == '__main__':
    debug_gpu_api()
