{% extends "base.html" %}

{% block title %}SN加仓策略{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="fas fa-layer-group text-info"></i>
                    SN加仓策略
                </h2>
                <div class="badge bg-info fs-6">
                    <i class="fas fa-info-circle"></i>
                    自创分层加仓系统
                </div>
            </div>
        </div>
    </div>

    <!-- 策略说明卡片 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-lightbulb"></i>
                        SN加仓策略说明
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">
                                <i class="fas fa-chart-line"></i>
                                策略原理
                            </h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success"></i> 适用于下午及晚上的自动交易</li>
                                <li><i class="fas fa-check text-success"></i> 基于亚洲交易时段(7:00-当前)趋势进行反向操作</li>
                                <li><i class="fas fa-check text-success"></i> 亚洲时段涨势 → 程序做跌</li>
                                <li><i class="fas fa-check text-success"></i> 亚洲时段跌势 → 程序做涨</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-warning">
                                <i class="fas fa-layer-group"></i>
                                分层设置
                            </h6>
                            <div class="table-responsive">
                                <table class="table table-sm table-bordered">
                                    <thead class="table-light">
                                        <tr>
                                            <th>组别</th>
                                            <th>订单</th>
                                            <th>止损</th>
                                            <th>止盈</th>
                                            <th>手数</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr class="table-success">
                                            <td>第1组</td>
                                            <td>1,2</td>
                                            <td>5点</td>
                                            <td>10点</td>
                                            <td>0.01</td>
                                        </tr>
                                        <tr class="table-warning">
                                            <td>第2组</td>
                                            <td>3,4</td>
                                            <td>6点</td>
                                            <td>15点</td>
                                            <td>0.01</td>
                                        </tr>
                                        <tr class="table-danger">
                                            <td>第3组</td>
                                            <td>5,6</td>
                                            <td>8点</td>
                                            <td>30点</td>
                                            <td>0.01</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    
                    <div class="alert alert-warning mt-3">
                        <h6 class="alert-heading">
                            <i class="fas fa-exclamation-triangle"></i>
                            特殊规则
                        </h6>
                        <p class="mb-0">
                            如果第2组实现盈利自动平仓，但趋势未达到第3组止盈点，又返回到第2组止盈点时，
                            <strong class="text-danger">对第3组执行平仓</strong>。
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 上一轮执行状态显示 -->
    <div class="row mb-3" id="lastExecutionStatus" style="display: none;">
        <div class="col-12">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">
                            <i class="fas fa-history"></i>
                            上一轮SN加仓执行状态
                        </h6>
                        <button type="button" class="btn btn-sm btn-outline-light" onclick="hideLastExecutionStatus()">
                            <i class="fas fa-times"></i>
                            隐藏
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div id="lastExecutionContent">
                        <!-- 动态内容 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 操作步骤 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cogs"></i>
                        SN加仓配置
                    </h5>
                </div>
                <div class="card-body">
                    <!-- 步骤指示器 -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="progress-steps">
                                <div class="step active" id="step1">
                                    <div class="step-number">1</div>
                                    <div class="step-title">趋势判断</div>
                                </div>
                                <div class="step" id="step2">
                                    <div class="step-number">2</div>
                                    <div class="step-title">交易品种</div>
                                </div>
                                <div class="step" id="step3">
                                    <div class="step-number">3</div>
                                    <div class="step-title">参数确认</div>
                                </div>
                                <div class="step" id="step4">
                                    <div class="step-number">4</div>
                                    <div class="step-title">开始执行</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 步骤1：趋势判断 -->
                    <div class="step-content" id="content-step1">
                        <div class="card border-primary">
                            <div class="card-header bg-primary text-white">
                                <h6 class="mb-0">
                                    <i class="fas fa-chart-line"></i>
                                    步骤1：亚洲交易时段趋势判断
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">选择交易品种进行趋势分析</label>
                                            <select class="form-select" id="trendAnalysisSymbol">
                                                <option value="XAUUSD">黄金 (XAUUSD)</option>
                                                <option value="EURUSD">欧美 (EURUSD)</option>
                                                <option value="GBPUSD">镑美 (GBPUSD)</option>
                                                <option value="USDJPY">美日 (USDJPY)</option>
                                                <option value="USDCHF">美瑞 (USDCHF)</option>
                                            </select>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <button type="button" class="btn btn-primary" onclick="analyzeDayTrend()">
                                                <i class="fas fa-search"></i>
                                                分析亚洲时段趋势
                                            </button>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="trend-analysis-result" id="trendAnalysisResult" style="display: none;">
                                            <div class="alert alert-info">
                                                <h6 class="alert-heading">
                                                    <i class="fas fa-chart-bar"></i>
                                                    趋势分析结果
                                                </h6>
                                                <div id="trendResultContent"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="manual-trend-selection mt-3">
                                    <h6>或手动选择白天趋势：</h6>
                                    <div class="btn-group" role="group">
                                        <input type="radio" class="btn-check" name="dayTrend" id="trendUp" value="up">
                                        <label class="btn btn-outline-success" for="trendUp">
                                            <i class="fas fa-arrow-up"></i>
                                            白天上涨
                                        </label>
                                        
                                        <input type="radio" class="btn-check" name="dayTrend" id="trendDown" value="down">
                                        <label class="btn btn-outline-danger" for="trendDown">
                                            <i class="fas fa-arrow-down"></i>
                                            白天下跌
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="mt-3">
                                    <button type="button" class="btn btn-success" onclick="nextStep(2)" id="step1NextBtn" disabled>
                                        下一步
                                        <i class="fas fa-arrow-right"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 步骤2：交易品种选择 -->
                    <div class="step-content" id="content-step2" style="display: none;">
                        <div class="card border-warning">
                            <div class="card-header bg-warning text-dark">
                                <h6 class="mb-0">
                                    <i class="fas fa-coins"></i>
                                    步骤2：选择交易品种
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">交易品种</label>
                                            <select class="form-select" id="tradingSymbol">
                                                <option value="XAUUSD">黄金 (XAUUSD)</option>
                                                <option value="EURUSD">欧美 (EURUSD)</option>
                                                <option value="GBPUSD">镑美 (GBPUSD)</option>
                                                <option value="USDJPY">美日 (USDJPY)</option>
                                                <option value="USDCHF">美瑞 (USDCHF)</option>
                                            </select>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label class="form-label">基于趋势判断，程序将执行</label>
                                            <div class="alert alert-info" id="tradingDirectionInfo">
                                                <i class="fas fa-info-circle"></i>
                                                请先完成趋势判断
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="current-price-info" id="currentPriceInfo">
                                            <div class="d-flex justify-content-between align-items-center mb-2">
                                                <h6 class="mb-0">当前价格信息</h6>
                                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="refreshCurrentPriceStep2()">
                                                    <i class="fas fa-sync"></i>
                                                    刷新
                                                </button>
                                            </div>
                                            <div class="price-display" id="priceDisplayStep2">
                                                <div class="text-center text-muted">
                                                    <i class="fas fa-spinner fa-spin"></i>
                                                    正在获取价格...
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mt-3">
                                    <button type="button" class="btn btn-secondary" onclick="prevStep(1)">
                                        <i class="fas fa-arrow-left"></i>
                                        上一步
                                    </button>
                                    <button type="button" class="btn btn-success ms-2" onclick="nextStep(3)">
                                        下一步
                                        <i class="fas fa-arrow-right"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 步骤3：参数确认 -->
                    <div class="step-content" id="content-step3" style="display: none;">
                        <div class="card border-info">
                            <div class="card-header bg-info text-white">
                                <h6 class="mb-0">
                                    <i class="fas fa-cog"></i>
                                    步骤3：参数确认与调整
                                </h6>
                            </div>
                            <div class="card-body">
                                <!-- 当前价格显示 -->
                                <div class="row mb-3">
                                    <div class="col-12">
                                        <div class="alert alert-primary">
                                            <div class="row align-items-center">
                                                <div class="col-md-6">
                                                    <h6 class="mb-0">
                                                        <i class="fas fa-chart-line"></i>
                                                        当前价格信息
                                                    </h6>
                                                    <div id="currentPriceDisplay">
                                                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="refreshCurrentPrice()">
                                                            <i class="fas fa-sync"></i>
                                                            获取当前价格
                                                        </button>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="text-end">
                                                        <small class="text-muted">价格将用于计算止盈止损价格</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-9">
                                        <h6>
                                            <i class="fas fa-edit"></i>
                                            SN加仓参数配置
                                            <small class="text-muted">(可编辑)</small>
                                        </h6>
                                        <div class="table-responsive">
                                            <table class="table table-bordered table-hover">
                                                <thead class="table-dark">
                                                    <tr>
                                                        <th style="width: 70px;">组别</th>
                                                        <th style="width: 70px;">订单</th>
                                                        <th style="width: 70px;">方向</th>
                                                        <th style="width: 90px;">当前价格</th>
                                                        <th style="width: 70px;">手数</th>
                                                        <th style="width: 80px;">止损(点)</th>
                                                        <th style="width: 80px;">止盈(点)</th>
                                                        <th style="width: 90px;">止损价</th>
                                                        <th style="width: 90px;">止盈价</th>
                                                        <th style="width: 90px;">预计亏损</th>
                                                        <th style="width: 90px;">预计盈利</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="parameterTable">
                                                    <!-- 动态生成 -->
                                                </tbody>
                                            </table>
                                        </div>

                                        <div class="row mt-3">
                                            <div class="col-md-4">
                                                <div class="alert alert-info">
                                                    <h6 class="alert-heading">
                                                        <i class="fas fa-info-circle"></i>
                                                        编辑限制
                                                    </h6>
                                                    <ul class="mb-0 small">
                                                        <li>手数：0.01 - 0.05</li>
                                                        <li>止损点：1 - 15</li>
                                                        <li>止盈点：1 - 200</li>
                                                    </ul>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="resetToDefault()">
                                                    <i class="fas fa-undo"></i>
                                                    重置为默认
                                                </button>
                                                <button type="button" class="btn btn-outline-primary btn-sm ms-2" onclick="recalculateAll()">
                                                    <i class="fas fa-calculator"></i>
                                                    重新计算
                                                </button>
                                                <button type="button" class="btn btn-outline-warning btn-sm ms-2" onclick="debugPriceCalculation()">
                                                    <i class="fas fa-bug"></i>
                                                    调试价格
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 快捷调整按钮 -->
                                    <div class="col-12 mt-3">
                                        <div class="card border-primary">
                                            <div class="card-header bg-primary text-white">
                                                <h6 class="card-title mb-0">
                                                    <i class="fas fa-sliders-h"></i>
                                                    快捷参数调整
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-md-4">
                                                        <div class="d-grid gap-2">
                                                            <button type="button" class="btn btn-outline-success" onclick="increaseVolume()">
                                                                <i class="fas fa-plus"></i>
                                                                增加手数 (+0.01)
                                                            </button>
                                                            <small class="text-muted text-center">当前范围: 0.01 - 0.05手</small>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="d-grid gap-2">
                                                            <button type="button" class="btn btn-outline-warning" onclick="increaseStopLoss()">
                                                                <i class="fas fa-shield-alt"></i>
                                                                止损点增加 (+100)
                                                            </button>
                                                            <small class="text-muted text-center">当前范围: 100 - 1000点</small>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="d-grid gap-2">
                                                            <button type="button" class="btn btn-outline-info" onclick="increaseTakeProfit()">
                                                                <i class="fas fa-target"></i>
                                                                止盈点增加 (+100)
                                                            </button>
                                                            <small class="text-muted text-center">当前范围: 100 - 10000点</small>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row mt-3">
                                                    <div class="col-12">
                                                        <h6 class="text-center mb-2">
                                                            <i class="fas fa-magic"></i> 方案切换
                                                        </h6>
                                                        <div class="d-grid gap-2">
                                                            <button type="button" class="btn btn-outline-success" onclick="applyBalancedPlan()">
                                                                <i class="fas fa-balance-scale"></i>
                                                                平衡方案 ($10/$15/$30)
                                                            </button>
                                                            <button type="button" class="btn btn-outline-warning" onclick="applyExtremePlan()">
                                                                <i class="fas fa-bolt"></i>
                                                                极端行情 ($20/$30/$60)
                                                            </button>
                                                        </div>
                                                        <small class="text-muted text-center d-block mt-1">快速切换不同的止盈策略</small>
                                                    </div>
                                                </div>
                                                <div class="row mt-2">
                                                    <div class="col-12">
                                                        <div class="alert alert-info mb-0">
                                                            <small>
                                                                <i class="fas fa-info-circle"></i>
                                                                <strong>使用说明：</strong>
                                                                点击按钮可快速调整所有订单的参数。调整后会自动重新计算风险收益。
                                                            </small>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-3">
                                        <!-- 风险收益计算 -->
                                        <div class="risk-reward-panel">
                                            <div class="card border-warning mb-3">
                                                <div class="card-header bg-warning text-dark">
                                                    <h6 class="card-title mb-0">
                                                        <i class="fas fa-calculator"></i>
                                                        风险收益分析
                                                    </h6>
                                                </div>
                                                <div class="card-body">
                                                    <div class="risk-reward-item mb-2">
                                                        <div class="d-flex justify-content-between">
                                                            <span class="text-danger">
                                                                <i class="fas fa-arrow-down"></i>
                                                                最大亏损:
                                                            </span>
                                                            <strong class="text-danger" id="maxLoss">$0.00</strong>
                                                        </div>
                                                    </div>
                                                    <div class="risk-reward-item mb-2">
                                                        <div class="d-flex justify-content-between">
                                                            <span class="text-success">
                                                                <i class="fas fa-arrow-up"></i>
                                                                盈利区间:
                                                            </span>
                                                            <strong class="text-success" id="maxProfit">$0.00 - $0.00</strong>
                                                        </div>
                                                    </div>
                                                    <hr>
                                                    <div class="risk-reward-item mb-2">
                                                        <div class="d-flex justify-content-between">
                                                            <span>盈亏比:</span>
                                                            <strong id="riskRewardRatio">0:0</strong>
                                                        </div>
                                                    </div>
                                                    <div class="risk-reward-item mb-2">
                                                        <div class="d-flex justify-content-between">
                                                            <span>总手数:</span>
                                                            <strong id="totalVolume">0.00</strong>
                                                        </div>
                                                    </div>
                                                    <div class="risk-reward-item">
                                                        <div class="d-flex justify-content-between">
                                                            <span>订单数:</span>
                                                            <strong id="totalOrders">6</strong>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="alert alert-warning">
                                                <h6 class="alert-heading">
                                                    <i class="fas fa-exclamation-triangle"></i>
                                                    风险提示
                                                </h6>
                                                <ul class="mb-0 small">
                                                    <li>分3组执行，风险递增</li>
                                                    <li>第2组特殊平仓规则</li>
                                                    <li>确保账户资金充足</li>
                                                    <li>建议先小手数测试</li>
                                                </ul>
                                            </div>

                                            <div class="alert alert-info">
                                                <h6 class="alert-heading">
                                                    <i class="fas fa-robot"></i>
                                                    自动管理
                                                </h6>
                                                <ul class="mb-0 small">
                                                    <li>自动监控止损止盈</li>
                                                    <li>第2组盈利后监控第3组</li>
                                                    <li>智能平仓管理</li>
                                                    <li>实时状态更新</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="mt-3">
                                    <button type="button" class="btn btn-secondary" onclick="prevStep(2)">
                                        <i class="fas fa-arrow-left"></i>
                                        上一步
                                    </button>
                                    <button type="button" class="btn btn-success ms-2" onclick="nextStep(4)">
                                        下一步
                                        <i class="fas fa-arrow-right"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 步骤4：开始执行 -->
                    <div class="step-content" id="content-step4" style="display: none;">
                        <div class="card border-success">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0">
                                    <i class="fas fa-play"></i>
                                    步骤4：开始执行SN加仓
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>执行确认</h6>
                                        <div class="execution-summary" id="executionSummary">
                                            <!-- 动态生成执行摘要 -->
                                        </div>
                                        
                                        <div class="form-check mt-3">
                                            <input class="form-check-input" type="checkbox" id="riskConfirmation">
                                            <label class="form-check-label" for="riskConfirmation">
                                                我已充分了解SN加仓策略的风险，确认开始执行
                                            </label>
                                        </div>
                                        
                                        <div class="mt-3">
                                            <button type="button" class="btn btn-secondary" onclick="prevStep(3)">
                                                <i class="fas fa-arrow-left"></i>
                                                上一步
                                            </button>
                                            <button type="button" class="btn btn-success ms-2" onclick="startSNPositionAdding()" id="startExecutionBtn" disabled>
                                                <i class="fas fa-rocket"></i>
                                                开始执行
                                            </button>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="execution-status" id="executionStatus" style="display: none;">
                                            <h6>执行状态</h6>
                                            <div class="progress mb-3">
                                                <div class="progress-bar" role="progressbar" style="width: 0%" id="executionProgress"></div>
                                            </div>
                                            <div class="execution-log" id="executionLog">
                                                <!-- 执行日志 -->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- SN加仓监控面板 -->
    <div class="row mt-4" id="monitoringPanel" style="display: none;">
        <div class="col-12">
            <div class="card border-primary">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-monitor-heart-rate"></i>
                        SN加仓监控面板
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="positions-monitor" id="positionsMonitor">
                                <!-- 持仓监控 -->
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="control-panel">
                                <h6>控制面板</h6>
                                <button type="button" class="btn btn-warning btn-sm mb-2" onclick="pauseSNStrategy()">
                                    <i class="fas fa-pause"></i>
                                    暂停策略
                                </button>
                                <button type="button" class="btn btn-danger btn-sm mb-2" onclick="stopSNStrategy()">
                                    <i class="fas fa-stop"></i>
                                    停止策略
                                </button>
                                <button type="button" class="btn btn-info btn-sm mb-2" onclick="refreshMonitoring()">
                                    <i class="fas fa-sync"></i>
                                    刷新状态
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* 步骤指示器样式 */
.progress-steps {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    position: relative;
}

.progress-steps::before {
    content: '';
    position: absolute;
    top: 20px;
    left: 0;
    right: 0;
    height: 2px;
    background-color: #e9ecef;
    z-index: 1;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 2;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #e9ecef;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-bottom: 8px;
    transition: all 0.3s ease;
}

.step.active .step-number {
    background-color: #0d6efd;
    color: white;
}

.step.completed .step-number {
    background-color: #198754;
    color: white;
}

.step-title {
    font-size: 0.875rem;
    color: #6c757d;
    text-align: center;
}

.step.active .step-title {
    color: #0d6efd;
    font-weight: 600;
}

.step.completed .step-title {
    color: #198754;
}

/* 价格显示样式 */
.price-display {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
}

.price-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 10px 0;
}

.price-value {
    font-size: 1.2rem;
    font-weight: bold;
}

.price-change {
    font-size: 0.9rem;
}

.price-up {
    color: #198754;
}

.price-down {
    color: #dc3545;
}

/* 执行日志样式 */
.execution-log {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    max-height: 300px;
    overflow-y: auto;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
}

.log-entry {
    margin-bottom: 5px;
    padding: 2px 0;
}

.log-success {
    color: #198754;
}

.log-error {
    color: #dc3545;
}

.log-warning {
    color: #fd7e14;
}

.log-info {
    color: #0d6efd;
}

/* 参数表格样式 */
.parameter-input {
    width: 80px;
    padding: 4px 8px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    text-align: center;
    font-size: 0.875rem;
}

.parameter-input:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
    outline: 0;
}

.parameter-input.is-invalid {
    border-color: #dc3545;
}

.price-display-cell {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    color: #495057;
}

.stop-loss-price {
    color: #dc3545;
    font-weight: bold;
}

.take-profit-price {
    color: #198754;
    font-weight: bold;
}

/* 风险收益面板样式 */
.risk-reward-panel {
    position: sticky;
    top: 20px;
}

.risk-reward-item {
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
}

.risk-reward-item:last-child {
    border-bottom: none;
}

/* 当前价格显示样式 */
.current-price-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 6px;
    margin: 10px 0;
}

/* 价格信息卡片样式 */
.price-info-card {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    margin: 10px 0;
}

.price-item {
    text-align: center;
}

.price-label {
    font-size: 0.875rem;
    color: #6c757d;
    margin-bottom: 4px;
}

.price-value {
    font-size: 1.1rem;
    font-weight: bold;
    font-family: 'Courier New', monospace;
}

.price-bid {
    color: #dc3545;
}

.price-ask {
    color: #198754;
}

.price-spread {
    color: #0d6efd;
}

/* 表格行悬停效果 */
.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.075);
}

/* 组别标识样式 */
.group-badge {
    font-size: 0.75rem;
    padding: 4px 8px;
    border-radius: 12px;
    font-weight: bold;
}

.group-1 {
    background-color: #d4edda;
    color: #155724;
}

.group-2 {
    background-color: #fff3cd;
    color: #856404;
}

.group-3 {
    background-color: #f8d7da;
    color: #721c24;
}
</style>

<script>
// SN加仓策略全局变量
let snConfig = {
    dayTrend: null,
    tradingSymbol: 'XAUUSD',
    tradingDirection: null,
    currentStep: 1,
    isExecuting: false,
    positions: [],
    currentPrice: null,
    parameters: [
        { group: '第1组', groupClass: 'group-1', orders: [1, 2], volume: 0.01, stopLoss: 200, takeProfit: 800 },
        { group: '第2组', groupClass: 'group-2', orders: [3, 4], volume: 0.01, stopLoss: 250, takeProfit: 1200 },
        { group: '第3组', groupClass: 'group-3', orders: [5, 6], volume: 0.01, stopLoss: 400, takeProfit: 2000 }
    ]
};

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 SN加仓策略页面初始化...');

    // 初始化事件监听
    initializeEventListeners();

    // 更新步骤显示
    updateStepDisplay();

    // 检查是否有活跃的SN策略
    checkActiveSNStrategies();
});

// 初始化事件监听
function initializeEventListeners() {
    // 趋势选择监听
    document.querySelectorAll('input[name="dayTrend"]').forEach(radio => {
        radio.addEventListener('change', function() {
            snConfig.dayTrend = this.value;
            updateTradingDirection();
            document.getElementById('step1NextBtn').disabled = false;
        });
    });
    
    // 交易品种选择监听
    document.getElementById('tradingSymbol').addEventListener('change', function() {
        snConfig.tradingSymbol = this.value;
        updateTradingDirection();
    });
    
    // 风险确认监听
    document.getElementById('riskConfirmation').addEventListener('change', function() {
        document.getElementById('startExecutionBtn').disabled = !this.checked;
    });
}

// 分析亚洲交易时段趋势
function analyzeDayTrend() {
    const symbol = document.getElementById('trendAnalysisSymbol').value;

    showNotification('正在分析亚洲时段趋势...', 'info');

    // 调用后端API进行趋势分析
    fetch('/api/sn-strategy/analyze-trend', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ symbol: symbol })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const resultContent = `
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <strong>分析品种:</strong> ${symbol}<br>
                        <strong>交易时段:</strong> <span class="badge bg-info">${data.trading_session || '亚洲交易时段'}</span><br>
                        <strong>趋势方向:</strong>
                        <span class="badge ${data.trend === 'up' ? 'bg-success' : 'bg-danger'}">
                            <i class="fas fa-arrow-${data.trend === 'up' ? 'up' : 'down'}"></i>
                            ${data.trend === 'up' ? '上涨' : '下跌'}
                        </span><br>
                        <strong>置信度:</strong> ${data.confidence}%<br>
                        <strong>价格变化:</strong> ${data.change_percent}%<br>
                        <strong>波动率:</strong> ${data.volatility}%<br>
                        <strong>分析时段:</strong> ${data.analysis_period}<br>
                        <strong>数据来源:</strong> <span class="badge bg-success">${data.data_source}</span>
                    </div>
                    <div>
                        <button type="button" class="btn btn-sm btn-primary" onclick="applyTrendAnalysis('${data.trend}')">
                            应用此趋势
                        </button>
                    </div>
                </div>
            `;

            document.getElementById('trendResultContent').innerHTML = resultContent;
            document.getElementById('trendAnalysisResult').style.display = 'block';

            showNotification('趋势分析完成', 'success');
        } else {
            showNotification('趋势分析失败: ' + data.error, 'error');
        }
    })
    .catch(error => {
        console.error('趋势分析错误:', error);
        showNotification('趋势分析失败，请重试', 'error');
    });
}

// 应用趋势分析结果
function applyTrendAnalysis(trend) {
    document.getElementById(trend === 'up' ? 'trendUp' : 'trendDown').checked = true;
    snConfig.dayTrend = trend;
    updateTradingDirection();
    document.getElementById('step1NextBtn').disabled = false;
    
    showNotification('趋势设置已应用', 'success');
}

// 更新交易方向显示
function updateTradingDirection() {
    if (!snConfig.dayTrend) return;
    
    const direction = snConfig.dayTrend === 'up' ? 'down' : 'up';
    const directionText = direction === 'up' ? '做多(买入)' : '做空(卖出)';
    const directionColor = direction === 'up' ? 'success' : 'danger';
    const directionIcon = direction === 'up' ? 'arrow-up' : 'arrow-down';
    
    snConfig.tradingDirection = direction;
    
    const infoHtml = `
        <i class="fas fa-${directionIcon} text-${directionColor}"></i>
        基于白天${snConfig.dayTrend === 'up' ? '上涨' : '下跌'}趋势，程序将执行
        <strong class="text-${directionColor}">${directionText}</strong>
        操作
    `;
    
    document.getElementById('tradingDirectionInfo').innerHTML = infoHtml;
    document.getElementById('tradingDirectionInfo').className = `alert alert-${directionColor}`;
}

// 获取当前价格
function getCurrentPrice() {
    const symbol = snConfig.tradingSymbol;

    showNotification('正在获取价格信息...', 'info');

    // 调用后端API获取价格
    fetch('/api/sn-strategy/get-price', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ symbol: symbol })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const spread = data.ask - data.bid;
            const decimals = symbol === 'XAUUSD' ? 2 : 5;

            const priceHtml = `
                <div class="price-info">
                    <span>买价(Ask):</span>
                    <span class="price-value text-success">${data.ask.toFixed(decimals)}</span>
                </div>
                <div class="price-info">
                    <span>卖价(Bid):</span>
                    <span class="price-value text-danger">${data.bid.toFixed(decimals)}</span>
                </div>
                <div class="price-info">
                    <span>点差:</span>
                    <span class="price-change text-info">
                        ${spread.toFixed(decimals)}
                    </span>
                </div>
                <div class="price-info">
                    <span>更新时间:</span>
                    <span class="text-muted">${new Date(data.time * 1000).toLocaleTimeString()}</span>
                </div>
            `;

            document.getElementById('priceDisplay').innerHTML = priceHtml;
            showNotification('价格信息已更新', 'success');
        } else {
            showNotification('获取价格失败: ' + data.error, 'error');
        }
    })
    .catch(error => {
        console.error('获取价格错误:', error);
        showNotification('获取价格失败，请重试', 'error');
    });
}

// 步骤导航
function nextStep(step) {
    if (step === 2 && !snConfig.dayTrend) {
        showNotification('请先选择白天趋势', 'warning');
        return;
    }
    
    snConfig.currentStep = step;
    updateStepDisplay();

    if (step === 2) {
        // 进入步骤2时自动获取价格
        refreshCurrentPriceStep2();
    } else if (step === 3) {
        generateParameterTable();
    } else if (step === 4) {
        generateExecutionSummary();
    }
}

function prevStep(step) {
    snConfig.currentStep = step;
    updateStepDisplay();
}

// 更新步骤显示
function updateStepDisplay() {
    // 更新步骤指示器
    for (let i = 1; i <= 4; i++) {
        const stepElement = document.getElementById(`step${i}`);
        const contentElement = document.getElementById(`content-step${i}`);
        
        if (i < snConfig.currentStep) {
            stepElement.className = 'step completed';
        } else if (i === snConfig.currentStep) {
            stepElement.className = 'step active';
        } else {
            stepElement.className = 'step';
        }
        
        contentElement.style.display = i === snConfig.currentStep ? 'block' : 'none';
    }
}

// 步骤2：刷新当前价格
function refreshCurrentPriceStep2() {
    const symbol = document.getElementById('tradingSymbol').value;

    // 显示加载状态
    document.getElementById('priceDisplayStep2').innerHTML = `
        <div class="text-center text-muted">
            <i class="fas fa-spinner fa-spin"></i>
            正在获取 ${symbol} 价格...
        </div>
    `;

    fetch('/api/sn-strategy/get-price', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ symbol: symbol })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 转换为北京时间 - 使用当前时间而不是服务器时间戳
            const beijingTime = new Date().toLocaleString('zh-CN', {
                timeZone: 'Asia/Shanghai',
                hour12: false,
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });

            const priceHtml = `
                <div class="price-info-card">
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="price-item">
                                <div class="price-label">买价(Ask)</div>
                                <div class="price-value text-success">${data.ask.toFixed(getDecimalPlaces())}</div>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="price-item">
                                <div class="price-label">卖价(Bid)</div>
                                <div class="price-value text-danger">${data.bid.toFixed(getDecimalPlaces())}</div>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="price-item">
                                <div class="price-label">点差</div>
                                <div class="price-value text-info">${(data.ask - data.bid).toFixed(getDecimalPlaces())}</div>
                            </div>
                        </div>
                    </div>
                    <div class="text-center mt-2">
                        <small class="text-muted">
                            <i class="fas fa-clock"></i>
                            更新时间: ${beijingTime} (北京时间)
                        </small>
                    </div>
                </div>
            `;

            document.getElementById('priceDisplayStep2').innerHTML = priceHtml;

            // 保存价格数据
            snConfig.currentPrice = data;

            showNotification('价格获取成功', 'success');
        } else {
            document.getElementById('priceDisplayStep2').innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    获取价格失败: ${data.error}
                </div>
            `;
            showNotification('获取价格失败: ' + data.error, 'error');
        }
    })
    .catch(error => {
        console.error('获取价格错误:', error);
        document.getElementById('priceDisplayStep2').innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i>
                网络错误，请重试
            </div>
        `;
        showNotification('获取价格失败，请重试', 'error');
    });
}

// 生成参数表格
function generateParameterTable() {
    // 首先获取当前价格
    refreshCurrentPrice().then(() => {
        const direction = snConfig.tradingDirection;
        const directionText = direction === 'up' ? '买入' : '卖出';
        const directionColor = direction === 'up' ? 'success' : 'danger';
        const currentPrice = snConfig.currentPrice;

        if (!currentPrice) {
            showNotification('请先获取当前价格', 'warning');
            return;
        }

        console.log('🔍 开始生成参数表格');
        console.log(`   交易方向: ${direction}`);
        console.log(`   当前价格: bid=${currentPrice.bid}, ask=${currentPrice.ask}`);
        console.log(`   参数配置:`, snConfig.parameters);

        let tableHtml = '';
        snConfig.parameters.forEach((group, groupIndex) => {
            group.orders.forEach((orderNum, orderIndex) => {
                const stopLossPrice = calculateStopLossPrice(currentPrice.bid, currentPrice.ask, direction, group.stopLoss);
                const takeProfitPrice = calculateTakeProfitPrice(currentPrice.bid, currentPrice.ask, direction, group.takeProfit);
                const entryPrice = direction === 'up' ? currentPrice.ask : currentPrice.bid;

                // 调试输出
                console.log(`🔍 ${group.group} 订单${orderNum}:`);
                console.log(`   止盈点数: ${group.takeProfit}点`);
                console.log(`   入场价: ${entryPrice}`);
                console.log(`   止盈价: ${takeProfitPrice.toFixed(getDecimalPlaces())}`);
                console.log(`   计算: ${entryPrice} ${direction === 'up' ? '+' : '-'} (${group.takeProfit} × 0.01) = ${takeProfitPrice}`);

                // 计算预计亏损和盈利（单个订单）
                const expectedLoss = (group.stopLoss * 0.01 * group.volume).toFixed(2);
                const expectedProfit = (group.takeProfit * 0.01 * group.volume).toFixed(2);

                tableHtml += `
                    <tr>
                        <td>
                            <span class="group-badge ${group.groupClass}">${group.group}</span>
                        </td>
                        <td class="text-center">${orderNum}</td>
                        <td class="text-center">
                            <span class="badge bg-${directionColor}">
                                <i class="fas fa-arrow-${direction === 'up' ? 'up' : 'down'}"></i>
                                ${directionText}
                            </span>
                        </td>
                        <td class="price-display-cell text-center">${entryPrice.toFixed(getDecimalPlaces())}</td>
                        <td class="text-center">
                            <input type="number" class="parameter-input"
                                   value="${group.volume}"
                                   min="0.01" max="0.05" step="0.01"
                                   onchange="updateVolume(${groupIndex}, this.value)"
                                   data-group="${groupIndex}" data-field="volume">
                        </td>
                        <td class="text-center">
                            <input type="number" class="parameter-input"
                                   value="${group.stopLoss}"
                                   min="100" max="1000" step="100"
                                   onchange="updateStopLoss(${groupIndex}, this.value)"
                                   data-group="${groupIndex}" data-field="stopLoss">
                        </td>
                        <td class="text-center">
                            <input type="number" class="parameter-input"
                                   value="${group.takeProfit}"
                                   min="100" max="10000" step="100"
                                   onchange="updateTakeProfit(${groupIndex}, this.value)"
                                   data-group="${groupIndex}" data-field="takeProfit">
                        </td>
                        <td class="stop-loss-price text-center">${stopLossPrice.toFixed(getDecimalPlaces())}</td>
                        <td class="take-profit-price text-center">${takeProfitPrice.toFixed(getDecimalPlaces())}</td>
                        <td class="text-center text-danger">
                            <strong>-$${expectedLoss}</strong>
                        </td>
                        <td class="text-center text-success">
                            <strong>+$${expectedProfit}</strong>
                        </td>
                    </tr>
                `;
            });
        });

        document.getElementById('parameterTable').innerHTML = tableHtml;

        // 计算风险收益
        calculateRiskReward();
    });
}

// 获取小数位数
function getDecimalPlaces() {
    return snConfig.tradingSymbol === 'XAUUSD' ? 2 : 5;
}

// 计算止损价格
function calculateStopLossPrice(bid, ask, direction, stopLossPoints) {
    // 修正黄金点值：XAUUSD的1点 = 0.01美元，外汇1点 = 0.0001
    const pointValue = snConfig.tradingSymbol === 'XAUUSD' ? 0.01 : 0.0001;
    const entryPrice = direction === 'up' ? ask : bid;

    if (direction === 'up') {
        return entryPrice - (stopLossPoints * pointValue);
    } else {
        return entryPrice + (stopLossPoints * pointValue);
    }
}

// 计算止盈价格
function calculateTakeProfitPrice(bid, ask, direction, takeProfitPoints) {
    // 修正黄金点值：XAUUSD的1点 = 0.01美元，外汇1点 = 0.0001
    const pointValue = snConfig.tradingSymbol === 'XAUUSD' ? 0.01 : 0.0001;
    const entryPrice = direction === 'up' ? ask : bid;

    if (direction === 'up') {
        return entryPrice + (takeProfitPoints * pointValue);
    } else {
        return entryPrice - (takeProfitPoints * pointValue);
    }
}

// 刷新当前价格
function refreshCurrentPrice() {
    return new Promise((resolve, reject) => {
        const symbol = snConfig.tradingSymbol;

        fetch('/api/sn-strategy/get-price', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ symbol: symbol })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                snConfig.currentPrice = data;

                const priceHtml = `
                    <div class="current-price-info">
                        <div class="price-item">
                            <div class="price-label">买价(Ask)</div>
                            <div class="price-value price-ask">${data.ask.toFixed(getDecimalPlaces())}</div>
                        </div>
                        <div class="price-item">
                            <div class="price-label">卖价(Bid)</div>
                            <div class="price-value price-bid">${data.bid.toFixed(getDecimalPlaces())}</div>
                        </div>
                        <div class="price-item">
                            <div class="price-label">点差</div>
                            <div class="price-value price-spread">${(data.ask - data.bid).toFixed(getDecimalPlaces())}</div>
                        </div>
                        <div class="price-item">
                            <div class="price-label">更新时间</div>
                            <div class="price-value text-muted" style="font-size: 0.8rem;">
                                ${new Date().toLocaleString('zh-CN', {
                                    timeZone: 'Asia/Shanghai',
                                    hour12: false,
                                    month: '2-digit',
                                    day: '2-digit',
                                    hour: '2-digit',
                                    minute: '2-digit',
                                    second: '2-digit'
                                })} (北京时间)
                            </div>
                        </div>
                    </div>
                `;

                document.getElementById('currentPriceDisplay').innerHTML = priceHtml;
                resolve(data);
            } else {
                showNotification('获取价格失败: ' + data.error, 'error');
                reject(data.error);
            }
        })
        .catch(error => {
            console.error('获取价格错误:', error);
            showNotification('获取价格失败，请重试', 'error');
            reject(error);
        });
    });
}

// 更新手数
function updateVolume(groupIndex, value) {
    const volume = parseFloat(value);
    if (volume < 0.01 || volume > 0.05) {
        showNotification('手数必须在0.01-0.05之间', 'warning');
        // 重置为原值
        event.target.value = snConfig.parameters[groupIndex].volume;
        return;
    }

    snConfig.parameters[groupIndex].volume = volume;
    calculateRiskReward();
    showNotification('手数已更新', 'success');
}

// 更新止损点数
function updateStopLoss(groupIndex, value) {
    const stopLoss = parseInt(value);
    if (stopLoss < 1 || stopLoss > 15) {
        showNotification('止损点数必须在1-15之间', 'warning');
        // 重置为原值
        event.target.value = snConfig.parameters[groupIndex].stopLoss;
        return;
    }

    snConfig.parameters[groupIndex].stopLoss = stopLoss;

    // 重新生成表格以更新止损价格
    updatePricesInTable();
    calculateRiskReward();
    showNotification('止损点数已更新', 'success');
}

// 更新止盈点数
function updateTakeProfit(groupIndex, value) {
    const takeProfit = parseInt(value);
    if (takeProfit < 1 || takeProfit > 200) {
        showNotification('止盈点数必须在1-200之间', 'warning');
        // 重置为原值
        event.target.value = snConfig.parameters[groupIndex].takeProfit;
        return;
    }

    snConfig.parameters[groupIndex].takeProfit = takeProfit;

    // 重新生成表格以更新止盈价格
    updatePricesInTable();
    calculateRiskReward();
    showNotification('止盈点数已更新', 'success');
}

// 更新表格中的价格
function updatePricesInTable() {
    if (!snConfig.currentPrice) return;

    const direction = snConfig.tradingDirection;
    const currentPrice = snConfig.currentPrice;

    // 更新止损价和止盈价
    const rows = document.querySelectorAll('#parameterTable tr');
    let orderIndex = 0;

    snConfig.parameters.forEach((group, groupIndex) => {
        group.orders.forEach((orderNum) => {
            if (rows[orderIndex]) {
                const stopLossPrice = calculateStopLossPrice(currentPrice.bid, currentPrice.ask, direction, group.stopLoss);
                const takeProfitPrice = calculateTakeProfitPrice(currentPrice.bid, currentPrice.ask, direction, group.takeProfit);

                // 计算预计亏损和盈利
                const expectedLoss = (group.stopLoss * 0.01 * group.volume).toFixed(2);
                const expectedProfit = (group.takeProfit * 0.01 * group.volume).toFixed(2);

                const cells = rows[orderIndex].cells;
                cells[7].textContent = stopLossPrice.toFixed(getDecimalPlaces()); // 止损价
                cells[8].textContent = takeProfitPrice.toFixed(getDecimalPlaces()); // 止盈价
                cells[9].innerHTML = `<strong>-$${expectedLoss}</strong>`; // 预计亏损
                cells[10].innerHTML = `<strong>+$${expectedProfit}</strong>`; // 预计盈利
            }
            orderIndex++;
        });
    });
}

// 计算风险收益
function calculateRiskReward() {
    if (!snConfig.currentPrice) return;

    // 修正点值计算
    // 黄金(XAUUSD): 1点 = 0.01美元，0.01手 = 0.01美元/点
    // 外汇对: 1点 = 0.0001，0.01手 = 0.1美元/点
    let pointValue, dollarPerPoint;

    if (snConfig.tradingSymbol === 'XAUUSD') {
        pointValue = 0.01;  // 黄金1点 = 0.01美元
        dollarPerPoint = 1;  // 0.01手黄金，1点 = 0.01美元
    } else {
        pointValue = 0.0001;  // 外汇1点 = 0.0001
        dollarPerPoint = 0.1;   // 0.01手外汇，1点 = 0.1美元（对于USD计价货币对）
    }

    let totalVolume = 0;
    let maxLoss = 0;
    let maxProfit = 0;
    let minProfit = 0; // 最小盈利（使用第2组止盈价格平仓）

    // 分别计算每组的盈亏
    let group1Profit = 0, group2Profit = 0, group3Profit = 0;
    let group1Loss = 0, group2Loss = 0, group3Loss = 0;

    snConfig.parameters.forEach((group, index) => {
        const groupVolume = group.volume * group.orders.length;
        totalVolume += groupVolume;

        // 计算该组的最大亏损和盈利
        // 公式：点数 × 每点价值 × 手数
        const groupLoss = group.stopLoss * dollarPerPoint * groupVolume;
        const groupProfit = group.takeProfit * dollarPerPoint * groupVolume;

        maxLoss += groupLoss;
        maxProfit += groupProfit;

        // 分组记录
        if (index === 0) { // 第1组
            group1Profit = groupProfit;
            group1Loss = groupLoss;
        } else if (index === 1) { // 第2组
            group2Profit = groupProfit;
            group2Loss = groupLoss;
        } else if (index === 2) { // 第3组
            group3Profit = groupProfit;
            group3Loss = groupLoss;
        }

        console.log(`${group.group}: 手数=${groupVolume}, 止损=${group.stopLoss}点, 止盈=${group.takeProfit}点`);
        console.log(`  亏损=${groupLoss.toFixed(2)}美元, 盈利=${groupProfit.toFixed(2)}美元`);
    });

    // 计算最小盈利：第1组+第2组+第3组(使用第2组止盈价格平仓)
    // 第3组使用第2组的止盈点数计算
    const group3MinProfit = snConfig.parameters[1].takeProfit * dollarPerPoint * (snConfig.parameters[2].volume * snConfig.parameters[2].orders.length);
    minProfit = group1Profit + group2Profit + group3MinProfit;

    console.log(`盈利区间计算:`);
    console.log(`  最小盈利 = 第1组(${group1Profit}) + 第2组(${group2Profit}) + 第3组按第2组止盈(${group3MinProfit}) = ${minProfit.toFixed(2)}美元`);
    console.log(`  最大盈利 = 第1组(${group1Profit}) + 第2组(${group2Profit}) + 第3组最大盈利(${group3Profit}) = ${maxProfit.toFixed(2)}美元`);

    // 更新显示
    document.getElementById('maxLoss').textContent = `$${maxLoss.toFixed(2)}`;
    document.getElementById('maxProfit').innerHTML = `$${minProfit.toFixed(2)} - $${maxProfit.toFixed(2)}`;
    document.getElementById('totalVolume').textContent = totalVolume.toFixed(2);

    // 计算盈亏比（使用最小盈利）
    const minRiskRewardRatio = maxLoss > 0 ? (minProfit / maxLoss).toFixed(2) : '0';
    const maxRiskRewardRatio = maxLoss > 0 ? (maxProfit / maxLoss).toFixed(2) : '0';
    document.getElementById('riskRewardRatio').textContent = `1:${minRiskRewardRatio} - 1:${maxRiskRewardRatio}`;
}

// 重置为默认值
function resetToDefault() {
    snConfig.parameters = [
        { group: '第1组', groupClass: 'group-1', orders: [1, 2], volume: 0.01, stopLoss: 200, takeProfit: 800 },
        { group: '第2组', groupClass: 'group-2', orders: [3, 4], volume: 0.01, stopLoss: 250, takeProfit: 1200 },
        { group: '第3组', groupClass: 'group-3', orders: [5, 6], volume: 0.01, stopLoss: 400, takeProfit: 2000 }
    ];

    console.log('🔄 重置参数为默认值:', snConfig.parameters);

    // 强制清空表格内容，然后重新生成
    document.getElementById('parameterTable').innerHTML = '';

    generateParameterTable();
    showNotification('参数已重置为默认值', 'success');
}

// 应用平衡方案
function applyBalancedPlan() {
    snConfig.parameters = [
        { group: '第1组', groupClass: 'group-1', orders: [1, 2], volume: 0.01, stopLoss: 200, takeProfit: 1000 },
        { group: '第2组', groupClass: 'group-2', orders: [3, 4], volume: 0.01, stopLoss: 250, takeProfit: 1500 },
        { group: '第3组', groupClass: 'group-3', orders: [5, 6], volume: 0.01, stopLoss: 400, takeProfit: 3000 }
    ];

    console.log('📊 应用平衡方案:', snConfig.parameters);
    generateParameterTable();
    showNotification('已应用平衡方案 ($10/$15/$30)', 'success');
}

// 应用极端行情方案
function applyExtremePlan() {
    snConfig.parameters = [
        { group: '第1组', groupClass: 'group-1', orders: [1, 2], volume: 0.01, stopLoss: 200, takeProfit: 2000 },
        { group: '第2组', groupClass: 'group-2', orders: [3, 4], volume: 0.01, stopLoss: 250, takeProfit: 3000 },
        { group: '第3组', groupClass: 'group-3', orders: [5, 6], volume: 0.01, stopLoss: 400, takeProfit: 6000 }
    ];

    console.log('⚡ 应用极端行情方案:', snConfig.parameters);
    generateParameterTable();
    showNotification('已应用极端行情方案 ($20/$30/$60)', 'warning');
}

// 重新计算所有
function recalculateAll() {
    if (!snConfig.currentPrice) {
        refreshCurrentPrice().then(() => {
            updatePricesInTable();
            calculateRiskReward();
        });
    } else {
        updatePricesInTable();
        calculateRiskReward();
    }
    showNotification('重新计算完成', 'success');
}

// 快捷调整函数
// 增加手数
function increaseVolume() {
    let hasChanges = false;

    snConfig.parameters.forEach(group => {
        if (group.volume < 0.05) {
            group.volume = Math.min(0.05, group.volume + 0.01);
            hasChanges = true;
        }
    });

    if (hasChanges) {
        console.log('📈 手数增加后的参数:', snConfig.parameters);
        generateParameterTable();
        showNotification('手数已增加 +0.01', 'success');
    } else {
        showNotification('手数已达到最大值 0.05', 'warning');
    }
}

// 增加止损点
function increaseStopLoss() {
    let hasChanges = false;

    snConfig.parameters.forEach(group => {
        if (group.stopLoss < 1000) {
            group.stopLoss = Math.min(1000, group.stopLoss + 100);
            hasChanges = true;
        }
    });

    if (hasChanges) {
        console.log('🛡️ 止损点增加后的参数:', snConfig.parameters);
        generateParameterTable();
        showNotification('止损点已增加 +100', 'success');
    } else {
        showNotification('止损点已达到最大值 1000', 'warning');
    }
}

// 增加止盈点
function increaseTakeProfit() {
    let hasChanges = false;

    snConfig.parameters.forEach(group => {
        if (group.takeProfit < 10000) {
            group.takeProfit = Math.min(10000, group.takeProfit + 100);
            hasChanges = true;
        }
    });

    if (hasChanges) {
        console.log('🎯 止盈点增加后的参数:', snConfig.parameters);
        generateParameterTable();
        showNotification('止盈点已增加 +100', 'success');
    } else {
        showNotification('止盈点已达到最大值 10000', 'warning');
    }
}

// 上一轮执行状态管理
function showLastExecutionStatus(executionData) {
    const statusHtml = `
        <div class="row">
            <div class="col-md-6">
                <h6><i class="fas fa-info-circle text-info"></i> 执行信息</h6>
                <ul class="list-unstyled">
                    <li><strong>策略ID:</strong> ${executionData.strategy_id || 'N/A'}</li>
                    <li><strong>执行时间:</strong> ${new Date().toLocaleString('zh-CN', {timeZone: 'Asia/Shanghai'})}</li>
                    <li><strong>交易品种:</strong> ${snConfig.tradingSymbol}</li>
                    <li><strong>交易方向:</strong> ${snConfig.tradingDirection === 'up' ? '买入' : '卖出'}</li>
                    <li><strong>总订单数:</strong> ${executionData.total_orders || 0}</li>
                    <li><strong>总手数:</strong> ${executionData.total_volume || 0}</li>
                </ul>
            </div>
            <div class="col-md-6">
                <h6><i class="fas fa-chart-line text-success"></i> 执行状态</h6>
                <div class="alert alert-${executionData.success ? 'success' : 'danger'} mb-2">
                    <i class="fas fa-${executionData.success ? 'check-circle' : 'exclamation-triangle'}"></i>
                    ${executionData.success ? '执行成功' : '执行失败'}
                </div>
                ${executionData.success ? `
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-primary btn-sm" onclick="viewStrategyDetails('${executionData.strategy_id}')">
                            <i class="fas fa-eye"></i>
                            查看策略详情
                        </button>
                        <button class="btn btn-outline-warning btn-sm" onclick="stopStrategy('${executionData.strategy_id}')">
                            <i class="fas fa-stop"></i>
                            停止策略
                        </button>
                    </div>
                ` : `
                    <div class="alert alert-warning mb-0">
                        <small><strong>错误信息:</strong> ${executionData.error || '未知错误'}</small>
                    </div>
                `}
            </div>
        </div>
    `;

    document.getElementById('lastExecutionContent').innerHTML = statusHtml;
    document.getElementById('lastExecutionStatus').style.display = 'block';
}

function hideLastExecutionStatus() {
    document.getElementById('lastExecutionStatus').style.display = 'none';
}

function viewStrategyDetails(strategyId) {
    console.log('🔍 查看策略详情:', strategyId);

    // 查看策略详情
    fetch(`/api/sn-strategy/status/${strategyId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.strategy) {
                console.log('策略详情:', data.strategy);

                // 检查策略是否已经结束或平仓
                if (data.strategy.status === 'completed' || data.strategy.status === 'stopped') {
                    showNotification(`策略已${data.strategy.status === 'completed' ? '完成' : '停止'}，所有持仓已平仓`, 'info');
                }

                showStrategyDetailsModal(data.strategy);
            } else {
                showNotification('获取策略详情失败: ' + (data.error || '策略不存在'), 'error');
            }
        })
        .catch(error => {
            console.error('获取策略详情错误:', error);
            showNotification('网络错误，无法获取策略详情', 'error');
        });
}

// 显示策略详情模态框
function showStrategyDetailsModal(strategy) {
    const startTime = new Date(strategy.start_time).toLocaleString('zh-CN', {
        timeZone: 'Asia/Shanghai',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });

    const totalPositions = strategy.positions ? strategy.positions.length : 0;
    const totalVolume = strategy.positions ?
        Math.round(strategy.positions.reduce((sum, pos) => sum + (pos.volume || 0), 0) * 100) / 100 : 0;

    const modalHtml = `
        <div class="modal fade" id="strategyDetailsModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">
                            <i class="fas fa-chart-line"></i>
                            SN策略详情 - ${strategy.id}
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-primary">基本信息</h6>
                                <table class="table table-sm">
                                    <tr>
                                        <td><strong>策略ID:</strong></td>
                                        <td>${strategy.id}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>状态:</strong></td>
                                        <td>
                                            <span class="badge bg-${strategy.status === 'active' ? 'success' : 'secondary'}">
                                                ${strategy.status === 'active' ? '运行中' : '已停止'}
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>开始时间:</strong></td>
                                        <td>${startTime}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>交易品种:</strong></td>
                                        <td>${strategy.config?.trading_symbol || 'N/A'}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>交易方向:</strong></td>
                                        <td>${strategy.config?.trading_direction === 'up' ? '买入(做多)' : '卖出(做空)'}</td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-success">持仓统计</h6>
                                <table class="table table-sm">
                                    <tr>
                                        <td><strong>持仓数量:</strong></td>
                                        <td>${totalPositions}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>总手数:</strong></td>
                                        <td>${totalVolume}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>总盈亏:</strong></td>
                                        <td>
                                            <span class="text-${(strategy.total_profit || 0) >= 0 ? 'success' : 'danger'}">
                                                $${(strategy.total_profit || 0).toFixed(2)}
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>特殊规则:</strong></td>
                                        <td>
                                            <span class="badge bg-${strategy.special_rule_triggered ? 'warning' : 'secondary'}">
                                                ${strategy.special_rule_triggered ? '已触发' : '未触发'}
                                            </span>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>

                        ${totalPositions > 0 ? `
                            <div class="row mt-3">
                                <div class="col-12">
                                    <h6 class="text-info">持仓详情</h6>
                                    <div class="table-responsive">
                                        <table class="table table-sm table-striped">
                                            <thead class="table-dark">
                                                <tr>
                                                    <th>订单号</th>
                                                    <th>类型</th>
                                                    <th>手数</th>
                                                    <th>开仓价</th>
                                                    <th>止损</th>
                                                    <th>止盈</th>
                                                    <th>当前盈亏</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                ${strategy.positions.map(pos => `
                                                    <tr>
                                                        <td>${pos.order_id || 'N/A'}</td>
                                                        <td>
                                                            <span class="badge bg-${pos.type === 'buy' ? 'success' : 'danger'}">
                                                                ${pos.type === 'buy' ? '买入' : '卖出'}
                                                            </span>
                                                        </td>
                                                        <td>${pos.volume || 0}</td>
                                                        <td>${pos.open_price ? parseFloat(pos.open_price).toFixed(2) : 'N/A'}</td>
                                                        <td>${pos.stop_loss ? parseFloat(pos.stop_loss).toFixed(2) : 'N/A'}</td>
                                                        <td>${pos.take_profit ? parseFloat(pos.take_profit).toFixed(2) : 'N/A'}</td>
                                                        <td>
                                                            <span class="text-${(pos.profit || 0) >= 0 ? 'success' : 'danger'}">
                                                                $${(pos.profit || 0).toFixed(2)}
                                                            </span>
                                                        </td>
                                                    </tr>
                                                `).join('')}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times"></i>
                            关闭
                        </button>
                        ${strategy.status === 'active' ? `
                            <button type="button" class="btn btn-warning" onclick="stopStrategyFromModal('${strategy.id}')">
                                <i class="fas fa-stop"></i>
                                停止策略
                            </button>
                            <button type="button" class="btn btn-danger" onclick="closeAllPositionsFromModal('${strategy.id}')">
                                <i class="fas fa-times-circle"></i>
                                一键平仓
                            </button>
                        ` : ''}
                    </div>
                </div>
            </div>
        </div>
    `;

    // 移除已存在的模态框
    const existingModal = document.getElementById('strategyDetailsModal');
    if (existingModal) {
        existingModal.remove();
    }

    // 添加新的模态框
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('strategyDetailsModal'));
    modal.show();
}

function stopStrategy(strategyId) {
    if (confirm('确定要停止这个SN策略吗？')) {
        fetch(`/api/sn-strategy/stop/${strategyId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('策略已停止', 'success');
                hideLastExecutionStatus();
            } else {
                showNotification('停止策略失败: ' + data.error, 'error');
            }
        })
        .catch(error => {
            console.error('停止策略错误:', error);
            showNotification('停止策略失败', 'error');
        });
    }
}

// 从模态框停止策略
function stopStrategyFromModal(strategyId) {
    if (confirm('确定要停止这个SN策略吗？')) {
        fetch(`/api/sn-strategy/stop/${strategyId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('策略已停止', 'success');
                // 关闭模态框
                const modal = bootstrap.Modal.getInstance(document.getElementById('strategyDetailsModal'));
                if (modal) modal.hide();
                // 刷新状态
                checkActiveSNStrategies();
            } else {
                showNotification('停止策略失败: ' + data.error, 'error');
            }
        })
        .catch(error => {
            console.error('停止策略错误:', error);
            showNotification('停止策略失败', 'error');
        });
    }
}

// 一键平仓功能
function closeAllPositionsFromModal(strategyId) {
    if (confirm('⚠️ 确定要一键平仓吗？\n\n这将立即关闭该策略的所有持仓，无法撤销！')) {
        showNotification('正在执行一键平仓...', 'info');

        fetch(`/api/sn-strategy/close-all/${strategyId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(`✅ 一键平仓成功！已关闭 ${data.closed_positions || 0} 个持仓`, 'success');

                // 关闭模态框
                const modal = bootstrap.Modal.getInstance(document.getElementById('strategyDetailsModal'));
                if (modal) modal.hide();

                // 刷新状态
                checkActiveSNStrategies();

                // 显示平仓结果
                if (data.close_results && data.close_results.length > 0) {
                    console.log('平仓结果详情:', data.close_results);
                    showCloseResultsModal(data.close_results);
                }
            } else {
                showNotification('❌ 一键平仓失败: ' + data.error, 'error');
            }
        })
        .catch(error => {
            console.error('一键平仓错误:', error);
            showNotification('❌ 一键平仓失败，请检查网络连接', 'error');
        });
    }
}

// 显示平仓结果模态框
function showCloseResultsModal(closeResults) {
    const totalProfit = closeResults.reduce((sum, result) => sum + (result.profit || 0), 0);

    const modalHtml = `
        <div class="modal fade" id="closeResultsModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header bg-success text-white">
                        <h5 class="modal-title">
                            <i class="fas fa-check-circle"></i>
                            一键平仓结果
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-success">
                            <h6><i class="fas fa-chart-line"></i> 平仓汇总</h6>
                            <ul class="mb-0">
                                <li><strong>平仓数量:</strong> ${closeResults.length} 个持仓</li>
                                <li><strong>总盈亏:</strong>
                                    <span class="text-${totalProfit >= 0 ? 'success' : 'danger'}">
                                        $${totalProfit.toFixed(2)}
                                    </span>
                                </li>
                                <li><strong>平仓时间:</strong> ${new Date().toLocaleString('zh-CN', {timeZone: 'Asia/Shanghai'})}</li>
                            </ul>
                        </div>

                        <h6 class="text-primary">平仓详情</h6>
                        <div class="table-responsive">
                            <table class="table table-sm table-striped">
                                <thead class="table-dark">
                                    <tr>
                                        <th>订单号</th>
                                        <th>类型</th>
                                        <th>手数</th>
                                        <th>开仓价</th>
                                        <th>平仓价</th>
                                        <th>盈亏</th>
                                        <th>状态</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${closeResults.map(result => `
                                        <tr>
                                            <td>${result.order_id || 'N/A'}</td>
                                            <td>
                                                <span class="badge bg-${result.type === 'buy' ? 'success' : 'danger'}">
                                                    ${result.type === 'buy' ? '买入' : '卖出'}
                                                </span>
                                            </td>
                                            <td>${result.volume || 0}</td>
                                            <td>${result.open_price || 'N/A'}</td>
                                            <td>${result.close_price || 'N/A'}</td>
                                            <td>
                                                <span class="text-${(result.profit || 0) >= 0 ? 'success' : 'danger'}">
                                                    $${(result.profit || 0).toFixed(2)}
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-${result.success ? 'success' : 'danger'}">
                                                    ${result.success ? '成功' : '失败'}
                                                </span>
                                            </td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" data-bs-dismiss="modal">
                            <i class="fas fa-check"></i>
                            确定
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 移除已存在的模态框
    const existingModal = document.getElementById('closeResultsModal');
    if (existingModal) {
        existingModal.remove();
    }

    // 添加新的模态框
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('closeResultsModal'));
    modal.show();
}

// 检查活跃的SN策略
function checkActiveSNStrategies() {
    console.log('🔍 检查活跃的SN策略...');

    // 首先检查本地存储的执行记录
    const lastExecution = localStorage.getItem('lastSNExecution');
    if (lastExecution) {
        try {
            const executionData = JSON.parse(lastExecution);
            console.log('📋 发现本地执行记录:', executionData);
            showLastExecutionFromStorage(executionData);
        } catch (error) {
            console.error('解析本地执行记录失败:', error);
            localStorage.removeItem('lastSNExecution');
        }
    }

    // 然后检查服务器上的活跃策略
    fetch('/api/sn-strategy/list')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.strategies && data.strategies.length > 0) {
                console.log('📊 发现活跃的SN策略:', data.strategies);

                // 如果没有本地记录，显示服务器策略
                if (!lastExecution) {
                    showActiveSNStrategies(data.strategies);
                }

                showNotification(`发现 ${data.strategies.length} 个活跃的SN策略`, 'info');
            } else {
                console.log('📭 没有发现活跃的SN策略');

                // 如果没有活跃策略但有本地记录，说明策略可能已结束
                if (lastExecution) {
                    const executionData = JSON.parse(lastExecution);
                    executionData.status = 'completed';
                    showLastExecutionFromStorage(executionData);
                }
            }
        })
        .catch(error => {
            console.error('检查活跃策略错误:', error);
        });
}

// 显示活跃的SN策略
function showActiveSNStrategies(strategies) {
    const strategiesHtml = strategies.map(strategy => {
        const statusBadge = strategy.status === 'active' ?
            '<span class="badge bg-success">运行中</span>' :
            '<span class="badge bg-secondary">已停止</span>';

        const startTime = new Date(strategy.start_time).toLocaleString('zh-CN', {
            timeZone: 'Asia/Shanghai',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });

        return `
            <div class="col-md-6 mb-3">
                <div class="card border-${strategy.status === 'active' ? 'success' : 'secondary'}">
                    <div class="card-header bg-${strategy.status === 'active' ? 'success' : 'secondary'} text-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">
                                <i class="fas fa-chart-line"></i>
                                SN策略 ${strategy.id.split('_')[2] || ''}
                            </h6>
                            ${statusBadge}
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">
                                <small class="text-muted">品种:</small><br>
                                <strong>${strategy.config?.trading_symbol || 'N/A'}</strong>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">方向:</small><br>
                                <strong>${strategy.config?.trading_direction === 'up' ? '买入' : '卖出'}</strong>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-6">
                                <small class="text-muted">开始时间:</small><br>
                                <strong>${startTime}</strong>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">持仓数:</small><br>
                                <strong>${strategy.positions?.length || 0}</strong>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-12">
                                <div class="d-grid gap-2 d-md-flex">
                                    <button class="btn btn-outline-primary btn-sm" onclick="viewStrategyDetails('${strategy.id}')">
                                        <i class="fas fa-eye"></i>
                                        查看详情
                                    </button>
                                    ${strategy.status === 'active' ? `
                                        <button class="btn btn-outline-warning btn-sm" onclick="stopStrategy('${strategy.id}')">
                                            <i class="fas fa-stop"></i>
                                            停止策略
                                        </button>
                                    ` : ''}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }).join('');

    const statusHtml = `
        <div class="alert alert-info">
            <h6><i class="fas fa-info-circle"></i> 当前活跃的SN策略 (${strategies.length}个)</h6>
        </div>
        <div class="row">
            ${strategiesHtml}
        </div>
    `;

    document.getElementById('lastExecutionContent').innerHTML = statusHtml;
    document.getElementById('lastExecutionStatus').style.display = 'block';
}

// 从本地存储显示上一轮执行状态
function showLastExecutionFromStorage(executionData) {
    const executionTime = new Date(executionData.execution_time).toLocaleString('zh-CN', {
        timeZone: 'Asia/Shanghai',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });

    const statusHtml = `
        <div class="row">
            <div class="col-md-8">
                <h6><i class="fas fa-history text-info"></i> 上一轮SN加仓执行记录</h6>
                <div class="row">
                    <div class="col-md-6">
                        <ul class="list-unstyled">
                            <li><strong>策略ID:</strong> ${executionData.strategy_id || 'N/A'}</li>
                            <li><strong>执行时间:</strong> ${executionTime}</li>
                            <li><strong>交易品种:</strong> ${executionData.config?.trading_symbol || 'N/A'}</li>
                            <li><strong>交易方向:</strong> ${executionData.config?.trading_direction === 'up' ? '买入' : '卖出'}</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <ul class="list-unstyled">
                            <li><strong>总订单数:</strong> ${executionData.total_orders || 0}</li>
                            <li><strong>总手数:</strong> ${Math.round((executionData.total_volume || 0) * 100) / 100}</li>
                            <li><strong>持仓数:</strong> ${executionData.positions?.length || 0}</li>
                            <li><strong>状态:</strong>
                                <span class="badge bg-${executionData.status === 'completed' ? 'secondary' : 'success'}">
                                    ${executionData.status === 'completed' ? '已完成' : '运行中'}
                                </span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="d-grid gap-2">
                    ${executionData.status !== 'completed' ? `
                        <button class="btn btn-outline-primary btn-sm" onclick="viewStrategyDetails('${executionData.strategy_id}')">
                            <i class="fas fa-eye"></i>
                            查看策略详情
                        </button>
                        <button class="btn btn-outline-warning btn-sm" onclick="stopStrategy('${executionData.strategy_id}')">
                            <i class="fas fa-stop"></i>
                            停止策略
                        </button>
                    ` : `
                        <div class="alert alert-secondary mb-2">
                            <small><i class="fas fa-info-circle"></i> 策略已完成或停止</small>
                        </div>
                    `}
                    <button class="btn btn-outline-danger btn-sm" onclick="clearExecutionRecord()">
                        <i class="fas fa-trash"></i>
                        清除记录
                    </button>
                </div>
            </div>
        </div>
    `;

    document.getElementById('lastExecutionContent').innerHTML = statusHtml;
    document.getElementById('lastExecutionStatus').style.display = 'block';
}

// 清除执行记录
function clearExecutionRecord() {
    if (confirm('确定要清除上一轮执行记录吗？')) {
        localStorage.removeItem('lastSNExecution');
        document.getElementById('lastExecutionStatus').style.display = 'none';
        showNotification('执行记录已清除', 'success');
    }
}

// 添加返回首页按钮
function addReturnToHomeButton() {
    const monitoringPanel = document.getElementById('monitoringPanel');

    // 检查是否已经添加了按钮
    if (document.getElementById('returnToHomeBtn')) {
        return;
    }

    const buttonHtml = `
        <div class="alert alert-success mt-3" id="returnToHomeAlert">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <i class="fas fa-check-circle"></i>
                    <strong>SN策略执行成功！</strong> 策略正在后台监控中。
                </div>
                <button type="button" class="btn btn-primary" id="returnToHomeBtn" onclick="returnToHome()">
                    <i class="fas fa-home"></i>
                    返回首页配置新策略
                </button>
            </div>
        </div>
    `;

    monitoringPanel.insertAdjacentHTML('afterbegin', buttonHtml);
}

// 返回首页
function returnToHome() {
    // 重置到第一步
    resetToFirstStep();

    // 检查并显示活跃策略
    checkActiveSNStrategies();

    showNotification('已返回首页，可开始配置新的SN策略', 'info');
}

// 重置到第一步
function resetToFirstStep() {
    // 重置步骤状态
    snConfig.currentStep = 1;

    // 隐藏所有步骤内容
    document.querySelectorAll('.step-content').forEach(content => {
        content.style.display = 'none';
    });

    // 显示第一步内容
    document.getElementById('content-step1').style.display = 'block';

    // 更新步骤指示器
    updateStepIndicator(1);

    // 重置执行状态
    snConfig.isExecuting = false;

    // 隐藏监控面板
    document.getElementById('monitoringPanel').style.display = 'none';

    // 重置参数为默认值
    snConfig.parameters = [
        { group: '第1组', groupClass: 'group-1', orders: [1, 2], volume: 0.01, stopLoss: 200, takeProfit: 800 },
        { group: '第2组', groupClass: 'group-2', orders: [3, 4], volume: 0.01, stopLoss: 250, takeProfit: 1200 },
        { group: '第3组', groupClass: 'group-3', orders: [5, 6], volume: 0.01, stopLoss: 400, takeProfit: 2000 }
    ];

    // 清空当前价格
    snConfig.currentPrice = null;

    console.log('🔄 已重置到第一步');
}

// 调试价格计算
function debugPriceCalculation() {
    if (!snConfig.currentPrice) {
        showNotification('请先获取当前价格', 'warning');
        return;
    }

    const direction = snConfig.tradingDirection;
    const currentPrice = snConfig.currentPrice;
    const entryPrice = direction === 'up' ? currentPrice.ask : currentPrice.bid;

    console.log('=== SN加仓价格计算调试 ===');
    console.log(`交易品种: ${snConfig.tradingSymbol}`);
    console.log(`交易方向: ${direction}`);
    console.log(`当前价格: bid=${currentPrice.bid}, ask=${currentPrice.ask}`);
    console.log(`入场价格: ${entryPrice}`);
    console.log('');

    snConfig.parameters.forEach((group, index) => {
        const stopLossPrice = calculateStopLossPrice(currentPrice.bid, currentPrice.ask, direction, group.stopLoss);
        const takeProfitPrice = calculateTakeProfitPrice(currentPrice.bid, currentPrice.ask, direction, group.takeProfit);

        console.log(`${group.group}:`);
        console.log(`  止损点数: ${group.stopLoss}点 → 止损价: ${stopLossPrice.toFixed(getDecimalPlaces())}`);
        console.log(`  止盈点数: ${group.takeProfit}点 → 止盈价: ${takeProfitPrice.toFixed(getDecimalPlaces())}`);
        console.log(`  计算公式: ${entryPrice} ${direction === 'up' ? '+' : '-'} (${group.takeProfit} × 0.01) = ${takeProfitPrice}`);
        console.log('');
    });

    // 检查是否有重复的止盈价格
    const takeProfitPrices = snConfig.parameters.map(group =>
        calculateTakeProfitPrice(currentPrice.bid, currentPrice.ask, direction, group.takeProfit)
    );

    const uniquePrices = [...new Set(takeProfitPrices)];

    if (uniquePrices.length !== takeProfitPrices.length) {
        console.error('❌ 发现重复的止盈价格！');
        console.error('止盈价格数组:', takeProfitPrices);
        showNotification('❌ 发现重复的止盈价格，请检查控制台', 'error');
    } else {
        console.log('✅ 所有止盈价格都不同');
        showNotification('✅ 价格计算正常，查看控制台了解详情', 'success');
    }
}

// 生成执行摘要
function generateExecutionSummary() {
    // 计算总手数 - 使用精确计算避免浮点数精度问题
    const totalVolume = snConfig.parameters.reduce((sum, group) => {
        return Math.round((sum + (group.volume * group.orders.length)) * 100) / 100;
    }, 0);

    // 获取风险收益数据
    const maxLoss = document.getElementById('maxLoss').textContent;
    const maxProfit = document.getElementById('maxProfit').textContent;
    const riskRewardRatio = document.getElementById('riskRewardRatio').textContent;

    const summaryHtml = `
        <div class="alert alert-info">
            <h6 class="alert-heading">执行摘要</h6>
            <ul class="mb-0">
                <li><strong>交易品种:</strong> ${snConfig.tradingSymbol}</li>
                <li><strong>白天趋势:</strong> ${snConfig.dayTrend === 'up' ? '上涨' : '下跌'}</li>
                <li><strong>执行方向:</strong> ${snConfig.tradingDirection === 'up' ? '做多' : '做空'}</li>
                <li><strong>订单数量:</strong> 6个订单，分3组</li>
                <li><strong>总手数:</strong> ${totalVolume.toFixed(2)}手</li>
                <li><strong>最大亏损:</strong> <span class="text-danger">${maxLoss}</span></li>
                <li><strong>盈利区间:</strong> <span class="text-success">${maxProfit}</span></li>
                <li><strong>盈亏比:</strong> ${riskRewardRatio}</li>
                <li><strong>预计执行时间:</strong> 约2-3分钟</li>
            </ul>
        </div>

        <div class="alert alert-warning mt-2">
            <h6 class="alert-heading">
                <i class="fas fa-exclamation-triangle"></i>
                最终确认
            </h6>
            <p class="mb-0">
                请确认您已充分了解SN加仓策略的风险，包括分层加仓的特殊规则和可能的资金损失。
                执行后将无法撤销已成交的订单。
            </p>
        </div>
    `;

    document.getElementById('executionSummary').innerHTML = summaryHtml;
}

// 开始执行SN加仓
function startSNPositionAdding() {
    if (!document.getElementById('riskConfirmation').checked) {
        showNotification('请先确认风险提示', 'warning');
        return;
    }

    snConfig.isExecuting = true;
    document.getElementById('startExecutionBtn').disabled = true;
    document.getElementById('executionStatus').style.display = 'block';

    showNotification('开始执行SN加仓策略...', 'info');
    addExecutionLog('🚀 开始执行SN加仓策略...', 'info');

    // 调用后端API执行SN加仓策略
    fetch('/api/sn-strategy/execute', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            dayTrend: snConfig.dayTrend,
            tradingSymbol: snConfig.tradingSymbol,
            tradingDirection: snConfig.tradingDirection,
            parameters: snConfig.parameters  // 传递完整的参数配置
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            addExecutionLog('✅ SN加仓策略执行成功', 'success');

            // 安全地获取数据，提供默认值
            const totalOrders = data.total_orders || (data.executed_orders ? data.executed_orders.length : 0) || 0;
            // 修复浮点数精度问题
            const totalVolume = data.total_volume || (data.executed_orders ?
                Math.round(data.executed_orders.reduce((sum, order) => sum + (order.volume || 0), 0) * 100) / 100 : 0) || 0;
            const positions = data.executed_orders || data.positions || [];

            addExecutionLog(`📊 总订单数: ${totalOrders}`, 'info');
            addExecutionLog(`📈 总手数: ${totalVolume.toFixed(2)}`, 'info');

            // 更新持仓信息
            snConfig.positions = positions;

            updateExecutionProgress(100);
            showNotification('SN加仓策略执行完成', 'success');

            // 显示监控面板
            document.getElementById('monitoringPanel').style.display = 'block';
            startMonitoring();

            // 保存执行结果到本地存储
            const executionResult = {
                success: true,
                strategy_id: data.strategy_id,
                total_orders: totalOrders,
                total_volume: totalVolume,
                execution_time: new Date().toISOString(),
                positions: positions,
                config: {
                    trading_symbol: snConfig.tradingSymbol,
                    trading_direction: snConfig.tradingDirection
                }
            };

            // 保存到本地存储
            localStorage.setItem('lastSNExecution', JSON.stringify(executionResult));

            // 添加返回首页按钮
            addReturnToHomeButton();

            showNotification('SN策略执行成功，正在监控中。可点击"返回首页"开始新配置。', 'success');
        } else {
            addExecutionLog('❌ SN加仓策略执行失败', 'error');
            addExecutionLog(`错误详情: ${data.error}`, 'error');

            // 如果是部分执行失败，显示已执行的订单信息
            if (data.partial_execution) {
                addExecutionLog(`⚠️ 部分执行失败，已执行订单数: ${data.executed_orders || 0}`, 'warning');
                addExecutionLog(`❌ 失败订单编号: ${data.failed_order}`, 'error');

                // 更新已执行的持仓信息
                if (data.executed_positions && data.executed_positions.length > 0) {
                    snConfig.positions = data.executed_positions;
                    addExecutionLog(`📊 已执行持仓数: ${data.executed_positions.length}`, 'info');

                    // 显示监控面板
                    document.getElementById('monitoringPanel').style.display = 'block';
                    updatePositionsMonitor();
                }

                showNotification('⚠️ SN加仓策略部分执行失败，请检查MT5连接和账户状态', 'error');
            } else {
                showNotification('❌ SN加仓策略执行失败: ' + data.error, 'error');
            }

            // 重置状态
            snConfig.isExecuting = false;
            document.getElementById('startExecutionBtn').disabled = false;
        }
    })
    .catch(error => {
        console.error('执行SN策略错误:', error);
        addExecutionLog('❌ 网络错误: ' + error.message, 'error');
        showNotification('执行失败，请检查网络连接', 'error');

        // 重置状态
        snConfig.isExecuting = false;
        document.getElementById('startExecutionBtn').disabled = false;
    });
}

// 执行SN加仓策略
function executeSNStrategy() {
    const groups = [
        { name: '第1组', orders: [1, 2], stopLoss: 5, takeProfit: 10 },
        { name: '第2组', orders: [3, 4], stopLoss: 6, takeProfit: 15 },
        { name: '第3组', orders: [5, 6], stopLoss: 8, takeProfit: 30 }
    ];
    
    let currentGroup = 0;
    let currentOrder = 0;
    let totalOrders = 6;
    
    function executeNextOrder() {
        if (currentOrder >= totalOrders) {
            // 所有订单执行完成
            updateExecutionProgress(100);
            addExecutionLog('✅ 所有订单执行完成，开始监控...', 'success');
            showNotification('SN加仓策略执行完成', 'success');
            
            // 显示监控面板
            document.getElementById('monitoringPanel').style.display = 'block';
            startMonitoring();
            return;
        }
        
        const group = groups[Math.floor(currentOrder / 2)];
        const orderInGroup = (currentOrder % 2) + 1;
        const orderNumber = currentOrder + 1;
        
        addExecutionLog(`📤 正在执行${group.name}订单${orderNumber}...`, 'info');
        
        // 模拟订单执行
        setTimeout(() => {
            const success = Math.random() > 0.1; // 90%成功率
            
            if (success) {
                addExecutionLog(`✅ 订单${orderNumber}执行成功 - ${snConfig.tradingDirection === 'up' ? '买入' : '卖出'} 0.01手`, 'success');
                snConfig.positions.push({
                    id: orderNumber,
                    group: group.name,
                    direction: snConfig.tradingDirection,
                    volume: 0.01,
                    stopLoss: group.stopLoss,
                    takeProfit: group.takeProfit,
                    status: 'active'
                });
            } else {
                addExecutionLog(`❌ 订单${orderNumber}执行失败，正在重试...`, 'error');
                // 重试逻辑
                setTimeout(() => {
                    addExecutionLog(`✅ 订单${orderNumber}重试成功`, 'success');
                    snConfig.positions.push({
                        id: orderNumber,
                        group: group.name,
                        direction: snConfig.tradingDirection,
                        volume: 0.01,
                        stopLoss: group.stopLoss,
                        takeProfit: group.takeProfit,
                        status: 'active'
                    });
                }, 1000);
            }
            
            currentOrder++;
            const progress = (currentOrder / totalOrders) * 100;
            updateExecutionProgress(progress);
            
            // 继续执行下一个订单
            setTimeout(executeNextOrder, 1500);
        }, Math.random() * 2000 + 1000); // 1-3秒随机延迟
    }
    
    executeNextOrder();
}

// 更新执行进度
function updateExecutionProgress(percentage) {
    const progressBar = document.getElementById('executionProgress');
    progressBar.style.width = percentage + '%';
    progressBar.textContent = Math.round(percentage) + '%';
}

// 添加执行日志
function addExecutionLog(message, type = 'info') {
    const logContainer = document.getElementById('executionLog');
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = document.createElement('div');
    logEntry.className = `log-entry log-${type}`;
    logEntry.textContent = `[${timestamp}] ${message}`;
    
    logContainer.appendChild(logEntry);
    logContainer.scrollTop = logContainer.scrollHeight;
}

// 开始监控
function startMonitoring() {
    updatePositionsMonitor();
    
    // 每5秒更新一次监控
    setInterval(() => {
        if (snConfig.isExecuting) {
            updatePositionsMonitor();
        }
    }, 5000);
}

// 更新持仓监控
async function updatePositionsMonitor() {
    try {
        // 获取实时MT5持仓数据
        const response = await fetch('/api/mt5/positions');
        const data = await response.json();

        if (data.success && data.positions) {
            // 过滤出SN策略相关的持仓
            const snPositions = data.positions.filter(pos =>
                pos.comment && pos.comment.includes('SN-')
            );

            if (snPositions.length === 0) {
                document.getElementById('positionsMonitor').innerHTML = `
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        暂无SN策略持仓
                    </div>
                `;
                return;
            }

            displayPositionsTable(snPositions);
        } else {
            // 如果无法获取实时数据，使用本地存储的数据
            const positions = snConfig.positions || [];
            if (positions.length === 0) {
                document.getElementById('positionsMonitor').innerHTML = `
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        无法获取实时持仓数据，请检查MT5连接
                    </div>
                `;
                return;
            }
            displayPositionsTable(positions);
        }
    } catch (error) {
        console.error('获取持仓数据失败:', error);
        // 使用本地存储的数据作为备用
        const positions = snConfig.positions || [];
        if (positions.length > 0) {
            displayPositionsTable(positions);
        } else {
            document.getElementById('positionsMonitor').innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle"></i>
                    获取持仓数据失败: ${error.message}
                </div>
            `;
        }
    }
}

// 显示持仓表格
function displayPositionsTable(positions) {

    const monitorHtml = `
        <h6>当前持仓状态</h6>
        <div class="table-responsive">
            <table class="table table-sm table-bordered">
                <thead class="table-dark">
                    <tr>
                        <th>订单</th>
                        <th>组别</th>
                        <th>方向</th>
                        <th>手数</th>
                        <th>入场价</th>
                        <th>止损价</th>
                        <th>止盈价</th>
                        <th>状态</th>
                    </tr>
                </thead>
                <tbody>
                    ${positions.map(pos => {
                        // 从注释中提取组别信息
                        let group = 'N/A';
                        if (pos.comment && pos.comment.includes('SN-')) {
                            const match = pos.comment.match(/SN-(.+?)-/);
                            if (match) {
                                group = match[1];
                            }
                        }

                        // 确定交易方向
                        const direction = pos.type === 0 || pos.type === 'buy' ? 'buy' : 'sell';
                        const directionText = direction === 'buy' ? '买入' : '卖出';
                        const directionClass = direction === 'buy' ? 'success' : 'danger';

                        // 使用MT5实际数据
                        const entryPrice = pos.price_open || pos.entry_price || pos.price || 0;
                        const stopLoss = pos.sl || pos.stop_loss || 0;
                        const takeProfit = pos.tp || pos.take_profit || 0;
                        const volume = pos.volume || 0;
                        const ticket = pos.ticket || pos.order_id || pos.id || 'N/A';

                        return `
                            <tr>
                                <td>#${ticket}</td>
                                <td>${group}</td>
                                <td>
                                    <span class="badge bg-${directionClass}">
                                        ${directionText}
                                    </span>
                                </td>
                                <td>${volume}</td>
                                <td>${entryPrice.toFixed(getDecimalPlaces())}</td>
                                <td class="text-danger">${stopLoss > 0 ? stopLoss.toFixed(getDecimalPlaces()) : 'N/A'}</td>
                                <td class="text-success">${takeProfit > 0 ? takeProfit.toFixed(getDecimalPlaces()) : 'N/A'}</td>
                                <td>
                                    <span class="badge bg-primary">
                                        活跃
                                    </span>
                                </td>
                            </tr>
                        `;
                    }).join('')}
                </tbody>
            </table>
        </div>
    `;
    
    document.getElementById('positionsMonitor').innerHTML = monitorHtml;
}

// 控制面板功能
function pauseSNStrategy() {
    showNotification('SN策略已暂停', 'warning');
}

function stopSNStrategy() {
    if (confirm('确定要停止SN加仓策略吗？这将平掉所有相关持仓。')) {
        snConfig.isExecuting = false;
        showNotification('SN策略已停止', 'info');
        document.getElementById('monitoringPanel').style.display = 'none';
    }
}

function refreshMonitoring() {
    updatePositionsMonitor();
    showNotification('监控状态已刷新', 'success');
}

// 通用通知函数
function showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    // 3秒后自动移除
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 3000);
}
</script>
{% endblock %}
