{"is_running": true, "current_config": {"aiStrategy": "10", "symbol": "XAUUSD", "lotSize": 0.01, "timeframe": "1h", "tradeLimit": "30", "stopLoss": 1.1, "takeProfit": 2.4, "strategyConfig": {"type": "auto"}, "strategy_info": {"id": 10, "name": "XAU-1H-0724-2维度-1-5Y", "timeframe": "1h", "parameters": {"name": "XAU-1H-0724-2维度-1-5Y", "ai_model": "AI策略训练", "symbols": ["XAUUSD"], "timeframe": "1h", "training_period": {"start": "2024-01-24", "end": "2025-07-01"}, "optimization_target": "total_return", "analysis_dimensions": {"technical": true, "fundamental": false, "sentiment": false, "volume": true}, "data_source": "mt5_data", "training_mode": "supervised", "start_date": "2024-01-24", "end_date": "2025-07-01", "symbols_text": "XAUUSD", "description": "本地训练的AI策略"}, "performance_metrics": {"win_rate": 0.7, "profit_factor": 1.5, "max_drawdown": -0.071, "sharpe_ratio": 1.42, "total_return": 0.251, "avg_trade_duration": "6.5小时", "total_trades": 71, "winning_trades": 49, "losing_trades": 22, "model_accuracy": 0.77, "data_samples": 7042, "symbols": ["XAUUSD"], "symbols_text": "XAUUSD", "timeframe": "1h", "data_source": "MT5", "training_period": {"start": "2024-01-24", "end": "2025-07-01", "range_text": "2024-01-24 至 2025-07-01"}, "analysis_dimensions": {"technical": true, "fundamental": false, "sentiment": false, "volume": true}, "optimization_target": "total_return"}, "training_data": {"data_source": "MT5", "symbols": ["XAUUSD"], "symbols_text": "XAUUSD", "timeframe": "1h", "training_period": {"start": "2024-01-24", "end": "2025-07-01", "range_text": "2024-01-24 至 2025-07-01"}, "analysis_dimensions": {"technical": true, "fundamental": false, "sentiment": false, "volume": true}, "optimization_target": "total_return", "training_mode": "supervised", "data_quality_score": 0.85, "total_samples": 1000}}}, "trade_count": 0, "daily_trades": {"2025-07-28": 2}, "total_pnl": 0.0, "last_updated": "2025-07-31T08:22:35.027670"}