from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime
import json

# 创建数据库实例
db = SQLAlchemy()

class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)  # 用户昵称(登录用户名)
    email = db.Column(db.String(120), unique=True, nullable=False)
    phone = db.Column(db.String(20), nullable=True)  # 手机号
    real_name = db.Column(db.String(100), nullable=True)  # 姓名(非必填)
    password_hash = db.Column(db.String(120), nullable=False)  # 加密后的密码
    password_salt = db.Column(db.String(32), nullable=False)  # 密码salt值
    user_type = db.Column(db.String(20), default='normal')  # 用户类别: normal, vip, admin
    is_approved = db.Column(db.Boolean, default=False)  # 是否审核通过
    is_active = db.Column(db.Boolean, default=True)  # 是否激活
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    approved_at = db.Column(db.DateTime, nullable=True)  # 审核通过时间
    approved_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True)  # 审核人
    is_active = db.Column(db.Boolean, default=True)

    def set_password(self, password):
        """设置密码（兼容旧版本，新版本使用带salt的密码）"""
        if hasattr(self, 'password_salt') and self.password_salt:
            # 新版本：使用salt
            self.password_hash = generate_password_hash(password + self.password_salt)
        else:
            # 旧版本兼容
            self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        """检查密码（兼容新旧版本）"""
        if hasattr(self, 'password_salt') and self.password_salt:
            # 新版本：使用salt
            return check_password_hash(self.password_hash, password + self.password_salt)
        else:
            # 旧版本兼容
            return check_password_hash(self.password_hash, password)

    def __repr__(self):
        return f'<User {self.username}>'

class AIModelConfig(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    api_key = db.Column(db.String(200), nullable=False)
    model_name = db.Column(db.String(100), nullable=False)
    base_url = db.Column(db.String(200))
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class TradingAccount(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    account_name = db.Column(db.String(100), nullable=False)
    account_type = db.Column(db.String(20), nullable=False)  # 'real' or 'demo'
    broker = db.Column(db.String(50), nullable=False)
    api_key = db.Column(db.String(200))
    api_secret = db.Column(db.String(200))
    balance = db.Column(db.Float, default=0.0)
    equity = db.Column(db.Float, default=0.0)
    margin = db.Column(db.Float, default=0.0)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class Trade(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    account_id = db.Column(db.Integer, db.ForeignKey('trading_account.id'), nullable=False)
    symbol = db.Column(db.String(20), nullable=False)
    trade_type = db.Column(db.String(10), nullable=False)  # 'buy' or 'sell'
    volume = db.Column(db.Float, nullable=False)
    open_price = db.Column(db.Float, nullable=False)
    close_price = db.Column(db.Float)
    stop_loss = db.Column(db.Float)
    take_profit = db.Column(db.Float)
    profit = db.Column(db.Float, default=0.0)
    status = db.Column(db.String(20), default='open')  # 'open', 'closed', 'cancelled'
    open_time = db.Column(db.DateTime, default=datetime.utcnow)
    close_time = db.Column(db.DateTime)
    strategy_name = db.Column(db.String(100))
    mt5_ticket = db.Column(db.BigInteger)  # MT5订单号

class Strategy(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    strategy_type = db.Column(db.String(50), nullable=False)  # 'technical', 'fundamental', 'ai'
    parameters = db.Column(db.Text)  # JSON string
    is_active = db.Column(db.Boolean, default=False)
    is_shared = db.Column(db.Boolean, default=False)  # 是否被管理员分享给所有用户
    shared_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True)  # 分享者（管理员）
    shared_at = db.Column(db.DateTime, nullable=True)  # 分享时间
    status = db.Column(db.String(20), default='training')  # 'training', 'completed', 'failed'
    training_results = db.Column(db.Text)  # 训练结果JSON
    performance_metrics = db.Column(db.Text)  # 性能指标JSON
    training_data = db.Column(db.Text)  # 训练数据配置JSON
    ai_model = db.Column(db.String(100))  # 使用的AI模型
    timeframe = db.Column(db.String(10))  # 时间框架
    symbols = db.Column(db.Text)  # 交易品种JSON
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def get_parameters(self):
        return json.loads(self.parameters) if self.parameters else {}

    def set_parameters(self, params):
        self.parameters = json.dumps(params)

    def get_training_results(self):
        return json.loads(self.training_results) if self.training_results else {}

    def set_training_results(self, results):
        self.training_results = json.dumps(results)

    def get_performance_metrics(self):
        return json.loads(self.performance_metrics) if self.performance_metrics else {}

    def set_performance_metrics(self, metrics):
        self.performance_metrics = json.dumps(metrics)

    def get_training_data(self):
        return json.loads(self.training_data) if self.training_data else {}

    def set_training_data(self, data):
        self.training_data = json.dumps(data)

    def get_symbols(self):
        return json.loads(self.symbols) if self.symbols else []

    def set_symbols(self, symbols_list):
        self.symbols = json.dumps(symbols_list)

    def can_access(self, user):
        """检查用户是否可以访问此策略"""
        # 策略创建者可以访问
        if self.user_id == user.id:
            return True
        # 管理员分享的策略所有用户都可以访问
        if self.is_shared:
            return True
        # 管理员可以访问所有策略
        if user.user_type == 'admin':
            return True
        return False

class BacktestResult(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    strategy_id = db.Column(db.Integer, db.ForeignKey('strategy.id'), nullable=False)
    symbol = db.Column(db.String(20), nullable=False)
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=False)
    initial_capital = db.Column(db.Float, nullable=False)
    final_capital = db.Column(db.Float, nullable=False)
    total_return = db.Column(db.Float, nullable=False)
    max_drawdown = db.Column(db.Float, nullable=False)
    sharpe_ratio = db.Column(db.Float)
    win_rate = db.Column(db.Float)
    total_trades = db.Column(db.Integer, default=0)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

# AIStrategy类已删除，统一使用Strategy类，strategy_type='ai'

class MarketData(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    symbol = db.Column(db.String(20), nullable=False)
    timestamp = db.Column(db.DateTime, nullable=False)
    open_price = db.Column(db.Float, nullable=False)
    high_price = db.Column(db.Float, nullable=False)
    low_price = db.Column(db.Float, nullable=False)
    close_price = db.Column(db.Float, nullable=False)
    volume = db.Column(db.Float, nullable=False)
    timeframe = db.Column(db.String(10), nullable=False)  # '1m', '5m', '1h', '1d', etc.

    __table_args__ = (db.UniqueConstraint('symbol', 'timestamp', 'timeframe'),)

class SystemSettings(db.Model):
    """系统设置"""
    id = db.Column(db.Integer, primary_key=True)
    setting_key = db.Column(db.String(100), unique=True, nullable=False)
    setting_value = db.Column(db.Text, nullable=True)
    setting_type = db.Column(db.String(20), default='string')  # string, boolean, integer, json
    description = db.Column(db.String(255), nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    @staticmethod
    def get_setting(key, default=None):
        """获取设置值"""
        setting = SystemSettings.query.filter_by(setting_key=key).first()
        if setting:
            if setting.setting_type == 'boolean':
                return setting.setting_value.lower() == 'true'
            elif setting.setting_type == 'integer':
                return int(setting.setting_value)
            elif setting.setting_type == 'json':
                return json.loads(setting.setting_value)
            else:
                return setting.setting_value
        return default

    @staticmethod
    def set_setting(key, value, setting_type='string', description=None):
        """设置值"""
        setting = SystemSettings.query.filter_by(setting_key=key).first()

        if setting_type == 'boolean':
            value = str(value).lower()
        elif setting_type == 'integer':
            value = str(value)
        elif setting_type == 'json':
            value = json.dumps(value)
        else:
            value = str(value)

        if setting:
            setting.setting_value = value
            setting.setting_type = setting_type
            setting.updated_at = datetime.utcnow()
            if description:
                setting.description = description
        else:
            setting = SystemSettings(
                setting_key=key,
                setting_value=value,
                setting_type=setting_type,
                description=description
            )
            db.session.add(setting)

        db.session.commit()
        return setting

class AgentTradingStrategy(db.Model):
    """智能体交易策略模型"""
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)

    # 用户交易策略和心得
    user_strategy = db.Column(db.Text)  # 用户的交易心得和要求
    risk_tolerance = db.Column(db.String(20), default='moderate')  # conservative, moderate, aggressive
    max_daily_trades = db.Column(db.Integer, default=10)
    max_position_size = db.Column(db.Float, default=1000.0)

    # AI策略配置（现在引用统一的strategy表）
    ai_strategy_id = db.Column(db.Integer, db.ForeignKey('strategy.id'))
    ai_model_config = db.Column(db.Text)  # JSON: 外部AI模型配置

    # 交易品种和时间配置
    trading_symbols = db.Column(db.Text)  # JSON: 允许交易的货币对列表
    trading_hours = db.Column(db.String(50), default='9:00-18:00')

    # 状态和性能
    is_active = db.Column(db.Boolean, default=False)
    performance_metrics = db.Column(db.Text)  # JSON: 性能指标
    last_evaluation = db.Column(db.DateTime)

    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def get_user_strategy(self):
        return self.user_strategy or ""

    def get_ai_model_config(self):
        return json.loads(self.ai_model_config) if self.ai_model_config else {}

    def set_ai_model_config(self, config):
        self.ai_model_config = json.dumps(config)

    def get_trading_symbols(self):
        return json.loads(self.trading_symbols) if self.trading_symbols else ['EURUSD']

    def set_trading_symbols(self, symbols):
        self.trading_symbols = json.dumps(symbols)

    def get_performance_metrics(self):
        return json.loads(self.performance_metrics) if self.performance_metrics else {}

    def set_performance_metrics(self, metrics):
        self.performance_metrics = json.dumps(metrics)

class AgentTradingSession(db.Model):
    """智能体交易会话模型"""
    id = db.Column(db.Integer, primary_key=True)
    strategy_id = db.Column(db.Integer, db.ForeignKey('agent_trading_strategy.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)

    # 会话信息
    session_name = db.Column(db.String(100))
    start_time = db.Column(db.DateTime, default=datetime.utcnow)
    end_time = db.Column(db.DateTime)
    status = db.Column(db.String(20), default='active')  # active, paused, stopped, completed

    # 交易统计
    total_trades = db.Column(db.Integer, default=0)
    winning_trades = db.Column(db.Integer, default=0)
    losing_trades = db.Column(db.Integer, default=0)
    total_profit = db.Column(db.Float, default=0.0)
    max_drawdown = db.Column(db.Float, default=0.0)

    # AI决策记录
    ai_decisions = db.Column(db.Text)  # JSON: AI决策历史
    evaluation_results = db.Column(db.Text)  # JSON: 评估结果

    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def get_ai_decisions(self):
        return json.loads(self.ai_decisions) if self.ai_decisions else []

    def add_ai_decision(self, decision):
        decisions = self.get_ai_decisions()
        decisions.append({
            'timestamp': datetime.utcnow().isoformat(),
            'decision': decision
        })
        # 只保留最近100个决策
        if len(decisions) > 100:
            decisions = decisions[-100:]
        self.ai_decisions = json.dumps(decisions)

    def get_evaluation_results(self):
        return json.loads(self.evaluation_results) if self.evaluation_results else []

    def add_evaluation_result(self, result):
        results = self.get_evaluation_results()
        results.append({
            'timestamp': datetime.utcnow().isoformat(),
            'result': result
        })
        # 只保留最近50个评估结果
        if len(results) > 50:
            results = results[-50:]
        self.evaluation_results = json.dumps(results)

class LowRiskTrade(db.Model):
    """低风险交易记录"""
    __tablename__ = 'low_risk_trades'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    symbol = db.Column(db.String(20), nullable=False)  # 交易品种
    action = db.Column(db.String(10), nullable=False)  # buy/sell
    volume = db.Column(db.Float, nullable=False)  # 交易手数
    entry_price = db.Column(db.Float, nullable=False)  # 入场价格
    stop_loss = db.Column(db.Float, nullable=True)  # 止损价格
    take_profit = db.Column(db.Float, nullable=True)  # 止盈价格
    mt5_order_id = db.Column(db.BigInteger, nullable=True)  # MT5订单号
    comment = db.Column(db.String(200), nullable=True)  # 交易备注

    # 交易状态
    status = db.Column(db.String(20), default='open')  # open/closed/cancelled
    close_price = db.Column(db.Float, nullable=True)  # 平仓价格
    close_time = db.Column(db.DateTime, nullable=True)  # 平仓时间
    pnl = db.Column(db.Float, default=0.0)  # 盈亏

    # 分析数据
    signal_type = db.Column(db.String(50), nullable=True)  # 信号类型
    confidence = db.Column(db.Float, nullable=True)  # 置信度
    market_analysis = db.Column(db.Text, nullable=True)  # 市场分析数据(JSON)
    operation_type = db.Column(db.String(20), default='manual')  # 操作类型: auto/manual

    # 时间戳
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 关联用户
    user = db.relationship('User', backref=db.backref('low_risk_trades', lazy=True))

    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'symbol': self.symbol,
            'action': self.action,
            'volume': self.volume,
            'entry_price': self.entry_price,
            'stop_loss': self.stop_loss,
            'take_profit': self.take_profit,
            'mt5_order_id': self.mt5_order_id,
            'comment': self.comment,
            'status': self.status,
            'close_price': self.close_price,
            'close_time': self.close_time.isoformat() if self.close_time else None,
            'pnl': self.pnl,
            'signal_type': self.signal_type,
            'confidence': self.confidence,
            'market_analysis': json.loads(self.market_analysis) if self.market_analysis else None,
            'operation_type': self.operation_type,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

    def set_market_analysis(self, analysis_data):
        """设置市场分析数据"""
        self.market_analysis = json.dumps(analysis_data)

    def get_market_analysis(self):
        """获取市场分析数据"""
        if self.market_analysis:
            try:
                return json.loads(self.market_analysis)
            except:
                return {}
        return {}

    def __repr__(self):
        return f'<LowRiskTrade {self.id}: {self.symbol} {self.action} {self.volume}>'

class LowRiskTradingConfig(db.Model):
    """低风险交易配置"""
    __tablename__ = 'low_risk_trading_configs'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)

    # 基础配置
    daily_limit = db.Column(db.Integer, default=2)  # 每日交易限制
    lot_size = db.Column(db.Float, default=0.01)  # 交易手数
    stop_loss_percent = db.Column(db.Float, default=0.5)  # 止损百分比
    take_profit_percent = db.Column(db.Float, default=1.5)  # 止盈百分比

    # 风险控制
    auto_trade = db.Column(db.Boolean, default=True)  # 自动交易
    trend_detection = db.Column(db.Boolean, default=True)  # 单边行情检测
    min_signals = db.Column(db.Integer, default=2)  # 最小确认信号数

    # 交易时间
    trading_start_time = db.Column(db.String(10), default='08:00')  # 交易开始时间
    trading_end_time = db.Column(db.String(10), default='22:00')  # 交易结束时间

    # 分析参数
    yearly_period = db.Column(db.Integer, default=365)  # 年度数据周期
    weekly_days = db.Column(db.Integer, default=7)  # 周波段分析天数
    overnight_start = db.Column(db.String(10), default='21:00')  # 昨夜分析起始时间

    # 状态
    enabled = db.Column(db.Boolean, default=False)  # 是否启用

    # 时间戳
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 关联用户
    user = db.relationship('User', backref=db.backref('low_risk_config', uselist=False))

    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'daily_limit': self.daily_limit,
            'lot_size': self.lot_size,
            'stop_loss_percent': self.stop_loss_percent,
            'take_profit_percent': self.take_profit_percent,
            'auto_trade': self.auto_trade,
            'trend_detection': self.trend_detection,
            'min_signals': self.min_signals,
            'trading_start_time': self.trading_start_time,
            'trading_end_time': self.trading_end_time,
            'yearly_period': self.yearly_period,
            'weekly_days': self.weekly_days,
            'overnight_start': self.overnight_start,
            'enabled': self.enabled,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

    def __repr__(self):
        return f'<LowRiskTradingConfig {self.user_id}: enabled={self.enabled}>'


class EntrySignal(db.Model):
    """入场信号记录"""
    __tablename__ = 'entry_signals'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)

    # 信号基本信息
    signal_type = db.Column(db.String(50), nullable=False)  # 信号类型：trend_buy, trend_sell, mean_reversion_buy等
    symbol = db.Column(db.String(20), nullable=False, default='XAUUSD')
    action = db.Column(db.String(10), nullable=False)  # buy/sell

    # 信号强度和置信度
    strength = db.Column(db.Float)  # 信号强度
    confidence = db.Column(db.Float)  # 置信度

    # 价格信息
    signal_price = db.Column(db.Float, nullable=False)  # 信号产生时的价格
    entry_price = db.Column(db.Float)  # 实际入场价格（如果执行了交易）

    # 执行状态
    is_executed = db.Column(db.Boolean, default=False)  # 是否执行了交易
    execution_result = db.Column(db.String(20))  # 执行结果：success, failed, skipped
    execution_reason = db.Column(db.Text)  # 执行或跳过的原因

    # 关联的交易记录
    trade_id = db.Column(db.Integer, db.ForeignKey('low_risk_trades.id'))  # 关联的交易记录ID

    # 市场分析数据
    market_analysis = db.Column(db.Text)  # JSON格式的市场分析数据

    # 时间信息
    signal_time = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)  # 信号产生时间
    execution_time = db.Column(db.DateTime)  # 执行时间

    # 关联关系
    user = db.relationship('User', backref=db.backref('entry_signals', lazy=True))
    trade = db.relationship('LowRiskTrade', backref=db.backref('entry_signal', uselist=False))

    def to_dict(self):
        return {
            'id': self.id,
            'signal_type': self.signal_type,
            'symbol': self.symbol,
            'action': self.action,
            'strength': self.strength,
            'confidence': self.confidence,
            'signal_price': self.signal_price,
            'entry_price': self.entry_price,
            'is_executed': self.is_executed,
            'execution_result': self.execution_result,
            'execution_reason': self.execution_reason,
            'trade_id': self.trade_id,
            'signal_time': self.signal_time.isoformat() if self.signal_time else None,
            'execution_time': self.execution_time.isoformat() if self.execution_time else None,
            'market_analysis': json.loads(self.market_analysis) if self.market_analysis else None
        }

    def __repr__(self):
        return f'<EntrySignal {self.signal_type} {self.action} {self.signal_time}>'
