#!/usr/bin/env python3
"""
修复模型的日期配置问题
"""

import sqlite3
import json
from datetime import datetime, timedelta

def fix_model_date_config():
    """修复模型的日期配置"""
    print("🔧 修复模型日期配置")
    print("=" * 80)
    
    model_id = "c7ffc593-4303-45b0-89ef-a44f211770c3"
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 查询当前配置
        cursor.execute('''
            SELECT config FROM deep_learning_models WHERE id = ?
        ''', (model_id,))
        
        result = cursor.fetchone()
        
        if not result:
            print(f"❌ 未找到模型: {model_id}")
            return False
        
        config = json.loads(result[0])
        print(f"📊 当前配置:")
        print(f"   数据配置: {config.get('data_config', {})}")
        
        # 修复日期配置
        data_config = config.get('data_config', {})
        
        # 设置合理的日期范围
        today = datetime.now()
        end_date = today - timedelta(days=1)  # 昨天
        start_date = end_date - timedelta(days=365)  # 一年前
        
        old_start = data_config.get('start_date')
        old_end = data_config.get('end_date')
        
        data_config['start_date'] = start_date.strftime('%Y-%m-%d')
        data_config['end_date'] = end_date.strftime('%Y-%m-%d')
        
        config['data_config'] = data_config
        
        print(f"\n🔄 修复日期配置:")
        print(f"   修复前: {old_start} 到 {old_end}")
        print(f"   修复后: {data_config['start_date']} 到 {data_config['end_date']}")
        
        # 更新数据库
        cursor.execute('''
            UPDATE deep_learning_models 
            SET config = ? 
            WHERE id = ?
        ''', (json.dumps(config), model_id))
        
        conn.commit()
        
        print(f"✅ 模型配置已更新")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False
    finally:
        if conn:
            conn.close()

def reset_training_task():
    """重置训练任务状态"""
    print(f"\n🔄 重置训练任务状态")
    print("=" * 50)
    
    task_id = "3f0e9a54-2111-4ec0-849b-8b4640a6f268"
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 重置任务状态
        cursor.execute('''
            UPDATE training_tasks 
            SET status = 'pending', 
                progress = 0, 
                current_epoch = 0, 
                train_loss = 0.0, 
                val_loss = 0.0,
                updated_at = datetime('now'),
                logs = ?
            WHERE id = ?
        ''', (json.dumps({
            'stage': 'reset',
            'message': '任务已重置，等待重新启动',
            'reset_time': datetime.now().isoformat(),
            'reason': '修复日期配置问题'
        }), task_id))
        
        conn.commit()
        
        if cursor.rowcount > 0:
            print(f"✅ 训练任务已重置")
            return True
        else:
            print(f"❌ 未找到训练任务")
            return False
        
    except Exception as e:
        print(f"❌ 重置失败: {e}")
        return False
    finally:
        if conn:
            conn.close()

def verify_fix():
    """验证修复结果"""
    print(f"\n🔍 验证修复结果")
    print("=" * 50)
    
    model_id = "c7ffc593-4303-45b0-89ef-a44f211770c3"
    task_id = "3f0e9a54-2111-4ec0-849b-8b4640a6f268"
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 检查模型配置
        cursor.execute('''
            SELECT config FROM deep_learning_models WHERE id = ?
        ''', (model_id,))
        
        result = cursor.fetchone()
        if result:
            config = json.loads(result[0])
            data_config = config.get('data_config', {})
            
            print(f"📊 修复后的模型配置:")
            print(f"   开始日期: {data_config.get('start_date')}")
            print(f"   结束日期: {data_config.get('end_date')}")
            print(f"   交易品种: {data_config.get('symbol')}")
            print(f"   时间框架: {data_config.get('timeframe')}")
            
            # 验证日期是否合理
            today = datetime.now()
            end_date = datetime.strptime(data_config.get('end_date'), '%Y-%m-%d')
            
            if end_date < today:
                print(f"   ✅ 结束日期合理（在今天之前）")
            else:
                print(f"   ❌ 结束日期仍有问题（在未来）")
        
        # 检查任务状态
        cursor.execute('''
            SELECT status, progress FROM training_tasks WHERE id = ?
        ''', (task_id,))
        
        result = cursor.fetchone()
        if result:
            status, progress = result
            print(f"\n📊 任务状态:")
            print(f"   状态: {status}")
            print(f"   进度: {progress}%")
            
            if status == 'pending' and progress == 0:
                print(f"   ✅ 任务已成功重置")
            else:
                print(f"   ⚠️ 任务状态可能需要进一步处理")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False
    finally:
        if conn:
            conn.close()

def main():
    """主函数"""
    print("🔧 修复AI训练模型日期配置问题")
    print("=" * 80)
    
    print("📋 问题分析:")
    print("• 模型配置的结束日期是2025-06-01（未来日期）")
    print("• 系统尝试获取未来数据导致训练卡住")
    print("• 需要修正为合理的历史日期范围")
    
    # 1. 修复模型日期配置
    config_fixed = fix_model_date_config()
    
    # 2. 重置训练任务
    if config_fixed:
        task_reset = reset_training_task()
    else:
        task_reset = False
    
    # 3. 验证修复结果
    if config_fixed and task_reset:
        verify_fix()
    
    # 4. 总结
    print(f"\n📊 修复结果总结")
    print("=" * 80)
    
    if config_fixed:
        print(f"✅ 模型配置修复: 成功")
    else:
        print(f"❌ 模型配置修复: 失败")
    
    if task_reset:
        print(f"✅ 任务状态重置: 成功")
    else:
        print(f"❌ 任务状态重置: 失败")
    
    if config_fixed and task_reset:
        print(f"\n🎉 修复完成！")
        print(f"💡 下一步操作:")
        print(f"   1. 在AI推理学习页面重新启动训练")
        print(f"   2. 监控训练进度是否正常更新")
        print(f"   3. 确认数据获取和特征计算正常")
        
        print(f"\n⚠️ 注意事项:")
        print(f"   • 新的日期范围是过去一年的数据")
        print(f"   • 如果仍有问题，考虑减少序列长度或批次大小")
        print(f"   • 可以尝试使用更简单的LSTM模型而不是attention_lstm")
    else:
        print(f"\n❌ 修复失败，需要手动处理")

if __name__ == '__main__':
    main()
