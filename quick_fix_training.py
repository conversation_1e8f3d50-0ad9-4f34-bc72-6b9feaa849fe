#!/usr/bin/env python3
import sqlite3
from datetime import datetime
import sys
import os

print("🔧 快速修复训练进度卡住问题")
print("=" * 50)

# 直接修复卡住的训练任务
try:
    print("🧹 清理卡住的训练任务...")
    conn = sqlite3.connect('trading_system.db')
    cursor = conn.cursor()

    # 先查看当前状态
    cursor.execute("SELECT id, status, progress FROM training_tasks WHERE status = 'running'")
    running_tasks = cursor.fetchall()

    if running_tasks:
        print(f"🔍 发现 {len(running_tasks)} 个运行中的任务:")
        for task_id, status, progress in running_tasks:
            print(f"  任务: {task_id[:8]}... 状态: {status} 进度: {progress}%")

    # 将所有running状态的任务标记为failed
    cursor.execute("""
        UPDATE training_tasks
        SET status = 'failed',
            logs = '{"stage": "cleanup", "message": "训练任务卡住，手动清理"}'
        WHERE status = 'running'
    """)

    affected_rows = cursor.rowcount
    conn.commit()
    conn.close()

    print(f"✅ 已清理 {affected_rows} 个卡住的训练任务")

    # 清理训练服务状态
    try:
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        from services.deep_learning_service import deep_learning_service

        print("🔄 清理训练服务状态...")
        deep_learning_service.training_control.clear()
        deep_learning_service.training_tasks.clear()
        print("✅ 训练服务状态已清理")

    except Exception as e:
        print(f"⚠️ 清理服务状态失败: {e}")

    print("\n💡 修复完成，建议:")
    print("1. 重新启动应用程序")
    print("2. 重新开始AI模型训练")
    print("3. 监控训练进度是否正常更新")

except Exception as e:
    print(f"❌ 修复失败: {e}")
    import traceback
    traceback.print_exc()
