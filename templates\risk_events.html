{% extends "base.html" %}

{% block title %}风险事件监控{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-exclamation-triangle text-warning"></i> 风险事件监控</h2>
                <div class="d-flex align-items-center">
                    <span class="badge bg-info me-2" id="lastUpdate">最后更新: 加载中...</span>
                    <button class="btn btn-sm btn-outline-primary" onclick="refreshRiskData()">
                        <i class="fas fa-sync"></i> 刷新
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 风险过滤设置 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-filter text-primary"></i> 风险事件过滤设置</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">关注的交易品种 <small class="text-muted">(只显示相关风险)</small></label>
                            <div class="d-flex flex-wrap gap-2">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="XAUUSD" id="symbol_XAUUSD" checked onchange="updateRiskFilter()">
                                    <label class="form-check-label" for="symbol_XAUUSD">
                                        <i class="fas fa-coins text-warning"></i> XAUUSD (黄金)
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="EURUSD" id="symbol_EURUSD" onchange="updateRiskFilter()">
                                    <label class="form-check-label" for="symbol_EURUSD">
                                        <i class="fas fa-euro-sign text-primary"></i> EURUSD
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="GBPUSD" id="symbol_GBPUSD" onchange="updateRiskFilter()">
                                    <label class="form-check-label" for="symbol_GBPUSD">
                                        <i class="fas fa-pound-sign text-info"></i> GBPUSD
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="USDJPY" id="symbol_USDJPY" onchange="updateRiskFilter()">
                                    <label class="form-check-label" for="symbol_USDJPY">
                                        <i class="fas fa-yen-sign text-success"></i> USDJPY
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="CRUDE_OIL" id="symbol_CRUDE_OIL" onchange="updateRiskFilter()">
                                    <label class="form-check-label" for="symbol_CRUDE_OIL">
                                        <i class="fas fa-oil-can text-dark"></i> 原油
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">风险分析模式</label>
                            <div class="d-flex flex-wrap gap-2">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="analysisMode" value="directional" id="mode_directional" checked onchange="updateRiskFilter()">
                                    <label class="form-check-label" for="mode_directional">
                                        <i class="fas fa-chart-line text-success"></i> 方向性分析
                                        <small class="text-muted d-block">分析上涨/下跌趋势</small>
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="analysisMode" value="conservative" id="mode_conservative" onchange="updateRiskFilter()">
                                    <label class="form-check-label" for="mode_conservative">
                                        <i class="fas fa-shield-alt text-warning"></i> 保守模式
                                        <small class="text-muted d-block">优先考虑平仓避险</small>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="alert alert-info mb-0">
                                <small>
                                    <i class="fas fa-info-circle"></i>
                                    <strong>方向性分析模式</strong>：分析风险事件对选定品种可能造成的上涨或下跌影响，提供交易建议。
                                    <strong>保守模式</strong>：遇到风险事件优先建议平仓或避免交易。
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 风险概览 -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-warning">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0"><i class="fas fa-tachometer-alt"></i> 总体风险</h5>
                </div>
                <div class="card-body text-center">
                    <h2 id="overallRisk" class="text-warning">加载中...</h2>
                    <p class="text-muted">风险等级</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="fas fa-list"></i> 事件总数</h5>
                </div>
                <div class="card-body text-center">
                    <h2 id="totalEvents" class="text-info">0</h2>
                    <p class="text-muted">个事件</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0"><i class="fas fa-fire"></i> 高风险事件</h5>
                </div>
                <div class="card-body text-center">
                    <h2 id="highRiskEvents" class="text-danger">0</h2>
                    <p class="text-muted">个高风险</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-success">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="fas fa-chart-line"></i> 风险评分</h5>
                </div>
                <div class="card-body text-center">
                    <h2 id="riskScore" class="text-success">0</h2>
                    <p class="text-muted">总评分</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 风险事件列表 -->
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-list"></i> 当前风险事件</h5>
                </div>
                <div class="card-body">
                    <div id="riskEventsList" style="max-height: 600px; overflow-y: auto;">
                        <div class="text-center">
                            <div class="spinner-border text-warning" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <p class="mt-2">加载风险事件...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-pie"></i> 风险分布</h5>
                </div>
                <div class="card-body">
                    <div id="riskDistribution">
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>极高风险</span>
                                <span class="badge bg-dark" id="criticalCount">0</span>
                            </div>
                            <div class="progress mb-2">
                                <div class="progress-bar bg-dark" id="criticalBar" style="width: 0%"></div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>高风险</span>
                                <span class="badge bg-danger" id="highCount">0</span>
                            </div>
                            <div class="progress mb-2">
                                <div class="progress-bar bg-danger" id="highBar" style="width: 0%"></div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>中风险</span>
                                <span class="badge bg-warning" id="mediumCount">0</span>
                            </div>
                            <div class="progress mb-2">
                                <div class="progress-bar bg-warning" id="mediumBar" style="width: 0%"></div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>低风险</span>
                                <span class="badge bg-success" id="lowCount">0</span>
                            </div>
                            <div class="progress mb-2">
                                <div class="progress-bar bg-success" id="lowBar" style="width: 0%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-header">
                    <h5><i class="fas fa-info-circle"></i> 风险说明</h5>
                </div>
                <div class="card-body">
                    <div class="small">
                        <div class="mb-2">
                            <span class="badge bg-dark me-2">极高风险</span>
                            建议立即保护性平仓
                        </div>
                        <div class="mb-2">
                            <span class="badge bg-danger me-2">高风险</span>
                            建议调整交易策略
                        </div>
                        <div class="mb-2">
                            <span class="badge bg-warning me-2">中风险</span>
                            需要密切关注
                        </div>
                        <div class="mb-2">
                            <span class="badge bg-success me-2">低风险</span>
                            正常监控即可
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 风险事件监控页面已加载');

    // 初始化过滤设置
    initializeRiskFilter();

    // 加载风险数据
    refreshRiskData();

    // 设置定期刷新（每分钟）
    setInterval(refreshRiskData, 60000);
});

// 刷新风险数据
async function refreshRiskData() {
    console.log('🔄 刷新风险事件数据...');
    
    try {
        const response = await fetch('/api/risk-events');
        const data = await response.json();
        
        if (data.success) {
            console.log('✅ 获取风险数据成功');
            updateRiskSummary(data.summary);
            updateRiskEventsList(data.events);
            updateLastUpdateTime();
        } else {
            console.error('❌ 获取风险数据失败:', data.error);
            showError('获取风险数据失败: ' + data.error);
        }
        
    } catch (error) {
        console.error('❌ 获取风险数据异常:', error);
        showError('获取风险数据异常: ' + error.message);
    }
}

// 更新风险概览
function updateRiskSummary(summary) {
    // 更新总体风险
    const overallRiskElement = document.getElementById('overallRisk');
    const riskColors = {
        'LOW': 'success',
        'MEDIUM': 'warning',
        'HIGH': 'danger', 
        'CRITICAL': 'dark'
    };
    const riskColor = riskColors[summary.overall_risk] || 'secondary';
    overallRiskElement.textContent = summary.overall_risk;
    overallRiskElement.className = `text-${riskColor}`;
    
    // 更新统计数据
    document.getElementById('totalEvents').textContent = summary.total_events;
    document.getElementById('highRiskEvents').textContent = 
        (summary.risk_counts.HIGH || 0) + (summary.risk_counts.CRITICAL || 0);
    document.getElementById('riskScore').textContent = summary.total_risk_score;
    
    // 更新风险分布
    updateRiskDistribution(summary.risk_counts, summary.total_events);
}

// 更新风险分布图
function updateRiskDistribution(riskCounts, totalEvents) {
    const counts = {
        critical: riskCounts.CRITICAL || 0,
        high: riskCounts.HIGH || 0,
        medium: riskCounts.MEDIUM || 0,
        low: riskCounts.LOW || 0
    };
    
    // 更新计数
    document.getElementById('criticalCount').textContent = counts.critical;
    document.getElementById('highCount').textContent = counts.high;
    document.getElementById('mediumCount').textContent = counts.medium;
    document.getElementById('lowCount').textContent = counts.low;
    
    // 更新进度条
    if (totalEvents > 0) {
        document.getElementById('criticalBar').style.width = `${(counts.critical / totalEvents) * 100}%`;
        document.getElementById('highBar').style.width = `${(counts.high / totalEvents) * 100}%`;
        document.getElementById('mediumBar').style.width = `${(counts.medium / totalEvents) * 100}%`;
        document.getElementById('lowBar').style.width = `${(counts.low / totalEvents) * 100}%`;
    } else {
        document.getElementById('criticalBar').style.width = '0%';
        document.getElementById('highBar').style.width = '0%';
        document.getElementById('mediumBar').style.width = '0%';
        document.getElementById('lowBar').style.width = '0%';
    }
}

// 更新风险事件列表
function updateRiskEventsList(events) {
    const container = document.getElementById('riskEventsList');
    const selectedSymbols = getSelectedSymbols();
    const analysisMode = getAnalysisMode();

    // 应用品种过滤
    const filteredEvents = filterRiskEvents(events, selectedSymbols, analysisMode);

    if (filteredEvents.length === 0) {
        const message = selectedSymbols.length > 0
            ? `当前无与 ${selectedSymbols.join(', ')} 相关的风险事件`
            : '当前无风险事件';

        container.innerHTML = `
            <div class="text-center text-muted">
                <i class="fas fa-check-circle fa-3x mb-3"></i>
                <h5>${message}</h5>
                <p>市场风险较低，可以正常交易</p>
            </div>
        `;
        return;
    }

    let html = '';

    filteredEvents.forEach(event => {
        const riskLevelColors = {
            'LOW': 'success',
            'MEDIUM': 'warning',
            'HIGH': 'danger',
            'CRITICAL': 'dark'
        };

        const riskLevelTexts = {
            'LOW': '低风险',
            'MEDIUM': '中风险',
            'HIGH': '高风险',
            'CRITICAL': '极高风险'
        };

        const riskLevel = event.risk_level || 'LOW';
        const riskColor = riskLevelColors[riskLevel] || 'secondary';
        const riskText = riskLevelTexts[riskLevel] || '未知';

        const eventTime = new Date(event.time).toLocaleString();

        // 为每个选择的品种分析影响
        let impactAnalysis = '';
        if (selectedSymbols.length > 0) {
            impactAnalysis = '<div class="mt-3"><h6 class="text-primary">影响分析：</h6>';
            selectedSymbols.forEach(symbol => {
                const impact = analyzeRiskImpact(event, symbol, analysisMode);
                const directionIcon = impact.direction === 'bullish' ? '📈' :
                                    impact.direction === 'bearish' ? '📉' : '➡️';
                const actionColor = impact.action === 'buy' ? 'success' :
                                  impact.action === 'sell' ? 'danger' :
                                  impact.action === 'close' ? 'warning' : 'secondary';
                const actionText = impact.action === 'buy' ? '建议买入' :
                                 impact.action === 'sell' ? '建议卖出' :
                                 impact.action === 'close' ? '建议平仓' : '继续观察';

                impactAnalysis += `
                    <div class="d-flex justify-content-between align-items-center mb-2 p-2 bg-light rounded">
                        <div>
                            <strong>${symbol}</strong> ${directionIcon}
                            <small class="text-muted">(置信度: ${Math.round(impact.confidence * 100)}%)</small>
                        </div>
                        <span class="badge bg-${actionColor}">${actionText}</span>
                    </div>
                    <small class="text-muted">${impact.reasoning}</small>
                `;
            });
            impactAnalysis += '</div>';
        }

        html += `
            <div class="border-bottom py-3">
                <div class="d-flex justify-content-between align-items-start">
                    <div class="flex-grow-1">
                        <div class="d-flex align-items-center mb-2">
                            <h6 class="mb-0 me-2">${event.title}</h6>
                            <span class="badge bg-${riskColor}">${riskText}</span>
                        </div>
                        <p class="text-muted mb-2">${event.description}</p>
                        <div class="d-flex gap-2 mb-2">
                            <span class="badge bg-secondary">${event.type || '未分类'}</span>
                            ${event.currency ? `<span class="badge bg-info">${event.currency}</span>` : ''}
                            ${event.symbol ? `<span class="badge bg-info">${event.symbol}</span>` : ''}
                            <span class="badge bg-light text-dark">${eventTime}</span>
                        </div>
                        ${impactAnalysis}
                    </div>
                </div>
            </div>
        `;
    });
    
    container.innerHTML = html;
}

// 更新最后更新时间
function updateLastUpdateTime() {
    const now = new Date();
    document.getElementById('lastUpdate').textContent = `最后更新: ${now.toLocaleTimeString()}`;
}

// 显示错误信息
function showError(message) {
    const container = document.getElementById('riskEventsList');
    container.innerHTML = `
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle"></i>
            <strong>错误：</strong> ${message}
        </div>
    `;
}

// 获取选择的交易品种
function getSelectedSymbols() {
    const checkboxes = document.querySelectorAll('input[type="checkbox"][id^="symbol_"]:checked');
    return Array.from(checkboxes).map(cb => cb.value);
}

// 获取风险分析模式
function getAnalysisMode() {
    const modeRadio = document.querySelector('input[name="analysisMode"]:checked');
    return modeRadio ? modeRadio.value : 'directional';
}

// 更新风险过滤设置
function updateRiskFilter() {
    const selectedSymbols = getSelectedSymbols();
    const analysisMode = getAnalysisMode();

    console.log('🔧 风险过滤设置已更新:', {
        symbols: selectedSymbols,
        mode: analysisMode
    });

    // 保存设置到localStorage
    localStorage.setItem('riskFilterSymbols', JSON.stringify(selectedSymbols));
    localStorage.setItem('riskAnalysisMode', analysisMode);

    // 重新加载风险数据
    refreshRiskData();

    // 显示过滤状态
    updateFilterStatus(selectedSymbols, analysisMode);
}

// 更新过滤状态显示
function updateFilterStatus(symbols, mode) {
    const statusText = symbols.length > 0
        ? `关注品种: ${symbols.join(', ')} | 模式: ${mode === 'directional' ? '方向性分析' : '保守模式'}`
        : '未选择关注品种';

    // 在页面顶部显示当前过滤状态
    let statusBar = document.getElementById('filterStatusBar');
    if (!statusBar) {
        statusBar = document.createElement('div');
        statusBar.id = 'filterStatusBar';
        statusBar.className = 'alert alert-info mb-3';
        document.querySelector('.container-fluid').insertBefore(statusBar, document.querySelector('.row'));
    }

    statusBar.innerHTML = `
        <i class="fas fa-filter"></i>
        <strong>当前过滤设置：</strong> ${statusText}
    `;
}

// 过滤风险事件
function filterRiskEvents(events, selectedSymbols, analysisMode) {
    if (selectedSymbols.length === 0) {
        return events; // 如果没有选择品种，显示所有事件
    }

    return events.filter(event => {
        // 检查事件是否与选择的品种相关
        const eventText = (event.title + ' ' + event.description + ' ' + (event.affected_instruments || '')).toLowerCase();

        return selectedSymbols.some(symbol => {
            const symbolLower = symbol.toLowerCase();

            // 品种关键词映射
            const symbolKeywords = {
                'xauusd': ['gold', 'xau', '黄金', '贵金属', 'precious metal'],
                'eurusd': ['eur', 'euro', '欧元', 'european', 'ecb'],
                'gbpusd': ['gbp', 'pound', '英镑', 'sterling', 'uk', 'britain'],
                'usdjpy': ['jpy', 'yen', '日元', 'japan', 'boj'],
                'crude_oil': ['oil', 'crude', '原油', '石油', 'wti', 'brent']
            };

            // 检查直接匹配
            if (eventText.includes(symbolLower)) {
                return true;
            }

            // 检查关键词匹配
            const keywords = symbolKeywords[symbolLower] || [];
            return keywords.some(keyword => eventText.includes(keyword));
        });
    });
}

// 分析风险事件对品种的影响
function analyzeRiskImpact(event, symbol, analysisMode) {
    const eventText = (event.title + ' ' + event.description).toLowerCase();
    const symbolLower = symbol.toLowerCase();

    // 基于事件内容和品种特性分析影响
    let impact = {
        direction: 'neutral', // 'bullish', 'bearish', 'neutral'
        confidence: 0.5,
        reasoning: '',
        action: 'monitor' // 'buy', 'sell', 'close', 'monitor'
    };

    // 黄金特定分析
    if (symbolLower === 'xauusd') {
        impact = analyzeGoldImpact(eventText, event.risk_level);
    }
    // 欧元特定分析
    else if (symbolLower === 'eurusd') {
        impact = analyzeEurImpact(eventText, event.risk_level);
    }
    // 英镑特定分析
    else if (symbolLower === 'gbpusd') {
        impact = analyzeGbpImpact(eventText, event.risk_level);
    }
    // 日元特定分析
    else if (symbolLower === 'usdjpy') {
        impact = analyzeJpyImpact(eventText, event.risk_level);
    }

    // 根据分析模式调整建议
    if (analysisMode === 'conservative' && event.risk_level === 'HIGH') {
        impact.action = 'close';
        impact.reasoning += ' (保守模式建议平仓避险)';
    }

    return impact;
}

// 黄金影响分析
function analyzeGoldImpact(eventText, riskLevel) {
    let impact = { direction: 'neutral', confidence: 0.5, reasoning: '', action: 'monitor' };

    // 利好黄金的因素
    if (eventText.includes('inflation') || eventText.includes('通胀') ||
        eventText.includes('uncertainty') || eventText.includes('不确定') ||
        eventText.includes('crisis') || eventText.includes('危机') ||
        eventText.includes('war') || eventText.includes('战争') ||
        eventText.includes('tension') || eventText.includes('紧张')) {
        impact.direction = 'bullish';
        impact.confidence = 0.7;
        impact.reasoning = '避险情绪推动黄金上涨';
        impact.action = 'buy';
    }
    // 利空黄金的因素
    else if (eventText.includes('rate hike') || eventText.includes('加息') ||
             eventText.includes('strong dollar') || eventText.includes('美元走强') ||
             eventText.includes('economic growth') || eventText.includes('经济增长')) {
        impact.direction = 'bearish';
        impact.confidence = 0.6;
        impact.reasoning = '美元走强或加息预期压制黄金';
        impact.action = 'sell';
    }

    // 极高风险事件
    if (riskLevel === 'CRITICAL') {
        impact.confidence = Math.min(impact.confidence + 0.2, 0.9);
        if (impact.direction === 'neutral') {
            impact.direction = 'bullish';
            impact.reasoning = '极高风险事件通常利好避险资产黄金';
            impact.action = 'buy';
        }
    }

    return impact;
}

// 欧元影响分析
function analyzeEurImpact(eventText, riskLevel) {
    let impact = { direction: 'neutral', confidence: 0.5, reasoning: '', action: 'monitor' };

    if (eventText.includes('ecb') || eventText.includes('欧央行') ||
        eventText.includes('eurozone') || eventText.includes('欧元区')) {
        if (eventText.includes('rate cut') || eventText.includes('降息')) {
            impact.direction = 'bearish';
            impact.confidence = 0.7;
            impact.reasoning = '欧央行降息利空欧元';
            impact.action = 'sell';
        } else if (eventText.includes('rate hike') || eventText.includes('加息')) {
            impact.direction = 'bullish';
            impact.confidence = 0.7;
            impact.reasoning = '欧央行加息利好欧元';
            impact.action = 'buy';
        }
    }

    return impact;
}

// 英镑影响分析
function analyzeGbpImpact(eventText, riskLevel) {
    let impact = { direction: 'neutral', confidence: 0.5, reasoning: '', action: 'monitor' };

    if (eventText.includes('boe') || eventText.includes('英央行') ||
        eventText.includes('uk') || eventText.includes('英国')) {
        if (eventText.includes('brexit') || eventText.includes('脱欧')) {
            impact.direction = 'bearish';
            impact.confidence = 0.6;
            impact.reasoning = '脱欧相关不确定性利空英镑';
            impact.action = 'sell';
        }
    }

    return impact;
}

// 日元影响分析
function analyzeJpyImpact(eventText, riskLevel) {
    let impact = { direction: 'neutral', confidence: 0.5, reasoning: '', action: 'monitor' };

    if (eventText.includes('boj') || eventText.includes('日央行') ||
        eventText.includes('japan') || eventText.includes('日本')) {
        if (eventText.includes('intervention') || eventText.includes('干预')) {
            impact.direction = 'bullish';
            impact.confidence = 0.8;
            impact.reasoning = '日本央行干预支撑日元';
            impact.action = 'buy';
        }
    }

    // 避险情绪通常利好日元
    if (riskLevel === 'HIGH' || riskLevel === 'CRITICAL') {
        impact.direction = 'bullish';
        impact.confidence = 0.6;
        impact.reasoning = '避险情绪推动日元上涨';
        impact.action = 'buy';
    }

    return impact;
}

// 初始化过滤设置
function initializeRiskFilter() {
    // 从localStorage恢复设置
    const savedSymbols = localStorage.getItem('riskFilterSymbols');
    const savedMode = localStorage.getItem('riskAnalysisMode');

    if (savedSymbols) {
        const symbols = JSON.parse(savedSymbols);
        symbols.forEach(symbol => {
            const checkbox = document.getElementById(`symbol_${symbol}`);
            if (checkbox) checkbox.checked = true;
        });
    }

    if (savedMode) {
        const modeRadio = document.getElementById(`mode_${savedMode}`);
        if (modeRadio) modeRadio.checked = true;
    }

    // 初始化过滤状态显示
    updateRiskFilter();
}
</script>
{% endblock %}
