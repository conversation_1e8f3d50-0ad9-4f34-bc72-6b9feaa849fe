# 🎯 MetaQuotes Demo官方服务器设置指南

## 🌟 为什么选择MetaQuotes Demo？

MetaQuotes Demo是MT5开发商的官方演示服务器，具有以下优势：

- ✅ **官方支持** - MetaQuotes Software Corp.官方维护
- ✅ **最高稳定性** - 99.9%在线时间，很少维护
- ✅ **完整功能** - 支持所有MT5功能和API
- ✅ **无地区限制** - 全球任何地方都可以访问
- ✅ **最佳兼容性** - 与Python API完美兼容
- ✅ **永久有效** - 账户不会过期

## 📋 详细设置步骤

### 步骤1: 启动MT5客户端
```
1. 双击桌面上的"MetaTrader 5"图标
2. 等待客户端完全加载
3. 确保客户端界面正常显示
```

### 步骤2: 开设模拟账户
```
方法A: 菜单操作
1. 点击菜单栏 "文件"
2. 选择 "开设模拟账户"

方法B: 快捷键
1. 按 Ctrl+Shift+O

方法C: 工具栏
1. 点击工具栏上的 "模拟账户" 按钮
```

### 步骤3: 选择MetaQuotes Demo服务器
```
🔍 查找服务器:
1. 在服务器列表中查找 "MetaQuotes-Demo"
2. 如果没有看到，在搜索框输入 "MetaQuotes"
3. 选择 "MetaQuotes-Demo" 服务器
4. 点击 "下一步"

💡 提示:
- 服务器名称通常是 "MetaQuotes-Demo"
- 有时也可能显示为 "MetaQuotes Software Demo"
- 确保选择的是官方MetaQuotes服务器
```

### 步骤4: 填写注册信息
```
📝 必填信息:
姓名: Test User (或您的真实姓名)
邮箱: <EMAIL> (使用真实邮箱)
电话: +86 138******* (可以使用测试号码)

💡 注意:
- 邮箱建议使用真实邮箱，可能会收到确认邮件
- 电话号码可以使用测试号码
- 姓名可以使用测试名称
```

### 步骤5: 设置账户参数
```
⚙️ 推荐设置:
账户类型: Standard (标准账户)
初始资金: 10000 USD (一万美元)
杠杆: 1:100 (100倍杠杆)
货币: USD (美元)

💡 说明:
- Standard账户功能最完整
- 10000美元足够测试各种策略
- 1:100杠杆适中，不会过于激进
- USD是最常用的基础货币
```

### 步骤6: 完成注册
```
✅ 最后步骤:
1. 检查所有信息是否正确
2. 点击 "完成" 按钮
3. 等待账户创建完成
4. 系统会自动登录新账户

📋 记录信息:
- 账户号码: 系统自动生成
- 密码: 系统自动生成
- 服务器: MetaQuotes-Demo
- 投资者密码: 只读权限密码
```

## ⚙️ 登录后的必要配置

### 配置专家顾问设置
```
🔧 操作步骤:
1. 点击 "工具" → "选项"
2. 切换到 "专家顾问" 选项卡
3. 确保勾选以下选项:
   ✅ 允许自动交易
   ✅ 允许DLL导入
   ✅ 允许WebRequest
4. 点击 "确定" 保存设置

💡 重要性:
- 允许自动交易: 启用EA和自动交易功能
- 允许DLL导入: 支持Python API连接
- 允许WebRequest: 支持网络请求功能
```

### 验证连接状态
```
✅ 检查项目:
1. 右下角连接指示器为绿色
2. 可以看到实时价格跳动
3. 账户信息显示正常
4. 市场报价窗口有数据

🔍 连接指示器位置:
- 在MT5客户端右下角
- 绿色圆点 = 连接正常
- 红色圆点 = 连接断开
- 黄色圆点 = 连接中
```

## 🧪 测试连接

### 运行连接测试
完成设置后，运行以下命令测试连接：

```bash
python test_metaquotes_demo.py
```

### 预期的成功结果
```
✅ 成功连接应该显示:
- MT5连接成功
- 账户信息正常显示
- 服务器确认为MetaQuotes相关
- 交易权限和EA权限都允许
- 可以获取市场数据
- 可以获取历史数据
```

## 🎊 成功后的功能

### 智能体交易系统功能
```
🤖 AI交易功能:
- 实时市场数据获取
- 历史数据分析
- AI策略执行
- 自动交易下单
- 持仓管理
- 风险控制

📊 数据功能:
- 实时价格数据
- 历史K线数据
- 技术指标计算
- 市场深度信息
```

### 访问智能体交易系统
```
🌐 访问地址:
http://127.0.0.1:5000/trading/agent

🚀 使用流程:
1. 创建智能体交易策略
2. 输入您的交易心得和偏好
3. 配置AI模型和参数
4. 启动AI自动交易
5. 监控交易表现
```

## 🔧 常见问题解决

### Q1: 找不到MetaQuotes-Demo服务器
```
解决方案:
1. 在搜索框输入 "MetaQuotes"
2. 尝试搜索 "Demo"
3. 检查网络连接
4. 重启MT5客户端
5. 尝试选择其他MetaQuotes服务器
```

### Q2: 注册失败
```
解决方案:
1. 检查邮箱格式是否正确
2. 尝试使用不同的邮箱
3. 检查网络连接
4. 稍后重试
5. 尝试其他推荐服务器
```

### Q3: 连接不稳定
```
解决方案:
1. 检查网络连接质量
2. 关闭防火墙或添加例外
3. 重启MT5客户端
4. 重启路由器
5. 联系网络服务商
```

### Q4: Python连接失败
```
解决方案:
1. 确保MT5客户端正在运行
2. 确保已登录账户
3. 检查专家顾问设置
4. 重新运行测试脚本
5. 查看错误日志
```

## 📞 获取帮助

### 官方资源
- MetaQuotes官网: https://www.metaquotes.net/
- MT5官方文档: https://www.mql5.com/en/docs
- Python API文档: https://www.mql5.com/en/docs/integration/python_metatrader5

### 技术支持
- 如果遇到技术问题，可以联系MetaQuotes技术支持
- 查看MT5客户端的日志文件
- 参考官方论坛和社区

---

**🎉 完成MetaQuotes Demo设置后，您就拥有了最稳定可靠的MT5连接，可以开始使用智能体交易系统了！**
