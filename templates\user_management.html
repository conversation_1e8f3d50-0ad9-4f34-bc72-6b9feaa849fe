{% extends "base.html" %}

{% block page_title %}用户管理{% endblock %}

{% block content %}
<!-- 用户统计和设置 - 居中显示 -->
<div class="row justify-content-center mb-4">
    <!-- 用户统计 -->
    <div class="col-lg-5 col-md-6 mb-3">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie"></i>
                    用户统计
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <div class="border-end">
                            <h4 class="text-primary" id="totalUsers">-</h4>
                            <small class="text-muted">总用户数</small>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <h4 class="text-warning" id="pendingUsers">-</h4>
                        <small class="text-muted">待审核</small>
                    </div>
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-success" id="approvedUsers">-</h4>
                            <small class="text-muted">已审核</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-info" id="vipUsers">-</h4>
                        <small class="text-muted">VIP用户</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 用户注册设置 -->
    <div class="col-lg-5 col-md-6 mb-3">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-cog"></i>
                    用户注册设置
                </h5>
            </div>
            <div class="card-body">
                <div class="form-check form-switch mb-3">
                    <input class="form-check-input" type="checkbox" id="enableUserRegistration">
                    <label class="form-check-label" for="enableUserRegistration">
                        开启用户注册功能
                    </label>
                </div>

                <div class="form-check form-switch mb-3">
                    <input class="form-check-input" type="checkbox" id="requireApproval">
                    <label class="form-check-label" for="requireApproval">
                        新用户需要审核
                    </label>
                </div>

                <div class="form-check form-switch mb-3">
                    <input class="form-check-input" type="checkbox" id="enableEmailVerification">
                    <label class="form-check-label" for="enableEmailVerification">
                        邮箱验证
                    </label>
                </div>

                <button class="btn btn-primary btn-sm w-100" onclick="saveUserSettings()">
                    <i class="fas fa-save"></i>
                    保存设置
                </button>
            </div>
        </div>
    </div>

    <!-- MT5交易设置 -->
    <div class="col-lg-5 col-md-6 mb-3">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line"></i>
                    MT5交易设置
                </h5>
            </div>
            <div class="card-body">
                <div class="form-check form-switch mb-3">
                    <input class="form-check-input" type="checkbox" id="mt5DemoAsReal" checked>
                    <label class="form-check-label" for="mt5DemoAsReal">
                        MT5 Demo账户作为真实账户处理
                    </label>
                    <small class="form-text text-muted">
                        开启后，MT5的Demo账户可以在真实交易页面使用
                    </small>
                </div>

                <div class="form-check form-switch mb-3">
                    <input class="form-check-input" type="checkbox" id="enableMT5AutoConnect" checked>
                    <label class="form-check-label" for="enableMT5AutoConnect">
                        启用MT5自动连接
                    </label>
                    <small class="form-text text-muted">
                        页面加载时自动尝试连接MT5客户端
                    </small>
                </div>

                <div class="form-check form-switch mb-3">
                    <input class="form-check-input" type="checkbox" id="enableMT5OrderSync" checked>
                    <label class="form-check-label" for="enableMT5OrderSync">
                        启用MT5订单同步
                    </label>
                    <small class="form-text text-muted">
                        同步MT5客户端的订单到Web界面
                    </small>
                </div>

                <button class="btn btn-success btn-sm w-100" onclick="saveMT5Settings()">
                    <i class="fas fa-save"></i>
                    保存MT5设置
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 用户组权限管理 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-users-cog"></i>
                    用户组权限管理
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>功能说明：</strong>管理员可以通过点选的方式，为不同用户组配置模块访问权限。
                </div>

                <div class="row">
                    <!-- 普通用户权限 -->
                    <div class="col-md-4 mb-4">
                        <div class="card border-primary">
                            <div class="card-header bg-primary text-white">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-user"></i>
                                    普通用户权限
                                </h6>
                            </div>
                            <div class="card-body">
                                <form id="normalUserPermissions">
                                    <div class="mb-3">
                                        <h6 class="text-primary">基础模块</h6>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="normal_dashboard" checked disabled>
                                            <label class="form-check-label" for="normal_dashboard">
                                                仪表盘 <small class="text-muted">(必需)</small>
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="normal_demo_trading" checked>
                                            <label class="form-check-label" for="normal_demo_trading">
                                                模拟交易
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="normal_trading_query">
                                            <label class="form-check-label" for="normal_trading_query">
                                                交易查询
                                            </label>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <h6 class="text-warning">交易模块</h6>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="normal_real_trading">
                                            <label class="form-check-label" for="normal_real_trading">
                                                真实交易
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="normal_sn_position">
                                            <label class="form-check-label" for="normal_sn_position">
                                                SN加仓
                                            </label>
                                        </div>
                                    </div>

                                    <button type="button" class="btn btn-primary btn-sm w-100" onclick="saveUserGroupPermissions('normal')">
                                        <i class="fas fa-save"></i>
                                        保存普通用户权限
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- VIP用户权限 -->
                    <div class="col-md-4 mb-4">
                        <div class="card border-warning">
                            <div class="card-header bg-warning text-dark">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-crown"></i>
                                    VIP用户权限
                                </h6>
                            </div>
                            <div class="card-body">
                                <form id="vipUserPermissions">
                                    <div class="mb-3">
                                        <h6 class="text-primary">基础模块</h6>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="vip_dashboard" checked disabled>
                                            <label class="form-check-label" for="vip_dashboard">
                                                仪表盘 <small class="text-muted">(必需)</small>
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="vip_demo_trading" checked>
                                            <label class="form-check-label" for="vip_demo_trading">
                                                模拟交易
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="vip_trading_query" checked>
                                            <label class="form-check-label" for="vip_trading_query">
                                                交易查询
                                            </label>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <h6 class="text-warning">交易模块</h6>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="vip_real_trading" checked>
                                            <label class="form-check-label" for="vip_real_trading">
                                                真实交易
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="vip_agent_trading" checked>
                                            <label class="form-check-label" for="vip_agent_trading">
                                                智能交易
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="vip_sn_position" checked>
                                            <label class="form-check-label" for="vip_sn_position">
                                                SN加仓
                                            </label>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <h6 class="text-success">分析模块</h6>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="vip_ai_training" checked>
                                            <label class="form-check-label" for="vip_ai_training">
                                                AI训练
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="vip_pattern_monitoring" checked>
                                            <label class="form-check-label" for="vip_pattern_monitoring">
                                                模式监控
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="vip_risk_events">
                                            <label class="form-check-label" for="vip_risk_events">
                                                风险事件
                                            </label>
                                        </div>
                                    </div>

                                    <button type="button" class="btn btn-warning btn-sm w-100" onclick="saveUserGroupPermissions('vip')">
                                        <i class="fas fa-save"></i>
                                        保存VIP用户权限
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- 管理员权限 -->
                    <div class="col-md-4 mb-4">
                        <div class="card border-danger">
                            <div class="card-header bg-danger text-white">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-user-shield"></i>
                                    管理员权限
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-success">
                                    <i class="fas fa-check-circle"></i>
                                    <strong>完全权限</strong>
                                    <br>管理员拥有所有模块的访问权限，包括用户管理和系统设置。
                                </div>

                                <div class="mb-3">
                                    <h6 class="text-info">管理员专属</h6>
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-check text-success"></i> 用户管理</li>
                                        <li><i class="fas fa-check text-success"></i> 系统设置</li>
                                        <li><i class="fas fa-check text-success"></i> AI模型管理</li>
                                        <li><i class="fas fa-check text-success"></i> 权限配置</li>
                                        <li><i class="fas fa-check text-success"></i> 所有交易模块</li>
                                        <li><i class="fas fa-check text-success"></i> 所有分析模块</li>
                                    </ul>
                                </div>

                                <button type="button" class="btn btn-outline-secondary btn-sm w-100" disabled>
                                    <i class="fas fa-lock"></i>
                                    权限不可修改
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-12">
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle"></i> 权限说明</h6>
                            <ul class="mb-0">
                                <li><strong>普通用户</strong>：基础交易功能，适合新手用户</li>
                                <li><strong>VIP用户</strong>：完整交易和分析功能，可查看自己训练的AI模型</li>
                                <li><strong>管理员</strong>：完全权限，可管理所有用户和系统设置</li>
                                <li><strong>权限继承</strong>：高级用户组自动包含低级用户组的所有权限</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 用户管理列表 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-users"></i>
                    用户管理列表
                </h5>
                <div>
                    <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addUserModal">
                        <i class="fas fa-plus"></i>
                        添加用户
                    </button>
                    <button class="btn btn-info btn-sm" onclick="refreshUserList()">
                        <i class="fas fa-sync"></i>
                        刷新
                    </button>
                </div>
            </div>
            <div class="card-body">
                <!-- 搜索和筛选 -->
                <div class="row mb-3">
                    <div class="col-md-4">
                        <input type="text" class="form-control" id="searchUser" placeholder="搜索用户名或姓名...">
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="filterUserType">
                            <option value="">所有用户类型</option>
                            <option value="normal">普通用户</option>
                            <option value="vip">VIP用户</option>
                            <option value="admin">管理员用户</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="filterApprovalStatus">
                            <option value="">所有审核状态</option>
                            <option value="approved">已审核</option>
                            <option value="pending">待审核</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-outline-secondary w-100" onclick="applyFilters()">
                            <i class="fas fa-filter"></i>
                            筛选
                        </button>
                    </div>
                </div>

                <!-- 用户表格 -->
                <div class="table-responsive">
                    <table class="table table-hover" id="usersTable">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>用户名</th>
                                <th>姓名</th>
                                <th>手机号</th>
                                <th>邮箱</th>
                                <th>用户类型</th>
                                <th>审核状态</th>
                                <th>注册时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="usersTableBody">
                            <tr>
                                <td colspan="9" class="text-center">
                                    <div class="spinner-border spinner-border-sm" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    正在加载用户列表...
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <nav aria-label="用户列表分页">
                    <ul class="pagination justify-content-center" id="usersPagination">
                        <!-- 分页按钮将通过JavaScript生成 -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>

<!-- 添加用户模态框 -->
<div class="modal fade" id="addUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">添加新用户</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addUserForm">
                    <div class="mb-3">
                        <label class="form-label">用户名 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" name="username" required>
                        <small class="text-muted">用于登录的用户名</small>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">邮箱 <span class="text-danger">*</span></label>
                        <input type="email" class="form-control" name="email" required>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">手机号</label>
                        <input type="tel" class="form-control" name="phone">
                    </div>

                    <div class="mb-3">
                        <label class="form-label">姓名</label>
                        <input type="text" class="form-control" name="real_name">
                        <small class="text-muted">真实姓名（可选）</small>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">密码 <span class="text-danger">*</span></label>
                        <input type="password" class="form-control" name="password" required>
                        <small class="text-muted">至少6位字符</small>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">确认密码 <span class="text-danger">*</span></label>
                        <input type="password" class="form-control" name="confirm_password" required>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">用户类型</label>
                        <select class="form-select" name="user_type">
                            <option value="normal">普通用户</option>
                            <option value="vip">VIP用户</option>
                            <option value="admin">管理员用户</option>
                        </select>
                    </div>

                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="auto_approve" id="autoApprove">
                        <label class="form-check-label" for="autoApprove">
                            自动审核通过
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="addUser()">添加用户</button>
            </div>
        </div>
    </div>
</div>

<!-- 编辑用户模态框 -->
<div class="modal fade" id="editUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">编辑用户</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editUserForm">
                    <input type="hidden" name="user_id" id="editUserId">

                    <div class="mb-3">
                        <label class="form-label">用户名</label>
                        <input type="text" class="form-control" name="username" id="editUsername" readonly>
                        <small class="text-muted">用户名不可修改</small>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">邮箱</label>
                        <input type="email" class="form-control" name="email" id="editEmail">
                    </div>

                    <div class="mb-3">
                        <label class="form-label">手机号</label>
                        <input type="tel" class="form-control" name="phone" id="editPhone">
                    </div>

                    <div class="mb-3">
                        <label class="form-label">姓名</label>
                        <input type="text" class="form-control" name="real_name" id="editRealName">
                    </div>

                    <div class="mb-3">
                        <label class="form-label">用户类型</label>
                        <select class="form-select" name="user_type" id="editUserType">
                            <option value="normal">普通用户</option>
                            <option value="vip">VIP用户</option>
                            <option value="admin">管理员用户</option>
                        </select>
                    </div>

                    <div class="row">
                        <div class="col-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="is_approved" id="editIsApproved">
                                <label class="form-check-label" for="editIsApproved">
                                    审核通过
                                </label>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="is_active" id="editIsActive">
                                <label class="form-check-label" for="editIsActive">
                                    账户激活
                                </label>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="updateUser()">保存修改</button>
            </div>
        </div>
    </div>
</div>

<script>
let currentPage = 1;
let totalPages = 1;
let usersData = [];

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    loadUserList();
    loadUserStats();
    loadRegistrationSettings();
});

// 加载用户列表
function loadUserList(page = 1) {
    currentPage = page;

    fetch(`/api/users?page=${page}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            usersData = data.users;
            totalPages = data.total_pages;
            displayUsers(data.users);
            updatePagination();
        } else {
            console.error('加载用户列表失败:', data.error);
        }
    })
    .catch(error => {
        console.error('请求失败:', error);
        document.getElementById('usersTableBody').innerHTML =
            '<tr><td colspan="9" class="text-center text-danger">加载失败，请刷新重试</td></tr>';
    });
}

// 显示用户列表
function displayUsers(users) {
    const tbody = document.getElementById('usersTableBody');

    if (users.length === 0) {
        tbody.innerHTML = '<tr><td colspan="9" class="text-center text-muted">暂无用户数据</td></tr>';
        return;
    }

    tbody.innerHTML = users.map(user => `
        <tr>
            <td>${user.id}</td>
            <td>
                <div class="d-flex align-items-center">
                    <div class="avatar-sm me-2">
                        <div class="avatar-title bg-primary rounded-circle">
                            ${user.username.charAt(0).toUpperCase()}
                        </div>
                    </div>
                    ${user.username}
                </div>
            </td>
            <td>${user.real_name || '-'}</td>
            <td>${user.phone || '-'}</td>
            <td>${user.email}</td>
            <td>
                <span class="badge bg-${getUserTypeBadgeColor(user.user_type)}">
                    ${getUserTypeText(user.user_type)}
                </span>
            </td>
            <td>
                <span class="badge bg-${user.is_approved ? 'success' : 'warning'}">
                    ${user.is_approved ? '已审核' : '待审核'}
                </span>
            </td>
            <td>${new Date(user.created_at).toLocaleDateString()}</td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-primary" onclick="editUser(${user.id})" title="编辑">
                        <i class="fas fa-edit"></i>
                    </button>
                    ${!user.is_approved ? `
                        <button class="btn btn-outline-success" onclick="approveUser(${user.id})" title="审核通过">
                            <i class="fas fa-check"></i>
                        </button>
                    ` : ''}
                    <button class="btn btn-outline-danger" onclick="deleteUser(${user.id})" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

// 获取用户类型徽章颜色
function getUserTypeBadgeColor(userType) {
    switch(userType) {
        case 'admin': return 'danger';
        case 'vip': return 'warning';
        case 'normal': return 'secondary';
        default: return 'secondary';
    }
}

// 获取用户类型文本
function getUserTypeText(userType) {
    switch(userType) {
        case 'admin': return '管理员';
        case 'vip': return 'VIP用户';
        case 'normal': return '普通用户';
        default: return '未知';
    }
}

// 更新分页
function updatePagination() {
    const pagination = document.getElementById('usersPagination');
    let paginationHTML = '';

    // 上一页
    paginationHTML += `
        <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="loadUserList(${currentPage - 1})">上一页</a>
        </li>
    `;

    // 页码
    for (let i = 1; i <= totalPages; i++) {
        if (i === currentPage || i === 1 || i === totalPages || (i >= currentPage - 1 && i <= currentPage + 1)) {
            paginationHTML += `
                <li class="page-item ${i === currentPage ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="loadUserList(${i})">${i}</a>
                </li>
            `;
        } else if (i === currentPage - 2 || i === currentPage + 2) {
            paginationHTML += '<li class="page-item disabled"><span class="page-link">...</span></li>';
        }
    }

    // 下一页
    paginationHTML += `
        <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="loadUserList(${currentPage + 1})">下一页</a>
        </li>
    `;

    pagination.innerHTML = paginationHTML;
}

// 加载用户统计
function loadUserStats() {
    fetch('/api/users/stats')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('totalUsers').textContent = data.stats.total;
            document.getElementById('pendingUsers').textContent = data.stats.pending;
            document.getElementById('approvedUsers').textContent = data.stats.approved;
            document.getElementById('vipUsers').textContent = data.stats.vip;
        }
    })
    .catch(error => console.error('加载统计失败:', error));
}

// 添加用户
function addUser() {
    const form = document.getElementById('addUserForm');
    const formData = new FormData(form);

    // 验证密码
    if (formData.get('password') !== formData.get('confirm_password')) {
        alert('两次输入的密码不一致');
        return;
    }

    const userData = {
        username: formData.get('username'),
        email: formData.get('email'),
        phone: formData.get('phone'),
        real_name: formData.get('real_name'),
        password: formData.get('password'),
        user_type: formData.get('user_type'),
        auto_approve: formData.get('auto_approve') === 'on'
    };

    fetch('/api/users', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(userData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('用户添加成功');
            bootstrap.Modal.getInstance(document.getElementById('addUserModal')).hide();
            form.reset();
            loadUserList();
            loadUserStats();
        } else {
            alert('添加失败: ' + data.error);
        }
    })
    .catch(error => {
        console.error('请求失败:', error);
        alert('添加失败，请重试');
    });
}

// 编辑用户
function editUser(userId) {
    const user = usersData.find(u => u.id === userId);
    if (!user) return;

    document.getElementById('editUserId').value = user.id;
    document.getElementById('editUsername').value = user.username;
    document.getElementById('editEmail').value = user.email;
    document.getElementById('editPhone').value = user.phone || '';
    document.getElementById('editRealName').value = user.real_name || '';
    document.getElementById('editUserType').value = user.user_type;
    document.getElementById('editIsApproved').checked = user.is_approved;
    document.getElementById('editIsActive').checked = user.is_active;

    new bootstrap.Modal(document.getElementById('editUserModal')).show();
}

// 更新用户
function updateUser() {
    const form = document.getElementById('editUserForm');
    const formData = new FormData(form);
    const userId = formData.get('user_id');

    const userData = {
        email: formData.get('email'),
        phone: formData.get('phone'),
        real_name: formData.get('real_name'),
        user_type: formData.get('user_type'),
        is_approved: formData.get('is_approved') === 'on',
        is_active: formData.get('is_active') === 'on'
    };

    fetch(`/api/users/${userId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(userData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('用户信息更新成功');
            bootstrap.Modal.getInstance(document.getElementById('editUserModal')).hide();
            loadUserList();
            loadUserStats();
        } else {
            alert('更新失败: ' + data.error);
        }
    })
    .catch(error => {
        console.error('请求失败:', error);
        alert('更新失败，请重试');
    });
}

// 审核用户
function approveUser(userId) {
    if (!confirm('确认审核通过该用户？')) return;

    fetch(`/api/users/${userId}/approve`, {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('用户审核通过');
            loadUserList();
            loadUserStats();
        } else {
            alert('审核失败: ' + data.error);
        }
    })
    .catch(error => {
        console.error('请求失败:', error);
        alert('审核失败，请重试');
    });
}

// 删除用户
function deleteUser(userId) {
    if (!confirm('确认删除该用户？此操作不可恢复！')) return;

    fetch(`/api/users/${userId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('用户删除成功');
            loadUserList();
            loadUserStats();
        } else {
            alert('删除失败: ' + data.error);
        }
    })
    .catch(error => {
        console.error('请求失败:', error);
        alert('删除失败，请重试');
    });
}

// 刷新用户列表
function refreshUserList() {
    loadUserList(currentPage);
    loadUserStats();
}

// 应用筛选
function applyFilters() {
    const searchTerm = document.getElementById('searchUser').value;
    const userType = document.getElementById('filterUserType').value;
    const approvalStatus = document.getElementById('filterApprovalStatus').value;

    const params = new URLSearchParams();
    if (searchTerm) params.append('search', searchTerm);
    if (userType) params.append('user_type', userType);
    if (approvalStatus) params.append('approval_status', approvalStatus);

    fetch(`/api/users?${params.toString()}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayUsers(data.users);
            // 重置分页为第一页
            currentPage = 1;
            totalPages = data.total_pages;
            updatePagination();
        }
    })
    .catch(error => console.error('筛选失败:', error));
}

// 加载注册设置
function loadRegistrationSettings() {
    fetch('/api/system-settings/registration')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('enableUserRegistration').checked = data.settings.enable_registration;
            document.getElementById('requireApproval').checked = data.settings.require_approval;
        }
    })
    .catch(error => console.error('加载设置失败:', error));
}

// 保存注册设置
function saveRegistrationSettings() {
    const settings = {
        enable_registration: document.getElementById('enableUserRegistration').checked,
        require_approval: document.getElementById('requireApproval').checked
    };

    fetch('/api/system-settings/registration', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(settings)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('设置保存成功');
        } else {
            alert('保存失败: ' + data.error);
        }
    })
    .catch(error => {
        console.error('请求失败:', error);
        alert('保存失败，请重试');
    });
}

// 加载MT5设置
function loadMT5Settings() {
    fetch('/api/system-settings/mt5')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('mt5DemoAsReal').checked = data.settings.mt5_demo_as_real;
            document.getElementById('enableMT5AutoConnect').checked = data.settings.enable_mt5_auto_connect;
            document.getElementById('enableMT5OrderSync').checked = data.settings.enable_mt5_order_sync;
        }
    })
    .catch(error => console.error('加载MT5设置失败:', error));
}

// 保存MT5设置
function saveMT5Settings() {
    const settings = {
        mt5_demo_as_real: document.getElementById('mt5DemoAsReal').checked,
        enable_mt5_auto_connect: document.getElementById('enableMT5AutoConnect').checked,
        enable_mt5_order_sync: document.getElementById('enableMT5OrderSync').checked
    };

    fetch('/api/system-settings/mt5', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(settings)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('MT5设置保存成功');
            // 刷新页面以应用新设置
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            alert('保存失败: ' + data.error);
        }
    })
    .catch(error => {
        console.error('请求失败:', error);
        alert('保存失败，请重试');
    });
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    loadUsers();
    loadUserStats();
    loadRegistrationSettings();
    loadMT5Settings();
});
</script>
{% endblock %}
