#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建AI交易相关的数据库表
解决AI推理交易无法执行的问题
"""

import sqlite3
import os
from datetime import datetime

def create_ai_trading_tables():
    """创建AI交易相关的数据库表"""
    
    # 尝试多个可能的数据库路径
    db_paths = [
        'instance/matetrade4.db',
        'matetrade4.db', 
        'trading_system.db',
        'instance/trading_system.db'
    ]
    
    conn = None
    db_path = None
    for path in db_paths:
        if os.path.exists(path):
            try:
                conn = sqlite3.connect(path)
                db_path = path
                print(f"✅ 连接到数据库: {path}")
                break
            except Exception as e:
                print(f"❌ 连接数据库失败 {path}: {e}")
                continue
    
    if not conn:
        print("❌ 无法连接到任何数据库文件")
        return False
    
    try:
        cursor = conn.cursor()
        
        print("🔧 开始创建AI交易相关表...")
        
        # 1. 创建AI交易会话表
        print("📋 创建 ai_trading_sessions 表...")
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS ai_trading_sessions (
                id TEXT PRIMARY KEY,
                user_id INTEGER NOT NULL,
                model_id TEXT NOT NULL,
                trading_config TEXT,
                status TEXT DEFAULT 'active',
                created_at TEXT NOT NULL,
                stopped_at TEXT
            )
        ''')
        
        # 2. 创建AI交易记录表
        print("📋 创建 ai_trades 表...")
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS ai_trades (
                id TEXT PRIMARY KEY,
                user_id INTEGER NOT NULL,
                symbol TEXT NOT NULL,
                action TEXT NOT NULL,
                lot_size REAL NOT NULL,
                entry_price REAL,
                sl_price REAL,
                tp_price REAL,
                order_id TEXT,
                inference_result TEXT,
                trading_config TEXT,
                status TEXT DEFAULT 'open',
                created_at TEXT NOT NULL,
                closed_at TEXT,
                profit REAL DEFAULT 0
            )
        ''')
        
        # 3. 创建索引以提高查询性能
        print("📋 创建索引...")
        cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_ai_trades_user_status 
            ON ai_trades(user_id, status)
        ''')
        
        cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_ai_trades_created_at 
            ON ai_trades(created_at)
        ''')
        
        cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_ai_trading_sessions_user_status 
            ON ai_trading_sessions(user_id, status)
        ''')
        
        # 提交更改
        conn.commit()
        
        print("✅ AI交易表创建成功！")
        
        # 验证表是否创建成功
        print("\n🔍 验证表结构...")
        
        # 检查ai_trades表
        cursor.execute("PRAGMA table_info(ai_trades)")
        ai_trades_columns = cursor.fetchall()
        print(f"✅ ai_trades表包含{len(ai_trades_columns)}个字段:")
        for col in ai_trades_columns:
            print(f"   - {col[1]} ({col[2]})")
        
        # 检查ai_trading_sessions表
        cursor.execute("PRAGMA table_info(ai_trading_sessions)")
        sessions_columns = cursor.fetchall()
        print(f"✅ ai_trading_sessions表包含{len(sessions_columns)}个字段:")
        for col in sessions_columns:
            print(f"   - {col[1]} ({col[2]})")
        
        conn.close()
        
        print(f"\n🎉 数据库表创建完成！数据库路径: {db_path}")
        return True
        
    except Exception as e:
        print(f"❌ 创建表失败: {e}")
        conn.close()
        return False

def test_ai_trading_functionality():
    """测试AI交易功能"""
    print("\n🧪 测试AI交易功能...")
    
    # 尝试连接数据库
    db_paths = [
        'instance/matetrade4.db',
        'matetrade4.db', 
        'trading_system.db',
        'instance/trading_system.db'
    ]
    
    conn = None
    for path in db_paths:
        if os.path.exists(path):
            try:
                conn = sqlite3.connect(path)
                break
            except:
                continue
    
    if not conn:
        print("❌ 无法连接到数据库")
        return False
    
    try:
        cursor = conn.cursor()
        
        # 测试插入交易会话
        test_session_id = "test-session-123"
        cursor.execute('''
            INSERT OR REPLACE INTO ai_trading_sessions
            (id, user_id, model_id, trading_config, status, created_at)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (test_session_id, 1, "test-model", '{"min_confidence": 0.8}', 'active', datetime.now().isoformat()))
        
        # 测试插入交易记录
        test_trade_id = "test-trade-123"
        cursor.execute('''
            INSERT OR REPLACE INTO ai_trades
            (id, user_id, symbol, action, lot_size, entry_price, status, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (test_trade_id, 1, "XAUUSD", "BUY", 0.1, 2650.50, 'open', datetime.now().isoformat()))
        
        conn.commit()
        
        # 测试查询
        cursor.execute("SELECT COUNT(*) FROM ai_trading_sessions WHERE status = 'active'")
        active_sessions = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM ai_trades WHERE status = 'open'")
        open_trades = cursor.fetchone()[0]
        
        print(f"✅ 测试成功！活跃会话: {active_sessions}, 开仓交易: {open_trades}")
        
        # 清理测试数据
        cursor.execute("DELETE FROM ai_trading_sessions WHERE id = ?", (test_session_id,))
        cursor.execute("DELETE FROM ai_trades WHERE id = ?", (test_trade_id,))
        conn.commit()
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        conn.close()
        return False

def main():
    """主函数"""
    print("🔧 AI交易数据库表创建工具")
    print("=" * 80)
    
    print("📋 问题描述:")
    print("• AI推理交易相关的数据库表不存在")
    print("• 导致推理结果无法转换为实际交易订单")
    print("• 持仓统计显示错误")
    print()
    
    # 创建表
    success = create_ai_trading_tables()
    
    if success:
        # 测试功能
        test_success = test_ai_trading_functionality()
        
        if test_success:
            print("\n🎉 修复完成！")
            print("=" * 80)
            print("✅ AI交易数据库表已创建并测试成功")
            print("✅ 现在可以正常使用AI推理交易功能")
            print()
            print("💡 下一步操作:")
            print("1. 重新启动应用程序")
            print("2. 进入模型推理页面")
            print("3. 点击'开始AI交易'按钮")
            print("4. 确认推理结果能够正常生成交易订单")
        else:
            print("\n❌ 测试失败，请检查数据库权限")
    else:
        print("\n❌ 创建表失败，请检查数据库文件和权限")

if __name__ == "__main__":
    main()
