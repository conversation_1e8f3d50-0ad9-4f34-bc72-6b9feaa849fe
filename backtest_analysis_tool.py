#!/usr/bin/env python3
"""
回测分析工具 - 测试多种策略参数组合
分析亏损原因并找出最优参数
"""

import requests
import json
import time
import pandas as pd
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import seaborn as sns

class BacktestAnalyzer:
    def __init__(self, base_url="http://127.0.0.1:5000"):
        self.base_url = base_url
        self.results = []
        
    def run_backtest(self, params):
        """运行单次回测"""
        try:
            url = f"{self.base_url}/api/low-risk-trading/backtest"
            
            response = requests.post(url, json=params, timeout=60)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    return data.get('results')
                else:
                    print(f"❌ 回测失败: {data.get('error')}")
                    return None
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ 请求失败: {e}")
            return None
    
    def test_parameter_combinations(self):
        """测试多种参数组合"""
        print("🚀 开始多参数回测分析...")
        
        # 定义测试参数范围
        test_configs = [
            # 基础配置
            {
                'name': '当前配置',
                'days': 7,
                'timeframe': '15m',
                'lot_size': 0.01,
                'stop_loss_percent': 0.5,
                'take_profit_percent': 1.5,
                'daily_limit': 1,
                'min_signals': 2
            },
            # 保守配置
            {
                'name': '保守配置',
                'days': 7,
                'timeframe': '15m',
                'lot_size': 0.01,
                'stop_loss_percent': 0.8,
                'take_profit_percent': 2.4,
                'daily_limit': 1,
                'min_signals': 3
            },
            # 积极配置
            {
                'name': '积极配置',
                'days': 7,
                'timeframe': '15m',
                'lot_size': 0.01,
                'stop_loss_percent': 0.3,
                'take_profit_percent': 1.0,
                'daily_limit': 2,
                'min_signals': 2
            },
            # 高频配置
            {
                'name': '高频配置',
                'days': 7,
                'timeframe': '5m',
                'lot_size': 0.01,
                'stop_loss_percent': 0.4,
                'take_profit_percent': 1.2,
                'daily_limit': 3,
                'min_signals': 2
            },
            # 长期配置
            {
                'name': '长期配置',
                'days': 7,
                'timeframe': '1h',
                'lot_size': 0.01,
                'stop_loss_percent': 1.0,
                'take_profit_percent': 3.0,
                'daily_limit': 1,
                'min_signals': 3
            },
            # 平衡配置1
            {
                'name': '平衡配置1',
                'days': 7,
                'timeframe': '15m',
                'lot_size': 0.01,
                'stop_loss_percent': 0.6,
                'take_profit_percent': 1.8,
                'daily_limit': 2,
                'min_signals': 2
            },
            # 平衡配置2
            {
                'name': '平衡配置2',
                'days': 7,
                'timeframe': '30m',
                'lot_size': 0.01,
                'stop_loss_percent': 0.7,
                'take_profit_percent': 2.1,
                'daily_limit': 1,
                'min_signals': 3
            },
            # 严格配置
            {
                'name': '严格配置',
                'days': 7,
                'timeframe': '15m',
                'lot_size': 0.01,
                'stop_loss_percent': 1.2,
                'take_profit_percent': 3.6,
                'daily_limit': 1,
                'min_signals': 4
            }
        ]
        
        # 执行所有配置的回测
        for i, config in enumerate(test_configs):
            print(f"\n📊 测试配置 {i+1}/{len(test_configs)}: {config['name']}")
            print(f"   参数: 止损{config['stop_loss_percent']}%, 止盈{config['take_profit_percent']}%, 时间框架{config['timeframe']}")
            
            result = self.run_backtest(config)
            
            if result:
                # 添加配置信息到结果
                result['config_name'] = config['name']
                result['config'] = config
                self.results.append(result)
                
                # 显示结果摘要
                summary = result.get('summary', {})
                print(f"   结果: 交易{summary.get('total_trades', 0)}笔, 胜率{summary.get('win_rate', 0):.1f}%, 盈亏${summary.get('total_pnl', 0):.2f}")
            else:
                print(f"   ❌ 回测失败")
            
            # 避免请求过于频繁
            time.sleep(2)
        
        print(f"\n✅ 完成 {len(self.results)} 个配置的回测")
    
    def analyze_results(self):
        """分析回测结果"""
        if not self.results:
            print("❌ 没有回测结果可分析")
            return
        
        print("\n" + "="*80)
        print("📊 回测结果分析报告")
        print("="*80)
        
        # 创建结果DataFrame
        data = []
        for result in self.results:
            summary = result.get('summary', {})
            config = result.get('config', {})
            
            data.append({
                '配置名称': result.get('config_name', '未知'),
                '时间框架': config.get('timeframe', ''),
                '止损%': config.get('stop_loss_percent', 0),
                '止盈%': config.get('take_profit_percent', 0),
                '每日限制': config.get('daily_limit', 0),
                '最小信号': config.get('min_signals', 0),
                '总交易数': summary.get('total_trades', 0),
                '盈利次数': summary.get('win_trades', 0),
                '亏损次数': summary.get('loss_trades', 0),
                '胜率%': summary.get('win_rate', 0),
                '总盈亏$': summary.get('total_pnl', 0),
                '平均盈亏$': summary.get('avg_pnl_per_trade', 0),
                '最大回撤$': summary.get('max_drawdown', 0),
                '盈亏比': summary.get('profit_loss_ratio', 0),
                '夏普比率': summary.get('sharpe_ratio', 0)
            })
        
        df = pd.DataFrame(data)
        
        # 按总盈亏排序
        df_sorted = df.sort_values('总盈亏$', ascending=False)
        
        print("\n🏆 配置排名 (按总盈亏排序):")
        print("-" * 120)
        print(f"{'排名':<4} {'配置名称':<12} {'时间框架':<8} {'止损%':<6} {'止盈%':<6} {'交易数':<6} {'胜率%':<7} {'总盈亏$':<10} {'盈亏比':<8}")
        print("-" * 120)
        
        for i, row in df_sorted.iterrows():
            rank = df_sorted.index.get_loc(i) + 1
            print(f"{rank:<4} {row['配置名称']:<12} {row['时间框架']:<8} {row['止损%']:<6.1f} {row['止盈%']:<6.1f} {row['总交易数']:<6} {row['胜率%']:<7.1f} {row['总盈亏$']:<10.2f} {row['盈亏比']:<8.2f}")
        
        # 分析最佳和最差配置
        best_config = df_sorted.iloc[0]
        worst_config = df_sorted.iloc[-1]
        
        print(f"\n🥇 最佳配置: {best_config['配置名称']}")
        print(f"   盈亏: ${best_config['总盈亏$']:.2f}, 胜率: {best_config['胜率%']:.1f}%, 交易数: {best_config['总交易数']}")
        print(f"   参数: 止损{best_config['止损%']:.1f}%, 止盈{best_config['止盈%']:.1f}%, 时间框架{best_config['时间框架']}")
        
        print(f"\n🥉 最差配置: {worst_config['配置名称']}")
        print(f"   盈亏: ${worst_config['总盈亏$']:.2f}, 胜率: {worst_config['胜率%']:.1f}%, 交易数: {worst_config['总交易数']}")
        print(f"   参数: 止损{worst_config['止损%']:.1f}%, 止盈{worst_config['止盈%']:.1f}%, 时间框架{worst_config['时间框架']}")
        
        # 统计分析
        profitable_configs = df[df['总盈亏$'] > 0]
        losing_configs = df[df['总盈亏$'] <= 0]
        
        print(f"\n📈 盈利配置: {len(profitable_configs)}/{len(df)} ({len(profitable_configs)/len(df)*100:.1f}%)")
        print(f"📉 亏损配置: {len(losing_configs)}/{len(df)} ({len(losing_configs)/len(df)*100:.1f}%)")
        
        if len(profitable_configs) > 0:
            print(f"\n💰 盈利配置平均表现:")
            print(f"   平均盈利: ${profitable_configs['总盈亏$'].mean():.2f}")
            print(f"   平均胜率: {profitable_configs['胜率%'].mean():.1f}%")
            print(f"   平均交易数: {profitable_configs['总交易数'].mean():.1f}")
        
        if len(losing_configs) > 0:
            print(f"\n💸 亏损配置平均表现:")
            print(f"   平均亏损: ${losing_configs['总盈亏$'].mean():.2f}")
            print(f"   平均胜率: {losing_configs['胜率%'].mean():.1f}%")
            print(f"   平均交易数: {losing_configs['总交易数'].mean():.1f}")
        
        # 参数相关性分析
        print(f"\n🔍 参数影响分析:")
        
        # 止损止盈比例分析
        df['止盈止损比'] = df['止盈%'] / df['止损%']
        correlation_ratio = df['总盈亏$'].corr(df['止盈止损比'])
        print(f"   止盈/止损比例与盈亏相关性: {correlation_ratio:.3f}")
        
        # 胜率分析
        correlation_winrate = df['总盈亏$'].corr(df['胜率%'])
        print(f"   胜率与总盈亏相关性: {correlation_winrate:.3f}")
        
        # 交易频率分析
        correlation_trades = df['总盈亏$'].corr(df['总交易数'])
        print(f"   交易频率与总盈亏相关性: {correlation_trades:.3f}")
        
        return df_sorted
    
    def generate_recommendations(self, df_sorted):
        """生成优化建议"""
        print(f"\n" + "="*80)
        print("💡 优化建议")
        print("="*80)
        
        # 基于最佳配置的建议
        best_config = df_sorted.iloc[0]
        
        if best_config['总盈亏$'] > 0:
            print(f"✅ 发现盈利配置，建议采用:")
            print(f"   配置名称: {best_config['配置名称']}")
            print(f"   止损比例: {best_config['止损%']:.1f}%")
            print(f"   止盈比例: {best_config['止盈%']:.1f}%")
            print(f"   时间框架: {best_config['时间框架']}")
            print(f"   每日交易限制: {best_config['每日限制']}")
            print(f"   最小信号数: {best_config['最小信号']}")
        else:
            print(f"⚠️ 所有配置都亏损，需要进一步优化:")
            
            # 分析亏损原因
            avg_winrate = df_sorted['胜率%'].mean()
            avg_ratio = df_sorted['盈亏比'].mean()
            
            print(f"\n🔍 亏损原因分析:")
            print(f"   平均胜率: {avg_winrate:.1f}% (目标: >50%)")
            print(f"   平均盈亏比: {avg_ratio:.2f} (目标: >1.5)")
            
            if avg_winrate < 40:
                print(f"   ❌ 胜率过低，建议:")
                print(f"      - 提高信号质量阈值")
                print(f"      - 增加信号确认数量")
                print(f"      - 使用更长时间框架")
            
            if avg_ratio < 1.5:
                print(f"   ❌ 盈亏比不佳，建议:")
                print(f"      - 增大止盈/止损比例")
                print(f"      - 优化止损策略")
                print(f"      - 考虑动态止盈")
        
        # 通用建议
        print(f"\n📋 通用优化建议:")
        print(f"   1. 信号质量: 提高信号生成阈值，减少假信号")
        print(f"   2. 风险管理: 优化止损止盈比例，建议1:2或1:3")
        print(f"   3. 时间框架: 测试更长时间框架，减少市场噪音")
        print(f"   4. 交易频率: 控制交易频率，提高信号质量")
        print(f"   5. 市场环境: 考虑不同市场环境的策略适应性")

def main():
    """主函数"""
    print("🔬 回测分析工具启动")
    print("="*50)
    
    analyzer = BacktestAnalyzer()
    
    # 测试多种参数组合
    analyzer.test_parameter_combinations()
    
    # 分析结果
    df_results = analyzer.analyze_results()
    
    # 生成建议
    if df_results is not None:
        analyzer.generate_recommendations(df_results)
    
    print(f"\n🎯 分析完成！请根据建议优化策略参数。")

if __name__ == "__main__":
    main()
