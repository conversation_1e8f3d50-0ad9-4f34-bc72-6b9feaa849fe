#!/usr/bin/env python3
"""
调试索引错误
"""

import requests
import json
import time

def test_and_get_error():
    """测试并获取详细错误"""
    
    print("🔍 调试索引错误")
    print("=" * 50)
    
    session = requests.Session()
    
    try:
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post('http://127.0.0.1:5000/login', data=login_data)
        if response.status_code != 200:
            print("❌ 登录失败")
            return
        
        print("✅ 登录成功")
        
        # 最简单的配置
        config = {
            'model_name': f'debug_test_{int(time.time())}',
            'model_type': 'LSTM',
            'symbol': 'XAUUSD',
            'timeframe': 'H1',
            'epochs': 1,
            'batch_size': 4,
            'learning_rate': 0.01,
            'validation_split': 0.2,
            'sequence_length': 3,
            'features': ['close', 'volume']
        }
        
        print(f"🚀 启动训练...")
        
        response = session.post(
            'http://127.0.0.1:5000/api/deep-learning/start-training',
            json=config,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                task_id = result.get('task_id')
                print(f"✅ 训练启动成功: {task_id}")
                
                # 等待5秒让错误发生
                time.sleep(5)
                
                # 获取错误信息
                progress_response = session.get(f'http://127.0.0.1:5000/api/deep-learning/training-progress/{task_id}')
                
                if progress_response.status_code == 200:
                    progress_result = progress_response.json()
                    
                    if progress_result.get('success'):
                        progress_data = progress_result['progress']
                        status = progress_data.get('status')
                        logs = progress_data.get('logs')
                        
                        print(f"📊 状态: {status}")
                        
                        if logs:
                            try:
                                log_data = json.loads(logs)
                                if 'error' in log_data:
                                    print(f"❌ 错误信息: {log_data['error']}")
                            except:
                                print(f"📝 原始日志: {logs}")
                        
                        print(f"\n💡 现在检查应用程序控制台的详细错误堆栈")
                        
            else:
                print(f"❌ 训练启动失败: {result.get('error')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == '__main__':
    test_and_get_error()
