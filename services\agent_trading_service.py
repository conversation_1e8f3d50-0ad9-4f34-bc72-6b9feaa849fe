#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能体交易服务
由外部AI大模型驱动的智能交易系统
"""

import json
import requests
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import logging

from models import db, AgentTradingStrategy, AgentTradingSession, Trade, TradingAccount, AIStrategy
from services.position_manager import position_manager
from services.ai_service import AIService

logger = logging.getLogger(__name__)

class AgentTradingService:
    """智能体交易服务"""

    def __init__(self):
        self.ai_service = AIService()
        self.active_sessions = {}  # 活跃的交易会话

        # 外部AI模型配置
        self.ai_models = {
            'deepseek_v3': {
                'api_url': 'https://api.deepseek.com/v1/chat/completions',
                'model': 'deepseek-chat',
                'api_key': 'sk-your-deepseek-api-key'
            },
            'openai_gpt4': {
                'api_url': 'https://api.openai.com/v1/chat/completions',
                'model': 'gpt-4',
                'api_key': 'sk-your-openai-api-key'
            },
            'claude_3': {
                'api_url': 'https://api.anthropic.com/v1/messages',
                'model': 'claude-3-sonnet-********',
                'api_key': 'sk-your-claude-api-key'
            }
        }

    def create_agent_strategy(self, user_id: int, strategy_data: Dict) -> AgentTradingStrategy:
        """
        创建智能体交易策略

        Args:
            user_id: 用户ID
            strategy_data: 策略数据

        Returns:
            AgentTradingStrategy: 创建的策略
        """
        try:
            strategy = AgentTradingStrategy(
                user_id=user_id,
                name=strategy_data.get('name', '智能体交易策略'),
                description=strategy_data.get('description', ''),
                user_strategy=strategy_data.get('user_strategy', ''),
                risk_tolerance=strategy_data.get('risk_tolerance', 'moderate'),
                max_daily_trades=strategy_data.get('max_daily_trades', 10),
                max_position_size=strategy_data.get('max_position_size', 1000.0),
                ai_strategy_id=strategy_data.get('ai_strategy_id'),
                trading_hours=strategy_data.get('trading_hours', '9:00-18:00')
            )

            # 设置AI模型配置
            ai_config = strategy_data.get('ai_model_config', {})
            strategy.set_ai_model_config(ai_config)

            # 设置交易品种
            symbols = strategy_data.get('trading_symbols', ['EURUSD'])
            strategy.set_trading_symbols(symbols)

            db.session.add(strategy)
            db.session.commit()

            logger.info(f"创建智能体交易策略成功: {strategy.id}")
            return strategy

        except Exception as e:
            logger.error(f"创建智能体交易策略失败: {e}")
            db.session.rollback()
            raise

    def start_agent_trading(self, strategy_id: int) -> AgentTradingSession:
        """
        启动智能体交易

        Args:
            strategy_id: 策略ID

        Returns:
            AgentTradingSession: 交易会话
        """
        try:
            strategy = AgentTradingStrategy.query.get(strategy_id)
            if not strategy:
                raise ValueError("策略不存在")

            if strategy.is_active:
                raise ValueError("策略已经在运行中")

            # 创建交易会话
            session = AgentTradingSession(
                strategy_id=strategy_id,
                user_id=strategy.user_id,
                session_name=f"{strategy.name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                status='active'
            )

            db.session.add(session)
            strategy.is_active = True
            db.session.commit()

            # 添加到活跃会话
            self.active_sessions[session.id] = {
                'session': session,
                'strategy': strategy,
                'last_decision_time': datetime.utcnow(),
                'decision_interval': 300  # 5分钟决策一次
            }

            logger.info(f"启动智能体交易会话: {session.id}")
            return session

        except Exception as e:
            logger.error(f"启动智能体交易失败: {e}")
            db.session.rollback()
            raise

    def stop_agent_trading(self, session_id: int) -> bool:
        """
        停止智能体交易

        Args:
            session_id: 会话ID

        Returns:
            bool: 是否成功停止
        """
        try:
            session = AgentTradingSession.query.get(session_id)
            if not session:
                return False

            session.status = 'stopped'
            session.end_time = datetime.utcnow()

            strategy = AgentTradingStrategy.query.get(session.strategy_id)
            if strategy:
                strategy.is_active = False

            db.session.commit()

            # 从活跃会话中移除
            if session_id in self.active_sessions:
                del self.active_sessions[session_id]

            logger.info(f"停止智能体交易会话: {session_id}")
            return True

        except Exception as e:
            logger.error(f"停止智能体交易失败: {e}")
            return False

    def get_market_context(self, symbols: List[str]) -> Dict:
        """
        获取市场上下文信息

        Args:
            symbols: 交易品种列表

        Returns:
            Dict: 市场上下文
        """
        context = {
            'timestamp': datetime.utcnow().isoformat(),
            'symbols': {},
            'market_sentiment': 'neutral'
        }

        for symbol in symbols:
            # 获取当前价格
            current_price = position_manager.get_current_price(symbol)

            # 模拟技术指标
            context['symbols'][symbol] = {
                'current_price': current_price,
                'trend': 'bullish' if hash(symbol) % 2 == 0 else 'bearish',
                'volatility': 'medium',
                'support': current_price * 0.99,
                'resistance': current_price * 1.01,
                'rsi': 45 + (hash(symbol) % 20),  # 模拟RSI 45-65
                'macd_signal': 'buy' if hash(symbol) % 3 == 0 else 'sell' if hash(symbol) % 3 == 1 else 'hold'
            }

        return context

    def call_external_ai(self, model_config: Dict, prompt: str) -> str:
        """
        调用外部AI模型

        Args:
            model_config: AI模型配置
            prompt: 提示词

        Returns:
            str: AI响应
        """
        try:
            model_name = model_config.get('model', 'deepseek_v3')

            if model_name not in self.ai_models:
                # 使用本地AI服务作为后备
                return self._call_local_ai(prompt)

            config = self.ai_models[model_name]

            headers = {
                'Content-Type': 'application/json',
                'Authorization': f"Bearer {config['api_key']}"
            }

            if model_name == 'claude_3':
                # Claude API格式
                data = {
                    'model': config['model'],
                    'max_tokens': 1000,
                    'messages': [{'role': 'user', 'content': prompt}]
                }
            else:
                # OpenAI/DeepSeek API格式
                data = {
                    'model': config['model'],
                    'messages': [{'role': 'user', 'content': prompt}],
                    'max_tokens': 1000,
                    'temperature': 0.7
                }

            response = requests.post(
                config['api_url'],
                headers=headers,
                json=data,
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()

                if model_name == 'claude_3':
                    return result['content'][0]['text']
                else:
                    return result['choices'][0]['message']['content']
            else:
                logger.warning(f"外部AI调用失败: {response.status_code}")
                return self._call_local_ai(prompt)

        except Exception as e:
            logger.error(f"调用外部AI失败: {e}")
            return self._call_local_ai(prompt)

    def _call_local_ai(self, prompt: str) -> str:
        """
        调用本地AI服务作为后备

        Args:
            prompt: 提示词

        Returns:
            str: AI响应
        """
        # 简化的本地AI响应
        if "买入" in prompt or "buy" in prompt.lower():
            return json.dumps({
                'action': 'buy',
                'confidence': 0.75,
                'reasoning': '基于技术分析，当前价格处于支撑位附近，建议买入',
                'risk_level': 'medium'
            }, ensure_ascii=False)
        elif "卖出" in prompt or "sell" in prompt.lower():
            return json.dumps({
                'action': 'sell',
                'confidence': 0.70,
                'reasoning': '基于技术分析，当前价格接近阻力位，建议卖出',
                'risk_level': 'medium'
            }, ensure_ascii=False)
        else:
            return json.dumps({
                'action': 'hold',
                'confidence': 0.60,
                'reasoning': '当前市场信号不明确，建议观望',
                'risk_level': 'low'
            }, ensure_ascii=False)

    def generate_trading_prompt(self, strategy: AgentTradingStrategy,
                              market_context: Dict,
                              session: AgentTradingSession) -> str:
        """
        生成交易决策提示词

        Args:
            strategy: 交易策略
            market_context: 市场上下文
            session: 交易会话

        Returns:
            str: 提示词
        """
        # 获取AI策略信息
        ai_strategy = None
        if strategy.ai_strategy_id:
            ai_strategy = AIStrategy.query.get(strategy.ai_strategy_id)

        # 获取最近的交易表现
        recent_decisions = session.get_ai_decisions()[-5:]  # 最近5个决策

        prompt = f"""
你是一个专业的外汇交易智能体，需要基于以下信息做出交易决策：

## 用户交易策略和要求
{strategy.get_user_strategy()}

## 风险偏好
- 风险承受度: {strategy.risk_tolerance}
- 最大日交易次数: {strategy.max_daily_trades}
- 最大持仓规模: ${strategy.max_position_size}

## AI策略信息
{f"AI策略名称: {ai_strategy.name}" if ai_strategy else "无关联AI策略"}
{f"AI策略描述: {ai_strategy.description}" if ai_strategy else ""}

## 当前市场情况
时间: {market_context['timestamp']}
市场情绪: {market_context['market_sentiment']}

交易品种分析:
"""

        for symbol, data in market_context['symbols'].items():
            prompt += f"""
{symbol}:
- 当前价格: ${data['current_price']:.5f}
- 趋势: {data['trend']}
- 波动性: {data['volatility']}
- 支撑位: ${data['support']:.5f}
- 阻力位: ${data['resistance']:.5f}
- RSI: {data['rsi']}
- MACD信号: {data['macd_signal']}
"""

        if recent_decisions:
            prompt += f"\n## 最近决策历史\n"
            for decision in recent_decisions:
                prompt += f"- {decision['timestamp']}: {decision['decision']}\n"

        prompt += f"""
## 当前交易会话统计
- 总交易次数: {session.total_trades}
- 盈利交易: {session.winning_trades}
- 亏损交易: {session.losing_trades}
- 总盈亏: ${session.total_profit:.2f}

## 请求
请基于以上信息，为每个交易品种提供交易决策。返回JSON格式，包含：
1. action: "buy", "sell", "hold"
2. symbol: 交易品种
3. confidence: 信心度 (0-1)
4. reasoning: 决策理由
5. position_size: 建议持仓大小
6. stop_loss: 止损价格
7. take_profit: 止盈价格
8. risk_level: "low", "medium", "high"

示例格式：
{{
    "decisions": [
        {{
            "action": "buy",
            "symbol": "EURUSD",
            "confidence": 0.8,
            "reasoning": "技术分析显示突破阻力位",
            "position_size": 0.1,
            "stop_loss": 1.0950,
            "take_profit": 1.1150,
            "risk_level": "medium"
        }}
    ],
    "overall_assessment": "市场整体看涨，建议适度增加多头持仓"
}}
"""

        return prompt

    def execute_ai_decision(self, session_id: int) -> Dict:
        """
        执行AI交易决策

        Args:
            session_id: 会话ID

        Returns:
            Dict: 执行结果
        """
        try:
            if session_id not in self.active_sessions:
                return {'success': False, 'error': '会话不存在或未激活'}

            session_data = self.active_sessions[session_id]
            session = session_data['session']
            strategy = session_data['strategy']

            # 检查是否到了决策时间
            now = datetime.utcnow()
            if (now - session_data['last_decision_time']).seconds < session_data['decision_interval']:
                return {'success': True, 'message': '未到决策时间'}

            # 获取市场上下文
            symbols = strategy.get_trading_symbols()
            market_context = self.get_market_context(symbols)

            # 生成交易提示词
            prompt = self.generate_trading_prompt(strategy, market_context, session)

            # 调用外部AI
            ai_config = strategy.get_ai_model_config()
            ai_response = self.call_external_ai(ai_config, prompt)

            # 解析AI响应
            try:
                decisions_data = json.loads(ai_response)
            except json.JSONDecodeError:
                # 如果不是JSON格式，尝试提取关键信息
                decisions_data = self._parse_text_response(ai_response)

            # 记录AI决策
            session.add_ai_decision({
                'prompt': prompt[:500] + '...',  # 只保存前500字符
                'response': ai_response,
                'market_context': market_context,
                'decisions': decisions_data
            })

            # 执行交易决策
            execution_results = []
            for decision in decisions_data.get('decisions', []):
                if decision['action'] in ['buy', 'sell']:
                    result = self._execute_trade(decision, strategy, session)
                    execution_results.append(result)

            # 更新会话统计
            session_data['last_decision_time'] = now
            db.session.commit()

            return {
                'success': True,
                'decisions': decisions_data,
                'executions': execution_results,
                'market_context': market_context
            }

        except Exception as e:
            logger.error(f"执行AI决策失败: {e}")
            return {'success': False, 'error': str(e)}

    def _parse_text_response(self, response: str) -> Dict:
        """
        解析文本格式的AI响应

        Args:
            response: AI响应文本

        Returns:
            Dict: 解析后的决策数据
        """
        # 简化的文本解析逻辑
        decisions = []

        if "买入" in response or "buy" in response.lower():
            decisions.append({
                'action': 'buy',
                'symbol': 'EURUSD',
                'confidence': 0.7,
                'reasoning': '基于AI文本分析',
                'position_size': 0.1,
                'risk_level': 'medium'
            })
        elif "卖出" in response or "sell" in response.lower():
            decisions.append({
                'action': 'sell',
                'symbol': 'EURUSD',
                'confidence': 0.7,
                'reasoning': '基于AI文本分析',
                'position_size': 0.1,
                'risk_level': 'medium'
            })

        return {
            'decisions': decisions,
            'overall_assessment': response[:200] + '...'
        }

    def _execute_trade(self, decision: Dict, strategy: AgentTradingStrategy, session: AgentTradingSession) -> Dict:
        """
        执行单个交易决策

        Args:
            decision: 交易决策
            strategy: 交易策略
            session: 交易会话

        Returns:
            Dict: 执行结果
        """
        try:
            # 检查日交易次数限制
            if session.total_trades >= strategy.max_daily_trades:
                return {'success': False, 'error': '已达到日交易次数限制'}

            # 检查持仓规模限制
            position_size = min(decision.get('position_size', 0.1), strategy.max_position_size / 10000)

            # 构建交易数据
            trade_data = {
                'symbol': decision['symbol'],
                'side': decision['action'],
                'amount': position_size,
                'ai_generated': True,
                'strategy_id': strategy.id,
                'stop_loss': decision.get('stop_loss'),
                'take_profit': decision.get('take_profit')
            }

            # 获取用户账户
            account = TradingAccount.query.filter_by(
                user_id=strategy.user_id,
                account_type='demo',
                is_active=True
            ).first()

            if not account:
                return {'success': False, 'error': '未找到可用账户'}

            # 创建交易记录
            current_price = position_manager.get_current_price(decision['symbol'])

            trade = Trade(
                account_id=account.id,
                symbol=decision['symbol'],
                trade_type=decision['action'],
                volume=position_size,
                open_price=current_price,
                stop_loss=trade_data.get('stop_loss'),
                take_profit=trade_data.get('take_profit'),
                status='open',
                strategy_name=f'智能体交易-{strategy.name}'
            )

            db.session.add(trade)

            # 更新会话统计
            session.total_trades += 1

            db.session.commit()

            logger.info(f"智能体交易执行成功: {decision['symbol']} {decision['action']} {position_size}")

            return {
                'success': True,
                'trade_id': trade.id,
                'symbol': decision['symbol'],
                'action': decision['action'],
                'amount': position_size,
                'price': current_price,
                'reasoning': decision.get('reasoning', '')
            }

        except Exception as e:
            logger.error(f"执行交易失败: {e}")
            db.session.rollback()
            return {'success': False, 'error': str(e)}

    def evaluate_performance(self, session_id: int) -> Dict:
        """
        评估交易表现

        Args:
            session_id: 会话ID

        Returns:
            Dict: 评估结果
        """
        try:
            session = AgentTradingSession.query.get(session_id)
            if not session:
                return {'success': False, 'error': '会话不存在'}

            strategy = AgentTradingStrategy.query.get(session.strategy_id)

            # 获取会话相关的交易
            trades = Trade.query.filter(
                Trade.strategy_name.like(f'%智能体交易-{strategy.name}%')
            ).all()

            # 计算性能指标
            total_trades = len(trades)
            closed_trades = [t for t in trades if t.status == 'closed']
            open_trades = [t for t in trades if t.status == 'open']

            winning_trades = [t for t in closed_trades if t.profit > 0]
            losing_trades = [t for t in closed_trades if t.profit < 0]

            total_profit = sum(t.profit for t in closed_trades)
            win_rate = len(winning_trades) / len(closed_trades) if closed_trades else 0

            # 计算浮动盈亏
            floating_pnl = 0
            try:
                for trade in open_trades:
                    current_profit = position_manager.calculate_profit(trade)
                    floating_pnl += current_profit
            except Exception as e:
                logger.warning(f"计算浮动盈亏失败: {e}")
                floating_pnl = 0

            # 计算最大回撤
            max_drawdown = 0
            try:
                running_profit = 0
                peak_profit = 0

                for trade in sorted(closed_trades, key=lambda x: x.close_time or datetime.utcnow()):
                    running_profit += trade.profit or 0
                    if running_profit > peak_profit:
                        peak_profit = running_profit
                    drawdown = (peak_profit - running_profit) / peak_profit if peak_profit > 0 else 0
                    max_drawdown = max(max_drawdown, drawdown)
            except Exception as e:
                logger.warning(f"计算最大回撤失败: {e}")
                max_drawdown = 0

            # 更新会话统计
            session.total_trades = total_trades
            session.winning_trades = len(winning_trades)
            session.losing_trades = len(losing_trades)
            session.total_profit = total_profit + floating_pnl
            session.max_drawdown = max_drawdown

            # 安全计算盈亏比
            profit_factor = 1.0
            try:
                if losing_trades:
                    total_wins = sum(t.profit or 0 for t in winning_trades)
                    total_losses = abs(sum(t.profit or 0 for t in losing_trades))
                    profit_factor = total_wins / total_losses if total_losses > 0 else float('inf')
                else:
                    profit_factor = float('inf') if winning_trades else 1.0
            except Exception as e:
                logger.warning(f"计算盈亏比失败: {e}")
                profit_factor = 1.0

            evaluation_result = {
                'timestamp': datetime.utcnow().isoformat(),
                'total_trades': total_trades,
                'closed_trades': len(closed_trades),
                'open_trades': len(open_trades),
                'win_rate': win_rate,
                'total_profit': total_profit,
                'floating_pnl': floating_pnl,
                'max_drawdown': max_drawdown,
                'avg_profit_per_trade': total_profit / len(closed_trades) if closed_trades else 0,
                'profit_factor': profit_factor
            }

            # 记录评估结果
            session.add_evaluation_result(evaluation_result)
            session.last_evaluation = datetime.utcnow()

            db.session.commit()

            return {
                'success': True,
                'evaluation': evaluation_result
            }

        except Exception as e:
            logger.error(f"评估交易表现失败: {e}")
            return {'success': False, 'error': str(e)}

    def get_active_sessions(self) -> List[Dict]:
        """
        获取活跃的交易会话

        Returns:
            List[Dict]: 活跃会话列表
        """
        sessions = []
        for session_id, session_data in self.active_sessions.items():
            session = session_data['session']
            strategy = session_data['strategy']

            sessions.append({
                'session_id': session.id,
                'strategy_name': strategy.name,
                'start_time': session.start_time.isoformat(),
                'total_trades': session.total_trades,
                'total_profit': session.total_profit,
                'status': session.status
            })

        return sessions

# 全局智能体交易服务实例
agent_trading_service = AgentTradingService()
