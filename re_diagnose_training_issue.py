#!/usr/bin/env python3
"""
重新诊断训练卡住问题（日期配置实际是正确的）
"""

import sqlite3
import json
import requests
from datetime import datetime
import time

def login_session():
    """登录并返回session"""
    session = requests.Session()
    login_data = {'username': 'admin', 'password': 'admin123'}
    
    try:
        response = session.post('http://127.0.0.1:5000/login', data=login_data)
        if response.status_code == 200:
            print("✅ 登录成功")
            return session
        else:
            print(f"❌ 登录失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return None

def check_current_task_status():
    """检查当前任务状态"""
    print("🔍 检查当前任务状态")
    print("=" * 50)
    
    task_id = "3f0e9a54-2111-4ec0-849b-8b4640a6f268"
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, model_id, status, progress, current_epoch, total_epochs, 
                   train_loss, val_loss, created_at, started_at, updated_at, logs
            FROM training_tasks 
            WHERE id = ?
        ''', (task_id,))
        
        task = cursor.fetchone()
        
        if task:
            print(f"📊 任务信息:")
            print(f"   任务ID: {task[0]}")
            print(f"   模型ID: {task[1]}")
            print(f"   状态: {task[2]}")
            print(f"   进度: {task[3]}%")
            print(f"   当前轮次: {task[4]}/{task[5]}")
            print(f"   创建时间: {task[8]}")
            print(f"   开始时间: {task[9]}")
            print(f"   更新时间: {task[10]}")
            
            if task[11]:  # logs
                try:
                    logs = json.loads(task[11])
                    print(f"   最新日志: {logs}")
                except:
                    print(f"   原始日志: {task[11]}")
            
            return task
        else:
            print(f"❌ 未找到任务")
            return None
            
    except Exception as e:
        print(f"❌ 查询失败: {e}")
        return None
    finally:
        if conn:
            conn.close()

def check_model_data_config():
    """检查模型数据配置"""
    print(f"\n🔍 重新检查模型数据配置")
    print("=" * 50)
    
    model_id = "c7ffc593-4303-45b0-89ef-a44f211770c3"
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT name, model_type, symbol, timeframe, config, status
            FROM deep_learning_models 
            WHERE id = ?
        ''', (model_id,))
        
        model = cursor.fetchone()
        
        if model:
            print(f"📊 模型信息:")
            print(f"   名称: {model[0]}")
            print(f"   类型: {model[1]}")
            print(f"   品种: {model[2]}")
            print(f"   时间框架: {model[3]}")
            print(f"   状态: {model[5]}")
            
            if model[4]:  # config
                config = json.loads(model[4])
                data_config = config.get('data_config', {})
                
                print(f"\n📅 数据配置:")
                print(f"   开始日期: {data_config.get('start_date')}")
                print(f"   结束日期: {data_config.get('end_date')}")
                print(f"   模式: {data_config.get('mode')}")
                
                # 验证日期
                today = datetime.now().date()
                try:
                    start_date = datetime.strptime(data_config.get('start_date'), '%Y-%m-%d').date()
                    end_date = datetime.strptime(data_config.get('end_date'), '%Y-%m-%d').date()
                    
                    print(f"\n📊 日期分析:")
                    print(f"   今天: {today}")
                    print(f"   开始日期: {start_date} ({'过去' if start_date < today else '未来'})")
                    print(f"   结束日期: {end_date} ({'过去' if end_date < today else '未来'})")
                    print(f"   数据天数: {(end_date - start_date).days}")
                    
                    if start_date < today and end_date < today:
                        print(f"   ✅ 日期配置正确（都是过去时间）")
                    else:
                        print(f"   ❌ 日期配置有问题")
                        
                except Exception as e:
                    print(f"   ❌ 日期解析失败: {e}")
                
                print(f"\n⚙️ 训练配置:")
                print(f"   序列长度: {config.get('sequence_length')}")
                print(f"   批次大小: {config.get('batch_size')}")
                print(f"   学习率: {config.get('learning_rate')}")
                print(f"   轮次: {config.get('epochs')}")
                
                return config
            
        return None
        
    except Exception as e:
        print(f"❌ 查询失败: {e}")
        return None
    finally:
        if conn:
            conn.close()

def check_mt5_connection():
    """检查MT5连接状态"""
    print(f"\n🔍 检查MT5连接状态")
    print("=" * 50)
    
    session = login_session()
    if not session:
        return False
    
    try:
        response = session.get('http://127.0.0.1:5000/api/mt5/status')
        
        if response.status_code == 200:
            result = response.json()
            print(f"📊 MT5状态: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            if result.get('connected'):
                print(f"✅ MT5连接正常")
                return True
            else:
                print(f"❌ MT5连接异常")
                return False
        else:
            print(f"❌ 无法获取MT5状态: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ MT5状态检查失败: {e}")
        return False

def test_data_retrieval():
    """测试数据获取"""
    print(f"\n🔍 测试数据获取")
    print("=" * 50)
    
    session = login_session()
    if not session:
        return False
    
    try:
        # 测试获取少量数据
        symbol = 'XAUUSD'
        timeframe = '5m'
        count = 100

        print(f"📊 测试参数: symbol={symbol}, timeframe={timeframe}, count={count}")

        start_time = time.time()
        response = session.get(f'http://127.0.0.1:5000/api/mt5/historical-data/{symbol}/{timeframe}?count={count}')
        end_time = time.time()
        
        print(f"⏱️ 请求耗时: {end_time - start_time:.2f}秒")
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                data = result.get('data', [])
                print(f"✅ 数据获取成功")
                print(f"   数据条数: {len(data)}")
                
                if data:
                    print(f"   第一条: {data[0]}")
                    print(f"   最后一条: {data[-1]}")
                
                return True
            else:
                print(f"❌ 数据获取失败: {result.get('error')}")
                return False
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 数据获取测试失败: {e}")
        return False

def check_system_resources():
    """检查系统资源"""
    print(f"\n🔍 检查系统资源")
    print("=" * 50)
    
    try:
        import psutil
        
        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=1)
        print(f"💻 CPU使用率: {cpu_percent}%")
        
        # 内存使用
        memory = psutil.virtual_memory()
        print(f"🧠 内存使用: {memory.percent}% ({memory.used // (1024**3)}GB / {memory.total // (1024**3)}GB)")
        
        # 磁盘使用
        disk = psutil.disk_usage('.')
        print(f"💾 磁盘使用: {disk.percent}% ({disk.used // (1024**3)}GB / {disk.total // (1024**3)}GB)")
        
        # 检查是否有资源瓶颈
        if cpu_percent > 90:
            print(f"⚠️ CPU使用率过高")
        if memory.percent > 90:
            print(f"⚠️ 内存使用率过高")
        if disk.percent > 90:
            print(f"⚠️ 磁盘使用率过高")
        
        return True
        
    except ImportError:
        print(f"⚠️ 无法导入psutil，跳过系统资源检查")
        return True
    except Exception as e:
        print(f"❌ 系统资源检查失败: {e}")
        return False

def analyze_real_problem(task_data, config, mt5_ok, data_ok):
    """分析真正的问题"""
    print(f"\n💡 重新分析问题")
    print("=" * 50)
    
    problems = []
    solutions = []
    
    if not task_data:
        problems.append("任务不存在")
        solutions.append("重新创建训练任务")
        return problems, solutions
    
    status = task_data[2]
    progress = task_data[3]
    
    print(f"📊 问题分析:")
    print(f"   任务状态: {status}")
    print(f"   训练进度: {progress}%")
    print(f"   MT5连接: {'正常' if mt5_ok else '异常'}")
    print(f"   数据获取: {'正常' if data_ok else '异常'}")
    
    # 分析可能的问题
    if not mt5_ok:
        problems.append("MT5连接异常")
        solutions.append("检查MT5终端是否运行")
        solutions.append("重新连接MT5")
    
    if not data_ok:
        problems.append("数据获取失败")
        solutions.append("检查MT5数据源")
        solutions.append("验证交易品种和时间框架")
    
    if config:
        sequence_length = config.get('sequence_length', 60)
        batch_size = config.get('batch_size', 32)
        model_type = config.get('model_type', 'lstm')
        
        if sequence_length > 100:
            problems.append(f"序列长度过长: {sequence_length}")
            solutions.append("减少序列长度到60以下")
        
        if batch_size > 64:
            problems.append(f"批次大小过大: {batch_size}")
            solutions.append("减少批次大小到32以下")
        
        if model_type == 'attention_lstm':
            problems.append("attention_lstm模型复杂度高")
            solutions.append("尝试使用更简单的lstm模型")
    
    if status == 'pending':
        problems.append("任务状态为pending，可能被重置了")
        solutions.append("重新启动训练")
    elif status == 'running' and progress == 25:
        problems.append("训练卡在数据准备阶段")
        solutions.append("检查数据量是否过大")
        solutions.append("减少训练数据范围")
        solutions.append("使用更小的批次大小")
    
    return problems, solutions

def main():
    """主函数"""
    print("🔧 重新诊断AI训练卡住问题")
    print("=" * 80)
    print("📅 当前时间: 2025年7月30日")
    print("📝 修正: 2025-06-01是过去时间，不是未来时间")
    
    # 1. 检查任务状态
    task_data = check_current_task_status()
    
    # 2. 检查模型配置
    config = check_model_data_config()
    
    # 3. 检查MT5连接
    mt5_ok = check_mt5_connection()
    
    # 4. 测试数据获取
    data_ok = test_data_retrieval()
    
    # 5. 检查系统资源
    check_system_resources()
    
    # 6. 分析真正的问题
    problems, solutions = analyze_real_problem(task_data, config, mt5_ok, data_ok)
    
    # 7. 总结
    print(f"\n📊 诊断结果")
    print("=" * 80)
    
    if problems:
        print(f"⚠️ 发现的问题:")
        for i, problem in enumerate(problems, 1):
            print(f"   {i}. {problem}")
    
    if solutions:
        print(f"\n🔧 建议的解决方案:")
        for i, solution in enumerate(solutions, 1):
            print(f"   {i}. {solution}")
    
    if not problems:
        print(f"✅ 未发现明显问题")
        print(f"💡 可能的原因:")
        print(f"   • 数据量过大导致处理缓慢")
        print(f"   • 特征计算耗时过长")
        print(f"   • 模型初始化问题")
        print(f"   • 内存不足导致交换")

if __name__ == '__main__':
    main()
