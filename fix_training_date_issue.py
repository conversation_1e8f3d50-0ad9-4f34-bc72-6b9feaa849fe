#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复AI推理模型训练中的日期配置问题
解决数据获取故障：2025-06-01 未来日期问题
"""

import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def fix_date_range_logic():
    """修复日期范围逻辑，确保不使用未来日期"""
    print("🔧 修复深度学习服务中的日期范围逻辑...")
    
    try:
        # 读取深度学习服务文件
        with open('services/deep_learning_service.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否有硬编码的未来日期
        if '2025-06-01' in content:
            print("❌ 发现硬编码的未来日期: 2025-06-01")
            # 替换为当前日期
            content = content.replace('2025-06-01', datetime.now().strftime('%Y-%m-%d'))
            print("✅ 已替换为当前日期")
        
        # 确保日期范围计算逻辑正确
        original_logic = '''            if end_date_str:
                end_date = datetime.strptime(end_date_str, '%Y-%m-%d')
            else:
                end_date = datetime.now()'''
        
        improved_logic = '''            if end_date_str:
                end_date = datetime.strptime(end_date_str, '%Y-%m-%d')
                # 确保结束日期不超过当前时间
                if end_date > datetime.now():
                    logger.warning(f"⚠️ 结束日期 {end_date_str} 是未来时间，调整为当前时间")
                    end_date = datetime.now()
            else:
                end_date = datetime.now()'''
        
        if original_logic in content:
            content = content.replace(original_logic, improved_logic)
            print("✅ 已改进日期范围验证逻辑")
        
        # 写回文件
        with open('services/deep_learning_service.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 深度学习服务日期逻辑修复完成")
        return True
        
    except Exception as e:
        print(f"❌ 修复日期逻辑失败: {e}")
        return False

def add_data_validation():
    """添加数据获取前的验证逻辑"""
    print("\n🔧 添加数据获取前的验证逻辑...")
    
    try:
        # 读取深度学习服务文件
        with open('services/deep_learning_service.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找数据获取函数
        search_pattern = 'def _get_mt5_historical_data(self, config: Dict[str, Any], data_config: Dict[str, Any], days: int):'
        
        if search_pattern in content:
            # 在函数开始处添加验证逻辑
            validation_code = '''        # 验证日期范围
        date_range = self._get_date_range_info(data_config)
        end_date_str = date_range['end_date']
        start_date_str = date_range['start_date']
        
        # 检查是否有未来日期
        end_date = datetime.strptime(end_date_str, '%Y-%m-%d')
        if end_date > datetime.now():
            logger.error(f"❌ 检测到未来结束日期: {end_date_str}")
            logger.info("🔧 自动调整为当前时间")
            # 重新计算日期范围
            current_end = datetime.now()
            current_start = current_end - timedelta(days=days)
            logger.info(f"📅 调整后的数据范围: {current_start.strftime('%Y-%m-%d')} 到 {current_end.strftime('%Y-%m-%d')}")
            
            # 更新data_config
            data_config = data_config.copy()
            data_config['end_date'] = current_end.strftime('%Y-%m-%d')
            data_config['start_date'] = current_start.strftime('%Y-%m-%d')
        
        '''
        
        # 在函数开始后插入验证代码
        function_start = content.find(search_pattern)
        if function_start != -1:
            # 找到函数体开始位置
            function_body_start = content.find('"""', function_start)
            function_body_start = content.find('"""', function_body_start + 3) + 3
            function_body_start = content.find('\n', function_body_start) + 1
            
            # 插入验证代码
            content = content[:function_body_start] + validation_code + content[function_body_start:]
            
            # 写回文件
            with open('services/deep_learning_service.py', 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✅ 已添加数据获取前的验证逻辑")
            return True
        else:
            print("⚠️ 未找到目标函数，跳过验证逻辑添加")
            return False
        
    except Exception as e:
        print(f"❌ 添加验证逻辑失败: {e}")
        return False

def improve_error_handling():
    """改进错误处理和日志"""
    print("\n🔧 改进错误处理和日志...")
    
    try:
        # 读取深度学习服务文件
        with open('services/deep_learning_service.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 改进错误日志
        old_error_msg = 'logger.error(f"❌ 获取MT5数据失败，已尝试{max_attempts}次")'
        new_error_msg = '''logger.error(f"❌ 获取MT5数据失败，已尝试{max_attempts}次")
                    logger.error(f"   数据范围: {start_date} 到 {end_date}")
                    logger.error(f"   品种: {symbol}, 时间框架: {timeframe}")
                    logger.error(f"   MT5时间框架: {mt5_timeframe}")
                    
                    # 检查日期范围是否合理
                    if end_date > datetime.now():
                        logger.error(f"   ⚠️ 结束日期是未来时间: {end_date}")
                    
                    # 获取MT5最后错误
                    try:
                        last_error = mt5.last_error()
                        logger.error(f"   MT5错误: {last_error}")
                    except:
                        pass'''
        
        if old_error_msg in content:
            content = content.replace(old_error_msg, new_error_msg)
            print("✅ 已改进错误日志")
        
        # 写回文件
        with open('services/deep_learning_service.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 错误处理改进完成")
        return True
        
    except Exception as e:
        print(f"❌ 改进错误处理失败: {e}")
        return False

def test_date_logic():
    """测试修复后的日期逻辑"""
    print("\n🧪 测试修复后的日期逻辑...")
    
    try:
        from services.deep_learning_service import deep_learning_service
        
        # 测试不同的日期配置
        test_configs = [
            {
                'mode': 'days',
                'training_days': 30,
                'end_date': '2025-06-01'  # 未来日期
            },
            {
                'mode': 'days',
                'training_days': 30
                # 无end_date，应使用当前时间
            },
            {
                'mode': 'range',
                'start_date': '2024-06-01',
                'end_date': '2025-06-01'  # 未来日期
            }
        ]
        
        for i, config in enumerate(test_configs, 1):
            print(f"\n测试配置 {i}: {config}")
            try:
                date_range = deep_learning_service._get_date_range_info(config)
                print(f"   结果: {date_range}")
                
                # 验证结束日期不是未来时间
                end_date = datetime.strptime(date_range['end_date'], '%Y-%m-%d')
                if end_date <= datetime.now():
                    print("   ✅ 日期范围正常")
                else:
                    print("   ❌ 仍有未来日期问题")
                    
            except Exception as e:
                print(f"   ❌ 测试失败: {e}")
        
        print("\n✅ 日期逻辑测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 日期逻辑测试失败: {e}")
        return False

def create_training_config_template():
    """创建正确的训练配置模板"""
    print("\n📝 创建正确的训练配置模板...")
    
    template = {
        "model_name": "XAUUSD_5M_Model",
        "symbol": "XAUUSD",
        "timeframe": "5m",
        "data_config": {
            "mode": "days",  # 使用天数模式，更安全
            "training_days": 365,  # 一年数据
            # 不设置end_date，自动使用当前时间
        },
        "model_config": {
            "model_type": "lstm",
            "sequence_length": 60,
            "hidden_size": 128,
            "num_layers": 2,
            "dropout": 0.2
        },
        "training_config": {
            "epochs": 100,
            "batch_size": 32,
            "learning_rate": 0.001,
            "early_stopping_patience": 10
        }
    }
    
    try:
        import json
        with open('training_config_template.json', 'w', encoding='utf-8') as f:
            json.dump(template, f, indent=2, ensure_ascii=False)
        
        print("✅ 训练配置模板已创建: training_config_template.json")
        print("💡 使用此模板可以避免日期配置问题")
        return True
        
    except Exception as e:
        print(f"❌ 创建配置模板失败: {e}")
        return False

def main():
    """主修复函数"""
    print("🔧 修复AI推理模型训练数据获取故障")
    print("=" * 60)
    print("问题: 训练使用未来日期 2025-06-01 导致数据获取失败")
    print("=" * 60)
    
    success_count = 0
    total_steps = 5
    
    # 步骤1: 修复日期范围逻辑
    if fix_date_range_logic():
        success_count += 1
    
    # 步骤2: 添加数据验证
    if add_data_validation():
        success_count += 1
    
    # 步骤3: 改进错误处理
    if improve_error_handling():
        success_count += 1
    
    # 步骤4: 测试修复结果
    if test_date_logic():
        success_count += 1
    
    # 步骤5: 创建配置模板
    if create_training_config_template():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"🎯 修复完成! 成功: {success_count}/{total_steps}")
    
    if success_count == total_steps:
        print("✅ 所有修复步骤都成功完成")
        print("\n💡 建议:")
        print("1. 重新启动应用程序")
        print("2. 使用新的训练配置模板")
        print("3. 确保训练时不使用未来日期")
    else:
        print("⚠️ 部分修复步骤失败，请检查错误信息")

if __name__ == "__main__":
    main()
