#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理trading_system.db中的ai_trades数据
"""

import sqlite3
import os

def clean_trading_system_database():
    """清理trading_system.db数据库"""
    print("🔧 清理trading_system.db数据库")
    print("=" * 50)
    
    db_path = 'trading_system.db'
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查ai_trades表
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='ai_trades'
        """)
        
        if not cursor.fetchone():
            print("ℹ️  ai_trades表不存在")
            conn.close()
            return True
        
        # 查看当前数据
        cursor.execute("SELECT COUNT(*) FROM ai_trades")
        total_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM ai_trades WHERE status = 'open'")
        open_count = cursor.fetchone()[0]
        
        print(f"📊 当前数据状态:")
        print(f"   - 总记录数: {total_count}")
        print(f"   - 开仓记录: {open_count}")
        
        if total_count > 0:
            # 显示记录详情
            cursor.execute("""
                SELECT id, symbol, action, lot_size, entry_price, order_id, status, created_at
                FROM ai_trades 
                ORDER BY created_at DESC
            """)
            records = cursor.fetchall()
            
            print(f"\n📋 ai_trades记录详情:")
            for record in records:
                trade_id, symbol, action, lot_size, entry_price, order_id, status, created_at = record
                print(f"   {trade_id[:8]}... | {symbol} | {action} | {lot_size} | {entry_price} | {order_id} | {status} | {created_at}")
            
            # 删除所有记录
            cursor.execute("DELETE FROM ai_trades")
            deleted_count = cursor.rowcount
            
            conn.commit()
            
            print(f"\n✅ 成功删除 {deleted_count} 条记录")
            
            # 验证删除结果
            cursor.execute("SELECT COUNT(*) FROM ai_trades")
            remaining_count = cursor.fetchone()[0]
            
            print(f"📊 删除后状态:")
            print(f"   - 剩余记录: {remaining_count}")
            
            if remaining_count == 0:
                print("✅ trading_system.db中的ai_trades表已清空")
            else:
                print(f"⚠️  仍有 {remaining_count} 条记录未删除")
        else:
            print("ℹ️  ai_trades表已经是空的")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 清理数据库失败: {e}")
        return False

def verify_all_databases():
    """验证所有数据库的清理状态"""
    print("\n🔍 验证所有数据库清理状态")
    print("=" * 50)
    
    databases = [
        'instance/matetrade4.db',
        'trading_system.db'
    ]
    
    all_clean = True
    
    for db_path in databases:
        if os.path.exists(db_path):
            try:
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                
                # 检查ai_trades表
                cursor.execute("""
                    SELECT name FROM sqlite_master 
                    WHERE type='table' AND name='ai_trades'
                """)
                
                if cursor.fetchone():
                    cursor.execute("SELECT COUNT(*) FROM ai_trades")
                    count = cursor.fetchone()[0]
                    
                    if count == 0:
                        print(f"✅ {db_path}: ai_trades表已清空")
                    else:
                        print(f"❌ {db_path}: 仍有{count}条记录")
                        all_clean = False
                else:
                    print(f"ℹ️  {db_path}: 无ai_trades表")
                
                conn.close()
                
            except Exception as e:
                print(f"❌ 检查{db_path}失败: {e}")
                all_clean = False
        else:
            print(f"ℹ️  {db_path}: 文件不存在")
    
    return all_clean

def main():
    """主函数"""
    print("🔧 清理trading_system.db中的AI交易数据")
    print("=" * 80)
    
    print("📋 发现的问题:")
    print("• trading_system.db中仍有3条ai_trades记录")
    print("• 这可能是持仓显示问题的根源")
    print()
    
    # 清理trading_system.db
    cleanup_success = clean_trading_system_database()
    
    # 验证所有数据库
    all_clean = verify_all_databases()
    
    print("\n📊 清理总结")
    print("=" * 80)
    
    if cleanup_success and all_clean:
        print("✅ 所有数据库清理完成")
        print("✅ ai_trades表已完全清空")
        print("✅ 持仓数据应该已经清除")
        print()
        print("💡 下一步操作:")
        print("1. 重启应用程序")
        print("2. 强制刷新浏览器 (Ctrl+F5)")
        print("3. 进入模型推理页面")
        print("4. 确认持仓数显示为0")
        print("5. 如果仍有问题，执行前端清理脚本")
    else:
        print("❌ 清理过程中出现问题")
        if not cleanup_success:
            print("❌ trading_system.db清理失败")
        if not all_clean:
            print("❌ 部分数据库仍有残留数据")
    
    return 0 if (cleanup_success and all_clean) else 1

if __name__ == "__main__":
    exit(main())
