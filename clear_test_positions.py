#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清除AI推理交易的测试持仓数据
"""

import sqlite3
import os

def clear_test_positions():
    """清除测试持仓数据"""
    print("🔧 清除AI推理交易测试持仓数据")
    print("=" * 50)
    
    # 尝试多个可能的数据库路径
    db_paths = [
        'instance/matetrade4.db',
        'matetrade4.db', 
        'trading_system.db',
        'instance/trading_system.db'
    ]
    
    conn = None
    db_path = None
    for path in db_paths:
        if os.path.exists(path):
            try:
                conn = sqlite3.connect(path)
                db_path = path
                print(f"✅ 连接到数据库: {path}")
                break
            except Exception as e:
                print(f"❌ 连接数据库失败 {path}: {e}")
                continue
    
    if not conn:
        print("❌ 无法连接到任何数据库文件")
        return False
    
    try:
        cursor = conn.cursor()
        
        # 检查当前数据
        cursor.execute("SELECT COUNT(*) FROM ai_trades WHERE status = 'open'")
        open_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM ai_trades")
        total_count = cursor.fetchone()[0]
        
        print(f"📊 当前数据状态:")
        print(f"   - 开仓记录: {open_count}")
        print(f"   - 总记录数: {total_count}")
        
        if open_count > 0:
            # 显示开仓记录详情
            cursor.execute("""
                SELECT id, symbol, action, lot_size, entry_price, order_id, created_at
                FROM ai_trades 
                WHERE status = 'open'
                ORDER BY created_at DESC
            """)
            open_trades = cursor.fetchall()
            
            print(f"\n📋 当前开仓记录:")
            for trade in open_trades:
                trade_id, symbol, action, lot_size, entry_price, order_id, created_at = trade
                print(f"   {trade_id[:8]}... | {symbol} | {action} | {lot_size} | {entry_price} | {order_id} | {created_at}")
            
            # 确认删除
            print(f"\n⚠️  准备删除 {open_count} 个开仓记录")
            
            # 删除所有AI交易记录（包括开仓和平仓）
            cursor.execute("DELETE FROM ai_trades")
            deleted_count = cursor.rowcount
            
            conn.commit()
            
            print(f"✅ 成功删除 {deleted_count} 条AI交易记录")
            
            # 验证删除结果
            cursor.execute("SELECT COUNT(*) FROM ai_trades")
            remaining_count = cursor.fetchone()[0]
            
            print(f"📊 删除后状态:")
            print(f"   - 剩余记录: {remaining_count}")
            
            if remaining_count == 0:
                print("✅ 所有AI交易记录已清除")
            else:
                print(f"⚠️  仍有 {remaining_count} 条记录未删除")
        else:
            print("ℹ️  没有开仓记录需要删除")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 清除数据失败: {e}")
        conn.close()
        return False

def main():
    """主函数"""
    print("🔧 AI推理交易测试数据清理工具")
    print("=" * 80)
    
    print("📋 问题描述:")
    print("• 当前持仓显示为3，但没有产生真实交易")
    print("• 这些是之前创建的测试数据")
    print("• 需要清除这些测试数据")
    print()
    
    # 清除测试数据
    success = clear_test_positions()
    
    print("\n📊 清理总结")
    print("=" * 80)
    
    if success:
        print("✅ 测试数据清理完成")
        print("✅ AI推理交易持仓已清零")
        print("✅ 现在可以进行真实的AI推理交易")
        print()
        print("💡 下一步:")
        print("1. 重启应用程序")
        print("2. 进入模型推理页面")
        print("3. 确认持仓数显示为0")
        print("4. 开始真实的AI推理交易测试")
    else:
        print("❌ 数据清理失败")
        print("❌ 请检查数据库连接和权限")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())
