#!/usr/bin/env python3
"""
迁移training_tasks表，添加updated_at字段
"""

import sqlite3
from datetime import datetime

def migrate_training_tasks_table():
    """迁移training_tasks表"""
    
    print("🔄 迁移training_tasks表")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 检查updated_at字段是否已存在
        cursor.execute("PRAGMA table_info(training_tasks)")
        columns = cursor.fetchall()
        
        column_names = [col[1] for col in columns]
        
        if 'updated_at' in column_names:
            print("✅ updated_at字段已存在，无需迁移")
            conn.close()
            return True
        
        print("📊 当前表结构:")
        for col in columns:
            print(f"  - {col[1]}: {col[2]}")
        
        # 添加updated_at字段（SQLite不支持CURRENT_TIMESTAMP作为默认值）
        print(f"\n🔧 添加updated_at字段...")

        cursor.execute("""
            ALTER TABLE training_tasks
            ADD COLUMN updated_at TIMESTAMP
        """)
        
        # 为现有记录设置updated_at值
        cursor.execute("""
            UPDATE training_tasks 
            SET updated_at = COALESCE(completed_at, started_at, created_at, CURRENT_TIMESTAMP)
            WHERE updated_at IS NULL
        """)
        
        conn.commit()
        
        # 验证迁移结果
        cursor.execute("PRAGMA table_info(training_tasks)")
        new_columns = cursor.fetchall()
        
        print(f"\n✅ 迁移完成，新表结构:")
        for col in new_columns:
            print(f"  - {col[1]}: {col[2]}")
        
        # 检查数据
        cursor.execute("SELECT COUNT(*) FROM training_tasks")
        count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM training_tasks WHERE updated_at IS NOT NULL")
        updated_count = cursor.fetchone()[0]
        
        print(f"\n📊 数据验证:")
        print(f"  总记录数: {count}")
        print(f"  有updated_at的记录: {updated_count}")
        
        conn.close()
        
        print(f"\n🎉 迁移成功完成!")
        return True
        
    except Exception as e:
        print(f"❌ 迁移失败: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

def fix_training_progress_update():
    """修复训练进度更新逻辑"""
    
    print(f"\n🔧 修复训练进度更新逻辑")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 查找状态为running但进度长时间未更新的任务
        cursor.execute("""
            SELECT id, model_id, status, progress, current_epoch, total_epochs,
                   created_at, updated_at,
                   CASE 
                       WHEN updated_at IS NULL THEN 
                           (julianday('now') - julianday(created_at)) * 24 * 60
                       ELSE 
                           (julianday('now') - julianday(updated_at)) * 24 * 60
                   END as minutes_since_update
            FROM training_tasks 
            WHERE status = 'running'
            ORDER BY created_at DESC
        """)
        
        running_tasks = cursor.fetchall()
        
        print(f"📊 找到 {len(running_tasks)} 个运行中的任务:")
        
        for task in running_tasks:
            task_id, model_id, status, progress, current_epoch, total_epochs, created_at, updated_at, minutes_since_update = task
            
            print(f"\n🔹 任务 {task_id}:")
            print(f"   模型: {model_id}")
            print(f"   进度: {progress}%")
            print(f"   轮次: {current_epoch}/{total_epochs}")
            print(f"   创建时间: {created_at}")
            print(f"   更新时间: {updated_at}")
            print(f"   距离上次更新: {minutes_since_update:.1f} 分钟")
            
            # 如果超过10分钟没有更新，可能是僵尸任务
            if minutes_since_update > 10:
                print(f"   ⚠️ 可能是僵尸任务（超过10分钟未更新）")
                
                # 询问是否要重置状态
                response = input(f"   是否将此任务标记为失败？(y/N): ").lower().strip()
                
                if response in ['y', 'yes']:
                    cursor.execute("""
                        UPDATE training_tasks 
                        SET status = 'failed', 
                            completed_at = CURRENT_TIMESTAMP,
                            updated_at = CURRENT_TIMESTAMP,
                            logs = json_set(COALESCE(logs, '{}'), '$.error', '任务超时，自动标记为失败')
                        WHERE id = ?
                    """, (task_id,))
                    
                    conn.commit()
                    print(f"   ✅ 任务已标记为失败")
            else:
                print(f"   ✅ 任务状态正常")
        
        if not running_tasks:
            print("📊 没有找到运行中的任务")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False

def test_progress_update_mechanism():
    """测试进度更新机制"""
    
    print(f"\n🧪 测试进度更新机制")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 创建一个测试任务
        test_task_id = f"test_progress_{int(datetime.now().timestamp())}"
        
        print(f"📝 创建测试任务: {test_task_id}")
        
        cursor.execute("""
            INSERT INTO training_tasks 
            (id, model_id, status, progress, current_epoch, total_epochs,
             train_loss, val_loss, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (test_task_id, 'test_model', 'running', 10.0, 1, 10,
              0.8, 0.9, datetime.now().isoformat(), datetime.now().isoformat()))
        
        conn.commit()
        
        # 模拟进度更新
        import time
        
        for epoch in range(2, 6):
            progress = 10 + (epoch - 1) * 20
            train_loss = 0.8 - (epoch - 1) * 0.1
            val_loss = 0.9 - (epoch - 1) * 0.08
            
            print(f"\n🔄 更新进度 - 轮次 {epoch}:")
            print(f"   进度: {progress}%")
            print(f"   训练损失: {train_loss:.4f}")
            print(f"   验证损失: {val_loss:.4f}")
            
            cursor.execute("""
                UPDATE training_tasks
                SET progress = ?, current_epoch = ?, train_loss = ?, val_loss = ?,
                    updated_at = ?
                WHERE id = ?
            """, (progress, epoch, train_loss, val_loss, 
                  datetime.now().isoformat(), test_task_id))
            
            conn.commit()
            
            # 验证更新
            cursor.execute("""
                SELECT progress, current_epoch, train_loss, val_loss, updated_at
                FROM training_tasks WHERE id = ?
            """, (test_task_id,))
            
            result = cursor.fetchone()
            if result:
                print(f"   ✅ 数据库已更新: 进度={result[0]}%, 轮次={result[1]}, 更新时间={result[4]}")
            else:
                print(f"   ❌ 数据库更新失败")
            
            time.sleep(1)
        
        # 完成任务
        cursor.execute("""
            UPDATE training_tasks
            SET status = 'completed', progress = 100, completed_at = ?, updated_at = ?
            WHERE id = ?
        """, (datetime.now().isoformat(), datetime.now().isoformat(), test_task_id))
        
        conn.commit()
        
        print(f"\n✅ 测试任务完成")
        
        # 清理测试任务
        cursor.execute("DELETE FROM training_tasks WHERE id = ?", (test_task_id,))
        conn.commit()
        
        print(f"🧹 测试任务已清理")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    
    print("🔧 深度学习训练进度修复")
    print("=" * 60)
    
    # 迁移数据库表
    if migrate_training_tasks_table():
        print("\n" + "=" * 30)
        print("修复训练进度")
        print("=" * 30)
        
        # 修复训练进度
        fix_training_progress_update()
        
        print("\n" + "=" * 30)
        print("测试进度更新")
        print("=" * 30)
        
        # 测试进度更新机制
        test_progress_update_mechanism()
        
        print(f"\n🎉 修复完成!")
        print("=" * 60)
        
        print(f"\n📋 修复内容:")
        print(f"✅ 添加了updated_at字段到training_tasks表")
        print(f"✅ 修复了现有记录的updated_at值")
        print(f"✅ 检查并处理了僵尸训练任务")
        print(f"✅ 验证了进度更新机制")
        
        print(f"\n💡 建议:")
        print(f"• 重启应用程序以确保新的表结构生效")
        print(f"• 检查训练进程是否正确调用进度更新函数")
        print(f"• 监控训练任务的updated_at字段变化")
        print(f"• 如果问题仍然存在，检查训练循环中的进度更新逻辑")
        
    else:
        print(f"\n❌ 迁移失败，无法继续修复")

if __name__ == '__main__':
    main()
