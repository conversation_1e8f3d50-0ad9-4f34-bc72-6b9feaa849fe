{% extends "base.html" %}

{% block page_title %}真实交易{% endblock %}

{% block content %}
<div class="row">
    <!-- 账户选择 -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-wallet"></i>
                    交易账户
                </h5>
            </div>
            <div class="card-body">
                <!-- MT5自动连接状态 -->
                <div class="alert alert-info mb-3" id="mt5ConnectionStatus">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <i class="fas fa-plug"></i>
                            <strong>MT5连接状态</strong>
                        </div>
                        <div>
                            <button class="btn btn-sm btn-outline-primary" onclick="autoConnectMT5()">
                                <i class="fas fa-sync"></i>
                                自动连接MT5
                            </button>
                        </div>
                    </div>
                    <small class="d-block mt-2" id="mt5StatusText">点击自动连接以获取MT5客户端账户信息</small>
                </div>

                <select class="form-select mb-3" id="accountSelect">
                    <option value="">选择交易账户</option>
                    {% for account in accounts %}
                    <option value="{{ account.id }}">
                        {{ account.account_name }} ({{ account.broker }})
                    </option>
                    {% endfor %}
                </select>

                <div class="d-flex gap-2 mb-3">
                    <button class="btn btn-outline-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addAccountModal">
                        <i class="fas fa-plus"></i>
                        手动添加账户
                    </button>
                    <button class="btn btn-outline-success btn-sm" onclick="refreshMT5Accounts()">
                        <i class="fas fa-refresh"></i>
                        刷新MT5账户
                    </button>
                </div>

                <div id="accountInfo" class="mt-3" style="display: none;">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h6 class="text-muted">余额</h6>
                                <h5 id="accountBalance">$0.00</h5>
                            </div>
                        </div>
                        <div class="col-6">
                            <h6 class="text-muted">净值</h6>
                            <h5 id="accountEquity">$0.00</h5>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 下单面板 -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line"></i>
                    <span id="tradingModeTitle">下单交易</span>
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>风险提示：</strong>真实交易涉及实际资金，请谨慎操作。建议先在模拟环境中测试策略。
                </div>

                <!-- AI智能交易切换 -->
                <div class="mb-4">
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="aiTradingSwitch" checked onchange="toggleAITrading()">
                        <label class="form-check-label" for="aiTradingSwitch">
                            <i class="fas fa-robot"></i>
                            <strong>AI智能交易</strong>
                        </label>
                    </div>
                    <small class="text-muted">启用后将使用AI策略进行自动化交易</small>
                </div>

                <!-- AI策略选择区域 -->
                <div id="aiTradingSection" style="display: none;">
                    <div class="alert alert-danger">
                        <i class="fas fa-robot"></i>
                        <strong>AI智能交易模式</strong> - 系统将根据选择的AI策略自动执行<strong>真实资金</strong>交易决策
                        <br><small>请确保您完全理解所选策略的风险特征</small>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">选择AI策略</label>
                        <select class="form-select" id="aiStrategySelect">
                            <option value="">正在加载AI策略...</option>
                        </select>
                        <small class="text-muted">
                            选择用于自动交易的AI策略
                            <button type="button" class="btn btn-link btn-sm p-0 ms-2" onclick="setAsDefaultAIStrategy()">
                                <i class="fas fa-star text-warning"></i> 设为默认
                            </button>
                        </small>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">交易货币对</label>
                        <select class="form-select" id="aiTradingSymbol" required>
                            <option value="">选择货币对</option>

                            <optgroup label="📈 主要外汇对">
                                <option value="EURUSD=X" selected>EUR/USD (欧元/美元)</option>
                                <option value="GBPUSD=X">GBP/USD (英镑/美元)</option>
                                <option value="USDJPY=X">USD/JPY (美元/日元)</option>
                                <option value="AUDUSD=X">AUD/USD (澳元/美元)</option>
                                <option value="USDCAD=X">USD/CAD (美元/加元)</option>
                                <option value="USDCHF=X">USD/CHF (美元/瑞郎)</option>
                                <option value="NZDUSD=X">NZD/USD (纽元/美元)</option>
                            </optgroup>

                            <optgroup label="🔄 交叉外汇对">
                                <option value="EURJPY=X">EUR/JPY (欧元/日元)</option>
                                <option value="GBPJPY=X">GBP/JPY (英镑/日元)</option>
                                <option value="EURGBP=X">EUR/GBP (欧元/英镑)</option>
                                <option value="AUDCAD=X">AUD/CAD (澳元/加元)</option>
                            </optgroup>

                            <optgroup label="🥇 贵金属">
                                <option value="XAUUSD">XAU/USD (黄金/美元)</option>
                                <option value="GC=F">黄金期货 (Gold Futures)</option>
                                <option value="GLD">黄金ETF (SPDR Gold Trust)</option>
                                <option value="XAGUSD">XAG/USD (白银/美元)</option>
                                <option value="SI=F">白银期货 (Silver Futures)</option>
                            </optgroup>

                            <optgroup label="🛢️ 大宗商品">
                                <option value="CL=F">原油期货 (WTI Crude Oil)</option>
                                <option value="BZ=F">布伦特原油期货</option>
                                <option value="NG=F">天然气期货</option>
                            </optgroup>

                            <optgroup label="₿ 加密货币">
                                <option value="BTC-USD">BTC/USD (比特币/美元)</option>
                                <option value="ETH-USD">ETH/USD (以太坊/美元)</option>
                            </optgroup>
                        </select>
                        <small class="text-muted">
                            选择AI交易的货币对
                            <button type="button" class="btn btn-link btn-sm p-0 ms-2" onclick="setAsDefaultSymbol()">
                                <i class="fas fa-star text-warning"></i> 设为默认
                            </button>
                            <button type="button" class="btn btn-link btn-sm p-0 ms-1" onclick="saveCurrentAsDefault()">
                                <i class="fas fa-save text-primary"></i> 保存所有设置
                            </button>
                        </small>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">风险等级</label>
                        <select class="form-select" id="riskLevel" onchange="updateStopLossTakeProfitByRisk()">
                            <option value="conservative" selected>保守型 - 止损1% 止盈1.5%</option>
                            <option value="moderate">稳健型 - 止损1% 止盈2%</option>
                            <option value="aggressive">激进型 - 止损1.5% 止盈3%</option>
                        </select>
                        <small class="text-muted">AI将根据风险等级自动调整止损止盈比例</small>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">交易时间段</label>
                        <select class="form-select" id="tradingTimeSlot">
                            <option value="09:00-12:00" selected>上午时段 (09:00-12:00)</option>
                            <option value="15:00-18:00">下午时段 (15:00-18:00)</option>
                            <option value="09:00-18:00">全天时段 (09:00-18:00)</option>
                            <option value="08:00-23:00">扩展时段 (08:00-23:00)</option>
                        </select>
                        <small class="text-muted">
                            <i class="fas fa-clock"></i>
                            AI只在指定时间段内进行交易，超出时间段将自动停止
                        </small>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">单次交易手数区间</label>
                        <div class="row">
                            <div class="col-6">
                                <div class="input-group">
                                    <span class="input-group-text">最小</span>
                                    <input type="number" class="form-control" id="minLotSize" value="0.01" min="0.01" max="0.05" step="0.01" onchange="validateLotSizeRange()">
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="input-group">
                                    <span class="input-group-text">最大</span>
                                    <input type="number" class="form-control" id="maxLotSize" value="0.04" min="0.01" max="0.05" step="0.01" onchange="validateLotSizeRange()">
                                </div>
                            </div>
                        </div>
                        <small class="text-muted">
                            <i class="fas fa-info-circle"></i>
                            交易手数范围：0.01-0.05手，系统将在此区间内选择合适的手数
                        </small>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">
                            <i class="fas fa-chart-line"></i>
                            单次运行交易次数限制
                        </label>
                        <select class="form-select" id="maxTradeCount">
                            <option value="10">10次交易后停止</option>
                            <option value="20">20次交易后停止</option>
                            <option value="30" selected>30次交易后停止</option>
                            <option value="unlimited">不限制交易次数</option>
                        </select>
                        <small class="text-muted">
                            <i class="fas fa-info-circle"></i>
                            AI达到指定交易次数后将自动停止，防止过度交易
                        </small>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">每日交易次数限制</label>
                        <input type="number" class="form-control" id="dailyTradeLimit" value="30" min="1" max="50">
                        <small class="text-muted">每日最大交易次数，防止过度交易</small>
                    </div>

                    <!-- 全程自动选项 -->
                    <div class="card border-warning mb-3">
                        <div class="card-header bg-light">
                            <div class="d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">
                                    <i class="fas fa-magic text-warning"></i>
                                    全程自动交易
                                </h6>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="fullAutoTrading" onchange="toggleFullAutoTrading()">
                                    <label class="form-check-label" for="fullAutoTrading">启用</label>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle"></i>
                                <strong>高级功能</strong> - 启用后，AI策略将全程参与交易决策
                            </div>
                            <div id="fullAutoTradingSettings" style="display: none;">
                                <div class="mb-3">
                                    <label class="form-label">动态离场策略</label>
                                    <select class="form-select" id="dynamicExitStrategy">
                                        <option value="conservative" selected>保守型 - 优先保护利润</option>
                                        <option value="balanced">平衡型 - 利润与趋势并重</option>
                                        <option value="aggressive">激进型 - 追求最大利润</option>
                                    </select>
                                    <small class="text-muted">AI将根据策略动态判断最佳离场时机</small>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">离场信号阈值</label>
                                    <select class="form-select" id="exitSignalThreshold">
                                        <option value="0.7" selected>70% - 较为保守</option>
                                        <option value="0.6">60% - 平衡</option>
                                        <option value="0.5">50% - 较为激进</option>
                                    </select>
                                    <small class="text-muted">AI信号置信度达到此阈值时执行离场</small>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="keepStopLossTakeProfit" checked>
                                    <label class="form-check-label" for="keepStopLossTakeProfit">
                                        保留止损止盈保护
                                    </label>
                                    <small class="form-text text-muted d-block">
                                        即使启用全程自动，仍保留传统止损止盈作为风险控制底线
                                    </small>
                                </div>
                            </div>
                            <div class="mt-2">
                                <small class="text-muted">
                                    <i class="fas fa-info-circle"></i>
                                    全程自动模式下，AI将在整个交易生命周期中持续分析市场，动态决定最佳离场时机
                                </small>
                            </div>
                        </div>
                    </div>

                    <!-- AI智能止盈止损设置 -->
                    <div class="card border-info mb-3">
                        <div class="card-header bg-light">
                            <div class="d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">
                                    <i class="fas fa-shield-alt text-info"></i>
                                    AI智能止盈止损
                                </h6>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="aiEnableStopLossTakeProfit" checked onchange="toggleAIStopLossTakeProfit()">
                                    <label class="form-check-label" for="aiEnableStopLossTakeProfit">启用</label>
                                </div>
                            </div>
                        </div>
                        <div class="card-body" id="aiStopLossTakeProfitSettings">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">
                                        <i class="fas fa-arrow-down text-danger"></i>
                                        智能止损比例 (%)
                                    </label>
                                    <input type="number" class="form-control" id="aiStopLossPercent" value="1.0" min="0.5" max="10" step="0.1">
                                    <small class="text-muted">保守型:1%, 稳健型:1%, 激进型:1.5%</small>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">
                                        <i class="fas fa-arrow-up text-success"></i>
                                        智能止盈比例 (%)
                                    </label>
                                    <input type="number" class="form-control" id="aiTakeProfitPercent" value="2.0" min="1" max="20" step="0.1">
                                    <small class="text-muted">保守型:1.5%, 稳健型:2%, 激进型:3%</small>
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="aiAutoAdjustStopLoss" checked>
                                    <label class="form-check-label" for="aiAutoAdjustStopLoss">
                                        <i class="fas fa-robot"></i>
                                        启用AI动态调整
                                    </label>
                                </div>
                                <small class="text-muted">AI将根据市场波动率、趋势强度和货币对特性动态调整止损止盈水平</small>
                            </div>

                            <!-- 止盈止损监控控制 -->
                            <div class="card border-success mb-3">
                                <div class="card-header bg-light">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h6 class="mb-0">
                                            <i class="fas fa-eye text-success"></i>
                                            自动监控服务
                                        </h6>
                                        <span id="monitoringStatus" class="badge bg-secondary">未启动</span>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="d-flex gap-2 mb-3">
                                        <button class="btn btn-success btn-sm" onclick="startStopLossMonitoring()">
                                            <i class="fas fa-play"></i> 启动监控
                                        </button>
                                        <button class="btn btn-danger btn-sm" onclick="stopStopLossMonitoring()">
                                            <i class="fas fa-stop"></i> 停止监控
                                        </button>
                                        <button class="btn btn-info btn-sm" onclick="checkMonitoringStatus()">
                                            <i class="fas fa-sync"></i> 检查状态
                                        </button>
                                    </div>
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle"></i>
                                        自动监控服务会每5秒检查一次所有持仓的止盈止损条件，并自动执行平仓
                                    </small>
                                </div>
                            </div>

                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle"></i>
                                <strong>真实交易风险提示：</strong>
                                <ul class="mb-0 mt-2">
                                    <li>AI智能止盈止损涉及真实资金，请谨慎设置参数</li>
                                    <li>建议先在模拟环境中测试策略效果</li>
                                    <li>市场极端波动时可能无法保证执行价格</li>
                                    <li>请确保您完全理解相关风险</li>
                                    <li><strong>启用自动监控后，系统会自动执行止盈止损平仓</strong></li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- 风险确认 -->
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="riskConfirmation" required>
                            <label class="form-check-label" for="riskConfirmation">
                                我已充分了解AI自动交易的风险，并同意承担所有可能的损失
                            </label>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-6">
                            <button type="button" class="btn btn-success w-100" id="startAITrading" onclick="startAITrading()">
                                <i class="fas fa-play"></i>
                                开始AI交易
                            </button>
                        </div>
                        <div class="col-6">
                            <button type="button" class="btn btn-danger w-100" id="stopAITrading" onclick="stopAITrading()" style="display: none;">
                                <i class="fas fa-stop"></i>
                                停止AI交易
                            </button>
                        </div>
                    </div>

                    <!-- AI交易状态 -->
                    <div id="aiTradingStatus" style="display: none;">
                        <div class="alert alert-success">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="fas fa-robot"></i>
                                    <strong>AI交易运行中</strong>
                                    <span class="ms-2">运行时间: <span id="aiRunningTime">00:00:00</span></span>
                                </div>
                                <div>
                                    <span class="badge bg-success">活跃</span>
                                </div>
                            </div>
                            <small class="d-block mt-2">
                                策略: <span id="currentAIStrategy">-</span> |
                                运行时间: <span id="aiRunningTime">00:00:00</span>
                            </small>
                            <small class="d-block mt-1">
                                <i class="fas fa-clock"></i>
                                中国标准时间: <span id="chinaTime">--:--:--</span> |
                                交易货币对: <span id="currentTradingSymbol">-</span>
                            </small>
                            <small class="d-block mt-1">
                                <i class="fas fa-chart-line"></i>
                                已执行交易: <span id="aiTradeCount" class="fw-bold text-primary">0</span> 次 |
                                交易限制: <span id="aiTradeLimit" class="fw-bold text-info">20</span> 次
                                <span id="aiTradeLimitWarning" class="text-warning ms-2" style="display: none;">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    即将达到限制
                                </span>
                            </small>
                        </div>
                    </div>

                    <!-- 入场信息区域 -->
                    <div id="entryInfoSection" style="display: none;">
                        <div class="card mt-3">
                            <div class="card-header bg-primary text-white">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="fas fa-chart-line"></i>
                                        <strong>策略分析 - 入场信息</strong>
                                    </div>
                                    <div>
                                        <span class="badge bg-light text-dark" id="analysisUpdateTime">--:--:--</span>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <!-- 符合入场信号的机会 -->
                                <div id="entrySignalsContainer">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <h6 class="mb-0">
                                            <i class="fas fa-bullseye text-success"></i>
                                            符合入场信号
                                        </h6>
                                        <span class="badge bg-success" id="entrySignalCount">0</span>
                                    </div>
                                    <div id="entrySignalsList" class="mb-3">
                                        <div class="text-muted text-center py-2">
                                            <i class="fas fa-search"></i>
                                            正在分析市场机会...
                                        </div>
                                    </div>
                                </div>

                                <!-- 策略模型工作状态 -->
                                <div id="strategyWorkingStatus">
                                    <h6 class="mb-2">
                                        <i class="fas fa-cogs text-info"></i>
                                        策略模型状态
                                    </h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <small class="text-muted">最新分析时间:</small><br>
                                            <span id="lastAnalysisTime" class="fw-bold">--:--:--</span>
                                        </div>
                                        <div class="col-md-6">
                                            <small class="text-muted">分析频率:</small><br>
                                            <span id="analysisFrequency" class="fw-bold">每30秒</span>
                                        </div>
                                    </div>
                                    <div class="row mt-2">
                                        <div class="col-md-6">
                                            <small class="text-muted">模型置信度:</small><br>
                                            <span id="modelConfidence" class="fw-bold">--</span>
                                        </div>
                                        <div class="col-md-6">
                                            <small class="text-muted">市场趋势:</small><br>
                                            <span id="marketTrend" class="fw-bold">--</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- 实时市场数据 -->
                                <div id="realtimeMarketData" class="mt-3">
                                    <h6 class="mb-2">
                                        <i class="fas fa-chart-area text-warning"></i>
                                        实时市场数据
                                    </h6>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <small class="text-muted">当前价格:</small><br>
                                            <span id="currentPrice" class="fw-bold">--</span>
                                        </div>
                                        <div class="col-md-4">
                                            <small class="text-muted">价格变化:</small><br>
                                            <span id="priceChange" class="fw-bold">--</span>
                                        </div>
                                        <div class="col-md-4">
                                            <small class="text-muted">波动率:</small><br>
                                            <span id="volatility" class="fw-bold">--</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 手动交易表单 -->
                <div id="manualTradingSection">
                    <form id="orderForm">
                    <div class="mb-3">
                        <label class="form-label">货币对</label>
                        <select class="form-select" id="symbolSelect" required>
                            <option value="">选择货币对</option>
                            <option value="EURUSD">EUR/USD</option>
                            <option value="GBPUSD">GBP/USD</option>
                            <option value="USDJPY">USD/JPY</option>
                            <option value="AUDUSD">AUD/USD</option>
                            <option value="USDCAD">USD/CAD</option>
                            <option value="XAUUSD">XAU/USD</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">订单类型</label>
                        <select class="form-select" id="orderType" required>
                            <option value="market">市价单</option>
                            <option value="limit">限价单</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">交易方向</label>
                        <div class="btn-group w-100" role="group">
                            <input type="radio" class="btn-check" name="orderSide" id="buyRadio" value="buy" checked>
                            <label class="btn btn-outline-success" for="buyRadio">买入</label>

                            <input type="radio" class="btn-check" name="orderSide" id="sellRadio" value="sell">
                            <label class="btn btn-outline-danger" for="sellRadio">卖出</label>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">数量</label>
                        <input type="number" class="form-control" id="orderAmount" step="0.001" required>
                    </div>

                    <div class="mb-3" id="priceGroup" style="display: none;">
                        <label class="form-label">价格</label>
                        <input type="number" class="form-control" id="orderPrice" step="0.01">
                    </div>

                    <!-- 手动止盈止损设置 -->
                    <div class="card border-warning mb-3">
                        <div class="card-header bg-light">
                            <div class="d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">
                                    <i class="fas fa-shield-alt text-warning"></i>
                                    手动止盈止损
                                </h6>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="enableManualStopLoss" onchange="toggleManualStopLoss()">
                                    <label class="form-check-label" for="enableManualStopLoss">启用</label>
                                </div>
                            </div>
                        </div>
                        <div class="card-body" id="manualStopLossSettings" style="display: none;">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">
                                        <i class="fas fa-arrow-down text-danger"></i>
                                        止损价格
                                    </label>
                                    <input type="number" class="form-control" id="stopLoss" step="0.00001" placeholder="设置止损价格">
                                    <small class="text-muted">价格低于此值时自动平仓</small>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">
                                        <i class="fas fa-arrow-up text-success"></i>
                                        止盈价格
                                    </label>
                                    <input type="number" class="form-control" id="takeProfit" step="0.00001" placeholder="设置止盈价格">
                                    <small class="text-muted">价格高于此值时自动平仓</small>
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="trailingStop">
                                    <label class="form-check-label" for="trailingStop">
                                        <i class="fas fa-chart-line"></i>
                                        启用追踪止损
                                    </label>
                                </div>
                                <small class="text-muted">止损价格会随着有利价格变动而调整</small>
                            </div>

                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle"></i>
                                <strong>风险提示：</strong>止盈止损功能可以帮助控制风险，但在极端市场条件下可能无法保证执行价格。
                            </div>
                        </div>
                    </div>

                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-paper-plane"></i>
                            提交订单
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 持仓和订单 -->
    <div class="col-lg-8">
        <!-- 保证金计算信息 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calculator"></i>
                    保证金计算参考
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <!-- 风险等级对应手数 -->
                    <div class="col-md-6">
                        <h6 class="text-primary mb-3">
                            <i class="fas fa-chart-bar"></i>
                            XAUUSD风险等级参考
                        </h6>
                        <div class="table-responsive">
                            <table class="table table-sm table-bordered">
                                <thead class="table-light">
                                    <tr>
                                        <th>风险等级</th>
                                        <th>推荐手数</th>
                                        <th>保证金需求</th>
                                        <th>适合用户</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr class="table-success">
                                        <td><span class="badge bg-success">保守型</span></td>
                                        <td>0.05手</td>
                                        <td>$165</td>
                                        <td>小额账户</td>
                                    </tr>
                                    <tr class="table-warning">
                                        <td><span class="badge bg-warning">稳健型</span></td>
                                        <td>0.08手</td>
                                        <td>$264</td>
                                        <td>中等账户</td>
                                    </tr>
                                    <tr class="table-danger">
                                        <td><span class="badge bg-danger">激进型</span></td>
                                        <td>0.10手</td>
                                        <td>$330</td>
                                        <td>大额账户</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- 保证金计算公式 -->
                    <div class="col-md-6">
                        <h6 class="text-info mb-3">
                            <i class="fas fa-formula"></i>
                            保证金计算公式
                        </h6>
                        <div class="bg-light p-3 rounded">
                            <div class="mb-3">
                                <strong>合约规格:</strong> 1手 = 100盎司黄金<br>
                                <strong>当前价格:</strong> $3,300/盎司<br>
                                <strong>杠杆比例:</strong> 1:100
                            </div>

                            <div class="border-top pt-3">
                                <h6 class="text-success">计算示例 (0.1手 XAUUSD):</h6>
                                <div class="font-monospace small">
                                    <div>合约价值 = 0.1手 × 100盎司 × $3,300</div>
                                    <div class="text-muted">= $33,000</div>
                                    <div class="mt-2">保证金 = $33,000 ÷ 100</div>
                                    <div class="text-success fw-bold">= $330</div>
                                </div>
                            </div>

                            <div class="alert alert-warning mt-3 mb-0">
                                <small>
                                    <i class="fas fa-info-circle"></i>
                                    <strong>提示:</strong> 保证金不是交易成本，而是开仓所需的"押金"。实际盈亏取决于价格变动。
                                </small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 实时保证金计算器 -->
                <div class="row mt-4">
                    <div class="col-12">
                        <h6 class="text-secondary mb-3">
                            <i class="fas fa-calculator"></i>
                            实时保证金计算器
                        </h6>
                        <div class="row">
                            <div class="col-md-3">
                                <label class="form-label small">交易品种</label>
                                <select class="form-select form-select-sm" id="marginSymbol">
                                    <option value="XAUUSD">XAUUSD (黄金)</option>
                                    <option value="EURUSD">EURUSD (欧美)</option>
                                    <option value="GBPUSD">GBPUSD (镑美)</option>
                                    <option value="USDJPY">USDJPY (美日)</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label small">手数</label>
                                <input type="number" class="form-control form-control-sm" id="marginLots"
                                       value="0.1" min="0.01" max="10" step="0.01" onchange="calculateMargin()">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label small">价格</label>
                                <input type="number" class="form-control form-control-sm" id="marginPrice"
                                       value="3300" step="0.01" onchange="calculateMargin()">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label small">杠杆</label>
                                <select class="form-select form-select-sm" id="marginLeverage" onchange="calculateMargin()">
                                    <option value="50">1:50</option>
                                    <option value="100" selected>1:100</option>
                                    <option value="200">1:200</option>
                                    <option value="500">1:500</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label small">所需保证金</label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="text" class="form-control form-control-sm" id="marginResult" readonly>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 实时价格图表 -->
        <div class="card mb-4">
            <div class="card-header chart-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0 text-dark">
                        <i class="fas fa-chart-line text-primary"></i>
                        实时价格图表
                    </h5>
                    <div class="d-flex gap-2">
                        <!-- 交易品种选择 -->
                        <select class="form-select form-select-sm chart-symbol-select" id="chartSymbol" style="width: 120px;" onchange="updateChart()">
                            <option value="XAUUSD">XAUUSD</option>
                            <option value="EURUSD">EURUSD</option>
                            <option value="GBPUSD">GBPUSD</option>
                            <option value="USDJPY">USDJPY</option>
                            <option value="USDCHF">USDCHF</option>
                            <option value="AUDUSD">AUDUSD</option>
                        </select>

                        <!-- 时间周期选择 -->
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-dark btn-sm timeframe-btn active" data-timeframe="M1" onclick="setTimeframe('M1')">1分</button>
                            <button type="button" class="btn btn-outline-dark btn-sm timeframe-btn" data-timeframe="M5" onclick="setTimeframe('M5')">5分</button>
                            <button type="button" class="btn btn-outline-dark btn-sm timeframe-btn" data-timeframe="M15" onclick="setTimeframe('M15')">15分</button>
                            <button type="button" class="btn btn-outline-dark btn-sm timeframe-btn" data-timeframe="H1" onclick="setTimeframe('H1')">1时</button>
                            <button type="button" class="btn btn-outline-dark btn-sm timeframe-btn" data-timeframe="H4" onclick="setTimeframe('H4')">4时</button>
                            <button type="button" class="btn btn-outline-dark btn-sm timeframe-btn" data-timeframe="D1" onclick="setTimeframe('D1')">日线</button>
                        </div>

                        <!-- 图表控制按钮 -->
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-dark btn-sm chart-control-btn" onclick="toggleFullscreen()" title="全屏">
                                <i class="fas fa-expand"></i>
                            </button>
                            <button type="button" class="btn btn-outline-dark btn-sm chart-control-btn" onclick="saveChart()" title="保存图表">
                                <i class="fas fa-download"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body p-2">
                <style>
                    /* 图表头部样式优化 */
                    .chart-header {
                        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                        border-bottom: 2px solid #dee2e6;
                    }

                    /* 时间周期按钮样式 */
                    .timeframe-btn {
                        border-color: #6c757d !important;
                        color: #495057 !important;
                        background-color: transparent !important;
                        transition: all 0.2s ease;
                    }

                    .timeframe-btn:hover {
                        background-color: #6c757d !important;
                        color: white !important;
                        transform: translateY(-1px);
                    }

                    .timeframe-btn.active {
                        background-color: #495057 !important;
                        color: white !important;
                        border-color: #495057 !important;
                        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                    }

                    /* 图表控制按钮样式 */
                    .chart-control-btn {
                        border-color: #6c757d !important;
                        color: #495057 !important;
                        background-color: transparent !important;
                        transition: all 0.2s ease;
                    }

                    .chart-control-btn:hover {
                        background-color: #6c757d !important;
                        color: white !important;
                        transform: translateY(-1px);
                    }

                    /* 品种选择下拉框样式 */
                    .chart-symbol-select {
                        border-color: #6c757d !important;
                        color: #495057 !important;
                        background-color: white !important;
                    }

                    .chart-symbol-select:focus {
                        border-color: #495057 !important;
                        box-shadow: 0 0 0 0.2rem rgba(73, 80, 87, 0.25) !important;
                    }

                    /* 技术指标面板样式 */
                    .indicator-panel {
                        background-color: #f8f9fa;
                        border-radius: 8px;
                        border: 1px solid #e9ecef;
                    }

                    .indicator-section h6 {
                        color: #495057 !important;
                        font-weight: 600;
                    }

                    /* 技术分析摘要样式 */
                    .analysis-summary {
                        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
                        border: 1px solid #e9ecef;
                        border-radius: 6px;
                    }

                    /* 关键价位样式 */
                    .key-levels {
                        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
                        border: 1px solid #e9ecef;
                        border-radius: 6px;
                    }

                    /* 实时数据样式 */
                    .realtime-data {
                        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
                        border: 1px solid #e9ecef;
                        border-radius: 6px;
                    }
                </style>
                <div class="row">
                    <!-- 主图表区域 -->
                    <div class="col-lg-9">
                        <div id="tradingChart" style="height: 400px; width: 100%;"></div>
                    </div>

                    <!-- 技术指标控制面板 -->
                    <div class="col-lg-3">
                        <div class="h-100 indicator-panel p-3">
                            <!-- 技术指标选择 -->
                            <div class="mb-3 indicator-section">
                                <h6 class="text-dark mb-2">
                                    <i class="fas fa-chart-bar text-success"></i>
                                    技术指标
                                </h6>
                                <div class="form-check form-check-sm">
                                    <input class="form-check-input" type="checkbox" id="showMA5" checked onchange="updateChart()">
                                    <label class="form-check-label small" for="showMA5">MA5</label>
                                </div>
                                <div class="form-check form-check-sm">
                                    <input class="form-check-input" type="checkbox" id="showMA20" checked onchange="updateChart()">
                                    <label class="form-check-label small" for="showMA20">MA20</label>
                                </div>
                                <div class="form-check form-check-sm">
                                    <input class="form-check-input" type="checkbox" id="showMA50" onchange="updateChart()">
                                    <label class="form-check-label small" for="showMA50">MA50</label>
                                </div>
                                <div class="form-check form-check-sm">
                                    <input class="form-check-input" type="checkbox" id="showBollinger" onchange="updateChart()">
                                    <label class="form-check-label small" for="showBollinger">布林带</label>
                                </div>
                                <div class="form-check form-check-sm">
                                    <input class="form-check-input" type="checkbox" id="showVolume" onchange="updateChart()">
                                    <label class="form-check-label small" for="showVolume">成交量</label>
                                </div>
                            </div>

                            <!-- 振荡器指标 -->
                            <div class="mb-3 indicator-section">
                                <h6 class="text-dark mb-2">
                                    <i class="fas fa-wave-square text-info"></i>
                                    振荡器
                                </h6>
                                <div class="form-check form-check-sm">
                                    <input class="form-check-input" type="checkbox" id="showRSI" onchange="updateChart()">
                                    <label class="form-check-label small" for="showRSI">RSI</label>
                                </div>
                                <div class="form-check form-check-sm">
                                    <input class="form-check-input" type="checkbox" id="showMACD" onchange="updateChart()">
                                    <label class="form-check-label small" for="showMACD">MACD</label>
                                </div>
                                <div class="form-check form-check-sm">
                                    <input class="form-check-input" type="checkbox" id="showStoch" onchange="updateChart()">
                                    <label class="form-check-label small" for="showStoch">随机指标</label>
                                </div>
                            </div>

                            <!-- 技术分析摘要 -->
                            <div class="mb-3 indicator-section">
                                <h6 class="text-dark mb-2">
                                    <i class="fas fa-brain text-success"></i>
                                    技术分析摘要
                                </h6>
                                <div class="analysis-summary p-2 small">
                                    <div class="d-flex justify-content-between mb-1">
                                        <span>移动平均:</span>
                                        <span class="badge bg-success" id="maTrend">买入</span>
                                    </div>
                                    <div class="d-flex justify-content-between mb-1">
                                        <span>技术指标:</span>
                                        <span class="badge bg-warning" id="indicatorTrend">中性</span>
                                    </div>
                                    <div class="d-flex justify-content-between mb-1">
                                        <span>总体评价:</span>
                                        <span class="badge bg-success" id="overallTrend">买入</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 关键价位 -->
                            <div class="mb-3 indicator-section">
                                <h6 class="text-dark mb-2">
                                    <i class="fas fa-crosshairs text-warning"></i>
                                    关键价位
                                </h6>
                                <div class="key-levels p-2 small">
                                    <div class="d-flex justify-content-between mb-1">
                                        <span>阻力位:</span>
                                        <span class="text-danger fw-bold" id="resistanceLevel">3350.25</span>
                                    </div>
                                    <div class="d-flex justify-content-between mb-1">
                                        <span>当前价:</span>
                                        <span class="fw-bold" id="currentPrice">3305.55</span>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <span>支撑位:</span>
                                        <span class="text-success fw-bold" id="supportLevel">3260.80</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 实时数据 -->
                            <div class="mb-3 indicator-section">
                                <h6 class="text-dark mb-2">
                                    <i class="fas fa-tachometer-alt text-secondary"></i>
                                    实时数据
                                </h6>
                                <div class="realtime-data p-2 small">
                                    <div class="d-flex justify-content-between mb-1">
                                        <span>涨跌:</span>
                                        <span class="text-success" id="priceChange">+12.45</span>
                                    </div>
                                    <div class="d-flex justify-content-between mb-1">
                                        <span>涨跌幅:</span>
                                        <span class="text-success" id="priceChangePercent">+0.38%</span>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <span>更新时间:</span>
                                        <span id="lastUpdate">--:--:--</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 盈亏统计 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie"></i>
                    盈亏统计
                </h5>
            </div>
            <div class="card-body">
                <!-- 今日盈亏统计 -->
                <div class="row text-center mb-4">
                    <div class="col-12">
                        <h6 class="text-primary mb-3">
                            <i class="fas fa-calendar-day"></i>
                            今日盈亏统计
                        </h6>
                    </div>
                    <div class="col-md-3">
                        <div class="border-end">
                            <h6 class="text-muted mb-1">今日盈亏</h6>
                            <h5 class="mb-0" id="todayPnL">
                                <span class="profit-positive">$0.00</span>
                            </h5>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="border-end">
                            <h6 class="text-muted mb-1">已平仓盈亏</h6>
                            <h5 class="mb-0" id="closedPnL">
                                <span class="profit-positive">$0.00</span>
                            </h5>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="border-end">
                            <h6 class="text-muted mb-1">持仓盈亏</h6>
                            <h5 class="mb-0" id="openPnL">
                                <span class="profit-positive">$0.00</span>
                            </h5>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <h6 class="text-muted mb-1">今日交易</h6>
                        <h5 class="mb-0" id="todayTrades">
                            <span class="text-info">0 笔</span>
                        </h5>
                    </div>
                </div>

                <!-- 本轮次AI交易盈亏统计 -->
                <div class="row text-center">
                    <div class="col-12">
                        <h6 class="text-success mb-3">
                            <i class="fas fa-robot"></i>
                            本轮次AI交易盈亏 <small class="text-muted">(仅统计当天)</small>
                        </h6>
                    </div>
                    <div class="col-md-3">
                        <div class="border-end">
                            <h6 class="text-muted mb-1">轮次盈亏</h6>
                            <h5 class="mb-0" id="roundPnL">
                                <span class="profit-positive">$0.00</span>
                            </h5>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="border-end">
                            <h6 class="text-muted mb-1">轮次交易</h6>
                            <h5 class="mb-0" id="roundTrades">
                                <span class="text-info">0 笔</span>
                            </h5>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="border-end">
                            <h6 class="text-muted mb-1">轮次状态</h6>
                            <h5 class="mb-0" id="roundStatus">
                                <span class="badge bg-secondary">未激活</span>
                            </h5>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <h6 class="text-muted mb-1">开始时间</h6>
                        <h5 class="mb-0" id="roundStartTime">
                            <small class="text-muted">--:--</small>
                        </h5>
                    </div>
                </div>

                <div class="mt-3">
                    <small class="text-muted">
                        <i class="fas fa-info-circle"></i>
                        数据每30秒自动更新。本轮次统计从AI交易开始到结束后30分钟内无新交易为止。
                    </small>
                </div>
            </div>
        </div>

        <!-- 当前持仓 -->
        <div class="card mb-4">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-coins"></i>
                        当前持仓
                    </h5>
                    <button class="btn btn-danger btn-sm" onclick="showCloseAllPositionsModal()" id="closeAllBtn" style="display: none;">
                        <i class="fas fa-times-circle"></i>
                        一键平仓
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="positionsTable">
                        <thead>
                            <tr>
                                <th>账户</th>
                                <th>货币对</th>
                                <th>方向</th>
                                <th>数量</th>
                                <th>开仓价</th>
                                <th>当前价</th>
                                <th>浮动盈亏</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="8" class="text-center text-muted">暂无持仓</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 交易历史 -->
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-history"></i>
                        交易历史 <small class="text-muted">(仅显示当天数据)</small>
                    </h5>
                    <button class="btn btn-outline-primary btn-sm" data-bs-toggle="collapse" data-bs-target="#tradeQueryPanel">
                        <i class="fas fa-search"></i>
                        交易查询
                    </button>
                </div>

                <!-- 交易查询面板 -->
                <div class="collapse mt-3" id="tradeQueryPanel">
                    <div class="card card-body bg-light">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label small">开始时间</label>
                                <input type="date" class="form-control form-control-sm" id="queryStartDate">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label small">结束时间</label>
                                <input type="date" class="form-control form-control-sm" id="queryEndDate">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label small">货币对</label>
                                <select class="form-select form-select-sm" id="querySymbol">
                                    <option value="">全部货币对</option>
                                    <option value="EURUSD">EUR/USD</option>
                                    <option value="GBPUSD">GBP/USD</option>
                                    <option value="USDJPY">USD/JPY</option>
                                    <option value="USDCHF">USD/CHF</option>
                                    <option value="AUDUSD">AUD/USD</option>
                                    <option value="USDCAD">USD/CAD</option>
                                    <option value="NZDUSD">NZD/USD</option>
                                    <option value="XAUUSD">XAU/USD (黄金)</option>
                                    <option value="XAGUSD">XAG/USD (白银)</option>
                                    <option value="BTCUSD">BTC/USD</option>
                                    <option value="ETHUSD">ETH/USD</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label small">交易方向</label>
                                <select class="form-select form-select-sm" id="queryDirection">
                                    <option value="">全部方向</option>
                                    <option value="buy">买入</option>
                                    <option value="sell">卖出</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label small">盈亏状态</label>
                                <select class="form-select form-select-sm" id="queryProfitStatus">
                                    <option value="">全部状态</option>
                                    <option value="profit">盈利</option>
                                    <option value="loss">亏损</option>
                                    <option value="breakeven">持平</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label small">最小金额</label>
                                <input type="number" class="form-control form-control-sm" id="queryMinAmount" placeholder="最小交易金额" step="0.01">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label small">最大金额</label>
                                <input type="number" class="form-control form-control-sm" id="queryMaxAmount" placeholder="最大交易金额" step="0.01">
                            </div>
                            <div class="col-md-3 d-flex align-items-end">
                                <div class="btn-group w-100">
                                    <button class="btn btn-primary btn-sm" onclick="executeTradeQuery()">
                                        <i class="fas fa-search"></i>
                                        查询
                                    </button>
                                    <button class="btn btn-outline-secondary btn-sm" onclick="resetTradeQuery()">
                                        <i class="fas fa-undo"></i>
                                        重置
                                    </button>
                                    <button class="btn btn-outline-success btn-sm" onclick="exportTradeData()">
                                        <i class="fas fa-download"></i>
                                        导出
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 查询统计信息 -->
                        <div class="row mt-3" id="queryStats" style="display: none;">
                            <div class="col-12">
                                <div class="alert alert-info mb-0">
                                    <div class="row text-center">
                                        <div class="col-md-2">
                                            <strong id="totalTrades">0</strong>
                                            <small class="d-block text-muted">总交易数</small>
                                        </div>
                                        <div class="col-md-2">
                                            <strong id="totalProfit" class="text-success">$0.00</strong>
                                            <small class="d-block text-muted">总盈亏</small>
                                        </div>
                                        <div class="col-md-2">
                                            <strong id="winRate">0%</strong>
                                            <small class="d-block text-muted">胜率</small>
                                        </div>
                                        <div class="col-md-2">
                                            <strong id="avgProfit">$0.00</strong>
                                            <small class="d-block text-muted">平均盈亏</small>
                                        </div>
                                        <div class="col-md-2">
                                            <strong id="maxProfit" class="text-success">$0.00</strong>
                                            <small class="d-block text-muted">最大盈利</small>
                                        </div>
                                        <div class="col-md-2">
                                            <strong id="maxLoss" class="text-danger">$0.00</strong>
                                            <small class="d-block text-muted">最大亏损</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="historyTable">
                        <thead>
                            <tr>
                                <th>账户</th>
                                <th>时间</th>
                                <th>货币对</th>
                                <th>方向</th>
                                <th>数量</th>
                                <th>开仓价</th>
                                <th>平仓价</th>
                                <th>止损</th>
                                <th>止盈</th>
                                <th>盈亏</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="11" class="text-center text-muted">暂无交易记录</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 添加账户模态框 -->
<div class="modal fade" id="addAccountModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">添加交易账户</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addAccountForm">
                    <div class="mb-3">
                        <label class="form-label">账户名称</label>
                        <input type="text" class="form-control" name="account_name" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">交易所</label>
                        <select class="form-select" name="broker" required>
                            <option value="">选择交易所</option>
                            <option value="binance">Binance</option>
                            <option value="okx">OKX</option>
                            <option value="huobi">Huobi</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">API Key</label>
                        <input type="text" class="form-control" name="api_key" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">API Secret</label>
                        <input type="password" class="form-control" name="api_secret" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">初始余额</label>
                        <input type="number" class="form-control" name="balance" step="0.01" value="0">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="addAccount()">添加</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- AI交易全局管理器 -->
<script src="{{ url_for('static', filename='js/ai_trading_manager.js') }}"></script>
<script>
let currentAccountId = null;
let currentAccountType = null;
let mt5Connected = false;

// 订单类型切换
document.getElementById('orderType').addEventListener('change', function() {
    const priceGroup = document.getElementById('priceGroup');
    if (this.value === 'limit') {
        priceGroup.style.display = 'block';
        document.getElementById('orderPrice').required = true;
    } else {
        priceGroup.style.display = 'none';
        document.getElementById('orderPrice').required = false;
    }
});

// MT5自动连接功能
function autoConnectMT5() {
    console.log('🔄 开始自动连接MT5...');

    const statusText = document.getElementById('mt5StatusText');
    const connectBtn = document.querySelector('#mt5ConnectionStatus button');

    // 更新UI状态
    statusText.textContent = '正在连接MT5客户端...';
    connectBtn.disabled = true;
    connectBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 连接中...';

    fetch('/api/mt5/auto-connect', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('✅ MT5连接成功:', data);
            mt5Connected = true;

            statusText.innerHTML = `
                <i class="fas fa-check-circle text-success"></i>
                MT5连接成功 - 账户: ${data.account_info.login} | 余额: $${data.account_info.balance.toFixed(2)}
            `;

            connectBtn.innerHTML = '<i class="fas fa-check"></i> 已连接';
            connectBtn.className = 'btn btn-sm btn-success';

            // 自动添加MT5账户到选择列表（Demo账户也作为真实账户处理）
            addMT5AccountToSelect(data.account_info);

            // 自动选择MT5账户（优先选择有余额的账户）
            const accountSelect = document.getElementById('accountSelect');
            const mt5AccountValue = `mt5_${data.account_info.login}`;

            // Demo账户也可以用于真实交易，优先选择有余额的账户
            if (data.account_info.balance > 0 || accountSelect.value === '') {
                accountSelect.value = mt5AccountValue;
                currentAccountId = mt5AccountValue;
                currentAccountType = 'mt5';
                console.log('✅ 自动选择MT5账户:', data.account_info.login, `(${data.account_info.account_type}) 余额: $${data.account_info.balance}`);
            } else {
                console.log('💡 MT5账户余额为0，不自动选择:', data.account_info.login);
            }

            // 加载账户信息
            loadMT5AccountInfo(data.account_info);
            loadMT5Positions();
            loadMT5TradeHistory();

            document.getElementById('accountInfo').style.display = 'block';

        } else {
            console.error('❌ MT5连接失败:', data.error);
            statusText.innerHTML = `
                <i class="fas fa-exclamation-triangle text-danger"></i>
                连接失败: ${data.error}
            `;
            connectBtn.innerHTML = '<i class="fas fa-retry"></i> 重试连接';
            connectBtn.disabled = false;
        }
    })
    .catch(error => {
        console.error('❌ MT5连接请求失败:', error);
        statusText.innerHTML = `
            <i class="fas fa-exclamation-triangle text-danger"></i>
            连接失败: 网络错误或MT5客户端未运行
        `;
        connectBtn.innerHTML = '<i class="fas fa-retry"></i> 重试连接';
        connectBtn.disabled = false;
    });
}

// 添加MT5账户到选择列表 - 根据系统设置处理Demo账户
function addMT5AccountToSelect(accountInfo) {
    // 检查系统设置是否允许Demo账户作为真实账户处理
    fetch('/api/system-settings/mt5')
    .then(response => response.json())
    .then(data => {
        const mt5DemoAsReal = data.success ? data.settings.mt5_demo_as_real : true;

        // 如果设置不允许Demo账户作为真实账户，则跳过Demo账户
        if (accountInfo.account_type === 'demo' && !mt5DemoAsReal) {
            console.log('🎮 跳过Demo账户（系统设置不允许）:', accountInfo.login);
            return;
        }

        const accountSelect = document.getElementById('accountSelect');
        const existingOption = accountSelect.querySelector(`option[value="mt5_${accountInfo.login}"]`);

        if (!existingOption) {
            const option = document.createElement('option');
            option.value = `mt5_${accountInfo.login}`;

            // 显示账户类型标识
            const accountTypeLabel = accountInfo.account_type === 'demo' ? '(Demo)' : '(Real)';
            option.textContent = `MT5-${accountInfo.login} ${accountTypeLabel} - $${accountInfo.balance.toFixed(2)}`;

            accountSelect.appendChild(option);
            console.log('✅ 已添加MT5账户到选择列表:', accountInfo.login, accountInfo.account_type);
        }
    })
    .catch(error => {
        console.error('获取MT5设置失败，使用默认设置:', error);
        // 默认允许Demo账户
        const accountSelect = document.getElementById('accountSelect');
        const existingOption = accountSelect.querySelector(`option[value="mt5_${accountInfo.login}"]`);

        if (!existingOption) {
            const option = document.createElement('option');
            option.value = `mt5_${accountInfo.login}`;

            const accountTypeLabel = accountInfo.account_type === 'demo' ? '(Demo)' : '(Real)';
            option.textContent = `MT5-${accountInfo.login} ${accountTypeLabel} - $${accountInfo.balance.toFixed(2)}`;

            accountSelect.appendChild(option);
            console.log('✅ 已添加MT5账户到选择列表（默认设置）:', accountInfo.login, accountInfo.account_type);
        }
    });
}

// 刷新MT5账户 - Demo账户也作为真实账户处理
function refreshMT5Accounts() {
    console.log('🔄 刷新MT5账户（包含Demo账户）...');

    // 清除现有的MT5选项
    const accountSelect = document.getElementById('accountSelect');
    const mt5Options = accountSelect.querySelectorAll('option[value^="mt5_"]');
    mt5Options.forEach(option => option.remove());

    // 重新连接并获取所有MT5账户（Demo也可用于真实交易）
    autoConnectMT5();
}

// 加载MT5账户信息
function loadMT5AccountInfo(accountInfo) {
    document.getElementById('accountBalance').textContent = '$' + accountInfo.balance.toFixed(2);
    document.getElementById('accountEquity').textContent = '$' + accountInfo.equity.toFixed(2);
}

// 账户选择
document.getElementById('accountSelect').addEventListener('change', function() {
    currentAccountId = this.value;

    if (currentAccountId) {
        // 判断账户类型
        if (currentAccountId.startsWith('mt5_')) {
            currentAccountType = 'mt5';
            loadMT5Positions();
            loadMT5TradeHistory();
        } else {
            currentAccountType = 'api';
            loadAccountInfo(currentAccountId);
            loadPositions(currentAccountId);
            loadTradeHistory(currentAccountId);
        }
        document.getElementById('accountInfo').style.display = 'block';
    } else {
        currentAccountType = null;
        document.getElementById('accountInfo').style.display = 'none';
    }
});

// 加载账户信息
function loadAccountInfo(accountId) {
    fetch(`/api/trading-accounts/${accountId}/info`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('accountBalance').textContent = '$' + data.balance.toFixed(2);
                document.getElementById('accountEquity').textContent = '$' + data.equity.toFixed(2);
            }
        })
        .catch(error => console.error('Error:', error));
}

// 加载持仓 (API账户)
function loadPositions(accountId) {
    fetch(`/api/trading-accounts/${accountId}/positions`)
        .then(response => response.json())
        .then(data => {
            const tbody = document.querySelector('#positionsTable tbody');
            tbody.innerHTML = '';

            // 控制一键平仓按钮显示
            const closeAllBtn = document.getElementById('closeAllBtn');

            if (data.length === 0) {
                tbody.innerHTML = '<tr><td colspan="8" class="text-center text-muted">暂无持仓</td></tr>';

                // 检查是否有MT5持仓，如果没有则隐藏一键平仓按钮
                const mt5Positions = document.querySelectorAll('#positionsTable tbody tr[data-source="mt5"]');
                if (mt5Positions.length === 0) {
                    closeAllBtn.style.display = 'none';
                }
            } else {
                // 显示一键平仓按钮
                closeAllBtn.style.display = 'block';

                data.forEach(position => {
                    // 获取账户显示名称
                    const accountDisplay = getAccountDisplayName(accountId, 'api');

                    const row = `
                        <tr data-source="api" data-trade-id="${position.trade_id}">
                            <td><span class="badge bg-secondary">${accountDisplay}</span></td>
                            <td>${position.symbol}</td>
                            <td><span class="badge bg-${position.side === 'buy' ? 'success' : 'danger'}">${position.side === 'buy' ? '多头' : '空头'}</span></td>
                            <td>${position.volume}</td>
                            <td>$${position.open_price.toFixed(4)}</td>
                            <td>$${position.current_price ? position.current_price.toFixed(4) : 'N/A'}</td>
                            <td class="${position.unrealized_pnl >= 0 ? 'profit-positive' : 'profit-negative'}">
                                $${position.unrealized_pnl.toFixed(2)}
                            </td>
                            <td>
                                <button class="btn btn-sm btn-outline-danger" onclick="closePosition(${position.trade_id})">
                                    平仓
                                </button>
                            </td>
                        </tr>
                    `;
                    tbody.innerHTML += row;
                });
            }
        })
        .catch(error => {
            console.error('Error:', error);
            // 错误处理
            const tbody = document.querySelector('#positionsTable tbody');
            if (tbody.innerHTML === '') {
                tbody.innerHTML = '<tr><td colspan="8" class="text-center text-danger">加载持仓失败</td></tr>';
            }
        });
}

// 加载MT5持仓
function loadMT5Positions() {
    fetch('/api/mt5/positions')
        .then(response => response.json())
        .then(data => {
            const tbody = document.querySelector('#positionsTable tbody');

            // 控制一键平仓按钮显示
            const closeAllBtn = document.getElementById('closeAllBtn');

            // 保留现有的API持仓行
            const existingApiRows = tbody.querySelectorAll('tr[data-source="api"]');

            // 清除MT5持仓行
            const existingMt5Rows = tbody.querySelectorAll('tr[data-source="mt5"]');
            existingMt5Rows.forEach(row => row.remove());

            // 如果没有任何持仓，显示空状态
            if ((!data.success || data.positions.length === 0) && existingApiRows.length === 0) {
                tbody.innerHTML = '<tr><td colspan="8" class="text-center text-muted">暂无持仓</td></tr>';
                closeAllBtn.style.display = 'none';
            } else if (data.success && data.positions.length > 0) {
                // 显示一键平仓按钮
                closeAllBtn.style.display = 'block';

                // 如果之前是空状态，清除空状态行
                const emptyRow = tbody.querySelector('tr td[colspan="8"]');
                if (emptyRow) {
                    emptyRow.parentElement.remove();
                }

                data.positions.forEach(position => {
                    // 获取账户显示名称
                    const accountDisplay = getAccountDisplayName(currentAccountId, 'mt5');

                    const row = document.createElement('tr');
                    row.setAttribute('data-source', 'mt5');
                    row.setAttribute('data-ticket', position.ticket);
                    row.innerHTML = `
                        <td><span class="badge bg-primary">${accountDisplay}</span></td>
                        <td>${position.symbol}</td>
                        <td><span class="badge bg-${position.type === 0 ? 'success' : 'danger'}">${position.type === 0 ? '多头' : '空头'}</span></td>
                        <td>${position.volume}</td>
                        <td>$${position.price_open.toFixed(5)}</td>
                        <td>$${position.price_current ? position.price_current.toFixed(5) : 'N/A'}</td>
                        <td class="${position.profit >= 0 ? 'profit-positive' : 'profit-negative'}">
                            $${position.profit.toFixed(2)}
                        </td>
                        <td>
                            <button class="btn btn-sm btn-outline-danger" onclick="closeMT5Position(${position.ticket})">
                                平仓
                            </button>
                        </td>
                    `;
                    tbody.appendChild(row);
                });
            } else if (existingApiRows.length > 0) {
                // 有API持仓但没有MT5持仓，保持一键平仓按钮显示
                closeAllBtn.style.display = 'block';
            }
        })
        .catch(error => {
            console.error('Error loading MT5 positions:', error);
        });
}

// 获取账户显示名称
function getAccountDisplayName(accountId, accountType) {
    if (accountType === 'mt5') {
        const accountNumber = accountId.replace('mt5_', '');
        return `MT5-${accountNumber}`;
    } else {
        // 对于API账户，可以从选择框中获取显示名称
        const accountSelect = document.getElementById('accountSelect');
        const selectedOption = accountSelect.querySelector(`option[value="${accountId}"]`);
        return selectedOption ? selectedOption.textContent.split(' ')[0] : accountId;
    }
}

// 加载交易历史 (API账户)
function loadTradeHistory(accountId) {
    fetch(`/api/trading-accounts/${accountId}/history`)
        .then(response => response.json())
        .then(data => {
            const tbody = document.querySelector('#historyTable tbody');
            tbody.innerHTML = '';

            if (data.length === 0) {
                tbody.innerHTML = '<tr><td colspan="11" class="text-center text-muted">暂无交易记录</td></tr>';
            } else {
                data.forEach(trade => {
                    // 获取账户显示名称
                    const accountDisplay = getAccountDisplayName(accountId, 'api');
                    // 转换为中国标准时间
                    const chinaTime = convertToChinaTime(trade.open_time);

                    const row = `
                        <tr>
                            <td><span class="badge bg-secondary">${accountDisplay}</span></td>
                            <td>${chinaTime}</td>
                            <td>${trade.symbol}</td>
                            <td><span class="badge bg-${trade.side === 'buy' ? 'success' : 'danger'}">${trade.side === 'buy' ? '买入' : '卖出'}</span></td>
                            <td>${trade.volume}</td>
                            <td>$${trade.open_price.toFixed(4)}</td>
                            <td>${trade.close_price ? '$' + trade.close_price.toFixed(4) : '-'}</td>
                            <td class="text-danger">${trade.stop_loss ? '$' + trade.stop_loss.toFixed(4) : '-'}</td>
                            <td class="text-success">${trade.take_profit ? '$' + trade.take_profit.toFixed(4) : '-'}</td>
                            <td class="${trade.profit >= 0 ? 'profit-positive' : 'profit-negative'}">
                                $${trade.profit.toFixed(2)}
                            </td>
                            <td><span class="badge bg-${trade.status === 'closed' ? 'success' : 'warning'}">${trade.status === 'closed' ? '已平仓' : '持仓中'}</span></td>
                        </tr>
                    `;
                    tbody.innerHTML += row;
                });
            }
        })
        .catch(error => console.error('Error:', error));
}

// 加载MT5交易历史（仅显示当天数据）
function loadMT5TradeHistory() {
    fetch('/api/mt5/trading-history-today')
        .then(response => response.json())
        .then(data => {
            const tbody = document.querySelector('#historyTable tbody');
            tbody.innerHTML = '';

            if (!data.success || data.trades.length === 0) {
                tbody.innerHTML = '<tr><td colspan="9" class="text-center text-muted">今日暂无交易记录</td></tr>';
            } else {
                data.trades.forEach(trade => {
                    // 获取账户显示名称
                    const accountDisplay = getAccountDisplayName(currentAccountId, 'mt5');
                    // 转换为中国标准时间
                    const chinaTime = convertToChinaTime(trade.time);

                    const row = `
                        <tr>
                            <td><span class="badge bg-primary">${accountDisplay}</span></td>
                            <td>${chinaTime}</td>
                            <td>${trade.symbol}</td>
                            <td><span class="badge bg-${trade.type === 0 ? 'success' : 'danger'}">${trade.type === 0 ? '买入' : '卖出'}</span></td>
                            <td>${trade.volume}</td>
                            <td>$${trade.price.toFixed(5)}</td>
                            <td>${trade.price_close ? '$' + trade.price_close.toFixed(5) : '-'}</td>
                            <td class="text-danger">${trade.sl ? '$' + trade.sl.toFixed(5) : '-'}</td>
                            <td class="text-success">${trade.tp ? '$' + trade.tp.toFixed(5) : '-'}</td>
                            <td class="${trade.profit >= 0 ? 'profit-positive' : 'profit-negative'}">
                                $${trade.profit.toFixed(2)}
                                ${trade.profit_corrected ? '<small class="text-info d-block">已修正</small>' : ''}
                                ${trade.profit_warning ? '<small class="text-warning d-block">异常值</small>' : ''}
                            </td>
                            <td><span class="badge bg-${getTradeStatusColor(trade)}">${getTradeStatusText(trade)}</span></td>
                        </tr>
                    `;
                    tbody.innerHTML += row;
                });
            }
        })
        .catch(error => console.error('Error loading MT5 history:', error));
}

// 转换为中国标准时间显示
function convertToChinaTime(dateString) {
    try {
        let date;

        // 处理不同的时间格式
        if (typeof dateString === 'number') {
            // Unix时间戳 (秒)
            date = new Date(dateString * 1000);
        } else if (typeof dateString === 'string') {
            // 字符串格式
            date = new Date(dateString);
        } else {
            date = new Date(dateString);
        }

        // 检查日期是否有效
        if (isNaN(date.getTime())) {
            console.warn('无效的时间数据:', dateString);
            return '无效时间';
        }

        // 转换为中国标准时间 (UTC+8)
        const utc = date.getTime() + (date.getTimezoneOffset() * 60000);
        const chinaTime = new Date(utc + (8 * 3600000));

        // 格式化为中文显示
        return chinaTime.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
        });
    } catch (error) {
        console.error('时间转换失败:', error, '原始数据:', dateString);
        return '时间转换失败'; // 回退到错误提示
    }
}

// 切换手动止盈止损设置
function toggleManualStopLoss() {
    const enabled = document.getElementById('enableManualStopLoss').checked;
    const settings = document.getElementById('manualStopLossSettings');

    if (settings) {
        if (enabled) {
            settings.style.display = 'block';
            settings.style.opacity = '1';
        } else {
            settings.style.display = 'none';
        }
    }
}

// 切换AI智能止盈止损设置
function toggleAIStopLossTakeProfit() {
    const enabled = document.getElementById('aiEnableStopLossTakeProfit').checked;
    const settings = document.getElementById('aiStopLossTakeProfitSettings');

    if (settings) {
        if (enabled) {
            settings.style.display = 'block';
            settings.style.opacity = '1';
        } else {
            settings.style.opacity = '0.5';
        }
    }
}

// 切换全程自动交易设置
function toggleFullAutoTrading() {
    const enabled = document.getElementById('fullAutoTrading').checked;
    const settings = document.getElementById('fullAutoTradingSettings');

    if (settings) {
        if (enabled) {
            settings.style.display = 'block';
            settings.style.opacity = '1';

            // 显示警告提示
            showNotification(
                '⚠️ 全程自动交易已启用\n\n' +
                'AI将全程参与交易决策，包括动态判断离场时机。\n' +
                '请确保您完全理解此功能的风险特征。',
                'warning'
            );
        } else {
            settings.style.display = 'none';
            settings.style.opacity = '0.5';

            showNotification(
                '✅ 全程自动交易已关闭\n\n' +
                '系统将仅使用传统的止损止盈进行风险控制。',
                'info'
            );
        }
    }

    console.log(`🔄 全程自动交易: ${enabled ? '启用' : '关闭'}`);
}

// 获取手动止盈止损设置
function getManualStopLossTakeProfitSettings() {
    const enabled = document.getElementById('enableManualStopLoss')?.checked || false;

    if (!enabled) {
        return {
            enabled: false,
            stopLoss: null,
            takeProfit: null,
            trailingStop: false
        };
    }

    return {
        enabled: true,
        stopLoss: parseFloat(document.getElementById('stopLoss')?.value) || null,
        takeProfit: parseFloat(document.getElementById('takeProfit')?.value) || null,
        trailingStop: document.getElementById('trailingStop')?.checked || false
    };
}

// 获取AI智能止盈止损设置
function getAIStopLossTakeProfitSettings() {
    const enabled = document.getElementById('aiEnableStopLossTakeProfit')?.checked || false;

    if (!enabled) {
        return {
            enabled: false,
            stopLoss: null,
            takeProfit: null
        };
    }

    const stopLossPercent = parseFloat(document.getElementById('aiStopLossPercent')?.value || 1.0);
    const takeProfitPercent = parseFloat(document.getElementById('aiTakeProfitPercent')?.value || 2.0);
    const autoAdjust = document.getElementById('aiAutoAdjustStopLoss')?.checked || false;

    return {
        enabled: true,
        stopLossPercent,
        takeProfitPercent,
        autoAdjust
    };
}

// 获取全程自动交易设置
function getFullAutoTradingSettings() {
    const enabled = document.getElementById('fullAutoTrading')?.checked || false;

    if (!enabled) {
        return {
            enabled: false,
            dynamicExitStrategy: null,
            exitSignalThreshold: null,
            keepStopLossTakeProfit: true
        };
    }

    const dynamicExitStrategy = document.getElementById('dynamicExitStrategy')?.value || 'conservative';
    const exitSignalThreshold = parseFloat(document.getElementById('exitSignalThreshold')?.value || 0.7);
    const keepStopLossTakeProfit = document.getElementById('keepStopLossTakeProfit')?.checked || true;

    return {
        enabled: true,
        dynamicExitStrategy,
        exitSignalThreshold,
        keepStopLossTakeProfit
    };
}

// 平仓MT5持仓
function closeMT5Position(ticket) {
    if (!confirm('确定要平仓这个持仓吗？')) {
        return;
    }

    fetch('/api/mt5/close-position', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            ticket: ticket
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('平仓成功！');
            loadMT5Positions();
            loadMT5TradeHistory();
        } else {
            showNotification(`❌ 平仓失败\n\n${data.error}\n\n💡 请检查持仓状态或稍后重试`, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('平仓请求失败');
    });
}

// 提交订单
document.getElementById('orderForm').addEventListener('submit', function(e) {
    e.preventDefault();

    if (!currentAccountId) {
        alert('请先选择交易账户');
        return;
    }

    // 获取止盈止损设置
    const stopLossTakeProfitSettings = getManualStopLossTakeProfitSettings();

    const orderData = {
        account_id: currentAccountId,
        account_type: currentAccountType,
        symbol: document.getElementById('symbolSelect').value,
        order_type: document.getElementById('orderType').value,
        side: document.querySelector('input[name="orderSide"]:checked').value,
        amount: parseFloat(document.getElementById('orderAmount').value),
        price: document.getElementById('orderPrice').value ? parseFloat(document.getElementById('orderPrice').value) : null,
        stop_loss: stopLossTakeProfitSettings.stopLoss,
        take_profit: stopLossTakeProfitSettings.takeProfit,
        trailing_stop: stopLossTakeProfitSettings.trailingStop,
        stop_loss_take_profit_enabled: stopLossTakeProfitSettings.enabled
    };

    fetch('/api/place-order', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(orderData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('订单提交成功！');
            this.reset();

            // 根据账户类型刷新数据
            if (currentAccountType === 'mt5') {
                loadMT5Positions();
                loadMT5TradeHistory();
            } else {
                loadPositions(currentAccountId);
                loadTradeHistory(currentAccountId);
                loadAccountInfo(currentAccountId);
            }
        } else {
            // 使用更友好的通知替代alert
            let errorMsg = data.error || '未知错误';
            if (errorMsg.includes('资金') || errorMsg.includes('保证金')) {
                showNotification(`❌ 订单提交失败\n\n${errorMsg}\n\n💡 建议检查账户余额或减少交易手数`, 'error');
            } else {
                showNotification(`❌ 订单提交失败\n\n${errorMsg}`, 'error');
            }
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('订单提交失败');
    });
});

// 平仓
function closePosition(tradeId) {
    if (confirm('确定要平仓吗？')) {
        fetch(`/api/close-position/${tradeId}`, {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('平仓成功！盈亏: $' + data.profit.toFixed(2));
                loadPositions(currentAccountId);
                loadTradeHistory(currentAccountId);
                loadAccountInfo(currentAccountId);
            } else {
                showNotification(`❌ 平仓失败\n\n${data.error}\n\n💡 请检查持仓状态或稍后重试`, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('平仓失败');
        });
    }
}

// 添加账户
function addAccount() {
    const form = document.getElementById('addAccountForm');
    const formData = new FormData(form);
    const accountData = {
        account_name: formData.get('account_name'),
        account_type: 'real',
        broker: formData.get('broker'),
        api_key: formData.get('api_key'),
        api_secret: formData.get('api_secret'),
        balance: parseFloat(formData.get('balance'))
    };

    fetch('/api/trading-accounts', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(accountData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('账户添加成功！');
            location.reload();
        } else {
            alert('账户添加失败: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('账户添加失败');
    });
}

// AI交易相关变量
let aiTradingActive = false;
let aiTradingInterval = null;
let aiStartTime = null;
let todayTradeCount = 0;

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    resetDailyTradeCount();
    initializeStopLossTakeProfit();
    initializeAIStopLossTakeProfit();
    initializeTradeCountLimit();
    validateLotSizeRange(); // 验证手数区间设置
    checkMonitoringStatus(); // 检查止盈止损监控状态

    // 初始化AI交易事件监听
    initializeAITradingEventListeners();

    // 初始化AI智能交易为默认启用状态
    initializeAITradingDefault();

    // 先加载默认设置
    loadDefaultSettings();

    // 然后加载AI策略（会应用默认选择）
    setTimeout(() => {
        loadAIStrategies();
    }, 500);

    // 添加设置事件监听器
    addSettingsEventListeners();

    // 尝试自动连接MT5
    setTimeout(() => {
        autoConnectMT5();
    }, 1000);

    // 延迟初始化AI交易管理器
    setTimeout(() => {
        if (window.AITradingManager) {
            console.log('🔄 延迟初始化AI交易管理器...');
            AITradingManager.initialize();
        } else {
            console.warn('⚠️ AI交易管理器未加载');
        }
    }, 2000);

    // 初始化盈亏统计
    setTimeout(() => {
        updateTodayPnLStats();
        updateRoundPnLStats();
    }, 1500);

    // 确保图表正确初始化（延迟执行以确保DOM完全加载）
    setTimeout(() => {
        console.log('🔄 延迟初始化图表...');
        if (typeof initTradingChart === 'function') {
            initTradingChart();
        }
    }, 2500);
});

// 初始化AI交易事件监听器
function initializeAITradingEventListeners() {
    console.log('🔧 初始化真实交易AI交易事件监听器...');

    // 监听AI交易状态更新
    window.addEventListener('aiTradingUpdate', function(event) {
        const { event: eventType, data } = event.detail;
        console.log('🔔 收到AI交易事件:', eventType, data);

        switch (eventType) {
            case 'tradingStarted':
                updateUIForTradingStarted(data.state);
                // 使用手数区间替代交易金额
                const lotSizeRange = data.state.minLotSize && data.state.maxLotSize ?
                    `${data.state.minLotSize}-${data.state.maxLotSize}手` : 'N/A';
                showNotification(`✅ AI交易已启动\n\n策略: ${data.state.strategyId || 'N/A'}\n风险等级: ${data.state.riskLevel || 'N/A'}\n手数区间: ${lotSizeRange}`, 'success');
                break;
            case 'tradingStopped':
                updateUIForTradingStopped();
                showNotification('🛑 AI交易已停止', 'warning');
                break;
            case 'tradingTimeStopped':
                updateUIForTradingStopped();
                showNotification(`⏰ AI交易已停止\n\n超出交易时间段: ${data.timeSlot}`, 'warning');
                break;
            case 'tradingTimeError':
                showNotification(`❌ 当前不在交易时间段内\n\n设置的交易时间: ${data.timeSlot}\n请在交易时间内启动AI交易`, 'error');
                break;
            case 'runningTimeUpdate':
                updateRunningTime(data.state);
                break;
            case 'tradeCountLimitReached':
                updateUIForPositionManagementMode(data);
                showNotification(`⚠️ 已达到交易次数限制\n\n已执行: ${data.currentCount} 次交易\n限制: ${data.limit} 次\n\n现在进入持仓管理模式`, 'warning');
                break;
            case 'allPositionsClosed':
                updateUIForTradingStopped();
                showNotification(`✅ 所有持仓已处理完毕\n\nAI交易已完全停止\n如需继续交易，请重新启动AI交易`, 'success');
                break;
            case 'tradingRestored':
                console.log('🔄 AI交易状态已恢复:', data.state);
                updateUIForTradingStarted(data.state);
                showNotification(`🔄 AI交易状态已恢复\n\n策略: ${data.state.strategyId}\n交易货币对: ${data.state.tradingSymbol}\n已执行: ${data.state.currentRunTradeCount || 0} 次交易`, 'info');
                break;
            case 'tradingTimeExpired':
                updateUIForTradingStopped();
                showNotification(`⏰ AI交易已停止\n\n超出交易时间段: ${data.state.tradingTimeSlot}\n请在交易时间内重新启动`, 'warning');
                break;
            case 'tradeExecuted':
                console.log('🎯 AI交易执行成功，刷新数据:', data);
                // 立即刷新持仓和历史记录
                setTimeout(() => {
                    refreshRealTradingData();
                }, 2000);
                // 显示交易执行通知
                if (data.orderData) {
                    showNotification(`💰 AI交易执行成功\n\n货币对: ${data.orderData.symbol}\n方向: ${data.orderData.side}\n数量: ${data.orderData.amount}\n价格: ${data.orderData.price}`, 'success');
                }
                break;
            case 'tradeError':
                console.error('❌ AI交易执行错误:', data);
                // 根据错误类型显示不同的提示
                let errorMessage = data.error || '未知错误';
                let suggestion = '';

                if (errorMessage.includes('资金') || errorMessage.includes('保证金') || errorMessage.includes('余额')) {
                    suggestion = '\n💡 建议：\n- 检查账户余额是否充足\n- 减少交易手数\n- 平掉部分持仓释放保证金';
                } else if (errorMessage.includes('连接') || errorMessage.includes('网络')) {
                    suggestion = '\n💡 建议：\n- 检查MT5客户端是否正常运行\n- 检查网络连接\n- 重新连接MT5';
                } else if (errorMessage.includes('权限') || errorMessage.includes('禁用')) {
                    suggestion = '\n💡 建议：\n- 检查账户交易权限\n- 启用MT5自动交易\n- 联系经纪商确认账户状态';
                } else {
                    suggestion = '\n💡 建议：\n- 检查MT5连接状态\n- 确认账户信息正确\n- 稍后重试';
                }

                showNotification(`❌ AI交易执行失败\n\n${errorMessage}${suggestion}`, 'error');
                break;
            case 'tradeNetworkError':
                console.error('❌ AI交易网络错误:', data);
                showNotification(`❌ AI交易网络错误\n\n错误信息: ${data.error}\n\n请检查网络连接和服务器状态`, 'error');
                break;
        }
    });

    console.log('✅ 真实交易AI交易事件监听器初始化完成');
}

// 初始化AI智能交易为默认启用状态
function initializeAITradingDefault() {
    console.log('🤖 初始化AI智能交易默认状态...');

    // 确保AI智能交易开关默认开启
    const aiTradingSwitch = document.getElementById('aiTradingSwitch');
    if (aiTradingSwitch) {
        aiTradingSwitch.checked = true;

        // 触发切换事件以显示AI交易界面
        toggleAITrading();

        console.log('✅ AI智能交易已设置为默认启用');
    }

    console.log('✅ AI智能交易默认状态初始化完成');
}

// 初始化止盈止损功能
function initializeStopLossTakeProfit() {
    console.log('🔧 初始化手动止盈止损功能...');

    // 添加事件监听器
    const enableCheckbox = document.getElementById('enableManualStopLoss');
    if (enableCheckbox) {
        enableCheckbox.addEventListener('change', toggleManualStopLoss);
    }

    console.log('✅ 手动止盈止损功能初始化完成');
}

// 初始化AI智能止盈止损功能
function initializeAIStopLossTakeProfit() {
    console.log('🤖 初始化AI智能止盈止损功能...');

    // 添加事件监听器
    const inputs = ['aiStopLossPercent', 'aiTakeProfitPercent'];
    inputs.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener('change', saveAIStopLossTakeProfitSettings);
        }
    });

    const checkboxes = ['aiEnableStopLossTakeProfit', 'aiAutoAdjustStopLoss'];
    checkboxes.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener('change', saveAIStopLossTakeProfitSettings);
        }
    });

    console.log('✅ AI智能止盈止损功能初始化完成');
}

// 初始化交易次数限制功能
function initializeTradeCountLimit() {
    console.log('📊 初始化交易次数限制功能...');

    // 添加事件监听器
    const maxTradeCountSelect = document.getElementById('maxTradeCount');
    if (maxTradeCountSelect) {
        maxTradeCountSelect.addEventListener('change', function() {
            const selectedValue = this.value;
            console.log('交易次数限制已更改为:', selectedValue);
            updateTradeCountLimitDisplay();
        });
    }

    // 初始化显示
    updateTradeCountLimitDisplay();

    console.log('✅ 交易次数限制功能初始化完成');
}

// 更新交易次数限制显示
function updateTradeCountLimitDisplay() {
    const maxTradeCountSelect = document.getElementById('maxTradeCount');
    const aiTradeLimitElement = document.getElementById('aiTradeLimit');

    if (maxTradeCountSelect && aiTradeLimitElement) {
        const selectedValue = maxTradeCountSelect.value;

        if (selectedValue === 'unlimited') {
            aiTradeLimitElement.textContent = '不限制';
            aiTradeLimitElement.className = 'fw-bold text-success';
        } else {
            aiTradeLimitElement.textContent = selectedValue;
            aiTradeLimitElement.className = 'fw-bold text-info';
        }
    }
}

// 保存AI止盈止损设置
function saveAIStopLossTakeProfitSettings() {
    const settings = getAIStopLossTakeProfitSettings();
    localStorage.setItem('realTradingAIStopLossSettings', JSON.stringify(settings));
    console.log('已保存AI止盈止损设置:', settings);
}

// 更新UI为交易开始状态
function updateUIForTradingStarted(state) {
    console.log('🎯 更新UI为AI交易开始状态:', state);

    // 更新按钮状态
    const startBtn = document.getElementById('startAITrading');
    const stopBtn = document.getElementById('stopAITrading');

    if (startBtn) {
        startBtn.style.display = 'none';
    }

    if (stopBtn) {
        stopBtn.style.display = 'block';
        stopBtn.innerHTML = '<i class="fas fa-stop"></i> 停止AI交易';
        stopBtn.className = 'btn btn-danger w-100';
    }

    // 显示AI交易状态
    const statusDiv = document.getElementById('aiTradingStatus');
    if (statusDiv) {
        statusDiv.style.display = 'block';

        // 更新状态信息
        const strategyElement = document.getElementById('currentAIStrategy');
        if (strategyElement) {
            strategyElement.textContent = state.strategyId || '-';
        }

        const symbolElement = document.getElementById('currentTradingSymbol');
        if (symbolElement) {
            symbolElement.textContent = state.tradingSymbol || '-';
        }

        // 更新交易次数统计
        updateAITradeCount(state.currentRunTradeCount || 0);
        updateTradeCountLimit(state.maxTradeCount);
    }

    // 显示入场信息区域
    const entryInfoSection = document.getElementById('entryInfoSection');
    if (entryInfoSection) {
        entryInfoSection.style.display = 'block';
    }

    // 开始中国标准时间显示
    startChinaTimeDisplay();

    // 开始入场信息监控
    startEntryInfoMonitoring(state);

    console.log('✅ UI已更新为AI交易运行状态');
}

// 更新UI为交易停止状态
function updateUIForTradingStopped() {
    console.log('🛑 更新UI为AI交易停止状态');

    // 更新按钮状态
    const startBtn = document.getElementById('startAITrading');
    const stopBtn = document.getElementById('stopAITrading');

    if (startBtn) {
        startBtn.style.display = 'block';
        startBtn.innerHTML = '<i class="fas fa-play"></i> 开始AI交易';
        startBtn.className = 'btn btn-success w-100';
    }

    if (stopBtn) {
        stopBtn.style.display = 'none';
    }

    // 隐藏AI交易状态
    const statusDiv = document.getElementById('aiTradingStatus');
    if (statusDiv) {
        statusDiv.style.display = 'none';
    }

    // 隐藏入场信息区域
    const entryInfoSection = document.getElementById('entryInfoSection');
    if (entryInfoSection) {
        entryInfoSection.style.display = 'none';
    }

    // 停止中国标准时间显示
    stopChinaTimeDisplay();

    // 停止入场信息监控
    stopEntryInfoMonitoring();

    console.log('✅ UI已更新为AI交易停止状态');
}

// 更新UI为持仓管理模式
function updateUIForPositionManagementMode(data) {
    console.log('🔄 更新UI为持仓管理模式:', data);

    const statusDiv = document.getElementById('aiTradingStatus');
    if (statusDiv) {
        statusDiv.innerHTML = `
            <div class="alert alert-warning">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fas fa-shield-alt"></i>
                        <strong>持仓管理模式</strong>
                        <span class="ms-2">运行时间: <span id="aiRunningTime">00:00:00</span></span>
                    </div>
                    <div>
                        <span class="badge bg-warning">管理中</span>
                    </div>
                </div>
                <small class="d-block mt-2">
                    策略: <span id="currentAIStrategy">-</span> |
                    运行时间: <span id="aiRunningTime">00:00:00</span>
                </small>
                <small class="d-block mt-1">
                    <i class="fas fa-clock"></i>
                    中国标准时间: <span id="chinaTime">--:--:--</span> |
                    交易货币对: <span id="currentTradingSymbol">-</span>
                </small>
                <small class="d-block mt-1">
                    <i class="fas fa-chart-line"></i>
                    已执行交易: <span id="aiTradeCount" class="fw-bold text-warning">${data.currentCount}</span> 次 |
                    交易限制: <span id="aiTradeLimit" class="fw-bold text-warning">${data.limit}</span> 次
                    <span class="text-warning ms-2">
                        <i class="fas fa-ban"></i>
                        已停止新增订单
                    </span>
                </small>
                <div class="mt-2">
                    <small class="text-muted">
                        <i class="fas fa-info-circle"></i>
                        已达到交易次数限制，停止新增订单，继续管理现有持仓直至全部平仓
                    </small>
                </div>
            </div>
        `;
    }

    // 修改停止按钮文本
    const stopBtn = document.getElementById('stopAITrading');
    if (stopBtn) {
        stopBtn.innerHTML = '<i class="fas fa-stop"></i> 强制停止管理';
        stopBtn.title = '强制停止持仓管理（可能导致持仓无法正常处理）';
    }

    // 继续显示中国标准时间
    startChinaTimeDisplay();
}

// 更新运行时间显示
function updateRunningTime(state) {
    if (!state.startTime) return;

    const now = Date.now();
    const elapsed = now - state.startTime;
    const hours = Math.floor(elapsed / 3600000);
    const minutes = Math.floor((elapsed % 3600000) / 60000);
    const seconds = Math.floor((elapsed % 60000) / 1000);

    const timeString = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

    const runningTimeElements = document.querySelectorAll('#aiRunningTime');
    runningTimeElements.forEach(element => {
        element.textContent = timeString;
    });
}

// 更新AI交易次数统计
function updateAITradeCount(count) {
    const tradeCountElement = document.getElementById('aiTradeCount');
    if (tradeCountElement) {
        tradeCountElement.textContent = count || 0;
    }
}

// 更新交易次数限制显示
function updateTradeCountLimit(limit) {
    const tradeLimitElement = document.getElementById('aiTradeLimit');
    if (tradeLimitElement) {
        if (limit === null || limit === 'unlimited') {
            tradeLimitElement.textContent = '不限制';
            tradeLimitElement.className = 'fw-bold text-success';
        } else {
            tradeLimitElement.textContent = limit;
            tradeLimitElement.className = 'fw-bold text-info';
        }
    }
}

// 开始中国标准时间显示
let chinaTimeInterval = null;

function startChinaTimeDisplay() {
    // 清除之前的定时器
    if (chinaTimeInterval) {
        clearInterval(chinaTimeInterval);
    }

    // 立即更新一次
    updateChinaTime();

    // 每秒更新
    chinaTimeInterval = setInterval(updateChinaTime, 1000);
}

function stopChinaTimeDisplay() {
    if (chinaTimeInterval) {
        clearInterval(chinaTimeInterval);
        chinaTimeInterval = null;
    }
}

function updateChinaTime() {
    const now = new Date();
    const utc = now.getTime() + (now.getTimezoneOffset() * 60000);
    const chinaTime = new Date(utc + (8 * 3600000)); // UTC+8

    const timeString = chinaTime.toLocaleTimeString('zh-CN', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });

    const chinaTimeElement = document.getElementById('chinaTime');
    if (chinaTimeElement) {
        chinaTimeElement.textContent = timeString;
    }
}

// 显示通知
function showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';

    notification.innerHTML = `
        <div style="white-space: pre-line;">${message}</div>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    // 5秒后自动移除
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 5000);
}

// 加载AI策略列表
function loadAIStrategies() {
    fetch('/api/ai-strategies/list')
    .then(response => response.json())
    .then(data => {
        const select = document.getElementById('aiStrategySelect');
        select.innerHTML = '<option value="">选择AI策略</option>';

        if (data.success && data.strategies.length > 0) {
            let hasSelectedStrategy = false;
            let firstCompletedStrategy = null;

            data.strategies.forEach((strategy, index) => {
                if (strategy.status === 'completed') {
                    const option = document.createElement('option');
                    option.value = strategy.id;

                    const statusText = strategy.is_active ? '✅' : '⏸️';
                    let displayText = `${statusText} ${strategy.name}`;

                    if (strategy.performance_metrics && strategy.performance_metrics.win_rate) {
                        displayText += ` (胜率${(strategy.performance_metrics.win_rate * 100).toFixed(1)}%)`;
                    }

                    option.textContent = displayText;
                    select.appendChild(option);

                    // 记录第一个已完成的策略
                    if (!firstCompletedStrategy) {
                        firstCompletedStrategy = option;
                    }

                    // 优先选中第一个激活的策略
                    if (strategy.is_active && !hasSelectedStrategy) {
                        option.selected = true;
                        hasSelectedStrategy = true;
                        console.log('✅ 默认选择激活的AI策略:', strategy.name);
                    }
                }
            });

            // 如果没有激活的策略，选择第一个已完成的策略
            if (!hasSelectedStrategy && firstCompletedStrategy) {
                firstCompletedStrategy.selected = true;
                console.log('✅ 默认选择第一个AI策略:', firstCompletedStrategy.textContent);
            }

            if (select.options.length === 1) {
                select.innerHTML = '<option value="">暂无可用的AI策略，请先训练AI策略</option>';
            } else {
                // AI策略加载完成后，应用保存的默认选择
                applyDefaultAIStrategySelection();
            }
        } else {
            select.innerHTML = '<option value="">暂无可用的AI策略，请先训练AI策略</option>';
        }
    })
    .catch(error => {
        console.error('加载AI策略失败:', error);
        document.getElementById('aiStrategySelect').innerHTML = '<option value="">加载失败，请刷新重试</option>';
    });
}

// 应用默认AI策略选择
function applyDefaultAIStrategySelection() {
    const defaultStrategyId = localStorage.getItem('defaultAIStrategy');
    const strategySelect = document.getElementById('aiStrategySelect');

    if (defaultStrategyId && strategySelect) {
        const option = strategySelect.querySelector(`option[value="${defaultStrategyId}"]`);
        if (option) {
            strategySelect.value = defaultStrategyId;
            console.log('✅ 应用保存的默认AI策略:', defaultStrategyId);
        } else {
            console.warn('⚠️ 保存的默认AI策略不存在，使用当前选择');
        }
    }
}

// 重置每日交易计数
function resetDailyTradeCount() {
    const today = new Date().toDateString();
    const lastResetDate = localStorage.getItem('lastTradeCountReset');

    if (lastResetDate !== today) {
        todayTradeCount = 0;
        localStorage.setItem('lastTradeCountReset', today);
        localStorage.setItem('todayTradeCount', '0');
    } else {
        todayTradeCount = parseInt(localStorage.getItem('todayTradeCount') || '0');
    }

    updateTodayTradeCount();
}

// 更新今日交易计数显示
function updateTodayTradeCount() {
    const element = document.getElementById('todayTradeCount');
    if (element) {
        element.textContent = todayTradeCount;
    }
}

// 切换AI交易模式
function toggleAITrading() {
    const isEnabled = document.getElementById('aiTradingSwitch').checked;
    const aiSection = document.getElementById('aiTradingSection');
    const manualSection = document.getElementById('manualTradingSection');
    const title = document.getElementById('tradingModeTitle');

    if (isEnabled) {
        aiSection.style.display = 'block';
        manualSection.style.display = 'none';
        title.textContent = 'AI智能交易';

        // 如果AI交易正在运行，先停止
        if (aiTradingActive) {
            stopAITrading();
        }
    } else {
        aiSection.style.display = 'none';
        manualSection.style.display = 'block';
        title.textContent = '下单交易';

        // 停止AI交易
        if (aiTradingActive) {
            stopAITrading();
        }
    }
}

// 开始AI交易
function startAITrading() {
    const strategyId = document.getElementById('aiStrategySelect').value;
    const riskLevel = document.getElementById('riskLevel').value;
    // 移除maxTradeAmount，改用手数区间
    const dailyLimit = parseInt(document.getElementById('dailyTradeLimit').value);
    const riskConfirmed = document.getElementById('riskConfirmation').checked;

    if (!strategyId) {
        alert('请选择一个AI策略');
        return;
    }

    const minLotSize = parseFloat(document.getElementById('minLotSize').value);
    const maxLotSize = parseFloat(document.getElementById('maxLotSize').value);

    if (!minLotSize || minLotSize < 0.01 || minLotSize > 0.05) {
        alert('请设置有效的最小手数（0.01-0.05）');
        return;
    }

    if (!maxLotSize || maxLotSize < 0.01 || maxLotSize > 0.05) {
        alert('请设置有效的最大手数（0.01-0.05）');
        return;
    }

    if (minLotSize > maxLotSize) {
        alert('最小手数不能大于最大手数');
        return;
    }

    if (!riskConfirmed) {
        alert('请确认您已了解AI自动交易的风险');
        return;
    }

    // 检查今日交易次数限制
    if (todayTradeCount >= dailyLimit) {
        alert(`今日交易次数已达到限制（${dailyLimit}次），请明日再试或调整限制`);
        return;
    }

    // 最终确认
    const confirmMessage = `确定要开始AI自动交易吗？\n\n风险提示：\n- 这将使用真实资金进行交易\n- 交易手数区间：${minLotSize}-${maxLotSize}手\n- 今日剩余交易次数：${dailyLimit - todayTradeCount}次\n- 所有损失将由您承担\n\n请确认您完全理解相关风险。`;

    if (!confirm(confirmMessage)) {
        return;
    }

    // 获取用户选择的配置
    const tradingSymbol = document.getElementById('aiTradingSymbol')?.value || 'EURUSD';
    const tradingTimeSlot = document.getElementById('tradingTimeSlot')?.value || '09:00-18:00';
    const maxTradeCount = getTradeCountLimit();

    // 获取全程自动设置
    const fullAutoSettings = getFullAutoTradingSettings();

    // 使用全局管理器启动AI交易
    const config = {
        strategyId: parseInt(strategyId),
        riskLevel: riskLevel,
        minLotSize: minLotSize,
        maxLotSize: maxLotSize,
        tradingSymbol: tradingSymbol,
        tradingTimeSlot: tradingTimeSlot,
        accountType: 'real',
        dailyLimit: dailyLimit,
        maxTradeCount: maxTradeCount,
        fullAutoTrading: fullAutoSettings
    };

    const success = AITradingManager.startAITrading(config);

    if (success) {
        console.log('真实AI交易已通过全局管理器启动:', config);
    }

}

// 停止AI交易 (使用全局管理器)
function stopAITrading() {
    // 使用全局管理器停止AI交易
    AITradingManager.stopAITrading('real');

    console.log('真实AI交易已通过全局管理器停止');
    alert('AI交易已停止');
}

// 获取交易次数限制
function getTradeCountLimit() {
    const maxTradeCountSelect = document.getElementById('maxTradeCount');
    if (maxTradeCountSelect) {
        const value = maxTradeCountSelect.value;
        return value === 'unlimited' ? null : parseInt(value);
    }
    return 30; // 默认限制
}

// 设置默认货币对
function setAsDefaultSymbol() {
    const selectedSymbol = document.getElementById('aiTradingSymbol').value;

    if (!selectedSymbol) {
        alert('请先选择一个货币对');
        return;
    }

    // 保存到localStorage
    localStorage.setItem('defaultTradingSymbol', selectedSymbol);

    // 显示成功提示
    const symbolText = document.getElementById('aiTradingSymbol').selectedOptions[0].text;
    showNotification(`✅ 已将 ${symbolText} 设为默认货币对`, 'success');

    console.log('✅ 默认货币对已设置:', selectedSymbol);
}

// 验证手数区间设置
function validateLotSizeRange() {
    const minInput = document.getElementById('minLotSize');
    const maxInput = document.getElementById('maxLotSize');

    let minValue = parseFloat(minInput.value) || 0.01;
    let maxValue = parseFloat(maxInput.value) || 0.04;

    // 限制在0.01-0.05范围内
    minValue = Math.max(0.01, Math.min(0.05, minValue));
    maxValue = Math.max(0.01, Math.min(0.05, maxValue));

    // 确保是0.01的整数倍
    minValue = Math.round(minValue * 100) / 100;
    maxValue = Math.round(maxValue * 100) / 100;

    // 确保最小值不大于最大值
    if (minValue > maxValue) {
        maxValue = minValue;
        console.log(`🔧 自动调整最大手数: ${maxInput.value} → ${maxValue.toFixed(2)}`);
    }

    // 更新输入框值
    minInput.value = minValue.toFixed(2);
    maxInput.value = maxValue.toFixed(2);

    console.log(`📊 手数区间验证: ${minValue.toFixed(2)} - ${maxValue.toFixed(2)}`);

    return { min: minValue, max: maxValue };
}

// 加载默认设置
function loadDefaultSettings() {
    console.log('🔄 加载真实交易默认设置...');

    // 加载默认货币对
    const defaultSymbol = localStorage.getItem('defaultTradingSymbol');
    if (defaultSymbol) {
        const symbolSelect = document.getElementById('aiTradingSymbol');
        const option = symbolSelect.querySelector(`option[value="${defaultSymbol}"]`);
        if (option) {
            symbolSelect.value = defaultSymbol;
            console.log('✅ 已加载默认货币对:', defaultSymbol);

            // 更新显示
            const symbolText = option.text;
            console.log(`📈 默认货币对: ${symbolText}`);
        } else {
            console.warn('⚠️ 默认货币对选项不存在:', defaultSymbol);
        }
    } else {
        // 如果没有保存的默认值，设置EUR/USD为默认
        const symbolSelect = document.getElementById('aiTradingSymbol');
        const eurUsdOption = symbolSelect.querySelector('option[value="EURUSD=X"]');
        if (eurUsdOption) {
            symbolSelect.value = 'EURUSD=X';
            console.log('✅ 设置EUR/USD为默认货币对');
        }
    }

    // 加载默认风险等级
    const defaultRiskLevel = localStorage.getItem('defaultRiskLevel');
    if (defaultRiskLevel) {
        const riskSelect = document.getElementById('riskLevel');
        const option = riskSelect.querySelector(`option[value="${defaultRiskLevel}"]`);
        if (option) {
            riskSelect.value = defaultRiskLevel;
            console.log('✅ 已加载默认风险等级:', defaultRiskLevel);
        }
    }

    // 加载默认交易金额
    const defaultTradeAmount = localStorage.getItem('defaultTradeAmount');
    if (defaultTradeAmount) {
        const amountInput = document.getElementById('maxTradeAmount');
        if (amountInput) {
            amountInput.value = defaultTradeAmount;
            console.log('✅ 已加载默认交易金额:', defaultTradeAmount);
        }
    }

    // 加载默认AI策略
    const defaultStrategyId = localStorage.getItem('defaultAIStrategy');
    if (defaultStrategyId) {
        const strategySelect = document.getElementById('aiStrategySelect');
        const option = strategySelect.querySelector(`option[value="${defaultStrategyId}"]`);
        if (option) {
            strategySelect.value = defaultStrategyId;
            console.log('✅ 已加载默认AI策略:', defaultStrategyId);
        } else {
            console.warn('⚠️ 默认AI策略选项不存在:', defaultStrategyId);
        }
    }

    console.log('✅ 真实交易默认设置加载完成');
}

// 添加设置变更监听器
function addSettingsEventListeners() {
    console.log('🔧 添加真实交易设置事件监听器...');

    // 货币对变更时自动保存
    const symbolSelect = document.getElementById('aiTradingSymbol');
    if (symbolSelect) {
        symbolSelect.addEventListener('change', function() {
            if (this.value) {
                localStorage.setItem('defaultTradingSymbol', this.value);
                console.log('✅ 自动保存默认货币对:', this.value);
            }
        });
    }

    // 风险等级变更时自动保存
    const riskSelect = document.getElementById('riskLevel');
    if (riskSelect) {
        riskSelect.addEventListener('change', function() {
            localStorage.setItem('defaultRiskLevel', this.value);
            console.log('✅ 自动保存默认风险等级:', this.value);
        });
    }

    // 交易金额变更时自动保存
    const amountInput = document.getElementById('maxTradeAmount');
    if (amountInput) {
        amountInput.addEventListener('change', function() {
            localStorage.setItem('defaultTradeAmount', this.value);
            console.log('✅ 自动保存默认交易金额:', this.value);
        });
    }

    // AI策略变更时自动保存
    const strategySelect = document.getElementById('aiStrategySelect');
    if (strategySelect) {
        strategySelect.addEventListener('change', function() {
            if (this.value) {
                localStorage.setItem('defaultAIStrategy', this.value);
                console.log('✅ 自动保存默认AI策略:', this.value);
            }
        });
    }

    console.log('✅ 真实交易设置事件监听器添加完成');
}

// 保存当前设置为默认
function saveCurrentAsDefault() {
    const symbol = document.getElementById('aiTradingSymbol').value;
    const riskLevel = document.getElementById('riskLevel').value;
    const tradeAmount = document.getElementById('maxTradeAmount').value;
    const strategyId = document.getElementById('aiStrategySelect').value;

    if (symbol) localStorage.setItem('defaultTradingSymbol', symbol);
    if (riskLevel) localStorage.setItem('defaultRiskLevel', riskLevel);
    if (tradeAmount) localStorage.setItem('defaultTradeAmount', tradeAmount);
    if (strategyId) localStorage.setItem('defaultAIStrategy', strategyId);

    showNotification('✅ 当前设置已保存为默认值\n包括AI策略、货币对、风险等级和交易金额', 'success');
    console.log('✅ 当前设置已保存为默认值');
}

// 设置默认AI策略
function setAsDefaultAIStrategy() {
    const selectedStrategyId = document.getElementById('aiStrategySelect').value;

    if (!selectedStrategyId) {
        alert('请先选择一个AI策略');
        return;
    }

    // 保存到localStorage
    localStorage.setItem('defaultAIStrategy', selectedStrategyId);

    // 显示成功提示
    const strategyText = document.getElementById('aiStrategySelect').selectedOptions[0].text;
    showNotification(`✅ 已将 ${strategyText} 设为默认AI策略`, 'success');

    console.log('✅ 默认AI策略已设置:', selectedStrategyId);
}

// 根据风险等级自动调整止损止盈
function updateStopLossTakeProfitByRisk() {
    const riskLevel = document.getElementById('riskLevel').value;
    const stopLossInput = document.getElementById('aiStopLossPercent');
    const takeProfitInput = document.getElementById('aiTakeProfitPercent');

    if (!stopLossInput || !takeProfitInput) {
        return;
    }

    // 根据风险等级设置止损止盈
    const riskConfig = {
        'conservative': { stopLoss: 1.0, takeProfit: 1.5 },  // 保守型
        'moderate': { stopLoss: 1.0, takeProfit: 2.0 },      // 稳健型
        'aggressive': { stopLoss: 1.5, takeProfit: 3.0 }     // 激进型
    };

    const config = riskConfig[riskLevel] || riskConfig['moderate'];

    stopLossInput.value = config.stopLoss;
    takeProfitInput.value = config.takeProfit;

    console.log(`✅ 根据风险等级 ${riskLevel} 自动调整: 止损${config.stopLoss}%, 止盈${config.takeProfit}%`);

    // 显示调整通知
    showNotification(`✅ 已根据${getRiskLevelName(riskLevel)}自动调整止损止盈\n\n止损: ${config.stopLoss}%\n止盈: ${config.takeProfit}%`, 'success');
}

// 获取风险等级中文名称
function getRiskLevelName(riskLevel) {
    const names = {
        'conservative': '保守型',
        'moderate': '稳健型',
        'aggressive': '激进型'
    };
    return names[riskLevel] || '稳健型';
}

// 实时保证金计算器
function calculateMargin() {
    const symbol = document.getElementById('marginSymbol').value;
    const lots = parseFloat(document.getElementById('marginLots').value) || 0;
    const price = parseFloat(document.getElementById('marginPrice').value) || 0;
    const leverage = parseFloat(document.getElementById('marginLeverage').value) || 100;

    let contractValue = 0;

    // 根据不同交易品种计算合约价值
    switch(symbol) {
        case 'XAUUSD':
            // 黄金: 1手 = 100盎司
            contractValue = lots * 100 * price;
            break;
        case 'EURUSD':
        case 'GBPUSD':
            // 外汇: 1手 = 100,000基础货币
            contractValue = lots * 100000 * price;
            break;
        case 'USDJPY':
            // 美日: 1手 = 100,000美元
            contractValue = lots * 100000;
            break;
        default:
            contractValue = lots * 100000 * price;
    }

    // 计算保证金
    const margin = contractValue / leverage;

    // 显示结果
    document.getElementById('marginResult').value = margin.toFixed(2);

    // 更新价格输入框的占位符
    updatePricePlaceholder(symbol);
}

// 更新价格输入框的占位符和默认值
function updatePricePlaceholder(symbol) {
    const priceInput = document.getElementById('marginPrice');

    switch(symbol) {
        case 'XAUUSD':
            if (!priceInput.value || priceInput.value == '1.1000') {
                priceInput.value = '3300.00';
            }
            priceInput.placeholder = '如: 3300.00';
            break;
        case 'EURUSD':
            if (!priceInput.value || priceInput.value == '3300.00') {
                priceInput.value = '1.1000';
            }
            priceInput.placeholder = '如: 1.1000';
            break;
        case 'GBPUSD':
            if (!priceInput.value || priceInput.value == '3300.00' || priceInput.value == '1.1000') {
                priceInput.value = '1.3000';
            }
            priceInput.placeholder = '如: 1.3000';
            break;
        case 'USDJPY':
            if (!priceInput.value || priceInput.value == '3300.00' || priceInput.value == '1.1000' || priceInput.value == '1.3000') {
                priceInput.value = '150.00';
            }
            priceInput.placeholder = '如: 150.00';
            break;
    }

    // 重新计算保证金
    calculateMargin();
}

// 页面加载时初始化保证金计算器
document.addEventListener('DOMContentLoaded', function() {
    // 添加品种选择变化事件
    document.getElementById('marginSymbol').addEventListener('change', function() {
        updatePricePlaceholder(this.value);
    });

    // 初始计算
    calculateMargin();

    // 图表初始化已移到主要的DOMContentLoaded事件中，避免重复初始化
    console.log('📊 保证金计算器初始化完成');
});

// ==================== 实时价格图表功能 ====================

let currentChartSymbol = 'XAUUSD';
let currentTimeframe = 'M1';
let chartUpdateInterval;
let tradingChart;

// 初始化交易图表
function initTradingChart() {
    console.log('🔄 初始化交易图表，当前时间框架:', currentTimeframe);

    // 确保时间框架按钮状态正确
    document.querySelectorAll('.timeframe-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    const activeBtn = document.querySelector(`[data-timeframe="${currentTimeframe}"]`);
    if (activeBtn) {
        activeBtn.classList.add('active');
    }

    // 加载Plotly.js
    if (typeof Plotly === 'undefined') {
        const script = document.createElement('script');
        script.src = 'https://cdn.plot.ly/plotly-3.0.1.min.js';
        script.onload = function() {
            console.log('✅ Plotly.js 加载完成，创建图表');
            createTradingChart();
            startChartUpdates();
        };
        document.head.appendChild(script);
    } else {
        console.log('✅ Plotly.js 已存在，直接创建图表');
        createTradingChart();
        startChartUpdates();
    }
}

// 创建交易图表
function createTradingChart() {
    console.log('📊 创建交易图表，品种:', currentChartSymbol, '时间框架:', currentTimeframe);

    const data = generateChartData();
    if (!data || data.length === 0) {
        console.error('❌ 图表数据为空');
        return;
    }

    console.log('📈 生成图表数据:', data.length, '条记录');
    const traces = [];

    // K线图
    traces.push({
        x: data.map(d => d.time),
        open: data.map(d => d.open),
        high: data.map(d => d.high),
        low: data.map(d => d.low),
        close: data.map(d => d.close),
        type: 'candlestick',
        name: currentChartSymbol,
        increasing: { line: { color: '#00ff88' } },
        decreasing: { line: { color: '#ff4444' } },
        xaxis: 'x',
        yaxis: 'y'
    });

    // 添加移动平均线
    if (document.getElementById('showMA5').checked) {
        traces.push({
            x: data.map(d => d.time),
            y: data.map(d => d.ma5),
            type: 'scatter',
            mode: 'lines',
            name: 'MA5',
            line: { color: '#ff7f0e', width: 1 },
            xaxis: 'x',
            yaxis: 'y'
        });
    }

    if (document.getElementById('showMA20').checked) {
        traces.push({
            x: data.map(d => d.time),
            y: data.map(d => d.ma20),
            type: 'scatter',
            mode: 'lines',
            name: 'MA20',
            line: { color: '#d62728', width: 1 },
            xaxis: 'x',
            yaxis: 'y'
        });
    }

    if (document.getElementById('showMA50').checked) {
        traces.push({
            x: data.map(d => d.time),
            y: data.map(d => d.ma50),
            type: 'scatter',
            mode: 'lines',
            name: 'MA50',
            line: { color: '#9467bd', width: 1 },
            xaxis: 'x',
            yaxis: 'y'
        });
    }

    // 布林带
    if (document.getElementById('showBollinger').checked) {
        traces.push({
            x: data.map(d => d.time),
            y: data.map(d => d.bb_upper),
            type: 'scatter',
            mode: 'lines',
            name: '布林上轨',
            line: { color: '#17becf', width: 1, dash: 'dash' },
            xaxis: 'x',
            yaxis: 'y'
        });

        traces.push({
            x: data.map(d => d.time),
            y: data.map(d => d.bb_lower),
            type: 'scatter',
            mode: 'lines',
            name: '布林下轨',
            line: { color: '#17becf', width: 1, dash: 'dash' },
            fill: 'tonexty',
            fillcolor: 'rgba(23, 190, 207, 0.1)',
            xaxis: 'x',
            yaxis: 'y'
        });
    }

    // 成交量
    if (document.getElementById('showVolume').checked) {
        traces.push({
            x: data.map(d => d.time),
            y: data.map(d => d.volume),
            type: 'bar',
            name: '成交量',
            marker: { color: 'rgba(158,202,225,0.8)' },
            xaxis: 'x',
            yaxis: 'y2'
        });
    }

    // RSI指标
    if (document.getElementById('showRSI').checked) {
        traces.push({
            x: data.map(d => d.time),
            y: data.map(d => d.rsi),
            type: 'scatter',
            mode: 'lines',
            name: 'RSI',
            line: { color: '#8c564b', width: 1 },
            xaxis: 'x',
            yaxis: 'y3'
        });
    }

    // MACD指标
    if (document.getElementById('showMACD').checked) {
        traces.push({
            x: data.map(d => d.time),
            y: data.map(d => d.macd),
            type: 'scatter',
            mode: 'lines',
            name: 'MACD',
            line: { color: '#e377c2', width: 1 },
            xaxis: 'x',
            yaxis: 'y4'
        });

        traces.push({
            x: data.map(d => d.time),
            y: data.map(d => d.macd_signal),
            type: 'scatter',
            mode: 'lines',
            name: 'MACD信号',
            line: { color: '#7f7f7f', width: 1 },
            xaxis: 'x',
            yaxis: 'y4'
        });
    }

    // 计算子图数量和高度
    let subplotCount = 1; // 主图
    if (document.getElementById('showVolume').checked) subplotCount++;
    if (document.getElementById('showRSI').checked) subplotCount++;
    if (document.getElementById('showMACD').checked) subplotCount++;

    const mainHeight = 0.6;
    const subHeight = (1 - mainHeight) / (subplotCount - 1);

    const layout = {
        title: `${currentChartSymbol} ${currentTimeframe}`,
        height: 400,
        margin: { t: 40, r: 10, b: 40, l: 60 },
        showlegend: false,
        xaxis: {
            type: 'date',
            rangeslider: { visible: false },
            domain: [0, 1]
        },
        yaxis: {
            title: '价格',
            domain: [subplotCount > 1 ? subHeight * (subplotCount - 1) : 0, 1]
        },
        template: 'plotly_white',
        dragmode: 'pan'
    };

    // 添加子图y轴
    let currentY = 2;
    if (document.getElementById('showVolume').checked) {
        layout[`yaxis${currentY}`] = {
            title: '成交量',
            domain: [subHeight * (subplotCount - currentY), subHeight * (subplotCount - currentY + 1) - 0.02],
            side: 'right'
        };
        currentY++;
    }

    if (document.getElementById('showRSI').checked) {
        layout[`yaxis${currentY}`] = {
            title: 'RSI',
            domain: [subHeight * (subplotCount - currentY), subHeight * (subplotCount - currentY + 1) - 0.02],
            range: [0, 100]
        };
        currentY++;
    }

    if (document.getElementById('showMACD').checked) {
        layout[`yaxis${currentY}`] = {
            title: 'MACD',
            domain: [0, subHeight - 0.02]
        };
    }

    const config = {
        responsive: true,
        displayModeBar: true,
        modeBarButtonsToRemove: ['pan2d', 'lasso2d', 'select2d'],
        displaylogo: false
    };

    Plotly.newPlot('tradingChart', traces, layout, config);

    // 更新技术分析摘要
    updateTechnicalSummary(data);
    updatePriceInfo(data);
}

// 生成图表数据
function generateChartData() {
    console.log('🔢 生成图表数据，时间框架:', currentTimeframe);

    const data = [];
    const now = new Date();
    let basePrice = getBasePrice(currentChartSymbol);
    const timeframeMs = getTimeframeMs(currentTimeframe);

    console.log('⏰ 时间框架毫秒数:', timeframeMs, '基础价格:', basePrice);

    // 生成历史数据
    const dataPoints = currentTimeframe === 'M1' ? 60 : 100; // 1分钟图显示更多数据点
    for (let i = dataPoints; i >= 0; i--) {
        const time = new Date(now.getTime() - i * timeframeMs);

        // 系统严禁使用模拟价格数据，需要从MT5获取真实数据
        console.error('❌ 系统严禁生成模拟价格数据，请使用MT5真实数据');
        return []; // 返回空数据，强制使用真实数据源

        // 计算技术指标
        const dataPoint = {
            time: time,
            open: open,
            high: high,
            low: low,
            close: close,
            volume: Math.random() * 1000 + 500
        };

        data.push(dataPoint);
    }

    // 计算移动平均线和技术指标
    for (let i = 0; i < data.length; i++) {
        // MA5
        if (i >= 4) {
            data[i].ma5 = data.slice(i-4, i+1).reduce((sum, d) => sum + d.close, 0) / 5;
        }

        // MA20
        if (i >= 19) {
            data[i].ma20 = data.slice(i-19, i+1).reduce((sum, d) => sum + d.close, 0) / 20;
        }

        // MA50
        if (i >= 49) {
            data[i].ma50 = data.slice(i-49, i+1).reduce((sum, d) => sum + d.close, 0) / 50;
        }

        // 布林带
        if (i >= 19) {
            const ma20 = data[i].ma20;
            const prices = data.slice(i-19, i+1).map(d => d.close);
            const std = Math.sqrt(prices.reduce((sum, p) => sum + Math.pow(p - ma20, 2), 0) / 20);
            data[i].bb_upper = ma20 + 2 * std;
            data[i].bb_lower = ma20 - 2 * std;
        }

        // RSI
        if (i >= 14) {
            const gains = [];
            const losses = [];
            for (let j = i-13; j <= i; j++) {
                const change = data[j].close - data[j-1].close;
                if (change > 0) gains.push(change);
                else losses.push(-change);
            }
            const avgGain = gains.reduce((sum, g) => sum + g, 0) / 14;
            const avgLoss = losses.reduce((sum, l) => sum + l, 0) / 14;
            const rs = avgGain / avgLoss;
            data[i].rsi = 100 - (100 / (1 + rs));
        }

        // MACD
        if (i >= 26) {
            // 简化的MACD计算
            const ema12 = data.slice(i-11, i+1).reduce((sum, d) => sum + d.close, 0) / 12;
            const ema26 = data.slice(i-25, i+1).reduce((sum, d) => sum + d.close, 0) / 26;
            data[i].macd = ema12 - ema26;

            if (i >= 34) {
                data[i].macd_signal = data.slice(i-8, i+1).reduce((sum, d) => sum + d.macd, 0) / 9;
            }
        }
    }

    return data;
}

// 获取基础价格
function getBasePrice(symbol) {
    const basePrices = {
        'XAUUSD': 3300.00,
        'EURUSD': 1.1000,
        'GBPUSD': 1.3000,
        'USDJPY': 150.00,
        'USDCHF': 0.9200,
        'AUDUSD': 0.7500
    };
    return basePrices[symbol] || 1.0000;
}

// 获取波动率
function getVolatility(symbol) {
    const volatilities = {
        'XAUUSD': 5.0,
        'EURUSD': 0.002,
        'GBPUSD': 0.003,
        'USDJPY': 0.5,
        'USDCHF': 0.002,
        'AUDUSD': 0.003
    };
    return volatilities[symbol] || 0.002;
}

// 获取时间框架毫秒数
function getTimeframeMs(timeframe) {
    const timeframes = {
        'M1': 60 * 1000,
        'M5': 5 * 60 * 1000,
        'M15': 15 * 60 * 1000,
        'H1': 60 * 60 * 1000,
        'H4': 4 * 60 * 60 * 1000,
        'D1': 24 * 60 * 60 * 1000
    };
    return timeframes[timeframe] || 60 * 1000;
}

// 更新图表
function updateChart() {
    createTradingChart();
}

// 设置时间框架
function setTimeframe(timeframe) {
    console.log('⏱️ 设置时间框架:', timeframe);
    currentTimeframe = timeframe;

    // 更新按钮状态
    document.querySelectorAll('.timeframe-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    const targetBtn = document.querySelector(`[data-timeframe="${timeframe}"]`);
    if (targetBtn) {
        targetBtn.classList.add('active');
        console.log('✅ 时间框架按钮状态已更新');
    } else {
        console.warn('⚠️ 未找到时间框架按钮:', timeframe);
    }

    // 重新创建图表和更新定时器
    updateChart();
    startChartUpdates();
}

// 更新图表品种
function updateChartSymbol() {
    currentChartSymbol = document.getElementById('chartSymbol').value;
    updateChart();
}

// 开始图表更新
function startChartUpdates() {
    // 清除现有定时器
    if (chartUpdateInterval) {
        clearInterval(chartUpdateInterval);
    }

    // 根据时间框架设置更新频率
    const updateFrequency = currentTimeframe === 'M1' ? 5000 :
                           currentTimeframe === 'M5' ? 15000 :
                           currentTimeframe === 'M15' ? 30000 : 60000;

    chartUpdateInterval = setInterval(() => {
        updateChart();
    }, updateFrequency);
}

// 更新技术分析摘要
function updateTechnicalSummary(data) {
    const latest = data[data.length - 1];
    const previous = data[data.length - 2];

    if (!latest || !previous) return;

    // 移动平均分析
    let maTrend = 'neutral';
    if (latest.ma5 && latest.ma20) {
        if (latest.close > latest.ma5 && latest.ma5 > latest.ma20) {
            maTrend = 'buy';
        } else if (latest.close < latest.ma5 && latest.ma5 < latest.ma20) {
            maTrend = 'sell';
        }
    }

    // 技术指标分析
    let indicatorTrend = 'neutral';
    if (latest.rsi) {
        if (latest.rsi > 70) {
            indicatorTrend = 'sell';
        } else if (latest.rsi < 30) {
            indicatorTrend = 'buy';
        }
    }

    // 总体评价
    let overallTrend = 'neutral';
    if (maTrend === 'buy' && indicatorTrend !== 'sell') {
        overallTrend = 'buy';
    } else if (maTrend === 'sell' && indicatorTrend !== 'buy') {
        overallTrend = 'sell';
    }

    // 更新显示
    const trendTexts = {
        'buy': '买入',
        'sell': '卖出',
        'neutral': '中性'
    };

    const trendColors = {
        'buy': 'bg-success',
        'sell': 'bg-danger',
        'neutral': 'bg-warning'
    };

    document.getElementById('maTrend').textContent = trendTexts[maTrend];
    document.getElementById('maTrend').className = `badge ${trendColors[maTrend]}`;

    document.getElementById('indicatorTrend').textContent = trendTexts[indicatorTrend];
    document.getElementById('indicatorTrend').className = `badge ${trendColors[indicatorTrend]}`;

    document.getElementById('overallTrend').textContent = trendTexts[overallTrend];
    document.getElementById('overallTrend').className = `badge ${trendColors[overallTrend]}`;
}

// 更新价格信息
function updatePriceInfo(data) {
    const latest = data[data.length - 1];
    const previous = data[data.length - 2];

    if (!latest || !previous) return;

    const currentPrice = latest.close;
    const priceChange = currentPrice - previous.close;
    const priceChangePercent = (priceChange / previous.close) * 100;

    // 更新当前价格
    document.getElementById('currentPrice').textContent = currentPrice.toFixed(5);

    // 更新涨跌
    const changeText = priceChange >= 0 ? `+${priceChange.toFixed(5)}` : priceChange.toFixed(5);
    const changePercentText = priceChangePercent >= 0 ? `+${priceChangePercent.toFixed(2)}%` : `${priceChangePercent.toFixed(2)}%`;
    const changeColor = priceChange >= 0 ? 'text-success' : 'text-danger';

    document.getElementById('priceChange').textContent = changeText;
    document.getElementById('priceChange').className = changeColor;

    document.getElementById('priceChangePercent').textContent = changePercentText;
    document.getElementById('priceChangePercent').className = changeColor;

    // 更新支撑阻力位
    const high = Math.max(...data.slice(-20).map(d => d.high));
    const low = Math.min(...data.slice(-20).map(d => d.low));

    document.getElementById('resistanceLevel').textContent = high.toFixed(5);
    document.getElementById('supportLevel').textContent = low.toFixed(5);

    // 更新时间
    document.getElementById('lastUpdate').textContent = new Date().toLocaleTimeString();
}

// 全屏显示
function toggleFullscreen() {
    const chartElement = document.getElementById('tradingChart');
    if (!document.fullscreenElement) {
        chartElement.requestFullscreen().then(() => {
            // 全屏后调整图表大小
            setTimeout(() => {
                Plotly.Plots.resize('tradingChart');
            }, 100);
        });
    } else {
        document.exitFullscreen();
    }
}

// 保存图表
function saveChart() {
    Plotly.downloadImage('tradingChart', {
        format: 'png',
        width: 1200,
        height: 800,
        filename: `${currentChartSymbol}_${currentTimeframe}_chart`
    });
}

// 监听图表品种变化
document.addEventListener('DOMContentLoaded', function() {
    const chartSymbolSelect = document.getElementById('chartSymbol');
    if (chartSymbolSelect) {
        chartSymbolSelect.addEventListener('change', updateChartSymbol);
    }
});

// 获取交易状态文本
function getTradeStatusText(trade) {
    // 根据交易的entry字段判断是开仓还是平仓
    if (trade.entry === 0) {
        return '开仓'; // 入场交易
    } else if (trade.entry === 1) {
        // 出场交易，根据原因判断平仓方式
        if (trade.reason === 3) { // DEAL_REASON_CLIENT
            return '手动平仓';
        } else if (trade.reason === 4) { // DEAL_REASON_MOBILE
            return '移动端平仓';
        } else if (trade.reason === 5) { // DEAL_REASON_WEB
            return 'Web平仓';
        } else if (trade.reason === 6) { // DEAL_REASON_EXPERT
            return 'EA平仓';
        } else if (trade.reason === 7) { // DEAL_REASON_SL
            return '止损平仓';
        } else if (trade.reason === 8) { // DEAL_REASON_TP
            return '止盈平仓';
        } else if (trade.reason === 9) { // DEAL_REASON_SO
            return '强制平仓';
        } else {
            return '系统平仓';
        }
    } else {
        return '未知状态';
    }
}

// 获取交易状态颜色
function getTradeStatusColor(trade) {
    if (trade.entry === 0) {
        return 'primary'; // 开仓用蓝色
    } else if (trade.entry === 1) {
        // 平仓根据原因使用不同颜色
        if (trade.reason === 3 || trade.reason === 4 || trade.reason === 5) {
            return 'success'; // 手动平仓用绿色
        } else if (trade.reason === 7) {
            return 'danger'; // 止损用红色
        } else if (trade.reason === 8) {
            return 'success'; // 止盈用绿色
        } else if (trade.reason === 9) {
            return 'warning'; // 强制平仓用黄色
        } else {
            return 'info'; // 其他系统平仓用蓝色
        }
    } else {
        return 'secondary'; // 未知状态用灰色
    }
}

// 更新AI运行时间
function updateAIRunningTime() {
    if (!aiStartTime) return;

    const now = new Date();
    const diff = now - aiStartTime;

    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((diff % (1000 * 60)) / 1000);

    const timeString = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    document.getElementById('aiRunningTime').textContent = timeString;
}

// 执行AI交易决策
function executeAITradingDecision(strategyId, riskLevel, maxTradeAmount, dailyLimit) {
    // 检查今日交易次数限制
    if (todayTradeCount >= dailyLimit) {
        console.log('今日交易次数已达限制，停止AI交易');
        stopAITrading();
        return;
    }

    console.log('执行AI交易决策...', { strategyId, riskLevel, maxTradeAmount, dailyLimit });

    // 调用AI策略分析API
    fetch('/api/ai-trading/analyze', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            strategy_id: strategyId,
            risk_level: riskLevel,
            max_trade_amount: maxTradeAmount,
            account_type: 'real'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.recommendation) {
            const rec = data.recommendation;

            // 如果AI建议执行交易
            if (rec.action !== 'hold') {
                executeAITrade(rec);
            } else {
                console.log('AI策略建议持有，暂不交易');
            }
        } else {
            console.error('AI分析失败:', data.error);
        }
    })
    .catch(error => {
        console.error('AI交易分析请求失败:', error);
    });
}

// 执行AI推荐的交易
function executeAITrade(recommendation) {
    const tradeData = {
        symbol: recommendation.symbol,
        side: recommendation.action, // 'buy' or 'sell'
        amount: recommendation.amount,
        type: 'market',
        ai_generated: true,
        strategy_id: recommendation.strategy_id
    };

    console.log('执行AI交易:', tradeData);

    // 提交AI生成的交易订单
    fetch('/api/real-trading/order', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(tradeData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('AI交易执行成功:', data);

            // 增加今日交易计数
            todayTradeCount++;
            localStorage.setItem('todayTradeCount', todayTradeCount.toString());
            updateTodayTradeCount();

            // 立即刷新持仓和历史记录
            setTimeout(() => {
                console.log('🔄 AI交易执行后刷新数据...');
                refreshRealTradingData();
            }, 2000); // 延迟2秒刷新，确保MT5订单已处理

        } else {
            console.error('AI交易执行失败:', data.error);
        }
    })
    .catch(error => {
        console.error('AI交易执行请求失败:', error);
    });
}

// 更新今日盈亏统计
function updateTodayPnLStats() {
    fetch('/api/dashboard/today-pnl')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 更新今日总盈亏
                const todayPnLElement = document.getElementById('todayPnL');
                if (todayPnLElement) {
                    const pnlSpan = todayPnLElement.querySelector('span');
                    pnlSpan.textContent = '$' + data.today_pnl.toFixed(2);
                    pnlSpan.className = data.today_pnl >= 0 ? 'profit-positive' : 'profit-negative';
                }

                // 更新已平仓盈亏
                const closedPnLElement = document.getElementById('closedPnL');
                if (closedPnLElement) {
                    const closedSpan = closedPnLElement.querySelector('span');
                    closedSpan.textContent = '$' + data.closed_pnl.toFixed(2);
                    closedSpan.className = data.closed_pnl >= 0 ? 'profit-positive' : 'profit-negative';
                }

                // 更新持仓盈亏
                const openPnLElement = document.getElementById('openPnL');
                if (openPnLElement) {
                    const openSpan = openPnLElement.querySelector('span');
                    openSpan.textContent = '$' + data.open_pnl.toFixed(2);
                    openSpan.className = data.open_pnl >= 0 ? 'profit-positive' : 'profit-negative';
                }

                // 更新今日交易笔数
                const todayTradesElement = document.getElementById('todayTrades');
                if (todayTradesElement) {
                    const tradesSpan = todayTradesElement.querySelector('span');
                    tradesSpan.textContent = data.today_trades + ' 笔';
                }

                console.log('✅ 今日盈亏统计已更新:', data);
            }
        })
        .catch(error => console.error('更新今日盈亏统计失败:', error));
}

// 更新本轮次AI交易盈亏统计
function updateRoundPnLStats() {
    fetch('/api/dashboard/round-pnl')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 更新轮次盈亏
                const roundPnLElement = document.getElementById('roundPnL');
                if (roundPnLElement) {
                    const pnlSpan = roundPnLElement.querySelector('span');
                    pnlSpan.textContent = '$' + data.round_pnl.toFixed(2);
                    pnlSpan.className = data.round_pnl >= 0 ? 'profit-positive' : 'profit-negative';
                }

                // 更新轮次交易笔数
                const roundTradesElement = document.getElementById('roundTrades');
                if (roundTradesElement) {
                    const tradesSpan = roundTradesElement.querySelector('span');
                    tradesSpan.textContent = data.round_trades + ' 笔';
                }

                // 更新轮次状态
                const roundStatusElement = document.getElementById('roundStatus');
                if (roundStatusElement) {
                    const statusSpan = roundStatusElement.querySelector('span');
                    if (data.round_status === 'active') {
                        statusSpan.textContent = '进行中';
                        statusSpan.className = 'badge bg-success';
                    } else if (data.round_status === 'completed') {
                        statusSpan.textContent = '已完成';
                        statusSpan.className = 'badge bg-primary';
                    } else {
                        statusSpan.textContent = '未激活';
                        statusSpan.className = 'badge bg-secondary';
                    }
                }

                // 更新开始时间
                const roundStartTimeElement = document.getElementById('roundStartTime');
                if (roundStartTimeElement) {
                    if (data.round_start_time) {
                        const startTime = new Date(data.round_start_time);
                        const timeStr = startTime.toLocaleTimeString('zh-CN', {
                            hour: '2-digit',
                            minute: '2-digit'
                        });
                        roundStartTimeElement.innerHTML = `<small class="text-info">${timeStr}</small>`;
                    } else {
                        roundStartTimeElement.innerHTML = '<small class="text-muted">--:--</small>';
                    }
                }

                console.log('✅ 本轮次盈亏统计已更新:', data);
            }
        })
        .catch(error => console.error('更新本轮次盈亏统计失败:', error));
}

// 刷新真实交易数据
function refreshRealTradingData() {
    console.log('🔄 刷新真实交易数据...');

    if (currentAccountId) {
        if (currentAccountType === 'mt5') {
            // MT5账户：刷新MT5数据
            loadMT5Positions();
            loadMT5TradeHistory();
        } else {
            // API账户：刷新API数据
            loadPositions(currentAccountId);
            loadTradeHistory(currentAccountId);
            loadAccountInfo(currentAccountId);
        }
    }

    // 同时尝试刷新MT5数据（如果有连接）
    if (mt5Connected) {
        loadMT5Positions();
        loadMT5TradeHistory();
    }

    // 更新今日盈亏统计
    updateTodayPnLStats();

    // 更新本轮次盈亏统计
    updateRoundPnLStats();
}

// 一键平仓相关函数
let currentPositionsData = []; // 存储当前持仓数据

// 显示一键平仓模态框
async function showCloseAllPositionsModal() {
    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('closeAllPositionsModal'));
    modal.show();

    // 重置表单
    document.getElementById('closeAllPassword').value = '';
    document.getElementById('confirmCloseAll').checked = false;

    // 获取实时持仓数据
    await updateCloseAllModalData();

    updateCloseAllButton();
}

// 更新一键平仓模态框数据
async function updateCloseAllModalData() {
    console.log('🔄 正在获取实时持仓数据...');

    // 显示加载状态
    document.getElementById('totalPositions').textContent = '加载中...';
    document.getElementById('totalUnrealizedPnL').textContent = '加载中...';
    document.getElementById('longPositions').textContent = '...';
    document.getElementById('shortPositions').textContent = '...';

    try {
        // 调用新的持仓概览API
        const response = await fetch('/api/all-positions-overview');
        const data = await response.json();

        console.log('📊 持仓概览数据:', data);

        if (data.success) {
            // 更新全局变量
            currentPositionsData = [];

            // 处理API持仓
            if (data.api_positions && data.api_positions.length > 0) {
                data.api_positions.forEach(position => {
                    currentPositionsData.push({
                        type: 'api',
                        tradeId: position.trade_id,
                        symbol: position.symbol,
                        side: position.side,
                        volume: position.volume,
                        unrealizedPnL: position.unrealized_pnl
                    });
                });
            }

            // 处理MT5持仓
            if (data.mt5_positions && data.mt5_positions.length > 0) {
                data.mt5_positions.forEach(position => {
                    currentPositionsData.push({
                        type: 'mt5',
                        ticket: position.ticket,
                        symbol: position.symbol,
                        side: position.side,
                        volume: position.volume,
                        unrealizedPnL: position.unrealized_pnl
                    });
                });
            }

            // 更新模态框显示
            document.getElementById('totalPositions').textContent = data.total_positions;
            document.getElementById('totalUnrealizedPnL').textContent = '$' + data.total_unrealized_pnl.toFixed(2);
            document.getElementById('totalUnrealizedPnL').className = data.total_unrealized_pnl >= 0 ? 'text-success' : 'text-danger';
            document.getElementById('longPositions').textContent = data.long_positions;
            document.getElementById('shortPositions').textContent = data.short_positions;

            console.log(`📈 持仓统计: 总计 ${data.total_positions} 个，多头 ${data.long_positions} 个，空头 ${data.short_positions} 个`);
            console.log(`💰 总浮动盈亏: $${data.total_unrealized_pnl.toFixed(2)}`);
            console.log('📋 持仓详情:', currentPositionsData);

            // 如果有错误详情，显示在控制台
            if (data.details && data.details.length > 0) {
                data.details.forEach(detail => {
                    if (detail.type.includes('error')) {
                        console.warn('⚠️', detail.message);
                    }
                });
            }

        } else {
            throw new Error(data.error || '获取持仓概览失败');
        }

    } catch (error) {
        console.error('❌ 获取持仓数据失败:', error);

        // 显示错误状态
        document.getElementById('totalPositions').textContent = '获取失败';
        document.getElementById('totalUnrealizedPnL').textContent = '获取失败';
        document.getElementById('longPositions').textContent = '0';
        document.getElementById('shortPositions').textContent = '0';

        // 尝试从表格获取数据作为备用
        console.log('🔄 尝试从表格获取持仓数据作为备用...');

        let totalPositions = 0;
        let totalUnrealizedPnL = 0;
        let longPositions = 0;
        let shortPositions = 0;

        currentPositionsData = [];

        const positionsTable = document.getElementById('positionsTable');
        if (positionsTable) {
            const rows = positionsTable.querySelectorAll('tbody tr');

            rows.forEach(row => {
                const cells = row.cells;
                if (cells.length > 1 && !cells[0].textContent.includes('暂无持仓') && !cells[0].textContent.includes('获取失败')) {
                    totalPositions++;

                    const side = cells[2].textContent.includes('多头') ? 'buy' : 'sell';
                    const unrealizedPnL = parseFloat(cells[6].textContent.replace('$', '').replace(',', '')) || 0;

                    if (side === 'buy') {
                        longPositions++;
                    } else {
                        shortPositions++;
                    }

                    totalUnrealizedPnL += unrealizedPnL;

                    // 从按钮获取ID
                    const closeButton = cells[7].querySelector('button');
                    if (closeButton) {
                        const onclickAttr = closeButton.getAttribute('onclick');

                        if (onclickAttr && onclickAttr.includes('closePosition')) {
                            const tradeId = onclickAttr.match(/\d+/)[0];
                            currentPositionsData.push({
                                type: 'api',
                                tradeId: tradeId,
                                symbol: cells[1].textContent.trim(),
                                side: side,
                                volume: cells[3].textContent.trim(),
                                unrealizedPnL: unrealizedPnL
                            });
                        } else if (onclickAttr && onclickAttr.includes('closeMT5Position')) {
                            const ticket = onclickAttr.match(/\d+/)[0];
                            currentPositionsData.push({
                                type: 'mt5',
                                ticket: ticket,
                                symbol: cells[1].textContent.trim(),
                                side: side,
                                volume: cells[3].textContent.trim(),
                                unrealizedPnL: unrealizedPnL
                            });
                        }
                    }
                }
            });

            // 更新显示（如果从表格获取到了数据）
            if (totalPositions > 0) {
                document.getElementById('totalPositions').textContent = totalPositions;
                document.getElementById('totalUnrealizedPnL').textContent = '$' + totalUnrealizedPnL.toFixed(2);
                document.getElementById('totalUnrealizedPnL').className = totalUnrealizedPnL >= 0 ? 'text-success' : 'text-danger';
                document.getElementById('longPositions').textContent = longPositions;
                document.getElementById('shortPositions').textContent = shortPositions;

                console.log(`📋 从表格获取到 ${totalPositions} 个持仓`);
            }
        }
    }

    console.log(`✅ 一键平仓模态框数据更新完成`);
}

// 监听密码输入和确认框变化
document.addEventListener('DOMContentLoaded', function() {
    const passwordInput = document.getElementById('closeAllPassword');
    const confirmCheckbox = document.getElementById('confirmCloseAll');

    if (passwordInput && confirmCheckbox) {
        passwordInput.addEventListener('input', updateCloseAllButton);
        confirmCheckbox.addEventListener('change', updateCloseAllButton);
    }
});

// 更新执行按钮状态
function updateCloseAllButton() {
    const password = document.getElementById('closeAllPassword')?.value;
    const confirmed = document.getElementById('confirmCloseAll')?.checked;
    const executeBtn = document.getElementById('executeCloseAllBtn');

    if (executeBtn) {
        executeBtn.disabled = !(password && confirmed && currentPositionsData.length > 0);
    }
}

// 执行一键平仓
function executeCloseAllPositions() {
    const password = document.getElementById('closeAllPassword').value;
    const confirmed = document.getElementById('confirmCloseAll').checked;

    // 验证密码
    if (password !== '12356') {
        alert('❌ 安全密码错误，请重新输入');
        document.getElementById('closeAllPassword').focus();
        return;
    }

    if (!confirmed) {
        alert('❌ 请确认您已了解风险');
        return;
    }

    if (currentPositionsData.length === 0) {
        alert('❌ 没有可平仓的持仓');
        return;
    }

    // 隐藏确认模态框
    const confirmModal = bootstrap.Modal.getInstance(document.getElementById('closeAllPositionsModal'));
    confirmModal.hide();

    // 显示进度模态框
    const progressModal = new bootstrap.Modal(document.getElementById('closeAllProgressModal'));
    progressModal.show();

    // 开始执行平仓
    executeCloseAllProcess();
}

// 执行平仓过程
async function executeCloseAllProcess() {
    const progressBar = document.getElementById('closeAllProgress');
    const statusDiv = document.getElementById('closeAllStatus');
    const detailsDiv = document.getElementById('closeAllDetails');

    try {
        // 初始化显示
        statusDiv.innerHTML = `<p class="text-center mb-0">正在执行一键平仓操作...</p>`;
        detailsDiv.innerHTML = '';
        progressBar.style.width = '10%';
        progressBar.textContent = '10%';

        // 添加开始日志
        detailsDiv.innerHTML += `<div class="text-info">[${new Date().toLocaleTimeString()}] 🚀 开始执行一键平仓操作</div>`;
        detailsDiv.innerHTML += `<div class="text-info">[${new Date().toLocaleTimeString()}] 🔐 密码验证通过，正在获取所有持仓...</div>`;

        // 更新进度
        progressBar.style.width = '30%';
        progressBar.textContent = '30%';

        // 调用一键平仓API
        const response = await fetch('/api/close-all-positions', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                password: '12356'
            })
        });

        const data = await response.json();

        // 更新进度
        progressBar.style.width = '60%';
        progressBar.textContent = '60%';

        // 显示执行结果
        if (data.success !== false) {
            // 显示详细结果
            detailsDiv.innerHTML += `<div class="text-success">[${new Date().toLocaleTimeString()}] 📊 找到 ${data.total_positions} 个持仓</div>`;

            // 显示每个持仓的处理结果
            if (data.details && data.details.length > 0) {
                data.details.forEach(detail => {
                    const timestamp = new Date().toLocaleTimeString();
                    const statusIcon = detail.status === 'success' ? '✅' : '❌';
                    const statusClass = detail.status === 'success' ? 'text-success' : 'text-danger';
                    const profitText = detail.profit !== 0 ? `, 盈亏: $${detail.profit.toFixed(2)}` : '';

                    let positionInfo = '';
                    if (detail.type === 'mt5') {
                        positionInfo = `MT5-${detail.symbol} (票据: ${detail.ticket})`;
                    } else {
                        positionInfo = `${detail.symbol} (交易ID: ${detail.trade_id})`;
                    }

                    detailsDiv.innerHTML += `<div class="${statusClass}">[${timestamp}] ${statusIcon} ${positionInfo} - ${detail.message}${profitText}</div>`;
                });
            }

            // 更新进度
            progressBar.style.width = '90%';
            progressBar.textContent = '90%';

            // 最终状态
            const finalStatus = `
                <div class="text-center">
                    <h6 class="${data.success ? 'text-success' : 'text-warning'}">
                        ${data.success ? '✅ 一键平仓执行完成' : '⚠️ 一键平仓部分完成'}
                    </h6>
                    <p class="mb-0">
                        总计: ${data.total_positions} 个持仓<br>
                        成功: <span class="text-success">${data.closed_positions}</span> 个<br>
                        失败: <span class="text-danger">${data.failed_positions}</span> 个<br>
                        总盈亏: <span class="${data.total_profit >= 0 ? 'text-success' : 'text-danger'}">$${data.total_profit.toFixed(2)}</span>
                    </p>
                </div>
            `;

            statusDiv.innerHTML = finalStatus;
            detailsDiv.innerHTML += `<div class="text-primary font-weight-bold">[${new Date().toLocaleTimeString()}] 🎯 一键平仓操作完成</div>`;

            // 更新进度条为完成状态
            progressBar.className = data.success ? 'progress-bar bg-success' : 'progress-bar bg-warning';
            progressBar.style.width = '100%';
            progressBar.textContent = '完成';

        } else {
            // 执行失败
            statusDiv.innerHTML = `
                <div class="text-center">
                    <h6 class="text-danger">❌ 一键平仓执行失败</h6>
                    <p class="mb-0">${data.error || '未知错误'}</p>
                </div>
            `;

            detailsDiv.innerHTML += `<div class="text-danger">[${new Date().toLocaleTimeString()}] ❌ 执行失败: ${data.error}</div>`;

            // 更新进度条为失败状态
            progressBar.className = 'progress-bar bg-danger';
            progressBar.style.width = '100%';
            progressBar.textContent = '失败';
        }

    } catch (error) {
        // 网络或其他异常
        statusDiv.innerHTML = `
            <div class="text-center">
                <h6 class="text-danger">❌ 一键平仓执行异常</h6>
                <p class="mb-0">网络错误或服务异常</p>
            </div>
        `;

        detailsDiv.innerHTML += `<div class="text-danger">[${new Date().toLocaleTimeString()}] ❌ 执行异常: ${error.message}</div>`;

        // 更新进度条为异常状态
        progressBar.className = 'progress-bar bg-danger';
        progressBar.style.width = '100%';
        progressBar.textContent = '异常';
    }

    // 滚动到底部
    detailsDiv.scrollTop = detailsDiv.scrollHeight;

    // 刷新持仓数据
    setTimeout(() => {
        refreshRealTradingData();
    }, 2000);

    // 5秒后自动关闭进度模态框
    setTimeout(() => {
        const progressModal = bootstrap.Modal.getInstance(document.getElementById('closeAllProgressModal'));
        if (progressModal) {
            progressModal.hide();
        }
    }, 5000);
}

// 定时刷新数据
setInterval(function() {
    refreshRealTradingData();
}, 10000); // 每10秒刷新一次

// ==================== 止盈止损监控功能 ====================

// 启动止盈止损监控
function startStopLossMonitoring() {
    console.log('🚀 启动止盈止损监控...');

    fetch('/api/stop-loss-take-profit/start', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('✅ 止盈止损监控启动成功');
            updateMonitoringStatus('运行中', 'bg-success');
            showAlert('止盈止损监控已启动', 'success');
        } else {
            console.error('❌ 启动监控失败:', data.error);
            showAlert('启动监控失败: ' + data.error, 'danger');
        }
    })
    .catch(error => {
        console.error('❌ 启动监控异常:', error);
        showAlert('启动监控异常: ' + error.message, 'danger');
    });
}

// 停止止盈止损监控
function stopStopLossMonitoring() {
    console.log('🛑 停止止盈止损监控...');

    fetch('/api/stop-loss-take-profit/stop', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('✅ 止盈止损监控停止成功');
            updateMonitoringStatus('已停止', 'bg-secondary');
            showAlert('止盈止损监控已停止', 'info');
        } else {
            console.error('❌ 停止监控失败:', data.error);
            showAlert('停止监控失败: ' + data.error, 'danger');
        }
    })
    .catch(error => {
        console.error('❌ 停止监控异常:', error);
        showAlert('停止监控异常: ' + error.message, 'danger');
    });
}

// 检查监控状态
function checkMonitoringStatus() {
    fetch('/api/stop-loss-take-profit/status')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const status = data.status;
            if (status.monitoring) {
                updateMonitoringStatus('运行中', 'bg-success');
            } else {
                updateMonitoringStatus('已停止', 'bg-secondary');
            }

            console.log('📊 监控状态:', status);
        } else {
            console.error('❌ 获取监控状态失败:', data.error);
            updateMonitoringStatus('状态异常', 'bg-warning');
        }
    })
    .catch(error => {
        console.error('❌ 检查监控状态异常:', error);
        updateMonitoringStatus('连接异常', 'bg-danger');
    });
}

// 更新监控状态显示
function updateMonitoringStatus(text, badgeClass) {
    const statusElement = document.getElementById('monitoringStatus');
    if (statusElement) {
        statusElement.textContent = text;
        statusElement.className = `badge ${badgeClass}`;
    }
}

// 显示提示信息
function showAlert(message, type) {
    // 创建提示框
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // 插入到页面顶部
    const container = document.querySelector('.container');
    if (container) {
        container.insertBefore(alertDiv, container.firstChild);

        // 3秒后自动消失
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 3000);
    }
}

// 设置定期检查监控状态
setInterval(checkMonitoringStatus, 30000); // 每30秒检查一次

// ==================== 交易查询功能 ====================

// 执行交易查询
function executeTradeQuery() {
    const queryParams = {
        start_date: document.getElementById('queryStartDate').value,
        end_date: document.getElementById('queryEndDate').value,
        symbol: document.getElementById('querySymbol').value,
        direction: document.getElementById('queryDirection').value,
        profit_status: document.getElementById('queryProfitStatus').value,
        min_amount: document.getElementById('queryMinAmount').value,
        max_amount: document.getElementById('queryMaxAmount').value
    };

    // 移除空值参数
    Object.keys(queryParams).forEach(key => {
        if (!queryParams[key]) {
            delete queryParams[key];
        }
    });

    console.log('🔍 执行交易查询:', queryParams);

    // 构建查询URL
    const queryString = new URLSearchParams(queryParams).toString();
    const url = `/api/trading/query${queryString ? '?' + queryString : ''}`;

    fetch(url)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayQueryResults(data.trades, data.statistics);
                showQueryStats(data.statistics);
            } else {
                console.error('查询失败:', data.error);
                alert('查询失败: ' + data.error);
            }
        })
        .catch(error => {
            console.error('查询请求失败:', error);
            alert('查询请求失败: ' + error.message);
        });
}

// 显示查询结果
function displayQueryResults(trades, statistics) {
    const tbody = document.querySelector('#historyTable tbody');
    tbody.innerHTML = '';

    if (trades && trades.length > 0) {
        trades.forEach(trade => {
            const profitClass = trade.profit >= 0 ? 'profit-positive' : 'profit-negative';
            const directionBadge = trade.direction === 'buy' ? 'bg-success' : 'bg-danger';
            const directionText = trade.direction === 'buy' ? '买入' : '卖出';

            const row = `
                <tr>
                    <td><span class="badge bg-primary">${trade.account_name || 'MT5'}</span></td>
                    <td>${new Date(trade.open_time).toLocaleString('zh-CN')}</td>
                    <td><strong>${trade.symbol}</strong></td>
                    <td><span class="badge ${directionBadge}">${directionText}</span></td>
                    <td>${trade.volume}</td>
                    <td>$${parseFloat(trade.open_price).toFixed(5)}</td>
                    <td>${trade.close_price ? '$' + parseFloat(trade.close_price).toFixed(5) : '-'}</td>
                    <td class="${profitClass}"><strong>$${parseFloat(trade.profit || 0).toFixed(2)}</strong></td>
                    <td><span class="badge ${trade.status === 'closed' ? 'bg-secondary' : 'bg-primary'}">${trade.status === 'closed' ? '已平仓' : '持仓中'}</span></td>
                </tr>
            `;
            tbody.innerHTML += row;
        });
    } else {
        tbody.innerHTML = '<tr><td colspan="9" class="text-center text-muted">没有找到符合条件的交易记录</td></tr>';
    }
}

// 显示查询统计信息
function showQueryStats(stats) {
    if (!stats) return;

    document.getElementById('totalTrades').textContent = stats.total_trades || 0;
    document.getElementById('totalProfit').textContent = `$${(stats.total_profit || 0).toFixed(2)}`;
    document.getElementById('totalProfit').className = stats.total_profit >= 0 ? 'text-success' : 'text-danger';
    document.getElementById('winRate').textContent = `${(stats.win_rate || 0).toFixed(1)}%`;
    document.getElementById('avgProfit').textContent = `$${(stats.avg_profit || 0).toFixed(2)}`;
    document.getElementById('avgProfit').className = stats.avg_profit >= 0 ? 'text-success' : 'text-danger';
    document.getElementById('maxProfit').textContent = `$${(stats.max_profit || 0).toFixed(2)}`;
    document.getElementById('maxLoss').textContent = `$${(stats.max_loss || 0).toFixed(2)}`;

    // 显示统计面板
    document.getElementById('queryStats').style.display = 'block';
}

// 重置查询条件
function resetTradeQuery() {
    document.getElementById('queryStartDate').value = '';
    document.getElementById('queryEndDate').value = '';
    document.getElementById('querySymbol').value = '';
    document.getElementById('queryDirection').value = '';
    document.getElementById('queryProfitStatus').value = '';
    document.getElementById('queryMinAmount').value = '';
    document.getElementById('queryMaxAmount').value = '';

    // 隐藏统计面板
    document.getElementById('queryStats').style.display = 'none';

    // 重新加载默认数据（仅显示当天数据）
    const selectedAccount = document.getElementById('accountSelect').value;
    if (selectedAccount) {
        loadTradeHistory(selectedAccount);
    } else {
        loadMT5TradeHistory(); // 现在只显示当天数据
    }
}

// 导出交易数据
function exportTradeData() {
    const queryParams = {
        start_date: document.getElementById('queryStartDate').value,
        end_date: document.getElementById('queryEndDate').value,
        symbol: document.getElementById('querySymbol').value,
        direction: document.getElementById('queryDirection').value,
        profit_status: document.getElementById('queryProfitStatus').value,
        min_amount: document.getElementById('queryMinAmount').value,
        max_amount: document.getElementById('queryMaxAmount').value,
        export: 'csv'
    };

    // 移除空值参数
    Object.keys(queryParams).forEach(key => {
        if (!queryParams[key]) {
            delete queryParams[key];
        }
    });

    const queryString = new URLSearchParams(queryParams).toString();
    const url = `/api/trading/export${queryString ? '?' + queryString : ''}`;

    // 创建下载链接
    const link = document.createElement('a');
    link.href = url;
    link.download = `trading_history_${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// 页面加载时设置默认日期
document.addEventListener('DOMContentLoaded', function() {
    // 设置默认查询时间为最近30天
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 30);

    document.getElementById('queryEndDate').value = endDate.toISOString().split('T')[0];
    document.getElementById('queryStartDate').value = startDate.toISOString().split('T')[0];
});

// ==================== 入场信息监控功能 ====================

let entryInfoInterval = null;
let currentTradingState = null;

// 开始入场信息监控
function startEntryInfoMonitoring(state) {
    console.log('🔍 开始入场信息监控...', state);

    currentTradingState = state;

    // 立即执行一次分析
    updateEntryInfo();

    // 设置定时更新（每30秒）
    if (entryInfoInterval) {
        clearInterval(entryInfoInterval);
    }

    entryInfoInterval = setInterval(() => {
        updateEntryInfo();
    }, 30000); // 30秒更新一次

    console.log('✅ 入场信息监控已启动');
}

// 停止入场信息监控
function stopEntryInfoMonitoring() {
    console.log('🛑 停止入场信息监控...');

    if (entryInfoInterval) {
        clearInterval(entryInfoInterval);
        entryInfoInterval = null;
    }

    currentTradingState = null;

    // 清空显示内容
    clearEntryInfoDisplay();

    console.log('✅ 入场信息监控已停止');
}

// 更新入场信息
async function updateEntryInfo() {
    if (!currentTradingState) {
        console.warn('⚠️ 当前交易状态为空，跳过入场信息更新');
        return;
    }

    try {
        console.log('🔄 更新入场信息...');

        // 更新分析时间
        updateAnalysisTime();

        // 获取策略分析结果
        const analysisResult = await fetchStrategyAnalysis();

        if (analysisResult && analysisResult.success) {
            // 更新入场信号
            updateEntrySignals(analysisResult.signals || []);

            // 更新策略模型状态
            updateStrategyStatus(analysisResult.strategy_status || {});

            // 更新市场数据
            updateMarketData(analysisResult.market_data || {});
        } else {
            console.warn('⚠️ 策略分析结果无效:', analysisResult);
            showAnalysisError(analysisResult?.error || '分析服务暂时不可用');
        }

    } catch (error) {
        console.error('❌ 更新入场信息失败:', error);
        showAnalysisError('网络连接异常，请检查连接状态');
    }
}

// 获取策略分析结果
async function fetchStrategyAnalysis() {
    const response = await fetch('/api/strategy-analysis', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            strategy_id: currentTradingState.strategyId,
            symbol: currentTradingState.tradingSymbol,
            account_type: 'real'
        })
    });

    return await response.json();
}

// 更新入场信号显示
function updateEntrySignals(signals) {
    const signalsList = document.getElementById('entrySignalsList');
    const signalCount = document.getElementById('entrySignalCount');

    if (!signalsList || !signalCount) return;

    // 过滤符合入场条件的信号
    const entrySignals = signals.filter(signal =>
        signal.action !== 'hold' && signal.confidence >= 0.6
    );

    signalCount.textContent = entrySignals.length;

    if (entrySignals.length === 0) {
        signalsList.innerHTML = `
            <div class="text-muted text-center py-2">
                <i class="fas fa-search"></i>
                暂无符合入场条件的信号
            </div>
        `;
    } else {
        // 按置信度排序，符合入场信号的显示在前列
        entrySignals.sort((a, b) => b.confidence - a.confidence);

        const signalsHtml = entrySignals.map(signal => `
            <div class="alert alert-${signal.action === 'buy' ? 'success' : 'warning'} py-2 mb-2">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <strong>${signal.action.toUpperCase()}</strong>
                        <span class="ms-2">${signal.symbol || currentTradingState.tradingSymbol}</span>
                    </div>
                    <div>
                        <span class="badge bg-primary">置信度: ${(signal.confidence * 100).toFixed(1)}%</span>
                    </div>
                </div>
                <small class="text-muted">
                    价格: ${signal.price?.toFixed(4) || '--'} |
                    时间: ${new Date(signal.timestamp).toLocaleTimeString()}
                </small>
            </div>
        `).join('');

        signalsList.innerHTML = signalsHtml;
    }
}

// 更新策略模型状态
function updateStrategyStatus(status) {
    const lastAnalysisTime = document.getElementById('lastAnalysisTime');
    const modelConfidence = document.getElementById('modelConfidence');
    const marketTrend = document.getElementById('marketTrend');

    if (lastAnalysisTime) {
        lastAnalysisTime.textContent = new Date().toLocaleTimeString();
    }

    if (modelConfidence && status.confidence !== undefined) {
        const confidence = (status.confidence * 100).toFixed(1);
        modelConfidence.textContent = `${confidence}%`;
        modelConfidence.className = `fw-bold ${confidence >= 70 ? 'text-success' : confidence >= 50 ? 'text-warning' : 'text-danger'}`;
    }

    if (marketTrend && status.trend) {
        marketTrend.textContent = status.trend;
        marketTrend.className = `fw-bold ${status.trend === '上涨' ? 'text-success' : status.trend === '下跌' ? 'text-danger' : 'text-muted'}`;
    }
}

// 更新市场数据
function updateMarketData(marketData) {
    const currentPrice = document.getElementById('currentPrice');
    const priceChange = document.getElementById('priceChange');
    const volatility = document.getElementById('volatility');

    if (currentPrice && marketData.price !== undefined) {
        currentPrice.textContent = marketData.price.toFixed(4);
    }

    if (priceChange && marketData.change !== undefined) {
        const change = marketData.change;
        const changePercent = marketData.changePercent || 0;
        priceChange.textContent = `${change >= 0 ? '+' : ''}${change.toFixed(4)} (${changePercent.toFixed(2)}%)`;
        priceChange.className = `fw-bold ${change >= 0 ? 'text-success' : 'text-danger'}`;
    }

    if (volatility && marketData.volatility !== undefined) {
        volatility.textContent = `${(marketData.volatility * 100).toFixed(2)}%`;
    }
}

// 更新分析时间
function updateAnalysisTime() {
    const analysisUpdateTime = document.getElementById('analysisUpdateTime');
    if (analysisUpdateTime) {
        analysisUpdateTime.textContent = new Date().toLocaleTimeString();
    }
}

// 显示分析错误
function showAnalysisError(errorMessage) {
    const signalsList = document.getElementById('entrySignalsList');
    if (signalsList) {
        signalsList.innerHTML = `
            <div class="text-danger text-center py-2">
                <i class="fas fa-exclamation-triangle"></i>
                ${errorMessage}
            </div>
        `;
    }
}

// 清空入场信息显示
function clearEntryInfoDisplay() {
    const elements = [
        'entrySignalsList',
        'lastAnalysisTime',
        'modelConfidence',
        'marketTrend',
        'currentPrice',
        'priceChange',
        'volatility',
        'analysisUpdateTime'
    ];

    elements.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = '--';
            element.className = element.className.replace(/text-(success|danger|warning|primary|info)/g, '');
        }
    });

    const signalCount = document.getElementById('entrySignalCount');
    if (signalCount) {
        signalCount.textContent = '0';
    }
}

</script>

<!-- 一键平仓确认模态框 -->
<div class="modal fade" id="closeAllPositionsModal" tabindex="-1" aria-labelledby="closeAllPositionsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="closeAllPositionsModalLabel">
                    <i class="fas fa-exclamation-triangle"></i>
                    一键平仓确认
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- 风险警告 -->
                <div class="alert alert-danger">
                    <h6><i class="fas fa-skull-crossbones"></i> 高风险操作警告</h6>
                    <ul class="mb-0">
                        <li><strong>不可撤销</strong>：此操作将立即平仓所有持仓，无法撤销</li>
                        <li><strong>市场风险</strong>：可能因市场滑点造成额外损失</li>
                        <li><strong>时机风险</strong>：可能错过后续盈利机会</li>
                        <li><strong>技术风险</strong>：网络延迟可能导致部分订单失败</li>
                    </ul>
                </div>

                <!-- 当前持仓概览 -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-list"></i> 当前持仓概览</h6>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-3">
                                <div class="border-end">
                                    <h6 class="text-muted">持仓数量</h6>
                                    <h5 id="totalPositions" class="text-primary">0</h5>
                                </div>
                            </div>
                            <div class="col-3">
                                <div class="border-end">
                                    <h6 class="text-muted">总浮动盈亏</h6>
                                    <h5 id="totalUnrealizedPnL" class="text-success">$0.00</h5>
                                </div>
                            </div>
                            <div class="col-3">
                                <div class="border-end">
                                    <h6 class="text-muted">多头持仓</h6>
                                    <h5 id="longPositions" class="text-success">0</h5>
                                </div>
                            </div>
                            <div class="col-3">
                                <h6 class="text-muted">空头持仓</h6>
                                <h5 id="shortPositions" class="text-danger">0</h5>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 密码验证 -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-lock"></i> 安全验证</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="closeAllPassword" class="form-label">
                                请输入安全密码以确认操作：
                            </label>
                            <input type="password" class="form-control" id="closeAllPassword"
                                   placeholder="输入密码确认一键平仓操作" maxlength="10">
                            <div class="form-text">
                                <i class="fas fa-info-circle"></i>
                                此密码用于防止误操作，请联系管理员获取
                            </div>
                        </div>

                        <!-- 二次确认 -->
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="confirmCloseAll">
                            <label class="form-check-label" for="confirmCloseAll">
                                我已充分了解风险，确认要执行一键平仓操作
                            </label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times"></i>
                    取消
                </button>
                <button type="button" class="btn btn-danger" onclick="executeCloseAllPositions()" id="executeCloseAllBtn" disabled>
                    <i class="fas fa-bomb"></i>
                    确认执行一键平仓
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 一键平仓执行进度模态框 -->
<div class="modal fade" id="closeAllProgressModal" tabindex="-1" aria-labelledby="closeAllProgressModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title" id="closeAllProgressModalLabel">
                    <i class="fas fa-cog fa-spin"></i>
                    正在执行一键平仓
                </h5>
            </div>
            <div class="modal-body">
                <div class="text-center mb-3">
                    <div class="spinner-border text-warning" role="status">
                        <span class="visually-hidden">执行中...</span>
                    </div>
                </div>

                <div class="progress mb-3">
                    <div class="progress-bar progress-bar-striped progress-bar-animated"
                         role="progressbar" style="width: 0%" id="closeAllProgress">
                    </div>
                </div>

                <div id="closeAllStatus">
                    <p class="text-center mb-0">正在准备平仓操作...</p>
                </div>

                <!-- 详细进度 -->
                <div class="mt-3">
                    <small class="text-muted">执行详情：</small>
                    <div id="closeAllDetails" class="small" style="max-height: 200px; overflow-y: auto;">
                        <!-- 动态添加执行详情 -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}
