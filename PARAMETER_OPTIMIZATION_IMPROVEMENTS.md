# 参数优化功能改进说明

## 概述

本次更新对模型推理中的参数优化回测功能进行了三项重要改进：

1. **增加盈利亏损单数字段** - 在参数组合排名列表中显示盈利单数和亏损单数
2. **优化排序逻辑避免重复执行** - 切换排序方式时不再重新执行整个优化过程
3. **增加结果导出下载功能** - 支持将优化结果导出为CSV文件

## 功能详情

### 1. 盈利亏损单数字段

#### 改进内容
- 在参数组合排名表格中新增"盈利单"和"亏损单"两列
- 显示每个参数组合的具体盈利交易数量和亏损交易数量
- 使用绿色显示盈利单数，红色显示亏损单数，便于快速识别

#### 技术实现
- **前端**: 修改`templates/model_inference.html`中的表格结构
- **后端**: 在`services/deep_learning_service.py`中确保统计数据包含`winning_trades`和`losing_trades`字段

#### 使用方法
1. 进入"模型推理"页面
2. 配置参数并点击"参数优化"
3. 在结果表格中可以看到新增的"盈利单"和"亏损单"列

### 2. 优化排序逻辑

#### 问题解决
- **原问题**: 切换排序方式时会重新执行整个参数优化过程，耗时较长
- **解决方案**: 在一次优化执行时计算所有排序方式的得分，切换时仅重新排序

#### 技术实现
- 新增`_calculate_all_scores()`方法，一次性计算所有风险偏好的得分
- 新增`resort_optimization_results()`方法，快速重新排序已有结果
- 修改API端点支持`resort_only`参数，区分完整优化和快速排序

#### 性能提升
- 排序切换时间从几分钟缩短到几秒钟
- 避免重复的回测计算，提高用户体验

#### 使用方法
1. 执行一次完整的参数优化
2. 在结果显示区域的"排序方式"下拉框中选择不同的排序偏好
3. 系统会快速重新排序，无需等待重新计算

### 3. 结果导出下载功能

#### 功能特点
- 支持导出CSV格式文件
- 包含所有参数组合的详细统计数据
- 自动生成带时间戳的文件名
- 包含优化摘要信息

#### 导出内容
CSV文件包含以下字段：
- 基本信息：排名、评分、收益率、胜率、盈利单数、亏损单数、总交易数
- 参数配置：手数、止损、止盈、置信度、悬崖勒马、移动止损等
- 详细统计：最大回撤、夏普比率、盈利因子、平均盈亏等
- 摘要信息：模型信息、优化周期、风险偏好、导出时间等

#### 使用方法
1. 执行参数优化并查看结果
2. 在排序方式选择区域点击"导出结果"按钮
3. 浏览器会自动下载CSV文件

## API 变更

### 新增API端点

#### `/api/deep-learning/export-optimization-results` (POST)
导出参数优化结果为CSV文件

**请求参数:**
```json
{
    "model_id": "模型ID",
    "symbol": "交易品种",
    "timeframe": "时间框架",
    "optimization_period": "优化周期",
    "risk_preference": "风险偏好"
}
```

**响应:**
- 成功：返回CSV文件下载
- 失败：返回错误信息JSON

### 修改的API端点

#### `/api/deep-learning/parameter-optimization` (POST)
新增`resort_only`参数支持快速重新排序

**新增参数:**
- `resort_only` (boolean): 是否仅重新排序，不重新执行回测

## 数据库变更

### 优化结果存储
- 在优化结果中新增`all_scores`字段，存储所有排序方式的得分
- 支持快速排序切换而无需重新计算

## 文件变更清单

### 前端文件
- `templates/model_inference.html`
  - 新增盈利单和亏损单表格列
  - 新增导出按钮
  - 修改排序切换逻辑
  - 新增导出功能JavaScript代码

### 后端文件
- `routes.py`
  - 修改参数优化API支持`resort_only`参数
  - 新增导出API端点
  - 新增`make_response`导入

- `services/deep_learning_service.py`
  - 新增`_calculate_all_scores()`方法
  - 新增`resort_optimization_results()`方法
  - 新增`export_optimization_results_csv()`方法
  - 新增`_get_risk_preference_name()`方法
  - 修改优化结果数据结构

## 使用建议

### 最佳实践
1. **首次优化**: 使用完整的参数优化功能，建议选择"一周数据"进行快速测试
2. **排序切换**: 在有优化结果后，可以快速切换不同的风险偏好查看排序
3. **结果分析**: 结合盈利单数、亏损单数和其他统计指标综合评估参数组合
4. **数据导出**: 定期导出优化结果进行离线分析和记录

### 注意事项
1. 导出功能需要先执行参数优化
2. 快速排序功能依赖于之前的完整优化结果
3. CSV文件采用UTF-8编码，建议使用支持该编码的软件打开
4. 大量参数组合的导出文件可能较大，请注意存储空间

## 故障排除

### 常见问题
1. **导出按钮无响应**: 确保已执行参数优化并有结果显示
2. **排序切换失败**: 检查是否有完整的优化结果数据
3. **CSV文件乱码**: 使用支持UTF-8编码的软件打开文件

### 日志查看
相关日志会记录在应用日志中，关键字包括：
- `参数优化`
- `重新排序`
- `导出结果`

## 版本兼容性

- 兼容现有的参数优化功能
- 新功能为增量更新，不影响原有功能
- 建议在测试环境验证后部署到生产环境
