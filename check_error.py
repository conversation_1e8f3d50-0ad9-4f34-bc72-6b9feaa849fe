import sqlite3
import json

conn = sqlite3.connect('trading_system.db')
cursor = conn.cursor()

cursor.execute("SELECT logs FROM training_tasks WHERE status = 'failed' ORDER BY created_at DESC LIMIT 1")
result = cursor.fetchone()

if result and result[0]:
    try:
        log_data = json.loads(result[0])
        print('错误信息:', log_data.get('error', '无'))
        print('阶段:', log_data.get('stage', '无'))
        print('消息:', log_data.get('message', '无'))
    except:
        print('原始日志:', result[0])
else:
    print('没有找到失败任务的日志')

conn.close()
