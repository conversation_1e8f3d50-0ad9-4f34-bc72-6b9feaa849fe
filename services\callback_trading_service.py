"""
回调交易服务
实现基于价格回调的交易策略
"""

import logging
import threading
import time
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import pandas as pd
import numpy as np

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CallbackTradingService:
    """回调交易服务"""
    
    def __init__(self):
        self.is_running = False
        self.trading_thread = None
        self.current_config = None
        # 使用主数据库，避免路径问题
        self.db_path = 'trading_system.db'
        self.start_time = None
        self.today_trades = 0
        self.last_trade_date = None

        # 初始化数据库
        self._init_database()
    
    def _init_database(self):
        """初始化数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 创建回调交易记录表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS callback_trades (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol TEXT NOT NULL,
                    type TEXT NOT NULL,
                    volume REAL NOT NULL,
                    open_price REAL NOT NULL,
                    close_price REAL,
                    stop_loss REAL,
                    take_profit REAL,
                    open_time TIMESTAMP NOT NULL,
                    close_time TIMESTAMP,
                    profit REAL DEFAULT 0,
                    status TEXT DEFAULT 'open',
                    strategy_params TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建回测结果表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS callback_backtest_results (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol TEXT NOT NULL,
                    timeframe TEXT NOT NULL,
                    start_date DATE NOT NULL,
                    end_date DATE NOT NULL,
                    total_trades INTEGER DEFAULT 0,
                    winning_trades INTEGER DEFAULT 0,
                    losing_trades INTEGER DEFAULT 0,
                    win_rate REAL DEFAULT 0,
                    total_profit REAL DEFAULT 0,
                    max_drawdown REAL DEFAULT 0,
                    strategy_params TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
            conn.close()
            
            logger.info("✅ 回调交易数据库初始化完成")
            
        except Exception as e:
            logger.error(f"❌ 初始化数据库失败: {e}")
    
    def start_trading(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """启动回调交易"""
        try:
            if self.is_running:
                return {'success': False, 'error': '回调交易已在运行中'}
            
            # 验证配置
            required_fields = ['symbol', 'timeframe', 'min_lot_size', 'max_lot_size', 
                             'trend_period', 'callback_percent', 'stop_loss_percent', 
                             'take_profit_percent', 'daily_trade_limit']
            
            for field in required_fields:
                if field not in config:
                    return {'success': False, 'error': f'缺少必要参数: {field}'}
            
            # 检查MT5连接
            from services.mt5_service import mt5_service
            if not mt5_service.connected:
                return {'success': False, 'error': 'MT5未连接，请先连接MT5终端'}
            
            self.current_config = config
            self.is_running = True
            self.start_time = datetime.now()
            self.today_trades = 0
            self.last_trade_date = datetime.now().date()
            
            # 启动交易线程
            self.trading_thread = threading.Thread(target=self._trading_loop, daemon=True)
            self.trading_thread.start()
            
            logger.info(f"✅ 回调交易已启动: {config['symbol']}")
            return {'success': True, 'message': '回调交易启动成功'}
            
        except Exception as e:
            logger.error(f"❌ 启动回调交易失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def stop_trading(self) -> Dict[str, Any]:
        """停止回调交易"""
        try:
            if not self.is_running:
                return {'success': False, 'error': '回调交易未在运行'}
            
            self.is_running = False
            
            if self.trading_thread and self.trading_thread.is_alive():
                self.trading_thread.join(timeout=5)
            
            logger.info("✅ 回调交易已停止")
            return {'success': True, 'message': '回调交易停止成功'}
            
        except Exception as e:
            logger.error(f"❌ 停止回调交易失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def get_status(self) -> Dict[str, Any]:
        """获取交易状态"""
        try:
            if not self.is_running:
                return {
                    'success': True,
                    'status': {'running': False},
                    'monitoring': {}
                }
            
            # 计算运行时间
            running_time = str(datetime.now() - self.start_time).split('.')[0] if self.start_time else '00:00:00'
            
            # 重置每日交易计数
            current_date = datetime.now().date()
            if self.last_trade_date != current_date:
                self.today_trades = 0
                self.last_trade_date = current_date
            
            # 获取当前市场数据
            monitoring_data = self._get_monitoring_data()
            
            return {
                'success': True,
                'status': {
                    'running': True,
                    'running_time': running_time,
                    'today_trades': self.today_trades,
                    'symbol': self.current_config['symbol'],
                    'timeframe': self.current_config['timeframe']
                },
                'monitoring': monitoring_data
            }
            
        except Exception as e:
            logger.error(f"❌ 获取交易状态失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def _get_monitoring_data(self) -> Dict[str, Any]:
        """获取监控数据"""
        try:
            from services.mt5_service import mt5_service
            import MetaTrader5 as mt5
            
            symbol = self.current_config['symbol']
            
            # 获取当前价格
            tick = mt5.symbol_info_tick(symbol)
            current_price = tick.bid if tick else 0
            
            # 获取历史数据分析趋势
            timeframe_map = {
                'H1': mt5.TIMEFRAME_H1,
                'H4': mt5.TIMEFRAME_H4,
                'D1': mt5.TIMEFRAME_D1
            }
            
            timeframe = timeframe_map.get(self.current_config['timeframe'], mt5.TIMEFRAME_H1)
            rates = mt5.copy_rates_from_pos(symbol, timeframe, 0, 100)
            
            if rates is not None and len(rates) > 0:
                df = pd.DataFrame(rates)
                trend = self._analyze_trend(df)
                callback_status = self._analyze_callback_opportunity(df)
            else:
                trend = 'unknown'
                callback_status = '数据获取失败'
            
            return {
                'current_price': f"{current_price:.4f}" if current_price > 0 else '--',
                'trend': trend,
                'callback_status': callback_status,
                'next_check': (datetime.now() + timedelta(minutes=5)).strftime('%H:%M:%S')
            }
            
        except Exception as e:
            logger.error(f"❌ 获取监控数据失败: {e}")
            return {
                'current_price': '--',
                'trend': 'unknown',
                'callback_status': '监控异常',
                'next_check': '--'
            }
    
    def _analyze_trend(self, df: pd.DataFrame) -> str:
        """分析趋势方向"""
        try:
            trend_period = self.current_config['trend_period']
            
            if len(df) < trend_period:
                return 'unknown'
            
            # 计算移动平均线
            df['ma'] = df['close'].rolling(window=trend_period).mean()
            
            # 获取最近的价格和移动平均线
            current_price = df.iloc[-1]['close']
            current_ma = df.iloc[-1]['ma']
            prev_ma = df.iloc[-2]['ma'] if len(df) > 1 else current_ma
            
            # 判断趋势
            if current_price > current_ma and current_ma > prev_ma:
                return 'up'
            elif current_price < current_ma and current_ma < prev_ma:
                return 'down'
            else:
                return 'sideways'
                
        except Exception as e:
            logger.error(f"❌ 分析趋势失败: {e}")
            return 'unknown'
    
    def _analyze_callback_opportunity(self, df: pd.DataFrame) -> str:
        """分析回调机会"""
        try:
            callback_percent = self.current_config['callback_percent']
            
            if len(df) < 20:
                return '数据不足'
            
            # 获取最近的高点和低点
            recent_high = df.tail(20)['high'].max()
            recent_low = df.tail(20)['low'].min()
            current_price = df.iloc[-1]['close']
            
            # 计算回调幅度
            if recent_high > recent_low:
                callback_from_high = (recent_high - current_price) / recent_high * 100
                callback_from_low = (current_price - recent_low) / recent_low * 100
                
                if callback_from_high >= callback_percent:
                    return f'从高点回调 {callback_from_high:.1f}%'
                elif callback_from_low >= callback_percent:
                    return f'从低点反弹 {callback_from_low:.1f}%'
                else:
                    return '等待回调机会'
            else:
                return '价格区间过小'
                
        except Exception as e:
            logger.error(f"❌ 分析回调机会失败: {e}")
            return '分析异常'
    
    def _trading_loop(self):
        """交易主循环"""
        logger.info("🔄 回调交易循环开始...")
        
        while self.is_running:
            try:
                # 检查每日交易限制
                current_date = datetime.now().date()
                if self.last_trade_date != current_date:
                    self.today_trades = 0
                    self.last_trade_date = current_date
                
                if self.today_trades >= self.current_config['daily_trade_limit']:
                    logger.info(f"📊 今日交易次数已达限制: {self.today_trades}")
                    time.sleep(300)  # 5分钟后再检查
                    continue
                
                # 分析市场并执行交易
                self._analyze_and_trade()
                
                # 等待下一个检查周期（5分钟）
                time.sleep(300)
                
            except Exception as e:
                logger.error(f"❌ 交易循环异常: {e}")
                time.sleep(60)  # 出错后等待1分钟
        
        logger.info("🛑 回调交易循环结束")
    
    def _analyze_and_trade(self):
        """分析市场并执行交易"""
        try:
            from services.mt5_service import mt5_service
            import MetaTrader5 as mt5
            
            symbol = self.current_config['symbol']
            
            # 获取市场数据
            timeframe_map = {
                'H1': mt5.TIMEFRAME_H1,
                'H4': mt5.TIMEFRAME_H4,
                'D1': mt5.TIMEFRAME_D1
            }
            
            timeframe = timeframe_map.get(self.current_config['timeframe'], mt5.TIMEFRAME_H1)
            rates = mt5.copy_rates_from_pos(symbol, timeframe, 0, 100)
            
            if rates is None or len(rates) == 0:
                logger.warning(f"⚠️ 无法获取 {symbol} 的市场数据")
                return
            
            df = pd.DataFrame(rates)
            df['time'] = pd.to_datetime(df['time'], unit='s')
            
            # 分析交易信号
            signal = self._generate_callback_signal(df)
            
            if signal and signal['action'] != 'hold':
                logger.info(f"🎯 检测到回调交易信号: {signal['action']} (置信度: {signal['confidence']:.3f})")
                
                # 执行交易
                result = self._execute_trade(signal)
                if result['success']:
                    self.today_trades += 1
                    logger.info(f"✅ 回调交易执行成功: {result['message']}")
                else:
                    logger.error(f"❌ 回调交易执行失败: {result['error']}")
            else:
                logger.info("📊 暂无回调交易机会")
                
        except Exception as e:
            logger.error(f"❌ 分析和交易失败: {e}")
    
    def _generate_callback_signal(self, df: pd.DataFrame) -> Optional[Dict[str, Any]]:
        """生成回调交易信号"""
        try:
            trend_period = self.current_config['trend_period']
            callback_percent = self.current_config['callback_percent']
            
            if len(df) < trend_period + 20:
                return None
            
            # 计算技术指标
            df['ma'] = df['close'].rolling(window=trend_period).mean()
            df['high_20'] = df['high'].rolling(window=20).max()
            df['low_20'] = df['low'].rolling(window=20).min()
            
            current_price = df.iloc[-1]['close']
            current_ma = df.iloc[-1]['ma']
            recent_high = df.iloc[-1]['high_20']
            recent_low = df.iloc[-1]['low_20']
            
            # 判断主趋势
            if current_ma > df.iloc[-5]['ma']:  # 上升趋势
                # 检查是否有足够的回调
                callback_from_high = (recent_high - current_price) / recent_high * 100
                
                if callback_from_high >= callback_percent and current_price > current_ma * 0.99:
                    # 回调后接近支撑，买入信号
                    return {
                        'action': 'buy',
                        'confidence': min(0.8, callback_from_high / callback_percent * 0.6),
                        'price': current_price,
                        'reason': f'上升趋势中回调{callback_from_high:.1f}%后的买入机会'
                    }
            
            elif current_ma < df.iloc[-5]['ma']:  # 下降趋势
                # 检查是否有足够的反弹
                callback_from_low = (current_price - recent_low) / recent_low * 100
                
                if callback_from_low >= callback_percent and current_price < current_ma * 1.01:
                    # 反弹后接近阻力，卖出信号
                    return {
                        'action': 'sell',
                        'confidence': min(0.8, callback_from_low / callback_percent * 0.6),
                        'price': current_price,
                        'reason': f'下降趋势中反弹{callback_from_low:.1f}%后的卖出机会'
                    }
            
            return {'action': 'hold', 'confidence': 0, 'reason': '无明确回调交易机会'}
            
        except Exception as e:
            logger.error(f"❌ 生成回调信号失败: {e}")
            return None
    
    def _execute_trade(self, signal: Dict[str, Any]) -> Dict[str, Any]:
        """执行交易"""
        try:
            from services.mt5_service import mt5_service
            
            symbol = self.current_config['symbol']
            action = signal['action']
            price = signal['price']
            
            # 计算手数
            lot_size = self.current_config['min_lot_size']
            
            # 计算止损止盈
            stop_loss_percent = self.current_config['stop_loss_percent'] / 100
            take_profit_percent = self.current_config['take_profit_percent'] / 100
            
            if action == 'buy':
                stop_loss = price * (1 - stop_loss_percent)
                take_profit = price * (1 + take_profit_percent)
            else:
                stop_loss = price * (1 + stop_loss_percent)
                take_profit = price * (1 - take_profit_percent)
            
            # 执行交易
            result = mt5_service.place_order(
                symbol=symbol,
                order_type=action,
                volume=lot_size,
                stop_loss=stop_loss,
                take_profit=take_profit,
                comment=f"CALLBACK_{action.upper()}"
            )
            
            if result['success']:
                # 保存交易记录
                self._save_trade_record(
                    symbol=symbol,
                    trade_type=action,
                    volume=lot_size,
                    open_price=price,
                    stop_loss=stop_loss,
                    take_profit=take_profit,
                    strategy_params=str(self.current_config)
                )
                
                return {
                    'success': True,
                    'message': f'回调交易成功: {action} {symbol} {lot_size}手'
                }
            else:
                return {
                    'success': False,
                    'error': result['error']
                }
                
        except Exception as e:
            logger.error(f"❌ 执行交易失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def _save_trade_record(self, **kwargs):
        """保存交易记录"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO callback_trades 
                (symbol, type, volume, open_price, stop_loss, take_profit, open_time, strategy_params)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                kwargs['symbol'],
                kwargs['trade_type'],
                kwargs['volume'],
                kwargs['open_price'],
                kwargs['stop_loss'],
                kwargs['take_profit'],
                datetime.now(),
                kwargs['strategy_params']
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"❌ 保存交易记录失败: {e}")
    
    def get_trade_history(self) -> Dict[str, Any]:
        """获取交易历史"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT * FROM callback_trades 
                ORDER BY open_time DESC 
                LIMIT 100
            ''')
            
            trades = []
            for row in cursor.fetchall():
                trades.append({
                    'id': row[0],
                    'symbol': row[1],
                    'type': row[2],
                    'volume': row[3],
                    'open_price': row[4],
                    'close_price': row[5],
                    'stop_loss': row[6],
                    'take_profit': row[7],
                    'open_time': row[8],
                    'close_time': row[9],
                    'profit': row[10] or 0,
                    'status': row[11]
                })
            
            conn.close()
            
            return {'success': True, 'trades': trades}
            
        except Exception as e:
            logger.error(f"❌ 获取交易历史失败: {e}")
            return {'success': False, 'error': str(e)}

# 全局回调交易服务实例
callback_trading_service = CallbackTradingService()
