#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MateTrade4 启动脚本
快速启动应用的便捷脚本
"""

import os
import sys
import subprocess
import webbrowser
import time
from pathlib import Path

def check_requirements():
    """检查依赖是否已安装"""
    try:
        import flask
        import flask_sqlalchemy
        import flask_login
        import yfinance
        import pandas
        import numpy
        import requests
        print("✓ 所有依赖包已安装")
        return True
    except ImportError as e:
        print(f"❌ 缺少依赖包: {e}")
        return False

def install_requirements():
    """安装依赖包"""
    print("正在安装依赖包...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                      check=True, capture_output=True)
        print("✓ 依赖包安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖包安装失败: {e}")
        return False

def check_database():
    """检查数据库是否存在"""
    db_path = Path("instance/matetrade4.db")
    if db_path.exists():
        print("✓ 数据库已存在")
        return True
    else:
        print("ℹ️ 数据库不存在，将自动创建")
        return False

def init_sample_data():
    """询问是否初始化示例数据"""
    response = input("是否要初始化示例数据？(y/n): ").lower().strip()
    if response in ['y', 'yes', '是']:
        try:
            subprocess.run([sys.executable, "init_data.py"], check=True)
            print("✓ 示例数据初始化完成")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ 示例数据初始化失败: {e}")
            return False
    return True

def start_application():
    """启动应用"""
    print("正在启动 MateTrade4...")
    
    # 启动Flask应用
    try:
        # 在新进程中启动应用
        process = subprocess.Popen([sys.executable, "app.py"])
        
        # 等待应用启动
        print("等待应用启动...")
        time.sleep(3)
        
        # 尝试打开浏览器
        try:
            webbrowser.open('http://localhost:5000')
            print("✓ 浏览器已打开")
        except Exception as e:
            print(f"⚠️ 无法自动打开浏览器: {e}")
            print("请手动访问: http://localhost:5000")
        
        print("\n" + "="*50)
        print("MateTrade4 AI自动交易系统已启动！")
        print("="*50)
        print("访问地址: http://localhost:5000")
        print("默认登录: admin / admin123")
        print("\n按 Ctrl+C 停止应用")
        print("="*50)
        
        # 等待用户中断
        try:
            process.wait()
        except KeyboardInterrupt:
            print("\n正在停止应用...")
            process.terminate()
            process.wait()
            print("✓ 应用已停止")
            
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False
    
    return True

def show_welcome():
    """显示欢迎信息"""
    print("\n" + "="*60)
    print("🚀 欢迎使用 MateTrade4 AI自动交易系统")
    print("="*60)
    print("📊 功能特色:")
    print("  • 集成5个AI大模型")
    print("  • 真实市场数据分析")
    print("  • 策略回测与优化")
    print("  • 自动交易执行")
    print("  • 风险管理工具")
    print("\n⚠️  风险提示:")
    print("  • 本系统仅供学习研究使用")
    print("  • 实盘交易存在资金损失风险")
    print("  • 请在充分了解风险后使用")
    print("="*60)

def main():
    """主函数"""
    show_welcome()
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        sys.exit(1)
    
    print(f"✓ Python版本: {sys.version}")
    
    # 检查依赖
    if not check_requirements():
        print("\n正在安装依赖包...")
        if not install_requirements():
            print("❌ 无法安装依赖包，请手动运行: pip install -r requirements.txt")
            sys.exit(1)
    
    # 检查数据库
    db_exists = check_database()
    
    # 如果数据库不存在，询问是否初始化示例数据
    if not db_exists:
        init_sample_data()
    
    # 启动应用
    start_application()

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 感谢使用 MateTrade4！")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ 启动过程中发生错误: {e}")
        print("请检查错误信息或联系技术支持")
        sys.exit(1)
