#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MetaTrader4 兼容连接服务
使用MetaTrader5库连接MT4软件
参考: https://blog.51cto.com/u_16213384/********
"""

import MetaTrader5 as mt5
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import logging
import time

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MT4Service:
    """MetaTrader4 兼容服务类"""
    
    def __init__(self):
        self.connected = False
        self.account_info = None
        self.symbols_info = {}
        self.mt4_mode = False
        
    def connect_mt4(self, path: str = None) -> bool:
        """
        连接到MT4终端
        
        Args:
            path: MT4终端路径 (可选)
            
        Returns:
            bool: 连接是否成功
        """
        try:
            logger.info("尝试连接MT4终端...")
            
            # 如果指定了路径，使用指定路径初始化
            if path:
                if not mt5.initialize(path):
                    logger.error(f"使用路径 {path} 初始化MT4失败: {mt5.last_error()}")
                    return False
            else:
                # 尝试自动检测MT4
                if not mt5.initialize():
                    logger.error(f"自动初始化MT4失败: {mt5.last_error()}")
                    return self._try_common_mt4_paths()
            
            # 检查终端信息
            terminal_info = mt5.terminal_info()
            if terminal_info:
                logger.info(f"连接到终端: {terminal_info.name}")
                logger.info(f"终端路径: {terminal_info.path}")
                logger.info(f"终端版本: {terminal_info.build}")
                
                # 检查是否为MT4
                if "MetaTrader 4" in terminal_info.name or "MT4" in terminal_info.name:
                    self.mt4_mode = True
                    logger.info("检测到MT4终端")
                else:
                    logger.info("检测到MT5终端")
            
            # 获取账户信息
            self.account_info = mt5.account_info()
            if self.account_info is None:
                logger.warning("无法获取账户信息，可能需要在终端中登录账户")
                # 即使没有账户信息，也认为连接成功
                self.connected = True
                return True
            
            self.connected = True
            logger.info(f"成功连接，账户: {self.account_info.login}")
            return True
            
        except Exception as e:
            logger.error(f"连接MT4时发生错误: {e}")
            return False
    
    def _try_common_mt4_paths(self) -> bool:
        """尝试常见的MT4安装路径"""
        common_paths = [
            r"C:\Program Files\MetaTrader 4\terminal.exe",
            r"C:\Program Files (x86)\MetaTrader 4\terminal.exe",
            r"C:\Program Files\MetaTrader 4\terminal64.exe",
            r"C:\Program Files (x86)\MetaTrader 4\terminal64.exe",
        ]
        
        import os
        for path in common_paths:
            if os.path.exists(path):
                logger.info(f"尝试路径: {path}")
                if mt5.initialize(path):
                    logger.info(f"成功使用路径: {path}")
                    return True
        
        logger.error("未找到可用的MT4安装路径")
        return False
    
    def get_account_info(self) -> Dict:
        """
        获取账户信息
        
        Returns:
            Dict: 账户信息字典
        """
        if not self.connected:
            return {"error": "未连接到MT4"}
        
        try:
            account = mt5.account_info()
            if account is None:
                return {
                    "error": "无法获取账户信息",
                    "suggestion": "请在MT4终端中登录账户"
                }
            
            return {
                "login": account.login,
                "trade_mode": account.trade_mode,
                "balance": account.balance,
                "equity": account.equity,
                "margin": account.margin,
                "free_margin": account.margin_free,
                "margin_level": account.margin_level,
                "currency": account.currency,
                "profit": account.profit,
                "credit": account.credit,
                "company": account.company,
                "server": account.server,
                "mt4_mode": self.mt4_mode
            }
        except Exception as e:
            logger.error(f"获取账户信息失败: {e}")
            return {"error": str(e)}
    
    def get_symbols(self) -> List[str]:
        """
        获取所有可用交易品种
        
        Returns:
            List[str]: 交易品种列表
        """
        if not self.connected:
            return []
        
        try:
            symbols = mt5.symbols_get()
            if symbols is None:
                logger.warning("无法获取交易品种")
                return []
            
            # MT4通常有更多的品种，过滤可见的
            visible_symbols = [symbol.name for symbol in symbols if symbol.visible]
            logger.info(f"获取到 {len(visible_symbols)} 个可用交易品种")
            
            return visible_symbols
            
        except Exception as e:
            logger.error(f"获取交易品种失败: {e}")
            return []
    
    def get_symbol_info(self, symbol: str) -> Dict:
        """
        获取交易品种信息
        
        Args:
            symbol: 交易品种名称
            
        Returns:
            Dict: 品种信息
        """
        if not self.connected:
            return {"error": "未连接到MT4"}
        
        try:
            symbol_info = mt5.symbol_info(symbol)
            if symbol_info is None:
                return {"error": f"品种 {symbol} 不存在"}
            
            return {
                "name": symbol_info.name,
                "description": symbol_info.description,
                "currency_base": symbol_info.currency_base,
                "currency_profit": symbol_info.currency_profit,
                "point": symbol_info.point,
                "digits": symbol_info.digits,
                "spread": symbol_info.spread,
                "volume_min": symbol_info.volume_min,
                "volume_max": symbol_info.volume_max,
                "volume_step": symbol_info.volume_step,
                "bid": symbol_info.bid,
                "ask": symbol_info.ask,
                "visible": symbol_info.visible
            }
            
        except Exception as e:
            logger.error(f"获取品种信息失败: {e}")
            return {"error": str(e)}
    
    def get_rates(self, symbol: str, timeframe: str = "H1", count: int = 1000) -> pd.DataFrame:
        """
        获取历史价格数据
        
        Args:
            symbol: 交易品种
            timeframe: 时间框架
            count: 数据条数
            
        Returns:
            pd.DataFrame: 价格数据
        """
        if not self.connected:
            return pd.DataFrame()
        
        try:
            # 时间框架映射
            tf_map = {
                'M1': mt5.TIMEFRAME_M1,
                'M5': mt5.TIMEFRAME_M5,
                'M15': mt5.TIMEFRAME_M15,
                'M30': mt5.TIMEFRAME_M30,
                'H1': mt5.TIMEFRAME_H1,
                'H4': mt5.TIMEFRAME_H4,
                'D1': mt5.TIMEFRAME_D1,
                'W1': mt5.TIMEFRAME_W1,
                'MN1': mt5.TIMEFRAME_MN1
            }
            
            tf = tf_map.get(timeframe, mt5.TIMEFRAME_H1)
            
            # 获取价格数据
            rates = mt5.copy_rates_from_pos(symbol, tf, 0, count)
            if rates is None:
                logger.error(f"无法获取 {symbol} 的价格数据")
                return pd.DataFrame()
            
            # 转换为DataFrame
            df = pd.DataFrame(rates)
            df['time'] = pd.to_datetime(df['time'], unit='s')
            df.set_index('time', inplace=True)
            
            # 重命名列以保持一致性
            df.columns = ['Open', 'High', 'Low', 'Close', 'Volume', 'Spread', 'Real_Volume']
            
            logger.info(f"获取到 {len(df)} 条 {symbol} 价格数据")
            return df[['Open', 'High', 'Low', 'Close', 'Volume']]
            
        except Exception as e:
            logger.error(f"获取价格数据失败: {e}")
            return pd.DataFrame()
    
    def send_order(self, symbol: str, order_type: str, volume: float,
                   price: float = 0.0, sl: float = 0.0, tp: float = 0.0,
                   comment: str = "Python order") -> Dict:
        """
        发送交易订单
        
        Args:
            symbol: 交易品种
            order_type: 订单类型 ('buy', 'sell', 'buy_limit', 'sell_limit', etc.)
            volume: 交易量
            price: 价格 (市价单可为0)
            sl: 止损价格
            tp: 止盈价格
            comment: 订单备注
            
        Returns:
            Dict: 订单结果
        """
        if not self.connected:
            return {"error": "未连接到MT4"}
        
        try:
            # 订单类型映射
            order_type_map = {
                'buy': mt5.ORDER_TYPE_BUY,
                'sell': mt5.ORDER_TYPE_SELL,
                'buy_limit': mt5.ORDER_TYPE_BUY_LIMIT,
                'sell_limit': mt5.ORDER_TYPE_SELL_LIMIT,
                'buy_stop': mt5.ORDER_TYPE_BUY_STOP,
                'sell_stop': mt5.ORDER_TYPE_SELL_STOP
            }
            
            mt5_order_type = order_type_map.get(order_type.lower(), mt5.ORDER_TYPE_BUY)
            
            # 获取品种信息
            symbol_info = mt5.symbol_info(symbol)
            if symbol_info is None:
                return {"error": f"品种 {symbol} 不存在"}
            
            # 如果品种不可见，尝试启用
            if not symbol_info.visible:
                if not mt5.symbol_select(symbol, True):
                    return {"error": f"无法启用品种 {symbol}"}
            
            # 准备交易请求
            if price == 0.0:
                # 市价单
                if mt5_order_type == mt5.ORDER_TYPE_BUY:
                    price = symbol_info.ask
                else:
                    price = symbol_info.bid
            
            request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": symbol,
                "volume": volume,
                "type": mt5_order_type,
                "price": price,
                "sl": sl,
                "tp": tp,
                "comment": comment,
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": mt5.ORDER_FILLING_IOC,
            }
            
            # 发送订单
            result = mt5.order_send(request)
            
            if result.retcode != mt5.TRADE_RETCODE_DONE:
                return {
                    "success": False,
                    "error": f"订单失败: {result.retcode}",
                    "comment": result.comment
                }
            
            return {
                "success": True,
                "order": result.order,
                "deal": result.deal,
                "volume": result.volume,
                "price": result.price,
                "comment": result.comment
            }
            
        except Exception as e:
            logger.error(f"发送订单失败: {e}")
            return {"error": str(e)}
    
    def get_positions(self) -> List[Dict]:
        """
        获取当前持仓
        
        Returns:
            List[Dict]: 持仓列表
        """
        if not self.connected:
            return []
        
        try:
            positions = mt5.positions_get()
            if positions is None:
                return []
            
            result = []
            for pos in positions:
                result.append({
                    "ticket": pos.ticket,
                    "symbol": pos.symbol,
                    "type": pos.type,
                    "volume": pos.volume,
                    "price_open": pos.price_open,
                    "price_current": pos.price_current,
                    "profit": pos.profit,
                    "swap": pos.swap,
                    "comment": pos.comment,
                    "time": datetime.fromtimestamp(pos.time)
                })
            
            return result
            
        except Exception as e:
            logger.error(f"获取持仓失败: {e}")
            return []
    
    def disconnect(self):
        """断开MT4连接"""
        if self.connected:
            mt5.shutdown()
            self.connected = False
            self.mt4_mode = False
            logger.info("已断开MT4连接")

# 全局MT4服务实例
mt4_service = MT4Service()
