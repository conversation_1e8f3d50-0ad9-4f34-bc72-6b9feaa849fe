#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图表服务
专业的K线图表和技术指标可视化
"""

import pandas as pd
import numpy as np
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import mplfinance as mpf
import io
import base64
from typing import Dict, List, Optional, Tuple
import logging

logger = logging.getLogger(__name__)

class ChartService:
    """图表服务类"""
    
    def __init__(self):
        self.default_colors = {
            'up': '#00ff88',
            'down': '#ff4444',
            'volume': '#1f77b4',
            'ma5': '#ff7f0e',
            'ma10': '#2ca02c',
            'ma20': '#d62728',
            'ma60': '#9467bd',
            'macd': '#17becf',
            'signal': '#bcbd22',
            'histogram': '#7f7f7f'
        }
    
    def create_candlestick_chart(self, data: pd.DataFrame, symbol: str, 
                               indicators: Dict = None, height: int = 600) -> str:
        """
        创建K线图表
        
        Args:
            data: OHLCV数据
            symbol: 交易品种名称
            indicators: 技术指标数据
            height: 图表高度
            
        Returns:
            str: HTML图表代码
        """
        try:
            # 创建子图
            rows = 1
            subplot_titles = [f'{symbol} K线图']
            
            # 如果有指标，增加子图
            if indicators:
                if 'volume' in indicators:
                    rows += 1
                    subplot_titles.append('成交量')
                if 'macd' in indicators:
                    rows += 1
                    subplot_titles.append('MACD')
                if 'rsi' in indicators:
                    rows += 1
                    subplot_titles.append('RSI')
            
            fig = make_subplots(
                rows=rows, cols=1,
                shared_xaxes=True,
                vertical_spacing=0.05,
                subplot_titles=subplot_titles,
                row_heights=[0.6] + [0.4/(rows-1)]*(rows-1) if rows > 1 else [1.0]
            )
            
            # 添加K线图
            fig.add_trace(
                go.Candlestick(
                    x=data.index,
                    open=data['Open'],
                    high=data['High'],
                    low=data['Low'],
                    close=data['Close'],
                    name='K线',
                    increasing_line_color=self.default_colors['up'],
                    decreasing_line_color=self.default_colors['down']
                ),
                row=1, col=1
            )
            
            # 添加移动平均线
            if indicators and 'ma' in indicators:
                ma_data = indicators['ma']
                for period, values in ma_data.items():
                    fig.add_trace(
                        go.Scatter(
                            x=data.index,
                            y=values,
                            mode='lines',
                            name=f'MA{period}',
                            line=dict(color=self.default_colors.get(f'ma{period}', '#888888'))
                        ),
                        row=1, col=1
                    )
            
            # 添加布林带
            if indicators and 'bollinger' in indicators:
                bb_data = indicators['bollinger']
                fig.add_trace(
                    go.Scatter(
                        x=data.index,
                        y=bb_data['upper'],
                        mode='lines',
                        name='布林上轨',
                        line=dict(color='rgba(255,0,0,0.3)'),
                        showlegend=False
                    ),
                    row=1, col=1
                )
                fig.add_trace(
                    go.Scatter(
                        x=data.index,
                        y=bb_data['lower'],
                        mode='lines',
                        name='布林下轨',
                        line=dict(color='rgba(255,0,0,0.3)'),
                        fill='tonexty',
                        fillcolor='rgba(255,0,0,0.1)',
                        showlegend=False
                    ),
                    row=1, col=1
                )
                fig.add_trace(
                    go.Scatter(
                        x=data.index,
                        y=bb_data['middle'],
                        mode='lines',
                        name='布林中轨',
                        line=dict(color='rgba(255,0,0,0.5)')
                    ),
                    row=1, col=1
                )
            
            current_row = 2
            
            # 添加成交量
            if indicators and 'volume' in indicators and current_row <= rows:
                colors = ['red' if close < open else 'green' 
                         for close, open in zip(data['Close'], data['Open'])]
                
                fig.add_trace(
                    go.Bar(
                        x=data.index,
                        y=data['Volume'],
                        name='成交量',
                        marker_color=colors,
                        opacity=0.7
                    ),
                    row=current_row, col=1
                )
                current_row += 1
            
            # 添加MACD
            if indicators and 'macd' in indicators and current_row <= rows:
                macd_data = indicators['macd']
                
                # MACD线
                fig.add_trace(
                    go.Scatter(
                        x=data.index,
                        y=macd_data['macd'],
                        mode='lines',
                        name='MACD',
                        line=dict(color=self.default_colors['macd'])
                    ),
                    row=current_row, col=1
                )
                
                # 信号线
                fig.add_trace(
                    go.Scatter(
                        x=data.index,
                        y=macd_data['signal'],
                        mode='lines',
                        name='信号线',
                        line=dict(color=self.default_colors['signal'])
                    ),
                    row=current_row, col=1
                )
                
                # 柱状图
                colors = ['red' if val < 0 else 'green' for val in macd_data['histogram']]
                fig.add_trace(
                    go.Bar(
                        x=data.index,
                        y=macd_data['histogram'],
                        name='MACD柱',
                        marker_color=colors,
                        opacity=0.7
                    ),
                    row=current_row, col=1
                )
                current_row += 1
            
            # 添加RSI
            if indicators and 'rsi' in indicators and current_row <= rows:
                rsi_data = indicators['rsi']
                
                fig.add_trace(
                    go.Scatter(
                        x=data.index,
                        y=rsi_data,
                        mode='lines',
                        name='RSI',
                        line=dict(color='purple')
                    ),
                    row=current_row, col=1
                )
                
                # 添加超买超卖线
                fig.add_hline(y=70, line_dash="dash", line_color="red", 
                             annotation_text="超买", row=current_row, col=1)
                fig.add_hline(y=30, line_dash="dash", line_color="green", 
                             annotation_text="超卖", row=current_row, col=1)
            
            # 更新布局
            fig.update_layout(
                title=f'{symbol} 技术分析图表',
                height=height,
                xaxis_rangeslider_visible=False,
                showlegend=True,
                template='plotly_white'
            )
            
            # 更新x轴
            fig.update_xaxes(
                type='date',
                tickformat='%Y-%m-%d %H:%M'
            )
            
            return fig.to_html(include_plotlyjs='cdn', div_id='chart')
            
        except Exception as e:
            logger.error(f"创建K线图表失败: {e}")
            return f"<div>图表创建失败: {str(e)}</div>"
    
    def create_mplfinance_chart(self, data: pd.DataFrame, symbol: str, 
                              style: str = 'yahoo') -> str:
        """
        使用mplfinance创建K线图
        
        Args:
            data: OHLCV数据
            symbol: 交易品种
            style: 图表样式
            
        Returns:
            str: base64编码的图片
        """
        try:
            # 准备数据
            df = data.copy()
            df.columns = ['Open', 'High', 'Low', 'Close', 'Volume']
            
            # 计算移动平均线
            df['MA5'] = df['Close'].rolling(5).mean()
            df['MA20'] = df['Close'].rolling(20).mean()
            
            # 创建图表
            fig, axes = mpf.plot(
                df,
                type='candle',
                style=style,
                title=f'{symbol} K线图',
                ylabel='价格',
                volume=True,
                mav=(5, 20),
                returnfig=True,
                figsize=(12, 8)
            )
            
            # 保存为base64
            buffer = io.BytesIO()
            fig.savefig(buffer, format='png', dpi=100, bbox_inches='tight')
            buffer.seek(0)
            
            image_base64 = base64.b64encode(buffer.getvalue()).decode()
            buffer.close()
            
            return f"data:image/png;base64,{image_base64}"
            
        except Exception as e:
            logger.error(f"创建mplfinance图表失败: {e}")
            return ""
    
    def create_performance_chart(self, equity_curve: pd.Series, 
                               benchmark: pd.Series = None) -> str:
        """
        创建策略表现图表
        
        Args:
            equity_curve: 权益曲线
            benchmark: 基准曲线
            
        Returns:
            str: HTML图表代码
        """
        try:
            fig = go.Figure()
            
            # 添加权益曲线
            fig.add_trace(
                go.Scatter(
                    x=equity_curve.index,
                    y=equity_curve.values,
                    mode='lines',
                    name='策略收益',
                    line=dict(color='blue', width=2)
                )
            )
            
            # 添加基准曲线
            if benchmark is not None:
                fig.add_trace(
                    go.Scatter(
                        x=benchmark.index,
                        y=benchmark.values,
                        mode='lines',
                        name='基准收益',
                        line=dict(color='red', width=2, dash='dash')
                    )
                )
            
            # 更新布局
            fig.update_layout(
                title='策略表现对比',
                xaxis_title='时间',
                yaxis_title='累计收益率 (%)',
                height=400,
                template='plotly_white',
                showlegend=True
            )
            
            return fig.to_html(include_plotlyjs='cdn', div_id='performance_chart')
            
        except Exception as e:
            logger.error(f"创建表现图表失败: {e}")
            return f"<div>图表创建失败: {str(e)}</div>"
    
    def create_drawdown_chart(self, drawdown: pd.Series) -> str:
        """
        创建回撤图表
        
        Args:
            drawdown: 回撤数据
            
        Returns:
            str: HTML图表代码
        """
        try:
            fig = go.Figure()
            
            # 添加回撤曲线
            fig.add_trace(
                go.Scatter(
                    x=drawdown.index,
                    y=drawdown.values * 100,
                    mode='lines',
                    name='回撤',
                    fill='tozeroy',
                    fillcolor='rgba(255,0,0,0.3)',
                    line=dict(color='red', width=2)
                )
            )
            
            # 更新布局
            fig.update_layout(
                title='策略回撤分析',
                xaxis_title='时间',
                yaxis_title='回撤 (%)',
                height=300,
                template='plotly_white'
            )
            
            return fig.to_html(include_plotlyjs='cdn', div_id='drawdown_chart')
            
        except Exception as e:
            logger.error(f"创建回撤图表失败: {e}")
            return f"<div>图表创建失败: {str(e)}</div>"
    
    def create_returns_distribution(self, returns: pd.Series) -> str:
        """
        创建收益分布图
        
        Args:
            returns: 收益率数据
            
        Returns:
            str: HTML图表代码
        """
        try:
            fig = go.Figure()
            
            # 添加直方图
            fig.add_trace(
                go.Histogram(
                    x=returns.values * 100,
                    nbinsx=50,
                    name='收益分布',
                    marker_color='lightblue',
                    opacity=0.7
                )
            )
            
            # 添加正态分布拟合线
            mean_return = returns.mean() * 100
            std_return = returns.std() * 100
            
            x_range = np.linspace(returns.min() * 100, returns.max() * 100, 100)
            normal_dist = (1 / (std_return * np.sqrt(2 * np.pi))) * \
                         np.exp(-0.5 * ((x_range - mean_return) / std_return) ** 2)
            
            fig.add_trace(
                go.Scatter(
                    x=x_range,
                    y=normal_dist * len(returns) * (returns.max() - returns.min()) * 100 / 50,
                    mode='lines',
                    name='正态分布拟合',
                    line=dict(color='red', width=2)
                )
            )
            
            # 更新布局
            fig.update_layout(
                title='收益率分布',
                xaxis_title='收益率 (%)',
                yaxis_title='频次',
                height=400,
                template='plotly_white',
                showlegend=True
            )
            
            return fig.to_html(include_plotlyjs='cdn', div_id='returns_dist_chart')
            
        except Exception as e:
            logger.error(f"创建收益分布图失败: {e}")
            return f"<div>图表创建失败: {str(e)}</div>"
    
    def create_correlation_heatmap(self, correlation_matrix: pd.DataFrame) -> str:
        """
        创建相关性热力图
        
        Args:
            correlation_matrix: 相关性矩阵
            
        Returns:
            str: HTML图表代码
        """
        try:
            fig = go.Figure(data=go.Heatmap(
                z=correlation_matrix.values,
                x=correlation_matrix.columns,
                y=correlation_matrix.index,
                colorscale='RdBu',
                zmid=0,
                text=correlation_matrix.round(2).values,
                texttemplate="%{text}",
                textfont={"size": 10},
                hoverongaps=False
            ))
            
            fig.update_layout(
                title='资产相关性热力图',
                height=500,
                template='plotly_white'
            )
            
            return fig.to_html(include_plotlyjs='cdn', div_id='correlation_heatmap')
            
        except Exception as e:
            logger.error(f"创建相关性热力图失败: {e}")
            return f"<div>图表创建失败: {str(e)}</div>"

# 全局图表服务实例
chart_service = ChartService()
