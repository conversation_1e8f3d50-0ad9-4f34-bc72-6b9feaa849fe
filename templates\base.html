<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}MateTrade4 AI自动交易系统{% endblock %}</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Plotly.js -->
    <script src="https://cdn.plot.ly/plotly-3.0.1.min.js"></script>
    <!-- 自定义CSS -->
    <link href="{{ url_for('static', filename='css/custom.css') }}" rel="stylesheet"></script>

    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 2px 0 5px rgba(0,0,0,0.1);
        }

        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            margin: 2px 0;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.2);
            transform: translateX(5px);
        }

        .sidebar .nav-link i {
            margin-right: 10px;
            width: 20px;
        }

        .main-content {
            background-color: #f8f9fa;
            min-height: 100vh;
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
        }

        .navbar-brand {
            font-weight: bold;
            color: #667eea !important;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 10px 25px;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
        }

        .profit-positive {
            color: #28a745;
        }

        .profit-negative {
            color: #dc3545;
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 左侧导航栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h4 class="text-white">
                            <i class="fas fa-chart-line"></i>
                            MateTrade4
                        </h4>
                        <small class="text-white-50">AI自动交易系统</small>
                    </div>

                    <ul class="nav flex-column">
                        <!-- 用户信息显示 -->
                        <li class="nav-item mb-3">
                            <div class="text-center">
                                <div class="text-white">
                                    <i class="fas fa-user-circle fa-2x"></i>
                                </div>
                                <div class="text-white mt-2">
                                    <strong>{{ current_user.username }}</strong>
                                    {% if current_user.user_type == 'admin' %}
                                        <span class="badge bg-danger ms-1">管理员</span>
                                    {% elif current_user.user_type == 'vip' %}
                                        <span class="badge bg-warning ms-1">VIP</span>
                                    {% else %}
                                        <span class="badge bg-secondary ms-1">普通用户</span>
                                    {% endif %}
                                </div>
                            </div>
                        </li>

                        <!-- 首页仪表盘 - 所有用户都可访问 -->
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'dashboard' %}active{% endif %}"
                               href="{{ url_for('dashboard') }}">
                                <i class="fas fa-tachometer-alt"></i>
                                首页仪表盘
                            </a>
                        </li>

                        <!-- 交易管理 - 所有用户都可访问 -->
                        <li class="nav-item">
                            <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-white-50">
                                <span>交易管理</span>
                            </h6>
                        </li>


                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'risk_events' %}active{% endif %}"
                               href="{{ url_for('risk_events') }}">
                                <i class="fas fa-exclamation-triangle text-warning"></i>
                                风险事件
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'pattern_monitoring' %}active{% endif %}"
                               href="{{ url_for('pattern_monitoring') }}">
                                <i class="fas fa-chart-line text-info"></i>
                                形态监测
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'low_risk_trading' %}active{% endif %}"
                               href="{{ url_for('low_risk_trading') }}">
                                <i class="fas fa-shield-alt text-success"></i>
                                低风险交易
                                <span class="badge bg-success ms-1">SAFE</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'strategy_trading' %}active{% endif %}"
                               href="{{ url_for('strategy_trading') }}">
                                <i class="fas fa-robot text-primary"></i>
                                策略交易
                                <span class="badge bg-primary ms-1">AI</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'ai_strategy_backtest' %}active{% endif %}"
                               href="{{ url_for('ai_strategy_backtest') }}">
                                <i class="fas fa-chart-line text-info"></i>
                                策略回测
                                <span class="badge bg-info ms-1">验证</span>
                            </a>
                        </li>



                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'mt5_connection' %}active{% endif %}"
                               href="{{ url_for('mt5_connection') }}">
                                <i class="fas fa-plug"></i>
                                MT5连接
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'trading_query' %}active{% endif %}"
                               href="{{ url_for('trading_query') }}">
                                <i class="fas fa-search"></i>
                                交易查询
                            </a>
                        </li>


                        <!-- 分析工具 - 仅VIP和管理员可见 -->
                        {% if current_user.user_type in ['vip', 'admin'] %}
                        <li class="nav-item">
                            <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-white-50">
                                <span>分析工具</span>
                                <span class="badge bg-warning">VIP</span>
                            </h6>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'ai_analysis_process' %}active{% endif %}"
                               href="{{ url_for('ai_analysis_process') }}">
                                <i class="fas fa-brain"></i>
                                AI策略分析过程
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'ai_training' %}active{% endif %}"
                               href="{{ url_for('ai_training') }}">
                                <i class="fas fa-graduation-cap"></i>
                                训练AI策略
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'charts' %}active{% endif %}"
                               href="{{ url_for('charts') }}">
                                <i class="fas fa-chart-candlestick"></i>
                                专业图表
                            </a>
                        </li>

                        <!-- 深度学习模块 -->
                        <li class="nav-item">
                            <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-white-50">
                                <span>深度学习</span>
                                <span class="badge bg-info">GPU</span>
                            </h6>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'deep_learning_dashboard' %}active{% endif %}"
                               href="{{ url_for('deep_learning_dashboard') }}">
                                <i class="fas fa-tachometer-alt"></i>
                                深度学习仪表板
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'model_training' %}active{% endif %}"
                               href="{{ url_for('model_training') }}">
                                <i class="fas fa-dumbbell"></i>
                                模型训练
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'model_management' %}active{% endif %}"
                               href="{{ url_for('model_management') }}">
                                <i class="fas fa-database"></i>
                                模型管理
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'model_inference' %}active{% endif %}"
                               href="{{ url_for('model_inference') }}">
                                <i class="fas fa-magic"></i>
                                模型推理
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'gpu_monitor' %}active{% endif %}"
                               href="{{ url_for('gpu_monitor') }}">
                                <i class="fas fa-microchip"></i>
                                GPU监控
                            </a>
                        </li>
                        {% endif %}
                        {% if current_user.user_type in ['vip', 'admin'] %}

                        {% endif %}

                        <!-- 用户管理和系统设置 - 仅管理员可见 -->
                        {% if current_user.user_type == 'admin' %}
                        <li class="nav-item">
                            <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-white-50">
                                <span>用户管理</span>
                                <span class="badge bg-danger">管理员</span>
                            </h6>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'user_management' %}active{% endif %}"
                               href="{{ url_for('user_management') }}">
                                <i class="fas fa-users"></i>
                                用户管理
                            </a>
                        </li>

                        <li class="nav-item">
                            <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-white-50">
                                <span>系统管理</span>
                                <span class="badge bg-danger">管理员</span>
                            </h6>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'settings' %}active{% endif %}"
                               href="{{ url_for('settings') }}">
                                <i class="fas fa-cog"></i>
                                系统设置
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'ai_strategy_management' %}active{% endif %}"
                               href="{{ url_for('ai_strategy_management') }}">
                                <i class="fas fa-share-alt"></i>
                                AI策略分享管理
                            </a>
                        </li>
                        {% endif %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('logout') }}">
                                <i class="fas fa-sign-out-alt"></i>
                                退出登录
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区域 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <!-- 顶部导航栏 -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">{% block page_title %}仪表盘{% endblock %}</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <span class="navbar-text">
                                欢迎, {{ current_user.username }}
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Flash消息 -->
                {% with messages = get_flashed_messages() %}
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-info alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <!-- 页面内容 -->
                {% block content %}{% endblock %}
            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- 全局状态管理器 -->
    <script src="{{ url_for('static', filename='js/global_state_manager.js') }}"></script>
    <!-- 自定义JS -->
    <script src="{{ url_for('static', filename='js/common.js') }}"></script>
    <!-- AI交易全局管理器 -->
    <script src="{{ url_for('static', filename='js/ai_trading_manager.js') }}"></script>

    {% block extra_js %}{% endblock %}
</body>
</html>
