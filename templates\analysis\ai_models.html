{% extends "base.html" %}

{% block page_title %}AI大模型分析{% endblock %}

{% block content %}
<div class="row">
    <!-- AI模型配置卡片 -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-robot"></i>
                    AI模型配置
                </h5>
            </div>
            <div class="card-body">
                <div class="row" id="aiModelCards">
                    {% for config in ai_configs %}
                    <div class="col-12 mb-3">
                        <div class="card border-primary">
                            <div class="card-body p-3">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h6 class="card-title mb-0">{{ config.name }}</h6>
                                    <span class="badge bg-{{ 'success' if config.is_active else 'secondary' }}">
                                        {{ '活跃' if config.is_active else '禁用' }}
                                    </span>
                                </div>
                                <p class="card-text small text-muted mb-2">
                                    模型: {{ config.model_name }}
                                </p>
                                <p class="card-text small text-muted mb-2">
                                    API: {{ config.base_url }}
                                </p>
                                <button class="btn btn-sm btn-primary" onclick="selectModel('{{ config.id }}', '{{ config.name }}')">
                                    选择使用
                                </button>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <!-- 分析面板 -->
    <div class="col-lg-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line"></i>
                    市场分析
                </h5>
            </div>
            <div class="card-body">
                <form id="analysisForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">选择交易品种</label>
                            <select class="form-select" id="symbolSelect" required>
                                <option value="">选择交易品种</option>

                                <optgroup label="📈 主要外汇对">
                                    <option value="EURUSD=X">EUR/USD (欧元/美元)</option>
                                    <option value="GBPUSD=X">GBP/USD (英镑/美元)</option>
                                    <option value="USDJPY=X">USD/JPY (美元/日元)</option>
                                    <option value="AUDUSD=X">AUD/USD (澳元/美元)</option>
                                    <option value="USDCAD=X">USD/CAD (美元/加元)</option>
                                    <option value="USDCHF=X">USD/CHF (美元/瑞郎)</option>
                                    <option value="NZDUSD=X">NZD/USD (纽元/美元)</option>
                                </optgroup>

                                <optgroup label="🔄 交叉外汇对">
                                    <option value="EURJPY=X">EUR/JPY (欧元/日元)</option>
                                    <option value="GBPJPY=X">GBP/JPY (英镑/日元)</option>
                                    <option value="EURGBP=X">EUR/GBP (欧元/英镑)</option>
                                    <option value="AUDCAD=X">AUD/CAD (澳元/加元)</option>
                                </optgroup>

                                <optgroup label="🥇 贵金属">
                                    <option value="XAUUSD">XAU/USD (黄金/美元) - 推荐</option>
                                    <option value="GC=F">黄金期货 (Gold Futures)</option>
                                    <option value="GLD">黄金ETF (SPDR Gold Trust)</option>
                                    <option value="XAGUSD">XAG/USD (白银/美元)</option>
                                    <option value="SI=F">白银期货 (Silver Futures)</option>
                                </optgroup>

                                <optgroup label="🛢️ 大宗商品">
                                    <option value="CL=F">原油期货 (WTI Crude Oil)</option>
                                    <option value="BZ=F">布伦特原油期货</option>
                                    <option value="NG=F">天然气期货</option>
                                </optgroup>

                                <optgroup label="₿ 加密货币">
                                    <option value="BTC-USD">BTC/USD (比特币/美元)</option>
                                    <option value="ETH-USD">ETH/USD (以太坊/美元)</option>
                                </optgroup>

                                <optgroup label="📊 股指">
                                    <option value="^GSPC">S&P 500 (标普500)</option>
                                    <option value="^DJI">Dow Jones (道琼斯)</option>
                                    <option value="^IXIC">NASDAQ (纳斯达克)</option>
                                    <option value="^GDAXI">DAX (德国DAX)</option>
                                    <option value="^FTSE">FTSE 100 (英国富时100)</option>
                                    <option value="^N225">Nikkei 225 (日经225)</option>
                                </optgroup>

                                <optgroup label="🏢 热门股票">
                                    <option value="AAPL">Apple Inc. (苹果)</option>
                                    <option value="MSFT">Microsoft (微软)</option>
                                    <option value="GOOGL">Alphabet (谷歌)</option>
                                    <option value="AMZN">Amazon (亚马逊)</option>
                                    <option value="TSLA">Tesla (特斯拉)</option>
                                    <option value="NVDA">NVIDIA (英伟达)</option>
                                </optgroup>
                            </select>
                            <small class="text-muted">推荐使用标记为"推荐"的符号，数据更稳定</small>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">分析类型</label>
                            <select class="form-select" id="analysisType" required>
                                <option value="technical">技术分析</option>
                                <option value="fundamental">基本面分析</option>
                                <option value="comprehensive">综合分析</option>
                            </select>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">数据时间范围</label>
                        <select class="form-select" id="timeRangeSelect" required>
                            <option value="1d|15m">1天 (15分钟K线) - 超短线分析</option>
                            <option value="3d|30m">3天 (30分钟K线) - 短线分析</option>
                            <option value="1wk|1h" selected>1周 (1小时K线) - 推荐</option>
                            <option value="2wk|2h">2周 (2小时K线) - 中短线分析</option>
                            <option value="1mo|1d">1个月 (日K线) - 中线分析</option>
                            <option value="3mo|1d">3个月 (日K线) - 长线分析</option>
                        </select>
                        <small class="text-muted">
                            <i class="fas fa-info-circle"></i>
                            推荐使用1周数据进行分析。注意：期货合约可能不支持高频数据，系统会自动调整到兼容的间隔
                        </small>
                        <div id="intervalCompatibilityWarning" class="alert alert-warning mt-2" style="display: none;">
                            <i class="fas fa-exclamation-triangle"></i>
                            <span id="compatibilityMessage"></span>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">选择AI模型</label>
                        <select class="form-select" id="modelSelect" required>
                            <option value="">请选择AI模型</option>
                            {% for config in ai_configs %}
                            {% if config.is_active %}
                            <option value="{{ config.id }}">{{ config.name }}</option>
                            {% endif %}
                            {% endfor %}
                        </select>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-brain"></i>
                        开始AI分析
                    </button>
                </form>
            </div>
        </div>

        <!-- 分析结果 -->
        <div class="card" id="analysisResult" style="display: none;">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar"></i>
                    分析结果
                </h5>
            </div>
            <div class="card-body">
                <div id="loadingSpinner" class="text-center" style="display: none;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">分析中...</span>
                    </div>
                    <p class="mt-2">AI正在分析中，请稍候...</p>
                </div>

                <div id="analysisContent">
                    <!-- 市场数据概览 -->
                    <div class="row mb-4" id="marketOverview" style="display: none;">
                        <div class="col-md-4">
                            <div class="text-center">
                                <h6 class="text-muted">当前价格</h6>
                                <h4 id="currentPrice">-</h4>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <h6 class="text-muted">价格变化</h6>
                                <h4 id="priceChange">-</h4>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <h6 class="text-muted">分析时间</h6>
                                <h6 id="analysisTime">-</h6>
                            </div>
                        </div>
                    </div>

                    <!-- 技术指标 -->
                    <div class="mb-4" id="technicalIndicators" style="display: none;">
                        <h6><i class="fas fa-chart-line"></i> 技术指标</h6>
                        <div class="row" id="indicatorsGrid">
                            <!-- 技术指标将在这里动态生成 -->
                        </div>
                    </div>

                    <!-- AI分析结果 -->
                    <div class="mb-4" id="aiAnalysisSection" style="display: none;">
                        <h6><i class="fas fa-brain"></i> AI分析报告</h6>
                        <div class="card bg-light">
                            <div class="card-body">
                                <div id="aiAnalysisText" style="white-space: pre-wrap;"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 历史分析记录 -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history"></i>
                    历史分析记录
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="analysisHistoryTable">
                        <thead>
                            <tr>
                                <th>时间</th>
                                <th>交易对</th>
                                <th>分析类型</th>
                                <th>AI模型</th>
                                <th>当时价格</th>
                                <th>建议</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="7" class="text-center text-muted">暂无分析记录</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let selectedModelId = null;
let selectedModelName = null;

// 选择AI模型
function selectModel(modelId, modelName) {
    selectedModelId = modelId;
    selectedModelName = modelName;
    document.getElementById('modelSelect').value = modelId;

    // 更新卡片状态
    document.querySelectorAll('#aiModelCards .card').forEach(card => {
        card.classList.remove('border-success');
        card.classList.add('border-primary');
    });

    event.target.closest('.card').classList.remove('border-primary');
    event.target.closest('.card').classList.add('border-success');

    // 显示选择提示
    const toast = document.createElement('div');
    toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed top-0 end-0 m-3';
    toast.style.zIndex = '9999';
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                已选择 ${modelName} 模型
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;
    document.body.appendChild(toast);

    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();

    setTimeout(() => {
        document.body.removeChild(toast);
    }, 3000);
}

// 提交分析表单
document.getElementById('analysisForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const symbol = document.getElementById('symbolSelect').value;
    const analysisType = document.getElementById('analysisType').value;
    const modelId = document.getElementById('modelSelect').value;
    const timeRange = document.getElementById('timeRangeSelect').value;

    if (!symbol || !analysisType || !modelId || !timeRange) {
        alert('请填写所有必填项');
        return;
    }

    // 解析时间范围参数
    const [period, interval] = timeRange.split('|');
    console.log('选择的时间范围:', { period, interval });

    // 显示分析结果区域和加载动画
    document.getElementById('analysisResult').style.display = 'block';
    document.getElementById('loadingSpinner').style.display = 'block';
    document.getElementById('marketOverview').style.display = 'none';
    document.getElementById('technicalIndicators').style.display = 'none';
    document.getElementById('aiAnalysisSection').style.display = 'none';

    // 发送分析请求
    fetch('/api/ai-analysis', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            symbol: symbol,
            analysis_type: analysisType,
            model_id: modelId,
            period: period,
            interval: interval
        })
    })
    .then(response => response.json())
    .then(data => {
        document.getElementById('loadingSpinner').style.display = 'none';

        if (data.success) {
            displayAnalysisResult(data.analysis);
        } else {
            alert('分析失败: ' + data.error);
        }
    })
    .catch(error => {
        document.getElementById('loadingSpinner').style.display = 'none';
        console.error('Error:', error);
        alert('分析请求失败');
    });
});

// 符号选择变化时的处理
document.getElementById('symbolSelect').addEventListener('change', function() {
    const selectedSymbol = this.value;
    if (selectedSymbol) {
        console.log('选择的交易品种:', selectedSymbol);

        // 显示符号信息提示
        showSymbolInfo(selectedSymbol);

        // 检查间隔兼容性
        checkIntervalCompatibility();
    }
});

// 时间范围选择变化时也检查兼容性
document.getElementById('timeRangeSelect').addEventListener('change', function() {
    checkIntervalCompatibility();
});

// 显示符号信息
function showSymbolInfo(symbol) {
    const symbolInfo = {
        'XAUUSD': '黄金是避险资产，通常在市场不确定时表现良好',
        'GC=F': '黄金期货，提供更直接的黄金价格数据',
        'GLD': '黄金ETF，跟踪黄金价格表现',
        'BTC-USD': '比特币价格波动较大，适合短期技术分析',
        'EURUSD=X': '最活跃的外汇对，流动性最好',
        '^GSPC': '美国股市整体表现的重要指标',
        'AAPL': '苹果公司股票，科技股代表',
        'CL=F': '原油期货，受地缘政治影响较大'
    };

    if (symbolInfo[symbol]) {
        console.log('符号信息:', symbolInfo[symbol]);
    }
}

// 时间范围选择变化时的处理
document.getElementById('timeRangeSelect').addEventListener('change', function() {
    const selectedRange = this.value;
    const [period, interval] = selectedRange.split('|');

    console.log('选择的时间范围:', { period, interval });

    // 显示时间范围建议
    showTimeRangeInfo(period, interval);
});

// 显示时间范围信息
function showTimeRangeInfo(period, interval) {
    const timeRangeInfo = {
        '1d': '1天数据适合超短线分析，捕捉日内波动',
        '3d': '3天数据适合短线分析，识别短期趋势',
        '1wk': '1周数据是推荐选择，平衡了数据量和分析深度',
        '2wk': '2周数据适合中短线分析，识别中期趋势',
        '1mo': '1个月数据适合中线分析，但数据量较大',
        '3mo': '3个月数据适合长线分析，数据量很大，分析时间较长'
    };

    const intervalInfo = {
        '5m': '5分钟K线，数据精度高，适合日内交易',
        '15m': '15分钟K线，平衡精度和数据量',
        '1h': '1小时K线，推荐选择，数据稳定',
        '2h': '2小时K线，减少噪音，趋势更明显',
        '1d': '日K线，长期趋势分析'
    };

    if (timeRangeInfo[period]) {
        console.log('时间范围说明:', timeRangeInfo[period]);
    }

    if (intervalInfo[interval]) {
        console.log('K线间隔说明:', intervalInfo[interval]);
    }
}

// 检查间隔兼容性
function checkIntervalCompatibility() {
    const symbol = document.getElementById('symbolSelect').value;
    const timeRange = document.getElementById('timeRangeSelect').value;

    if (!symbol || !timeRange) return;

    const [period, interval] = timeRange.split('|');

    // 定义符号类型和支持的间隔
    const symbolIntervalSupport = {
        // 期货合约 - 通常不支持高频数据
        futures: {
            symbols: ['GC=F', 'SI=F', 'CL=F', 'BZ=F', 'NG=F'],
            supportedIntervals: ['1d', '1h', '30m'],
            fallbackInterval: '1h',
            name: '期货合约'
        },
        // ETF - 支持大部分间隔
        etf: {
            symbols: ['GLD', 'SLV', 'IAU', 'USO'],
            supportedIntervals: ['1d', '1h', '30m', '15m', '5m'],
            fallbackInterval: '15m',
            name: 'ETF基金'
        },
        // 外汇 - 支持高频数据
        forex: {
            symbols: ['EURUSD=X', 'GBPUSD=X', 'USDJPY=X', 'AUDUSD=X'],
            supportedIntervals: ['1d', '1h', '30m', '15m', '5m'],
            fallbackInterval: '15m',
            name: '外汇对'
        },
        // 加密货币 - 支持高频数据
        crypto: {
            symbols: ['BTC-USD', 'ETH-USD'],
            supportedIntervals: ['1d', '1h', '30m', '15m', '5m', '1m'],
            fallbackInterval: '5m',
            name: '加密货币'
        }
    };

    // 获取符号类型
    let symbolType = null;
    let symbolConfig = null;

    for (const [type, config] of Object.entries(symbolIntervalSupport)) {
        if (config.symbols.includes(symbol)) {
            symbolType = type;
            symbolConfig = config;
            break;
        }
    }

    // 根据符号格式推断类型
    if (!symbolType) {
        if (symbol.endsWith('=F')) {
            symbolType = 'futures';
            symbolConfig = symbolIntervalSupport.futures;
        } else if (symbol.endsWith('=X')) {
            symbolType = 'forex';
            symbolConfig = symbolIntervalSupport.forex;
        } else if (symbol.includes('-USD')) {
            symbolType = 'crypto';
            symbolConfig = symbolIntervalSupport.crypto;
        }
    }

    const warningDiv = document.getElementById('intervalCompatibilityWarning');
    const messageSpan = document.getElementById('compatibilityMessage');

    if (symbolConfig && !symbolConfig.supportedIntervals.includes(interval)) {
        // 显示兼容性警告
        messageSpan.textContent = `${symbolConfig.name}可能不支持${interval}间隔数据，系统将自动调整为${symbolConfig.fallbackInterval}间隔`;
        warningDiv.style.display = 'block';
    } else {
        // 隐藏警告
        warningDiv.style.display = 'none';
    }
}

// 显示分析结果
function displayAnalysisResult(analysis) {
    console.log('显示分析结果:', analysis);

    // 检查是否有错误
    if (analysis.error) {
        console.error('分析过程中出现错误:', analysis.error);
        document.getElementById('aiAnalysisText').textContent = `分析失败: ${analysis.error}`;
        document.getElementById('aiAnalysisSection').style.display = 'block';
        return;
    }

    // 显示符号说明（如果有）
    if (analysis.symbol_note) {
        const symbolNoteDiv = document.createElement('div');
        symbolNoteDiv.className = 'alert alert-info mb-3';
        symbolNoteDiv.innerHTML = `<i class="fas fa-info-circle"></i> ${analysis.symbol_note}`;

        // 在市场概览前插入符号说明
        const marketOverview = document.getElementById('marketOverview');
        marketOverview.parentNode.insertBefore(symbolNoteDiv, marketOverview);
    }

    // 显示市场概览
    document.getElementById('currentPrice').textContent = '$' + (analysis.current_price || 0).toFixed(4);
    document.getElementById('priceChange').textContent = (analysis.price_change || 0).toFixed(2) + '%';
    document.getElementById('priceChange').className = (analysis.price_change || 0) >= 0 ? 'profit-positive' : 'profit-negative';
    document.getElementById('analysisTime').textContent = new Date(analysis.timestamp).toLocaleString();
    document.getElementById('marketOverview').style.display = 'block';

    // 显示技术指标
    if (analysis.indicators && Object.keys(analysis.indicators).length > 0) {
        const indicatorsGrid = document.getElementById('indicatorsGrid');
        indicatorsGrid.innerHTML = '';

        Object.entries(analysis.indicators).forEach(([key, value]) => {
            if (value !== null && value !== undefined && !isNaN(value)) {
                const col = document.createElement('div');
                col.className = 'col-md-3 mb-2';
                col.innerHTML = `
                    <div class="text-center">
                        <small class="text-muted">${getIndicatorName(key)}</small>
                        <div class="fw-bold">${value.toFixed(4)}</div>
                    </div>
                `;
                indicatorsGrid.appendChild(col);
            }
        });

        // 如果没有有效的技术指标，显示提示信息
        if (indicatorsGrid.children.length === 0) {
            indicatorsGrid.innerHTML = `
                <div class="col-12">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        暂无技术指标数据，可能是由于数据不足或计算错误
                    </div>
                </div>
            `;
        }

        document.getElementById('technicalIndicators').style.display = 'block';
    } else {
        // 如果没有指标数据，显示提示
        const indicatorsGrid = document.getElementById('indicatorsGrid');
        indicatorsGrid.innerHTML = `
            <div class="col-12">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    技术指标计算需要足够的历史数据，请稍后重试
                </div>
            </div>
        `;
        document.getElementById('technicalIndicators').style.display = 'block';
    }

    // 显示AI分析结果
    document.getElementById('aiAnalysisText').textContent = analysis.ai_analysis;
    document.getElementById('aiAnalysisSection').style.display = 'block';

    // 添加到历史记录
    addToAnalysisHistory(analysis);
}

// 获取指标中文名称
function getIndicatorName(key) {
    const names = {
        'sma_20': 'SMA20',
        'sma_50': 'SMA50',
        'ema_12': 'EMA12',
        'ema_26': 'EMA26',
        'macd': 'MACD',
        'macd_signal': 'MACD信号',
        'rsi': 'RSI',
        'bb_upper': '布林上轨',
        'bb_middle': '布林中轨',
        'bb_lower': '布林下轨'
    };
    return names[key] || key;
}

// 添加到分析历史
function addToAnalysisHistory(analysis) {
    const tbody = document.querySelector('#analysisHistoryTable tbody');

    // 如果是第一条记录，清除占位符
    if (tbody.children.length === 1 && tbody.children[0].children.length === 1) {
        tbody.innerHTML = '';
    }

    const row = document.createElement('tr');
    row.innerHTML = `
        <td>${new Date(analysis.timestamp).toLocaleString()}</td>
        <td>${analysis.symbol}</td>
        <td><span class="badge bg-info">${document.getElementById('analysisType').selectedOptions[0].text}</span></td>
        <td><span class="badge bg-primary">${selectedModelName}</span></td>
        <td>$${analysis.current_price.toFixed(4)}</td>
        <td class="${analysis.price_change >= 0 ? 'profit-positive' : 'profit-negative'}">
            ${analysis.price_change >= 0 ? '看涨' : '看跌'}
        </td>
        <td>
            <button class="btn btn-sm btn-outline-info" onclick="viewAnalysisDetail('${analysis.timestamp}')">
                查看详情
            </button>
        </td>
    `;

    // 插入到表格顶部
    tbody.insertBefore(row, tbody.firstChild);

    // 限制历史记录数量
    if (tbody.children.length > 10) {
        tbody.removeChild(tbody.lastChild);
    }
}

// 查看分析详情
function viewAnalysisDetail(timestamp) {
    // 这里可以实现查看历史分析详情的功能
    alert('查看分析详情功能待实现');
}
</script>
{% endblock %}
