#!/usr/bin/env python3
"""
诊断AI推理交易回测中没有交易的问题
"""

import requests
import json
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def diagnose_backtest_no_trades():
    """诊断回测没有交易的问题"""
    
    print("🔍 诊断AI推理交易回测无交易问题")
    print("=" * 70)
    
    try:
        # 登录
        session = requests.Session()
        login_data = {'username': 'admin', 'password': 'admin123'}
        login_response = session.post('http://127.0.0.1:5000/login', data=login_data)
        
        if login_response.status_code != 200:
            print("❌ 登录失败")
            return False
        
        print("✅ 登录成功")
        
        # 获取可用模型
        print("📋 获取可用模型...")
        response = session.get('http://127.0.0.1:5000/api/deep-learning/models')
        
        if response.status_code != 200:
            print("❌ 获取模型列表失败")
            return False
        
        result = response.json()
        models = result.get('models', [])
        completed_models = [m for m in models if m.get('status') == 'completed']
        
        if not completed_models:
            print("❌ 没有可用的训练完成模型")
            return False
        
        test_model = completed_models[0]
        model_id = test_model['id']
        
        print(f"🔍 使用模型: {test_model['name']} ({model_id[:8]}...)")
        
        # 1. 测试推理功能
        print(f"\n📊 步骤1: 测试推理功能")
        print("-" * 50)
        
        inference_data = {
            'model_id': model_id,
            'symbol': test_model['symbol'],
            'timeframe': test_model['timeframe'],
            'inference_mode': 'realtime',
            'data_points': 100,
            'show_confidence': True
        }
        
        response = session.post('http://127.0.0.1:5000/api/deep-learning/inference', 
                               json=inference_data)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                results = result.get('results', [])
                if results:
                    pred = results[0]
                    print(f"✅ 推理成功")
                    print(f"   预测: {pred.get('prediction')}")
                    print(f"   置信度: {pred.get('confidence', 0)*100:.1f}%")
                    print(f"   当前价格: {pred.get('current_price', 0):.5f}")
                else:
                    print("❌ 推理成功但无结果")
                    return False
            else:
                print(f"❌ 推理失败: {result.get('error')}")
                return False
        else:
            print(f"❌ 推理请求失败: HTTP {response.status_code}")
            return False
        
        # 2. 测试不同置信度阈值的回测
        print(f"\n📊 步骤2: 测试不同置信度阈值的回测")
        print("-" * 50)
        
        confidence_thresholds = [0.1, 0.2, 0.3, 0.5, 0.7]
        
        for min_confidence in confidence_thresholds:
            print(f"\n🔄 测试置信度阈值: {min_confidence}")
            
            backtest_data = {
                'model_id': model_id,
                'symbol': test_model['symbol'],
                'timeframe': test_model['timeframe'],
                'start_date': '2025-07-22',  # 缩短时间范围
                'end_date': '2025-07-29',
                'initial_balance': 10000,
                'lot_size': 0.01,
                'stop_loss_pips': 50,
                'take_profit_pips': 100,
                'min_confidence': min_confidence
            }
            
            response = session.post('http://127.0.0.1:5000/api/deep-learning/inference-backtest', 
                                   json=backtest_data)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    stats = result.get('statistics', {})
                    trades = result.get('trades', [])
                    
                    print(f"   ✅ 回测成功: {len(trades)} 笔交易")
                    print(f"      总收益: {stats.get('total_return', 0):.2f}%")
                    
                    if len(trades) > 0:
                        print(f"   🎉 找到有效的置信度阈值: {min_confidence}")
                        
                        # 显示前几笔交易
                        print(f"   📈 交易记录 (前3笔):")
                        for i, trade in enumerate(trades[:3], 1):
                            print(f"      {i}. {trade['prediction']} @ {trade['entry_price']:.5f} → {trade['exit_price']:.5f}")
                            print(f"         盈亏: ${trade['profit']:.2f}, 置信度: {trade['confidence']*100:.1f}%")
                        
                        return True
                else:
                    print(f"   ❌ 回测失败: {result.get('error')}")
            else:
                print(f"   ❌ 回测请求失败: HTTP {response.status_code}")
        
        # 3. 如果所有置信度都没有交易，进行深度诊断
        print(f"\n📊 步骤3: 深度诊断 - 检查推理过程")
        print("-" * 50)
        
        # 直接调用深度学习服务进行诊断
        from services.deep_learning_service import deep_learning_service
        
        # 获取历史数据
        data_result = deep_learning_service._get_historical_data(
            test_model['symbol'], 
            test_model['timeframe'], 
            '2025-07-25', 
            '2025-07-29', 
            100
        )
        
        if data_result.get('success'):
            historical_data = data_result['data']
            print(f"✅ 获取历史数据: {len(historical_data)} 条")
            
            # 测试智能推理
            if len(historical_data) > 0:
                print(f"\n🧠 测试智能推理...")
                
                # 获取模型信息
                model_info = deep_learning_service.get_model_by_id(model_id)
                
                # 测试前几个数据点的推理
                for i in range(min(5, len(historical_data))):
                    test_data = [historical_data[i]]
                    
                    try:
                        inference_results = deep_learning_service._intelligent_inference(
                            test_data, model_info, True
                        )
                        
                        if inference_results:
                            result = inference_results[0]
                            print(f"   数据点 {i+1}:")
                            print(f"      预测: {result.get('prediction')}")
                            print(f"      置信度: {result.get('confidence', 0)*100:.1f}%")
                            print(f"      价格: {result.get('current_price', 0):.5f}")
                            
                            # 检查是否满足交易条件
                            confidence = result.get('confidence', 0)
                            prediction = result.get('prediction')
                            
                            if confidence >= 0.1 and prediction in ['BUY', 'SELL']:
                                print(f"      ✅ 满足交易条件 (置信度 >= 0.1)")
                            else:
                                print(f"      ❌ 不满足交易条件")
                                if confidence < 0.1:
                                    print(f"         置信度过低: {confidence*100:.1f}% < 10%")
                                if prediction not in ['BUY', 'SELL']:
                                    print(f"         预测为HOLD: {prediction}")
                        else:
                            print(f"   数据点 {i+1}: ❌ 推理失败")
                            
                    except Exception as e:
                        print(f"   数据点 {i+1}: ❌ 推理异常: {e}")
            
        else:
            print(f"❌ 获取历史数据失败: {data_result.get('error')}")
        
        return False
        
    except Exception as e:
        print(f"❌ 诊断异常: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def main():
    """主函数"""
    
    print("🔧 AI推理交易回测无交易问题诊断")
    print("=" * 80)
    
    success = diagnose_backtest_no_trades()
    
    print(f"\n📊 诊断结果")
    print("=" * 80)
    
    if success:
        print("🎉 找到解决方案!")
        print("✅ 通过降低置信度阈值可以产生交易")
        
        print(f"\n💡 建议:")
        print("• 使用较低的置信度阈值 (0.1-0.3)")
        print("• 检查模型的预测分布")
        print("• 考虑调整交易参数")
        
    else:
        print("❌ 问题仍然存在")
        print("⚠️ 需要进一步分析推理逻辑")
        
        print(f"\n🔧 可能的原因:")
        print("• 模型预测置信度普遍过低")
        print("• 智能推理逻辑过于保守")
        print("• 价格变化不足以触发交易信号")
        print("• 数据预处理问题")
        
        print(f"\n🔧 建议解决方案:")
        print("• 降低置信度阈值到0.1或更低")
        print("• 调整价格变化阈值")
        print("• 优化智能推理算法")
        print("• 检查数据质量和格式")

if __name__ == '__main__':
    main()
