#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复MT5数据获取问题
分析并解决AI训练中的数据获取故障
"""

import sys
import os
import time
from datetime import datetime, timedelta

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_large_data_retrieval():
    """测试大量数据获取"""
    print("🔍 测试大量数据获取...")
    
    try:
        import MetaTrader5 as mt5
        
        if not mt5.initialize():
            print("❌ MT5初始化失败")
            return False
        
        symbol = "XAUUSD"
        timeframe = mt5.TIMEFRAME_M5
        
        # 测试不同时间范围的数据获取
        test_ranges = [
            ("1天", 1),
            ("1周", 7), 
            ("1个月", 30),
            ("3个月", 90),
            ("6个月", 180),
            ("1年", 365)
        ]
        
        for name, days in test_ranges:
            print(f"\n📊 测试 {name} 数据获取...")
            
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            print(f"   时间范围: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")
            
            start_time = time.time()
            rates = mt5.copy_rates_range(symbol, timeframe, start_date, end_date)
            elapsed = time.time() - start_time
            
            if rates is not None and len(rates) > 0:
                print(f"   ✅ 成功获取 {len(rates)} 条数据，耗时 {elapsed:.2f}秒")
                print(f"   📈 数据密度: {len(rates)/days:.1f} 条/天")
                
                # 检查数据质量
                if len(rates) < days * 100:  # 5分钟数据，一天约288条
                    print(f"   ⚠️ 数据密度较低，可能有缺失")
            else:
                print(f"   ❌ 获取失败，耗时 {elapsed:.2f}秒")
                error = mt5.last_error()
                print(f"   错误: {error}")
                
                # 如果1年数据失败，这里就是问题所在
                if days >= 365:
                    print(f"   💡 建议: 减少训练数据时间范围到6个月或更少")
                    break
        
        mt5.shutdown()
        return True
        
    except Exception as e:
        print(f"❌ 大量数据获取测试失败: {e}")
        return False

def test_chunked_data_retrieval():
    """测试分块数据获取"""
    print("\n🔍 测试分块数据获取策略...")
    
    try:
        import MetaTrader5 as mt5
        import numpy as np
        
        if not mt5.initialize():
            print("❌ MT5初始化失败")
            return False
        
        symbol = "XAUUSD"
        timeframe = mt5.TIMEFRAME_M5
        
        # 模拟获取1年数据，但分块获取
        total_days = 365
        chunk_days = 30  # 每次获取30天
        
        print(f"📊 分块获取策略: 总共{total_days}天，每块{chunk_days}天")
        
        end_date = datetime.now()
        start_date = end_date - timedelta(days=total_days)
        
        all_rates = []
        current_start = start_date
        chunk_count = 0
        
        while current_start < end_date:
            chunk_count += 1
            current_end = min(current_start + timedelta(days=chunk_days), end_date)
            
            print(f"   块 {chunk_count}: {current_start.strftime('%Y-%m-%d')} 到 {current_end.strftime('%Y-%m-%d')}")
            
            start_time = time.time()
            rates = mt5.copy_rates_range(symbol, timeframe, current_start, current_end)
            elapsed = time.time() - start_time
            
            if rates is not None and len(rates) > 0:
                all_rates.extend(rates)
                print(f"     ✅ 获取 {len(rates)} 条数据，耗时 {elapsed:.2f}秒")
            else:
                print(f"     ❌ 获取失败，耗时 {elapsed:.2f}秒")
                error = mt5.last_error()
                print(f"     错误: {error}")
            
            current_start = current_end
            time.sleep(0.1)  # 短暂延迟，避免请求过快
        
        print(f"\n📊 分块获取结果:")
        print(f"   总数据量: {len(all_rates)} 条")
        print(f"   总块数: {chunk_count}")
        
        if len(all_rates) > 0:
            print(f"   ✅ 分块获取策略成功")
            mt5.shutdown()
            return True
        else:
            print(f"   ❌ 分块获取策略失败")
            mt5.shutdown()
            return False
        
    except Exception as e:
        print(f"❌ 分块数据获取测试失败: {e}")
        return False

def implement_chunked_data_method():
    """在深度学习服务中实现分块数据获取方法"""
    print("\n🔧 实现分块数据获取方法...")
    
    chunked_method = '''
    def _get_mt5_data_chunked(self, symbol: str, timeframe: str, start_date: datetime, 
                             end_date: datetime, chunk_days: int = 30) -> np.ndarray:
        """分块获取MT5历史数据，避免大量数据请求失败"""
        try:
            import MetaTrader5 as mt5
            
            # 转换时间框架
            mt5_timeframe = self._convert_timeframe_to_mt5(timeframe)
            if mt5_timeframe is None:
                logger.error(f"❌ 不支持的时间框架: {timeframe}")
                return None
            
            logger.info(f"📊 分块获取数据: {symbol} {timeframe}")
            logger.info(f"   总范围: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")
            logger.info(f"   分块大小: {chunk_days}天")
            
            all_rates = []
            current_start = start_date
            chunk_count = 0
            failed_chunks = 0
            
            while current_start < end_date:
                chunk_count += 1
                current_end = min(current_start + timedelta(days=chunk_days), end_date)
                
                logger.info(f"🔄 获取块 {chunk_count}: {current_start.strftime('%Y-%m-%d')} 到 {current_end.strftime('%Y-%m-%d')}")
                
                # 尝试获取当前块的数据
                max_retries = 3
                for retry in range(max_retries):
                    try:
                        rates = mt5.copy_rates_range(symbol, mt5_timeframe, current_start, current_end)
                        if rates is not None and len(rates) > 0:
                            all_rates.extend(rates)
                            logger.info(f"✅ 块 {chunk_count} 成功: {len(rates)} 条数据")
                            break
                        else:
                            if retry < max_retries - 1:
                                logger.warning(f"⚠️ 块 {chunk_count} 重试 {retry + 1}/{max_retries}")
                                time.sleep(2)
                            else:
                                logger.warning(f"⚠️ 块 {chunk_count} 获取失败")
                                failed_chunks += 1
                    except Exception as e:
                        logger.warning(f"⚠️ 块 {chunk_count} 异常: {e}")
                        if retry < max_retries - 1:
                            time.sleep(2)
                        else:
                            failed_chunks += 1
                
                current_start = current_end
                time.sleep(0.1)  # 避免请求过快
            
            logger.info(f"📊 分块获取完成: 总块数={chunk_count}, 失败块数={failed_chunks}")
            
            if len(all_rates) == 0:
                logger.error("❌ 所有数据块都获取失败")
                return None
            
            # 转换为numpy数组
            rates_array = np.array(all_rates)
            
            # 去重和排序（按时间）
            unique_rates = np.unique(rates_array, axis=0)
            sorted_rates = unique_rates[unique_rates[:, 0].argsort()]  # 按时间排序
            
            # 提取OHLCV数据
            price_data = np.column_stack([
                sorted_rates['open'],
                sorted_rates['high'], 
                sorted_rates['low'],
                sorted_rates['close'],
                sorted_rates['tick_volume']
            ])
            
            logger.info(f"✅ 分块获取成功: {len(price_data)} 条有效数据")
            return price_data
            
        except Exception as e:
            logger.error(f"❌ 分块数据获取失败: {e}")
            return None
'''
    
    try:
        # 读取深度学习服务文件
        with open('services/deep_learning_service.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经有分块方法
        if '_get_mt5_data_chunked' in content:
            print("✅ 分块数据获取方法已存在")
            return True
        
        # 在类中添加分块方法
        class_pattern = 'class DeepLearningService:'
        class_pos = content.find(class_pattern)
        
        if class_pos != -1:
            # 找到合适的位置插入方法
            insert_pos = content.find('def _get_mt5_historical_data', class_pos)
            if insert_pos != -1:
                content = content[:insert_pos] + chunked_method + '\n    ' + content[insert_pos:]
                
                # 写回文件
                with open('services/deep_learning_service.py', 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print("✅ 已添加分块数据获取方法")
                return True
        
        print("❌ 无法找到合适位置添加分块方法")
        return False
        
    except Exception as e:
        print(f"❌ 实现分块方法失败: {e}")
        return False

def modify_data_retrieval_logic():
    """修改数据获取逻辑，使用分块方法处理大数据"""
    print("\n🔧 修改数据获取逻辑...")
    
    try:
        # 读取深度学习服务文件
        with open('services/deep_learning_service.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找原始数据获取逻辑
        old_logic = '''                while time.time() - start_time < timeout and attempt < max_attempts:
                    attempt += 1
                    logger.info(f"🔄 第{attempt}次尝试获取MT5数据...")

                    try:
                        rates = mt5.copy_rates_range(symbol, mt5_timeframe, start_date, end_date)
                        if rates is not None and len(rates) > 0:
                            logger.info(f"✅ 第{attempt}次尝试获取数据成功，获得{len(rates)}条记录")
                            break
                        else:
                            logger.warning(f"⚠️ 第{attempt}次获取数据为空")
                    except Exception as e:
                        logger.warning(f"⚠️ 第{attempt}次获取数据异常: {e}")

                    if attempt < max_attempts:
                        logger.info(f"等待3秒后重试...")
                        time.sleep(3)  # 等待3秒重试'''
        
        new_logic = '''                # 计算数据范围，如果超过90天，使用分块获取
                total_days = (end_date - start_date).days
                if total_days > 90:
                    logger.info(f"📊 数据范围较大({total_days}天)，使用分块获取策略")
                    
                    # 关闭当前MT5连接，使用分块方法
                    mt5.shutdown()
                    
                    # 使用分块获取
                    price_data = self._get_mt5_data_chunked(symbol, timeframe, start_date, end_date)
                    if price_data is not None:
                        logger.info(f"✅ 分块获取成功: {len(price_data)}条记录")
                        return price_data
                    else:
                        logger.error("❌ 分块获取也失败，尝试传统方法")
                        # 重新初始化MT5
                        if not mt5.initialize(timeout=5000):
                            logger.error("❌ MT5重新初始化失败")
                            return None
                
                # 传统获取方法（用于小数据量）
                while time.time() - start_time < timeout and attempt < max_attempts:
                    attempt += 1
                    logger.info(f"🔄 第{attempt}次尝试获取MT5数据...")

                    try:
                        rates = mt5.copy_rates_range(symbol, mt5_timeframe, start_date, end_date)
                        if rates is not None and len(rates) > 0:
                            logger.info(f"✅ 第{attempt}次尝试获取数据成功，获得{len(rates)}条记录")
                            break
                        else:
                            logger.warning(f"⚠️ 第{attempt}次获取数据为空")
                            # 添加更详细的错误信息
                            error = mt5.last_error()
                            logger.warning(f"   MT5错误: {error}")
                    except Exception as e:
                        logger.warning(f"⚠️ 第{attempt}次获取数据异常: {e}")

                    if attempt < max_attempts:
                        logger.info(f"等待3秒后重试...")
                        time.sleep(3)  # 等待3秒重试'''
        
        if old_logic in content:
            content = content.replace(old_logic, new_logic)
            
            # 写回文件
            with open('services/deep_learning_service.py', 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✅ 已修改数据获取逻辑，支持大数据分块获取")
            return True
        else:
            print("⚠️ 未找到目标代码段，可能已经修改过")
            return False
        
    except Exception as e:
        print(f"❌ 修改数据获取逻辑失败: {e}")
        return False

def main():
    """主修复函数"""
    print("🔧 修复MT5数据获取问题")
    print("=" * 60)
    print("问题分析: 大量数据请求导致MT5返回空结果")
    print("解决方案: 实现分块数据获取策略")
    print("=" * 60)
    
    success_count = 0
    total_steps = 4
    
    # 步骤1: 测试大量数据获取
    if test_large_data_retrieval():
        success_count += 1
    
    # 步骤2: 测试分块获取策略
    if test_chunked_data_retrieval():
        success_count += 1
    
    # 步骤3: 实现分块方法
    if implement_chunked_data_method():
        success_count += 1
    
    # 步骤4: 修改获取逻辑
    if modify_data_retrieval_logic():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"🎯 修复完成! 成功: {success_count}/{total_steps}")
    
    if success_count >= 3:
        print("✅ 主要修复步骤完成")
        print("\n💡 修复说明:")
        print("1. 添加了分块数据获取方法")
        print("2. 大于90天的数据自动使用分块获取")
        print("3. 增强了错误处理和重试机制")
        print("4. 保持了对小数据量的兼容性")
        print("\n🔄 请重新启动应用并重试训练")
    else:
        print("⚠️ 修复未完全成功，请检查错误信息")

if __name__ == "__main__":
    main()
