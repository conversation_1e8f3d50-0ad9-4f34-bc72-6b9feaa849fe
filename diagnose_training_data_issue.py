#!/usr/bin/env python3
"""
诊断训练数据问题
"""

import sqlite3
import json
import numpy as np
import torch
from torch.utils.data import Dataset, DataLoader

class TradingDataset(Dataset):
    """交易数据集类"""
    
    def __init__(self, sequences, targets):
        self.sequences = torch.FloatTensor(sequences)
        self.targets = torch.FloatTensor(targets)
    
    def __len__(self):
        return len(self.sequences)
    
    def __getitem__(self, idx):
        return self.sequences[idx], self.targets[idx]

def check_model_config():
    """检查模型配置"""
    print("🔍 检查模型配置")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 查询模型配置
        cursor.execute('''
            SELECT id, name, model_type, symbol, timeframe, config, status
            FROM deep_learning_models 
            WHERE id = 'c7ffc593-4303-45b0-89ef-a44f211770c3'
        ''')
        
        model = cursor.fetchone()
        
        if model:
            print(f"✅ 找到模型:")
            print(f"   模型ID: {model[0]}")
            print(f"   模型名称: {model[1]}")
            print(f"   模型类型: {model[2]}")
            print(f"   交易品种: {model[3]}")
            print(f"   时间框架: {model[4]}")
            print(f"   状态: {model[6]}")
            
            if model[5]:  # config
                try:
                    config = json.loads(model[5])
                    print(f"\n📊 模型配置:")
                    print(f"   序列长度: {config.get('sequence_length', 'N/A')}")
                    print(f"   批次大小: {config.get('batch_size', 'N/A')}")
                    print(f"   学习率: {config.get('learning_rate', 'N/A')}")
                    print(f"   轮次: {config.get('epochs', 'N/A')}")
                    print(f"   特征配置: {config.get('features', 'N/A')}")
                    print(f"   数据配置: {config.get('data_config', 'N/A')}")
                    
                    return config
                except Exception as e:
                    print(f"   ❌ 配置解析失败: {e}")
                    return None
            else:
                print(f"   ⚠️ 没有配置信息")
                return None
        else:
            print(f"❌ 未找到模型")
            return None
            
    except Exception as e:
        print(f"❌ 数据库查询失败: {e}")
        return None
    finally:
        if conn:
            conn.close()

def simulate_data_preparation(config):
    """模拟数据准备过程"""
    print(f"\n🔍 模拟数据准备过程")
    print("=" * 50)
    
    if not config:
        print("❌ 没有配置信息，无法模拟")
        return None, None, None, None
    
    try:
        # 模拟数据参数
        sequence_length = config.get('sequence_length', 60)
        batch_size = config.get('batch_size', 32)
        
        print(f"📊 数据参数:")
        print(f"   序列长度: {sequence_length}")
        print(f"   批次大小: {batch_size}")
        
        # 创建模拟数据
        print(f"\n🔄 创建模拟数据...")
        
        # 模拟价格数据 (1000个数据点，5个特征：OHLCV)
        num_samples = 1000
        num_features = 8  # 假设有8个特征
        
        print(f"   样本数量: {num_samples}")
        print(f"   特征数量: {num_features}")
        
        # 创建序列数据
        sequences = []
        targets = []
        
        for i in range(num_samples - sequence_length):
            # 创建随机序列数据
            seq = np.random.randn(sequence_length, num_features).astype(np.float32)
            target = np.random.randint(0, 2, dtype=np.float32)  # 二分类目标
            
            sequences.append(seq)
            targets.append(target)
        
        X = np.array(sequences)
        y = np.array(targets)
        
        print(f"   序列数据形状: {X.shape}")
        print(f"   目标数据形状: {y.shape}")
        
        # 分割训练和验证集
        split_idx = int(len(X) * 0.8)
        X_train, X_val = X[:split_idx], X[split_idx:]
        y_train, y_val = y[:split_idx], y[split_idx:]
        
        print(f"   训练集形状: {X_train.shape}")
        print(f"   验证集形状: {X_val.shape}")
        
        return X_train, X_val, y_train, y_val
        
    except Exception as e:
        print(f"❌ 数据准备失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return None, None, None, None

def test_data_loader(X_train, X_val, y_train, y_val, config):
    """测试数据加载器"""
    print(f"\n🔍 测试数据加载器")
    print("=" * 50)
    
    if X_train is None:
        print("❌ 没有训练数据，跳过测试")
        return False
    
    try:
        # 创建数据集
        print(f"📦 创建数据集...")
        train_dataset = TradingDataset(X_train, y_train)
        val_dataset = TradingDataset(X_val, y_val)
        
        print(f"   训练数据集大小: {len(train_dataset)}")
        print(f"   验证数据集大小: {len(val_dataset)}")
        
        # 创建数据加载器
        batch_size = min(config.get('batch_size', 32), 16)
        print(f"   批次大小: {batch_size}")
        
        train_loader = DataLoader(
            train_dataset,
            batch_size=batch_size,
            shuffle=True,
            num_workers=0,
            pin_memory=False
        )
        
        val_loader = DataLoader(
            val_dataset,
            batch_size=batch_size,
            shuffle=False,
            num_workers=0,
            pin_memory=False
        )
        
        print(f"   训练批次数量: {len(train_loader)}")
        print(f"   验证批次数量: {len(val_loader)}")
        
        # 测试第一个批次
        print(f"\n🔄 测试第一个训练批次...")
        
        import time
        start_time = time.time()
        
        try:
            for batch_idx, (batch_X, batch_y) in enumerate(train_loader):
                print(f"   批次 {batch_idx}: X形状={batch_X.shape}, y形状={batch_y.shape}")
                print(f"   X数据类型: {batch_X.dtype}")
                print(f"   y数据类型: {batch_y.dtype}")
                print(f"   X数据范围: {batch_X.min():.4f} - {batch_X.max():.4f}")
                print(f"   y数据范围: {batch_y.min():.4f} - {batch_y.max():.4f}")
                
                # 只测试前3个批次
                if batch_idx >= 2:
                    break
                    
        except Exception as loader_error:
            print(f"   ❌ 数据加载器测试失败: {loader_error}")
            import traceback
            print(f"   详细错误: {traceback.format_exc()}")
            return False
        
        end_time = time.time()
        print(f"   ✅ 数据加载器测试成功，耗时: {end_time - start_time:.3f}秒")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据加载器创建失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def test_gpu_operations():
    """测试GPU操作"""
    print(f"\n🔍 测试GPU操作")
    print("=" * 50)
    
    try:
        # 检查CUDA可用性
        cuda_available = torch.cuda.is_available()
        print(f"   CUDA可用: {cuda_available}")
        
        if cuda_available:
            print(f"   GPU设备数量: {torch.cuda.device_count()}")
            print(f"   当前GPU: {torch.cuda.current_device()}")
            print(f"   GPU名称: {torch.cuda.get_device_name()}")
            
            # 测试GPU内存分配
            print(f"\n🔄 测试GPU内存分配...")
            device = torch.device('cuda')
            
            # 创建测试张量
            test_tensor = torch.randn(100, 60, 8).to(device)
            print(f"   测试张量形状: {test_tensor.shape}")
            print(f"   测试张量设备: {test_tensor.device}")
            
            # 测试简单计算
            result = test_tensor.mean()
            print(f"   计算结果: {result.item():.4f}")
            
            # 清理内存
            del test_tensor
            torch.cuda.empty_cache()
            
            print(f"   ✅ GPU操作测试成功")
            return True
        else:
            print(f"   ⚠️ CUDA不可用，将使用CPU")
            return True
            
    except Exception as e:
        print(f"❌ GPU测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def main():
    """主函数"""
    print("🔧 训练数据问题诊断")
    print("=" * 80)
    
    # 1. 检查模型配置
    config = check_model_config()
    
    # 2. 模拟数据准备
    X_train, X_val, y_train, y_val = simulate_data_preparation(config)
    
    # 3. 测试数据加载器
    if config:
        data_loader_ok = test_data_loader(X_train, X_val, y_train, y_val, config)
    else:
        data_loader_ok = False
    
    # 4. 测试GPU操作
    gpu_ok = test_gpu_operations()
    
    # 5. 总结
    print(f"\n📊 诊断结果总结")
    print("=" * 80)
    
    if config:
        print(f"✅ 模型配置: 正常")
    else:
        print(f"❌ 模型配置: 异常")
    
    if data_loader_ok:
        print(f"✅ 数据加载器: 正常")
    else:
        print(f"❌ 数据加载器: 异常")
    
    if gpu_ok:
        print(f"✅ GPU操作: 正常")
    else:
        print(f"❌ GPU操作: 异常")
    
    if config and data_loader_ok and gpu_ok:
        print(f"\n💡 可能的问题:")
        print(f"   • 实际数据量过大，导致内存不足")
        print(f"   • MT5数据获取耗时过长")
        print(f"   • 特征计算过程卡住")
        print(f"   • attention_lstm模型复杂度过高")
        
        print(f"\n🔧 建议解决方案:")
        print(f"   1. 减少序列长度 (当前: {config.get('sequence_length', 60)})")
        print(f"   2. 减少批次大小 (当前: {config.get('batch_size', 32)})")
        print(f"   3. 减少训练数据天数")
        print(f"   4. 使用更简单的模型 (如LSTM而不是attention_lstm)")
        print(f"   5. 检查MT5连接和数据获取")
    else:
        print(f"\n❌ 发现基础问题，需要先解决配置或环境问题")

if __name__ == '__main__':
    main()
