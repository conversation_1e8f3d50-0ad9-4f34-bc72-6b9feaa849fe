#!/usr/bin/env python3
"""
诊断卡住的训练任务：3f0e9a54-2111-4ec0-849b-8b4640a6f268
"""

import requests
import json
import sqlite3
import time
from datetime import datetime

def login_session():
    """登录并返回session"""
    session = requests.Session()
    login_data = {'username': 'admin', 'password': 'admin123'}
    
    try:
        response = session.post('http://127.0.0.1:5000/login', data=login_data)
        if response.status_code == 200:
            print("✅ 登录成功")
            return session
        else:
            print(f"❌ 登录失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return None

def check_database_task_status(task_id):
    """检查数据库中的任务状态"""
    print(f"\n🔍 检查数据库中的任务状态")
    print("=" * 50)
    
    try:
        # 连接数据库
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 查询训练任务
        cursor.execute('''
            SELECT id, model_id, status, progress, current_epoch, total_epochs, 
                   train_loss, val_loss, created_at, started_at, updated_at, logs
            FROM training_tasks 
            WHERE id = ?
        ''', (task_id,))
        
        task = cursor.fetchone()
        
        if task:
            print(f"✅ 找到任务:")
            print(f"   任务ID: {task[0]}")
            print(f"   模型ID: {task[1]}")
            print(f"   状态: {task[2]}")
            print(f"   进度: {task[3]}%")
            print(f"   当前轮次: {task[4]}/{task[5]}")
            print(f"   训练损失: {task[6]}")
            print(f"   验证损失: {task[7]}")
            print(f"   创建时间: {task[8]}")
            print(f"   开始时间: {task[9]}")
            print(f"   更新时间: {task[10]}")
            
            if task[11]:  # logs
                try:
                    logs = json.loads(task[11])
                    print(f"   日志: {logs}")
                except:
                    print(f"   原始日志: {task[11]}")
            
            # 检查模型信息
            cursor.execute('''
                SELECT name, model_type, symbol, timeframe, status
                FROM deep_learning_models 
                WHERE id = ?
            ''', (task[1],))
            
            model = cursor.fetchone()
            if model:
                print(f"\n📊 关联模型:")
                print(f"   模型名称: {model[0]}")
                print(f"   模型类型: {model[1]}")
                print(f"   交易品种: {model[2]}")
                print(f"   时间框架: {model[3]}")
                print(f"   模型状态: {model[4]}")
            
            return task
        else:
            print(f"❌ 未找到任务: {task_id}")
            return None
            
    except Exception as e:
        print(f"❌ 数据库查询失败: {e}")
        return None
    finally:
        if conn:
            conn.close()

def check_api_progress(session, task_id):
    """检查API进度响应"""
    print(f"\n🔍 检查API进度响应")
    print("=" * 50)
    
    try:
        response = session.get(f'http://127.0.0.1:5000/api/deep-learning/training-progress/{task_id}')
        
        print(f"   HTTP状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"   API响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            return result
        else:
            print(f"   ❌ API请求失败")
            print(f"   响应内容: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ API请求异常: {e}")
        return None

def check_control_status(session, task_id):
    """检查训练控制状态"""
    print(f"\n🔍 检查训练控制状态")
    print("=" * 50)
    
    try:
        response = session.get(f'http://127.0.0.1:5000/api/deep-learning/training/{task_id}/control-status')
        
        print(f"   HTTP状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"   控制状态: {json.dumps(result, indent=2, ensure_ascii=False)}")
            return result
        else:
            print(f"   ❌ 控制状态请求失败")
            return None
            
    except Exception as e:
        print(f"❌ 控制状态请求异常: {e}")
        return None

def check_gpu_status(session):
    """检查GPU状态"""
    print(f"\n🔍 检查GPU状态")
    print("=" * 50)
    
    try:
        response = session.get('http://127.0.0.1:5000/api/deep-learning/gpu-status')
        
        print(f"   HTTP状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"   GPU状态: {json.dumps(result, indent=2, ensure_ascii=False)}")
            return result
        else:
            print(f"   ❌ GPU状态请求失败")
            return None
            
    except Exception as e:
        print(f"❌ GPU状态请求异常: {e}")
        return None

def monitor_progress_changes(session, task_id, duration=60):
    """监控进度变化"""
    print(f"\n📊 监控进度变化 ({duration}秒)")
    print("=" * 50)
    
    last_progress = None
    last_epoch = None
    last_update_time = None
    changes_detected = 0
    
    for i in range(duration // 5):  # 每5秒检查一次
        try:
            response = session.get(f'http://127.0.0.1:5000/api/deep-learning/training-progress/{task_id}')
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get('success'):
                    progress_data = result['progress']
                    current_progress = progress_data.get('progress', 0)
                    current_epoch = progress_data.get('epoch', 0)
                    current_status = progress_data.get('status', 'unknown')
                    
                    print(f"   第{i+1}次检查: 状态={current_status}, 进度={current_progress}%, 轮次={current_epoch}")
                    
                    # 检查是否有变化
                    if last_progress is not None:
                        if current_progress != last_progress or current_epoch != last_epoch:
                            changes_detected += 1
                            print(f"     ✅ 检测到进度变化!")
                    
                    last_progress = current_progress
                    last_epoch = current_epoch
                    last_update_time = datetime.now()
                    
                    # 如果训练完成或失败，停止监控
                    if current_status in ['completed', 'failed', 'stopped']:
                        print(f"   🏁 训练结束: {current_status}")
                        break
                        
                else:
                    print(f"   ❌ API错误: {result.get('error')}")
            else:
                print(f"   ❌ HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ 监控异常: {e}")
        
        time.sleep(5)
    
    print(f"\n📈 监控结果:")
    print(f"   检查次数: {i+1}")
    print(f"   变化次数: {changes_detected}")
    print(f"   最后进度: {last_progress}%")
    print(f"   最后轮次: {last_epoch}")
    
    return changes_detected > 0

def suggest_solutions(task_data, api_data, control_data):
    """建议解决方案"""
    print(f"\n💡 问题诊断和解决建议")
    print("=" * 50)
    
    if not task_data:
        print("❌ 任务不存在于数据库中")
        print("建议：检查任务ID是否正确")
        return
    
    status = task_data[2]  # status字段
    progress = task_data[3]  # progress字段
    updated_at = task_data[10]  # updated_at字段
    
    print(f"📊 当前状态分析:")
    print(f"   数据库状态: {status}")
    print(f"   数据库进度: {progress}%")
    print(f"   最后更新: {updated_at}")
    
    # 分析问题
    problems = []
    solutions = []
    
    if status == 'running':
        if api_data and api_data.get('success'):
            api_status = api_data['progress'].get('status')
            if api_status != status:
                problems.append(f"状态不一致: DB={status}, API={api_status}")
        
        # 检查更新时间
        if updated_at:
            try:
                from datetime import datetime
                last_update = datetime.fromisoformat(updated_at)
                now = datetime.now()
                time_diff = (now - last_update).total_seconds()
                
                if time_diff > 300:  # 5分钟没更新
                    problems.append(f"进度长时间未更新: {time_diff/60:.1f}分钟")
                    solutions.append("重启训练任务")
                    solutions.append("检查GPU资源使用情况")
                    solutions.append("减少批次大小")
            except:
                pass
    
    elif status in ['failed', 'stopped']:
        problems.append(f"训练已{status}")
        solutions.append("重新启动训练")
        solutions.append("检查错误日志")
    
    elif status == 'pending':
        problems.append("训练尚未开始")
        solutions.append("等待训练启动")
        solutions.append("检查系统资源")
    
    if problems:
        print(f"\n⚠️ 发现的问题:")
        for problem in problems:
            print(f"   • {problem}")
    
    if solutions:
        print(f"\n🔧 建议的解决方案:")
        for solution in solutions:
            print(f"   • {solution}")
    
    if not problems:
        print(f"✅ 未发现明显问题，训练可能正在正常进行")

def main():
    """主函数"""
    task_id = "3f0e9a54-2111-4ec0-849b-8b4640a6f268"
    
    print("🔧 诊断卡住的训练任务")
    print("=" * 80)
    print(f"任务ID: {task_id}")
    
    # 1. 检查数据库状态
    task_data = check_database_task_status(task_id)
    
    # 2. 登录并检查API
    session = login_session()
    if not session:
        return
    
    # 3. 检查API进度
    api_data = check_api_progress(session, task_id)
    
    # 4. 检查控制状态
    control_data = check_control_status(session, task_id)
    
    # 5. 检查GPU状态
    gpu_data = check_gpu_status(session)
    
    # 6. 监控进度变化
    progress_changing = monitor_progress_changes(session, task_id, duration=30)
    
    # 7. 分析问题并建议解决方案
    suggest_solutions(task_data, api_data, control_data)
    
    if not progress_changing:
        print(f"\n⚠️ 确认：训练进度确实没有变化")
        print(f"💡 立即解决方案:")
        print(f"   1. 停止当前训练: POST /api/deep-learning/training/{task_id}/stop")
        print(f"   2. 重新启动训练")
        print(f"   3. 检查系统资源和GPU状态")
    else:
        print(f"\n✅ 训练进度正在正常更新")

if __name__ == '__main__':
    main()
