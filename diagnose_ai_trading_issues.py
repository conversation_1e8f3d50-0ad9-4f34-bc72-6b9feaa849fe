#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断AI推理交易问题：
1. 数据库显示有持仓但MT5没有订单
2. 持仓卡片显示问题
"""

import sqlite3
import os
import json
from datetime import datetime

def check_ai_trades_database():
    """检查AI交易数据库记录"""
    print("🔍 检查AI交易数据库记录")
    print("=" * 50)
    
    # 尝试多个可能的数据库路径
    db_paths = [
        'instance/matetrade4.db',
        'matetrade4.db', 
        'trading_system.db',
        'instance/trading_system.db'
    ]
    
    conn = None
    db_path = None
    for path in db_paths:
        if os.path.exists(path):
            try:
                conn = sqlite3.connect(path)
                db_path = path
                print(f"✅ 连接到数据库: {path}")
                break
            except Exception as e:
                print(f"❌ 连接数据库失败 {path}: {e}")
                continue
    
    if not conn:
        print("❌ 无法连接到任何数据库文件")
        return False
    
    try:
        cursor = conn.cursor()
        
        # 检查ai_trades表
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='ai_trades'
        """)
        
        if not cursor.fetchone():
            print("❌ ai_trades表不存在")
            return False
        
        print("✅ ai_trades表存在")
        
        # 检查表结构
        cursor.execute("PRAGMA table_info(ai_trades)")
        columns = cursor.fetchall()
        print(f"📊 表结构 ({len(columns)}个字段):")
        for col in columns:
            print(f"   - {col[1]} ({col[2]})")
        
        # 检查数据
        cursor.execute("SELECT COUNT(*) FROM ai_trades")
        total_count = cursor.fetchone()[0]
        print(f"\n📊 总交易记录数: {total_count}")
        
        cursor.execute("SELECT COUNT(*) FROM ai_trades WHERE status = 'open'")
        open_count = cursor.fetchone()[0]
        print(f"📊 开仓记录数: {open_count}")
        
        cursor.execute("SELECT COUNT(*) FROM ai_trades WHERE status = 'closed'")
        closed_count = cursor.fetchone()[0]
        print(f"📊 平仓记录数: {closed_count}")
        
        # 显示最近的开仓记录
        cursor.execute("""
            SELECT id, symbol, action, lot_size, entry_price, order_id, created_at, status
            FROM ai_trades 
            WHERE status = 'open'
            ORDER BY created_at DESC 
            LIMIT 5
        """)
        open_trades = cursor.fetchall()
        
        if open_trades:
            print(f"\n📋 当前开仓记录 ({len(open_trades)}条):")
            for trade in open_trades:
                trade_id, symbol, action, lot_size, entry_price, order_id, created_at, status = trade
                print(f"   {trade_id[:8]}... | {symbol} | {action} | {lot_size} | {entry_price} | {order_id} | {created_at} | {status}")
        else:
            print("\n📋 没有开仓记录")
        
        # 显示最近的所有记录
        cursor.execute("""
            SELECT id, symbol, action, lot_size, entry_price, order_id, created_at, status
            FROM ai_trades 
            ORDER BY created_at DESC 
            LIMIT 10
        """)
        recent_trades = cursor.fetchall()
        
        if recent_trades:
            print(f"\n📋 最近10条交易记录:")
            for trade in recent_trades:
                trade_id, symbol, action, lot_size, entry_price, order_id, created_at, status = trade
                print(f"   {trade_id[:8]}... | {symbol} | {action} | {lot_size} | {entry_price} | {order_id} | {created_at} | {status}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 检查数据库失败: {e}")
        conn.close()
        return False

def analyze_mt5_order_issue():
    """分析MT5订单问题"""
    print("\n🔍 分析MT5订单问题")
    print("=" * 50)
    
    print("📋 可能的原因:")
    print("1. MT5连接问题")
    print("   - MT5终端未运行")
    print("   - MT5连接断开")
    print("   - 账户登录问题")
    print()
    
    print("2. 订单执行失败")
    print("   - 市场关闭")
    print("   - 资金不足")
    print("   - 订单参数错误")
    print("   - 经纪商限制")
    print()
    
    print("3. 数据同步问题")
    print("   - 数据库记录了但MT5执行失败")
    print("   - 订单ID记录错误")
    print("   - 状态更新不及时")
    print()
    
    print("🔧 诊断步骤:")
    print("1. 检查MT5连接状态")
    print("2. 查看MT5终端的专家日志")
    print("3. 检查账户余额和保证金")
    print("4. 验证交易时间和市场状态")
    print("5. 检查订单执行返回结果")

def analyze_position_display_issue():
    """分析持仓显示问题"""
    print("\n🔍 分析持仓卡片显示问题")
    print("=" * 50)
    
    print("📋 当前问题:")
    print("• 持仓数据显示在统计中，但没有卡片展示")
    print("• 需要在'平仓所有持仓'按钮下方显示详细卡片")
    print()
    
    print("🔧 需要实现的功能:")
    print("1. 持仓卡片自动显示")
    print("   - 当有持仓时自动显示卡片区域")
    print("   - 没有持仓时隐藏卡片区域")
    print()
    
    print("2. 卡片详细信息")
    print("   - 交易品种和方向")
    print("   - 手数和价格信息")
    print("   - 止损止盈设置")
    print("   - 当前盈亏状态")
    print("   - 持仓时间")
    print("   - 平仓操作按钮")
    print()
    
    print("3. 实时更新")
    print("   - 盈亏实时计算")
    print("   - 价格实时更新")
    print("   - 状态同步")

def generate_fix_recommendations():
    """生成修复建议"""
    print("\n💡 修复建议")
    print("=" * 50)
    
    print("🔧 修复MT5订单问题:")
    print("1. 增强MT5连接检查")
    print("   - 在执行交易前验证MT5连接")
    print("   - 添加连接重试机制")
    print("   - 记录详细的连接状态")
    print()
    
    print("2. 改进订单执行逻辑")
    print("   - 添加订单执行前的预检查")
    print("   - 验证账户状态和余额")
    print("   - 检查市场开放状态")
    print("   - 记录详细的执行日志")
    print()
    
    print("3. 完善错误处理")
    print("   - 订单失败时回滚数据库记录")
    print("   - 提供详细的错误信息")
    print("   - 实现自动重试机制")
    print()
    
    print("🔧 修复持仓卡片显示:")
    print("1. 修改前端逻辑")
    print("   - 在updateTradingStatistics中触发持仓卡片显示")
    print("   - 确保loadPositionDetails正确调用")
    print("   - 验证持仓数据获取API")
    print()
    
    print("2. 优化卡片样式")
    print("   - 确保卡片在正确位置显示")
    print("   - 添加响应式布局")
    print("   - 优化信息展示")
    print()
    
    print("3. 实现实时更新")
    print("   - 定期刷新持仓数据")
    print("   - 实时计算盈亏")
    print("   - 同步MT5状态")

def main():
    """主函数"""
    print("🔧 AI推理交易问题诊断工具")
    print("=" * 80)
    
    print("📋 问题描述:")
    print("1. AI推理交易显示'今日交易2，当前持仓3'")
    print("2. 但MT5软件上没有订单显示")
    print("3. 持仓卡片没有在指定位置显示")
    print()
    
    # 检查数据库
    db_success = check_ai_trades_database()
    
    # 分析问题
    analyze_mt5_order_issue()
    analyze_position_display_issue()
    
    # 生成修复建议
    generate_fix_recommendations()
    
    print("\n📊 诊断总结")
    print("=" * 80)
    
    if db_success:
        print("✅ 数据库连接正常，可以查看交易记录")
        print("🔍 主要问题可能是:")
        print("   1. MT5订单执行失败但数据库已记录")
        print("   2. 持仓卡片显示逻辑未正确实现")
        print()
        print("💡 建议优先修复:")
        print("   1. 检查MT5连接和订单执行逻辑")
        print("   2. 实现持仓卡片显示功能")
        print("   3. 添加数据一致性检查")
    else:
        print("❌ 数据库连接失败，需要先解决数据库问题")
    
    return 0 if db_success else 1

if __name__ == "__main__":
    exit(main())
