#!/usr/bin/env python3
"""
修复训练卡住问题
"""

import requests
import sqlite3
import json
import time
from datetime import datetime

def login_session():
    """创建登录会话"""
    session = requests.Session()
    
    try:
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post('http://127.0.0.1:5000/login', data=login_data)
        return session if response.status_code == 200 else None
            
    except Exception as e:
        print(f"❌ 登录失败: {e}")
        return None

def clean_stuck_tasks():
    """清理卡住的训练任务"""
    
    print("🧹 清理卡住的训练任务")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 查找可能卡住的任务（超过10分钟无更新的running任务）
        cursor.execute("""
            SELECT id, model_id, status, progress, updated_at
            FROM training_tasks 
            WHERE status IN ('running', 'pending')
            AND (
                updated_at IS NULL 
                OR datetime(updated_at) < datetime('now', '-10 minutes')
            )
        """)
        
        stuck_tasks = cursor.fetchall()
        
        if stuck_tasks:
            print(f"📊 发现 {len(stuck_tasks)} 个可能卡住的任务:")
            
            for task in stuck_tasks:
                task_id, model_id, status, progress, updated_at = task
                
                print(f"\n🔹 清理任务: {task_id[:8]}...")
                print(f"   状态: {status}")
                print(f"   进度: {progress}%")
                print(f"   更新时间: {updated_at}")
                
                # 将任务标记为失败
                cursor.execute("""
                    UPDATE training_tasks
                    SET status = 'failed', 
                        completed_at = ?,
                        updated_at = ?,
                        logs = json_set(COALESCE(logs, '{}'), '$.error', '任务卡住，已自动清理')
                    WHERE id = ?
                """, (datetime.now().isoformat(), datetime.now().isoformat(), task_id))
                
                # 同时更新模型状态
                cursor.execute("""
                    UPDATE deep_learning_models
                    SET status = 'failed'
                    WHERE id = ?
                """, (model_id,))
                
                print(f"   ✅ 任务已标记为失败")
            
            conn.commit()
            print(f"\n✅ 已清理 {len(stuck_tasks)} 个卡住的任务")
            
        else:
            print("✅ 没有发现卡住的任务")
        
        conn.close()
        return len(stuck_tasks) if stuck_tasks else 0
        
    except Exception as e:
        print(f"❌ 清理卡住任务失败: {e}")
        return 0

def start_optimized_training():
    """启动优化的训练任务"""
    
    print(f"\n🚀 启动优化的训练任务")
    print("=" * 50)
    
    session = login_session()
    if not session:
        print("❌ 无法登录")
        return None
    
    # 使用更保守的配置，避免卡住
    config = {
        'model_name': f'fixed_training_{int(time.time())}',
        'model_type': 'LSTM',  # 使用最稳定的LSTM
        'symbol': 'XAUUSD',
        'timeframe': 'H1',
        'epochs': 20,  # 减少轮次，快速验证
        'batch_size': 4,   # 很小的批次大小
        'learning_rate': 0.01,
        'validation_split': 0.2,
        'sequence_length': 10,  # 很短的序列长度
        'features': ['close'],  # 只使用一个特征
        'early_stopping': True,
        'patience': 10,
        'min_epochs': 3,  # 最少3轮就可以
        'use_gpu': True,
        'save_checkpoints': False  # 禁用检查点保存，减少IO
    }
    
    print(f"📝 优化配置:")
    print(f"   轮次: {config['epochs']} (减少)")
    print(f"   批次大小: {config['batch_size']} (很小)")
    print(f"   序列长度: {config['sequence_length']} (很短)")
    print(f"   特征数量: {len(config['features'])} (最少)")
    print(f"   检查点: {config['save_checkpoints']} (禁用)")
    
    try:
        response = session.post(
            'http://127.0.0.1:5000/api/deep-learning/start-training',
            json=config,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                task_id = result.get('task_id')
                print(f"✅ 优化训练启动成功!")
                print(f"   任务ID: {task_id}")
                return task_id
            else:
                print(f"❌ 训练启动失败: {result.get('error')}")
                return None
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 启动训练失败: {e}")
        return None

def monitor_training_health(task_id, duration=120):
    """监控训练健康状态"""
    
    print(f"\n📊 监控训练健康状态 (任务: {task_id[:8]}..., 时长: {duration//60}分钟)")
    print("=" * 60)
    
    session = login_session()
    if not session:
        print("❌ 无法登录")
        return False
    
    start_time = time.time()
    last_progress = -1
    last_epoch = -1
    progress_updates = 0
    epoch_updates = 0
    
    print(f"🔄 开始健康监控:")
    
    for i in range(duration // 5):  # 每5秒检查一次
        try:
            current_time = time.time()
            elapsed = current_time - start_time
            
            # 获取训练进度
            response = session.get(f'http://127.0.0.1:5000/api/deep-learning/training-progress/{task_id}')
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get('success'):
                    progress_data = result['progress']
                    
                    current_status = progress_data.get('status', 'unknown')
                    current_progress = progress_data.get('progress', 0)
                    current_epoch = progress_data.get('epoch', 0)
                    train_loss = progress_data.get('train_loss')
                    val_loss = progress_data.get('val_loss')
                    
                    # 检查进度更新
                    if current_progress != last_progress:
                        last_progress = current_progress
                        progress_updates += 1
                        print(f"   [{elapsed:.0f}s] 📈 进度更新: {current_progress}%")
                    
                    # 检查轮次更新
                    if current_epoch != last_epoch:
                        last_epoch = current_epoch
                        epoch_updates += 1
                        print(f"   [{elapsed:.0f}s] 🔄 轮次更新: {current_epoch}")
                        
                        if train_loss is not None:
                            print(f"   [{elapsed:.0f}s] 📉 损失: 训练={train_loss:.4f}, 验证={val_loss:.4f if val_loss else 'N/A'}")
                    
                    # 检查训练状态
                    if current_status == 'completed':
                        print(f"   [{elapsed:.0f}s] 🎉 训练完成!")
                        return True
                    elif current_status == 'failed':
                        print(f"   [{elapsed:.0f}s] ❌ 训练失败!")
                        return False
                    elif current_status not in ['running', 'pending']:
                        print(f"   [{elapsed:.0f}s] ⚠️ 训练状态异常: {current_status}")
                
                else:
                    print(f"   [{elapsed:.0f}s] ❌ API错误: {result.get('error')}")
            else:
                print(f"   [{elapsed:.0f}s] ❌ HTTP错误: {response.status_code}")
            
            time.sleep(5)  # 每5秒检查一次
            
        except Exception as e:
            print(f"   [{elapsed:.0f}s] ❌ 监控异常: {e}")
            time.sleep(5)
    
    # 分析健康状态
    print(f"\n📊 训练健康分析:")
    print(f"   监控时长: {duration}秒")
    print(f"   进度更新次数: {progress_updates}")
    print(f"   轮次更新次数: {epoch_updates}")
    
    if progress_updates > 0 or epoch_updates > 0:
        print(f"   ✅ 训练正常运行，有进度更新")
        return True
    else:
        print(f"   ❌ 训练可能卡住，无进度更新")
        return False

def main():
    """主函数"""
    
    print("🔧 训练卡住问题修复")
    print("=" * 80)
    
    print("📋 问题诊断结果:")
    print("• 发现多个训练任务卡在25%进度")
    print("• GPU使用率0%，表明训练循环未真正开始")
    print("• 可能在数据加载或模型初始化阶段卡住")
    
    # 1. 清理卡住的任务
    cleaned_count = clean_stuck_tasks()
    
    # 2. 启动优化的训练任务
    task_id = start_optimized_training()
    
    if task_id:
        # 3. 监控新任务的健康状态
        print(f"\n⏳ 等待训练开始...")
        time.sleep(10)  # 等待10秒让训练开始
        
        is_healthy = monitor_training_health(task_id, duration=120)  # 监控2分钟
        
        print(f"\n📋 修复结果")
        print("=" * 80)
        
        if is_healthy:
            print(f"🎉 训练卡住问题修复成功!")
            print(f"✅ 清理了 {cleaned_count} 个卡住的任务")
            print(f"✅ 新训练任务正常运行")
            print(f"✅ 进度正常更新")
            
            print(f"\n💡 修复措施:")
            print(f"• 使用了更保守的训练配置")
            print(f"• 减少了批次大小和序列长度")
            print(f"• 简化了特征选择")
            print(f"• 禁用了检查点保存")
            print(f"• 减少了训练轮次")
            
        else:
            print(f"⚠️ 训练卡住问题可能仍然存在")
            print(f"✅ 清理了 {cleaned_count} 个卡住的任务")
            print(f"❌ 新训练任务可能也卡住了")
            
            print(f"\n💡 进一步建议:")
            print(f"• 重启应用程序")
            print(f"• 检查MT5连接状态")
            print(f"• 检查系统内存和磁盘空间")
            print(f"• 尝试使用CPU模式训练")
            
    else:
        print(f"\n❌ 无法启动新的训练任务")
        print(f"✅ 清理了 {cleaned_count} 个卡住的任务")
        
        print(f"\n💡 建议:")
        print(f"• 检查应用程序日志")
        print(f"• 重启应用程序")
        print(f"• 检查数据库连接")
    
    print(f"\n🎯 预防措施")
    print("=" * 80)
    
    print(f"📊 训练配置建议:")
    print(f"• 批次大小: 4-16 (避免过大)")
    print(f"• 序列长度: 10-30 (避免过长)")
    print(f"• 特征数量: 1-3 (从简单开始)")
    print(f"• 训练轮次: 10-50 (避免过多)")
    print(f"• 启用早停: 防止无限训练")
    
    print(f"\n🔧 监控建议:")
    print(f"• 定期检查训练进度")
    print(f"• 关注GPU使用率")
    print(f"• 设置合理的超时时间")
    print(f"• 保存训练日志用于调试")

if __name__ == '__main__':
    main()
