"""
止盈止损监控服务
自动监控MT5持仓，根据设置的止盈止损条件自动平仓
"""

import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import MetaTrader5 as mt5


class StopLossTakeProfitService:
    """止盈止损监控服务"""
    
    def __init__(self):
        self.monitoring = False
        self.monitor_thread = None
        self.stop_event = threading.Event()
        self.check_interval = 5  # 每5秒检查一次
        
    def start_monitoring(self):
        """开始监控"""
        if self.monitoring:
            print("⚠️ 止盈止损监控已在运行")
            return
        
        print("🚀 启动止盈止损监控服务...")
        self.monitoring = True
        self.stop_event.clear()
        
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        
        print("✅ 止盈止损监控服务已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        if not self.monitoring:
            print("⚠️ 止盈止损监控未在运行")
            return
        
        print("🛑 停止止盈止损监控服务...")
        self.monitoring = False
        self.stop_event.set()
        
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=10)
        
        print("✅ 止盈止损监控服务已停止")
    
    def _monitor_loop(self):
        """监控循环"""
        print("🔄 止盈止损监控循环开始...")
        
        while not self.stop_event.is_set():
            try:
                self._check_all_positions()
                
                # 等待下次检查
                self.stop_event.wait(timeout=self.check_interval)
                
            except Exception as e:
                print(f"❌ 止盈止损监控异常: {e}")
                time.sleep(10)  # 出错时等待10秒
    
    def _check_all_positions(self):
        """检查所有持仓"""
        try:
            from services.mt5_service import mt5_service
            
            # 获取所有持仓
            positions = mt5_service.get_positions()
            
            if not positions:
                return
            
            print(f"🔍 检查 {len(positions)} 个持仓的止盈止损条件...")
            
            for position in positions:
                self._check_position_stop_loss_take_profit(position)
                
        except Exception as e:
            print(f"❌ 检查持仓异常: {e}")
    
    def _check_position_stop_loss_take_profit(self, position: Dict):
        """检查单个持仓的止盈止损条件"""
        try:
            symbol = position['symbol']
            ticket = position['ticket']
            position_type = position['type']  # 0=买入, 1=卖出
            open_price = position['price_open']
            current_price = position['price_current']
            volume = position['volume']
            profit = position['profit']
            
            # 检查是否已设置止损止盈
            sl = position.get('sl', 0)  # 止损价格
            tp = position.get('tp', 0)  # 止盈价格
            
            should_close = False
            close_reason = ""
            
            # 检查止损
            if sl > 0:
                if position_type == 0:  # 买入持仓
                    if current_price <= sl:
                        should_close = True
                        close_reason = "止损平仓"
                else:  # 卖出持仓
                    if current_price >= sl:
                        should_close = True
                        close_reason = "止损平仓"
            
            # 检查止盈
            if tp > 0 and not should_close:
                if position_type == 0:  # 买入持仓
                    if current_price >= tp:
                        should_close = True
                        close_reason = "止盈平仓"
                else:  # 卖出持仓
                    if current_price <= tp:
                        should_close = True
                        close_reason = "止盈平仓"
            
            # 执行平仓
            if should_close:
                print(f"🎯 触发{close_reason}: {symbol} 订单{ticket}")
                print(f"   开仓价: {open_price}, 当前价: {current_price}")
                print(f"   止损: {sl}, 止盈: {tp}, 盈亏: ${profit:.2f}")
                
                self._execute_auto_close(ticket, close_reason)
            
        except Exception as e:
            print(f"❌ 检查持仓 {position.get('ticket', 'Unknown')} 异常: {e}")
    
    def _execute_auto_close(self, ticket: int, reason: str):
        """执行自动平仓"""
        try:
            from services.mt5_service import mt5_service
            
            print(f"🔄 执行自动平仓: 订单{ticket} - {reason}")
            
            result = mt5_service.close_position(ticket)
            
            if result and hasattr(result, 'retcode') and result.retcode == 10009:
                print(f"✅ 自动平仓成功: 订单{ticket} - {reason}")
                
                # 记录平仓日志
                self._log_auto_close(ticket, reason, True, result.order)
                
            else:
                error_msg = result.comment if result else '未知错误'
                print(f"❌ 自动平仓失败: 订单{ticket} - {error_msg}")
                
                # 记录失败日志
                self._log_auto_close(ticket, reason, False, error_msg)
                
        except Exception as e:
            print(f"❌ 执行自动平仓异常: 订单{ticket} - {e}")
            self._log_auto_close(ticket, reason, False, str(e))
    
    def _log_auto_close(self, ticket: int, reason: str, success: bool, result):
        """记录自动平仓日志"""
        try:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            status = "成功" if success else "失败"
            
            log_message = f"[{timestamp}] 自动平仓{status}: 订单{ticket} - {reason}"
            if success:
                log_message += f" (新订单: {result})"
            else:
                log_message += f" (错误: {result})"
            
            print(f"📝 {log_message}")
            
            # 这里可以添加更详细的日志记录，比如写入文件或数据库
            
        except Exception as e:
            print(f"❌ 记录平仓日志异常: {e}")
    
    def get_monitoring_status(self) -> Dict:
        """获取监控状态"""
        return {
            'monitoring': self.monitoring,
            'check_interval': self.check_interval,
            'thread_alive': self.monitor_thread.is_alive() if self.monitor_thread else False
        }
    
    def set_check_interval(self, interval: int):
        """设置检查间隔（秒）"""
        if interval < 1:
            interval = 1
        elif interval > 60:
            interval = 60
        
        self.check_interval = interval
        print(f"✅ 止盈止损检查间隔已设置为 {interval} 秒")


# 创建全局实例
stop_loss_take_profit_service = StopLossTakeProfitService()
