#!/usr/bin/env python3
"""
重新连接MT5并检查连接状态
"""

import sys
import os
import time
import requests

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_mt5_connection():
    """检查MT5连接状态"""
    print("🔍 检查MT5连接状态")
    print("=" * 50)
    
    try:
        from services.mt5_service import mt5_service
        
        # 获取连接状态
        status = mt5_service.get_connection_status()
        
        print(f"连接状态: {'✅ 已连接' if status.get('connected') else '❌ 未连接'}")
        print(f"重连服务: {'✅ 活跃' if status.get('reconnect_service_active') else '❌ 未活跃'}")
        
        if 'last_successful_connection' in status:
            print(f"最后成功连接: {status['last_successful_connection']}")
        
        if 'reconnect_attempts' in status:
            print(f"重连尝试次数: {status['reconnect_attempts']}")
        
        if 'connection_lost_time' in status:
            print(f"连接丢失时间: {status['connection_lost_time']}")
        
        return status.get('connected', False)
        
    except Exception as e:
        print(f"❌ 检查MT5连接状态失败: {e}")
        return False

def test_mt5_direct_connection():
    """直接测试MT5连接"""
    print("\n🧪 直接测试MT5连接")
    print("=" * 50)
    
    try:
        import MetaTrader5 as mt5
        
        print("1. 尝试初始化MT5...")
        if not mt5.initialize():
            error = mt5.last_error()
            print(f"❌ MT5初始化失败: {error}")
            return False
        
        print("✅ MT5初始化成功")
        
        print("2. 获取终端信息...")
        terminal_info = mt5.terminal_info()
        if terminal_info:
            print(f"   终端名称: {terminal_info.name}")
            print(f"   终端版本: {terminal_info.build}")
            print(f"   终端路径: {terminal_info.path}")
            print(f"   连接状态: {'✅ 已连接' if terminal_info.connected else '❌ 未连接'}")
            print(f"   DLL权限: {'✅ 允许' if terminal_info.dlls_allowed else '❌ 禁止'}")
            print(f"   交易权限: {'✅ 允许' if terminal_info.trade_allowed else '❌ 禁止'}")
        else:
            print("❌ 无法获取终端信息")
            return False
        
        print("3. 获取账户信息...")
        account_info = mt5.account_info()
        if account_info:
            print(f"   账户: {account_info.login}")
            print(f"   服务器: {account_info.server}")
            print(f"   余额: ${account_info.balance:.2f}")
            print(f"   权益: ${account_info.equity:.2f}")
            print(f"   公司: {account_info.company}")
            print(f"   货币: {account_info.currency}")
        else:
            print("❌ 无法获取账户信息")
            return False
        
        print("4. 测试品种信息...")
        symbol_info = mt5.symbol_info("XAUUSD")
        if symbol_info:
            print(f"   XAUUSD 可用: ✅")
            print(f"   当前价格: {symbol_info.bid:.5f} / {symbol_info.ask:.5f}")
            print(f"   点差: {(symbol_info.ask - symbol_info.bid):.5f}")
        else:
            print("   XAUUSD 不可用: ❌")
        
        print("5. 测试历史数据获取...")
        from datetime import datetime, timedelta
        end_time = datetime.now()
        start_time = end_time - timedelta(days=1)
        
        rates = mt5.copy_rates_range("XAUUSD", mt5.TIMEFRAME_H1, start_time, end_time)
        if rates is not None and len(rates) > 0:
            print(f"   获取到 {len(rates)} 条历史数据")
            print(f"   最新数据时间: {datetime.fromtimestamp(rates[-1]['time'])}")
            print(f"   最新收盘价: {rates[-1]['close']:.5f}")
        else:
            print("   ❌ 无法获取历史数据")
            return False
        
        print("✅ MT5连接测试完全成功")
        return True
        
    except ImportError:
        print("❌ MetaTrader5库未安装")
        print("请运行: pip install MetaTrader5")
        return False
    except Exception as e:
        print(f"❌ MT5连接测试失败: {e}")
        return False

def reconnect_mt5():
    """重新连接MT5"""
    print("\n🔄 重新连接MT5")
    print("=" * 50)
    
    try:
        from services.mt5_service import mt5_service
        
        print("1. 断开现有连接...")
        mt5_service.disconnect()
        time.sleep(2)
        
        print("2. 尝试重新连接...")
        success = mt5_service.connect()
        
        if success:
            print("✅ MT5重新连接成功")
            
            # 验证连接
            status = mt5_service.get_connection_status()
            print(f"连接状态: {'✅ 已连接' if status.get('connected') else '❌ 未连接'}")
            
            return True
        else:
            print("❌ MT5重新连接失败")
            return False
            
    except Exception as e:
        print(f"❌ 重新连接MT5失败: {e}")
        return False

def force_reconnect_mt5():
    """强制重连MT5"""
    print("\n💪 强制重连MT5")
    print("=" * 50)
    
    try:
        from services.mt5_service import mt5_service
        
        print("1. 强制重连...")
        mt5_service.force_reconnect()
        
        # 等待重连完成
        print("2. 等待重连完成...")
        time.sleep(5)
        
        # 检查连接状态
        status = mt5_service.get_connection_status()
        if status.get('connected'):
            print("✅ 强制重连成功")
            return True
        else:
            print("❌ 强制重连失败")
            return False
            
    except Exception as e:
        print(f"❌ 强制重连失败: {e}")
        return False

def test_data_retrieval():
    """测试数据获取功能"""
    print("\n📊 测试数据获取功能")
    print("=" * 50)
    
    try:
        from services.deep_learning_service import DeepLearningService
        
        dl_service = DeepLearningService()
        
        print("1. 测试获取历史数据...")
        from datetime import datetime, timedelta
        
        end_date = datetime.now()
        start_date = end_date - timedelta(days=7)
        
        result = dl_service._get_historical_data(
            symbol='XAUUSD',
            timeframe='H1',
            start_date=start_date.strftime('%Y-%m-%d'),
            end_date=end_date.strftime('%Y-%m-%d')
        )
        
        if result.get('success'):
            data = result['data']
            print(f"✅ 成功获取 {len(data)} 条历史数据")
            
            if data:
                print(f"   最新数据时间: {data[-1].get('timestamp')}")
                print(f"   最新收盘价: {data[-1].get('close', 0):.5f}")
                print(f"   数据范围: {data[0].get('timestamp')} 到 {data[-1].get('timestamp')}")
            
            return True
        else:
            print(f"❌ 获取历史数据失败: {result.get('error')}")
            return False
            
    except Exception as e:
        print(f"❌ 测试数据获取失败: {e}")
        return False

def main():
    print("🔧 MT5重新连接和状态检查")
    print("=" * 60)
    
    # 步骤1: 检查当前连接状态
    current_connected = check_mt5_connection()
    
    # 步骤2: 直接测试MT5连接
    direct_test_success = test_mt5_direct_connection()
    
    if not direct_test_success:
        print("\n❌ 直接MT5连接测试失败")
        print("💡 请检查:")
        print("1. MT5终端是否正在运行")
        print("2. MT5是否已登录账户")
        print("3. MT5专家顾问设置是否正确")
        print("   - 工具 -> 选项 -> 专家顾问")
        print("   - 勾选 '允许自动交易' 和 '允许DLL导入'")
        return
    
    # 步骤3: 如果服务层连接失败，尝试重连
    if not current_connected:
        print("\n🔄 服务层连接失败，尝试重新连接...")
        
        # 尝试普通重连
        if not reconnect_mt5():
            # 尝试强制重连
            if not force_reconnect_mt5():
                print("❌ 所有重连尝试都失败了")
                return
    
    # 步骤4: 测试数据获取功能
    if test_data_retrieval():
        print("\n🎉 MT5连接和数据获取功能正常")
        print("✅ 现在可以使用真实的MT5数据进行:")
        print("   • 模型推理")
        print("   • 回测分析")
        print("   • 参数优化")
        print("   • 深度学习训练")
    else:
        print("\n⚠️ MT5连接正常，但数据获取存在问题")
        print("💡 可能的原因:")
        print("1. 品种权限问题")
        print("2. 历史数据不足")
        print("3. 服务器连接问题")

if __name__ == '__main__':
    main()
