"""
形态识别模块
用于识别技术分析中的各种价格形态
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

@dataclass
class PatternSignal:
    """形态信号数据类"""
    pattern_name: str
    pattern_type: str  # 'bullish', 'bearish', 'neutral'
    confidence: float  # 0.0 to 1.0
    entry_point: float
    stop_loss: float
    take_profit: float
    timestamp: datetime
    timeframe: str
    symbol: str
    description: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'pattern_name': self.pattern_name,
            'pattern_type': self.pattern_type,
            'confidence': self.confidence,
            'entry_point': self.entry_point,
            'stop_loss': self.stop_loss,
            'take_profit': self.take_profit,
            'timestamp': self.timestamp.isoformat() if isinstance(self.timestamp, datetime) else self.timestamp,
            'timeframe': self.timeframe,
            'symbol': self.symbol,
            'description': self.description
        }

class PatternRecognizer:
    """形态识别器"""
    
    def __init__(self):
        self.min_pattern_length = 5
        self.confidence_threshold = 0.6
        
    def analyze_patterns(self, df: pd.DataFrame, symbol: str = "XAUUSD", timeframe: str = "1H") -> List[PatternSignal]:
        """分析价格数据中的形态"""
        try:
            if df is None or len(df) < self.min_pattern_length:
                return []

            patterns = []

            # 确保数据格式正确
            if 'time' not in df.columns:
                df = df.reset_index()

            # 识别各种形态
            patterns.extend(self._detect_head_and_shoulders(df, symbol, timeframe))
            patterns.extend(self._detect_double_top_bottom(df, symbol, timeframe))
            patterns.extend(self._detect_triangles(df, symbol, timeframe))
            patterns.extend(self._detect_support_resistance(df, symbol, timeframe))

            # 按置信度排序
            patterns.sort(key=lambda x: x.confidence, reverse=True)

            return patterns

        except Exception as e:
            logger.error(f"形态分析失败: {e}")
            return []

    def identify_patterns(self, df: pd.DataFrame, symbol: str = "XAUUSD", timeframe: str = "1H") -> List[PatternSignal]:
        """识别形态 - 为了兼容性保留的方法名"""
        return self.analyze_patterns(df, symbol, timeframe)
    
    def _detect_head_and_shoulders(self, df: pd.DataFrame, symbol: str, timeframe: str) -> List[PatternSignal]:
        """检测头肩顶/头肩底形态"""
        patterns = []
        
        try:
            if len(df) < 20:
                return patterns
            
            # 简化的头肩形态检测
            highs = df['high'].values
            lows = df['low'].values
            closes = df['close'].values
            
            # 寻找可能的头肩顶
            for i in range(10, len(df) - 10):
                left_shoulder = highs[i-10:i-5].max()
                head = highs[i-5:i+5].max()
                right_shoulder = highs[i+5:i+10].max()
                
                # 头肩顶条件：头部高于两肩，两肩高度相近
                if (head > left_shoulder and head > right_shoulder and 
                    abs(left_shoulder - right_shoulder) / head < 0.05):
                    
                    confidence = min(0.9, 0.6 + (head - max(left_shoulder, right_shoulder)) / head)
                    
                    pattern = PatternSignal(
                        pattern_name="Head and Shoulders Top",
                        pattern_type="bearish",
                        confidence=confidence,
                        entry_point=closes[i],
                        stop_loss=head * 1.02,
                        take_profit=closes[i] * 0.95,
                        timestamp=df.iloc[i]['time'] if 'time' in df.columns else datetime.now(),
                        timeframe=timeframe,
                        symbol=symbol,
                        description="头肩顶形态，看跌信号"
                    )
                    patterns.append(pattern)
            
            # 寻找可能的头肩底
            for i in range(10, len(df) - 10):
                left_shoulder = lows[i-10:i-5].min()
                head = lows[i-5:i+5].min()
                right_shoulder = lows[i+5:i+10].min()
                
                # 头肩底条件：头部低于两肩，两肩高度相近
                if (head < left_shoulder and head < right_shoulder and 
                    abs(left_shoulder - right_shoulder) / head < 0.05):
                    
                    confidence = min(0.9, 0.6 + (min(left_shoulder, right_shoulder) - head) / head)
                    
                    pattern = PatternSignal(
                        pattern_name="Head and Shoulders Bottom",
                        pattern_type="bullish",
                        confidence=confidence,
                        entry_point=closes[i],
                        stop_loss=head * 0.98,
                        take_profit=closes[i] * 1.05,
                        timestamp=df.iloc[i]['time'] if 'time' in df.columns else datetime.now(),
                        timeframe=timeframe,
                        symbol=symbol,
                        description="头肩底形态，看涨信号"
                    )
                    patterns.append(pattern)
                    
        except Exception as e:
            logger.error(f"头肩形态检测失败: {e}")
        
        return patterns
    
    def _detect_double_top_bottom(self, df: pd.DataFrame, symbol: str, timeframe: str) -> List[PatternSignal]:
        """检测双顶/双底形态"""
        patterns = []
        
        try:
            if len(df) < 15:
                return patterns
            
            highs = df['high'].values
            lows = df['low'].values
            closes = df['close'].values
            
            # 检测双顶
            for i in range(5, len(df) - 10):
                for j in range(i + 5, min(i + 15, len(df) - 5)):
                    peak1 = highs[i-2:i+3].max()
                    peak2 = highs[j-2:j+3].max()
                    
                    # 双顶条件：两个峰值相近
                    if abs(peak1 - peak2) / max(peak1, peak2) < 0.02:
                        valley = lows[i+2:j-2].min()
                        
                        if valley < min(peak1, peak2) * 0.98:
                            confidence = 0.7
                            
                            pattern = PatternSignal(
                                pattern_name="Double Top",
                                pattern_type="bearish",
                                confidence=confidence,
                                entry_point=closes[j],
                                stop_loss=max(peak1, peak2) * 1.01,
                                take_profit=valley * 0.99,
                                timestamp=df.iloc[j]['time'] if 'time' in df.columns else datetime.now(),
                                timeframe=timeframe,
                                symbol=symbol,
                                description="双顶形态，看跌信号"
                            )
                            patterns.append(pattern)
                            break
            
            # 检测双底
            for i in range(5, len(df) - 10):
                for j in range(i + 5, min(i + 15, len(df) - 5)):
                    trough1 = lows[i-2:i+3].min()
                    trough2 = lows[j-2:j+3].min()
                    
                    # 双底条件：两个谷值相近
                    if abs(trough1 - trough2) / min(trough1, trough2) < 0.02:
                        peak = highs[i+2:j-2].max()
                        
                        if peak > max(trough1, trough2) * 1.02:
                            confidence = 0.7
                            
                            pattern = PatternSignal(
                                pattern_name="Double Bottom",
                                pattern_type="bullish",
                                confidence=confidence,
                                entry_point=closes[j],
                                stop_loss=min(trough1, trough2) * 0.99,
                                take_profit=peak * 1.01,
                                timestamp=df.iloc[j]['time'] if 'time' in df.columns else datetime.now(),
                                timeframe=timeframe,
                                symbol=symbol,
                                description="双底形态，看涨信号"
                            )
                            patterns.append(pattern)
                            break
                            
        except Exception as e:
            logger.error(f"双顶双底形态检测失败: {e}")
        
        return patterns
    
    def _detect_triangles(self, df: pd.DataFrame, symbol: str, timeframe: str) -> List[PatternSignal]:
        """检测三角形形态"""
        patterns = []
        
        try:
            if len(df) < 20:
                return patterns
            
            # 简化的三角形检测
            closes = df['close'].values
            highs = df['high'].values
            lows = df['low'].values
            
            # 检测上升三角形
            for i in range(10, len(df) - 5):
                recent_highs = highs[i-10:i]
                recent_lows = lows[i-10:i]
                
                # 上升三角形：阻力位水平，支撑位上升
                resistance = recent_highs.max()
                if len([h for h in recent_highs[-5:] if abs(h - resistance) / resistance < 0.01]) >= 2:
                    # 检查支撑位是否上升
                    early_lows = recent_lows[:5].mean()
                    late_lows = recent_lows[-5:].mean()
                    
                    if late_lows > early_lows:
                        confidence = 0.65
                        
                        pattern = PatternSignal(
                            pattern_name="Ascending Triangle",
                            pattern_type="bullish",
                            confidence=confidence,
                            entry_point=closes[i],
                            stop_loss=late_lows * 0.99,
                            take_profit=resistance * 1.02,
                            timestamp=df.iloc[i]['time'] if 'time' in df.columns else datetime.now(),
                            timeframe=timeframe,
                            symbol=symbol,
                            description="上升三角形，看涨信号"
                        )
                        patterns.append(pattern)
                        
        except Exception as e:
            logger.error(f"三角形形态检测失败: {e}")
        
        return patterns
    
    def _detect_support_resistance(self, df: pd.DataFrame, symbol: str, timeframe: str) -> List[PatternSignal]:
        """检测支撑阻力位"""
        patterns = []
        
        try:
            if len(df) < 10:
                return patterns
            
            closes = df['close'].values
            current_price = closes[-1]
            
            # 简单的支撑阻力检测
            recent_prices = closes[-20:] if len(closes) >= 20 else closes
            
            # 寻找支撑位（价格多次触及但未跌破的低点）
            support_level = recent_prices.min()
            touches = len([p for p in recent_prices if abs(p - support_level) / support_level < 0.005])
            
            if touches >= 2 and current_price > support_level * 1.01:
                confidence = min(0.8, 0.5 + touches * 0.1)
                
                pattern = PatternSignal(
                    pattern_name="Support Level",
                    pattern_type="bullish",
                    confidence=confidence,
                    entry_point=current_price,
                    stop_loss=support_level * 0.995,
                    take_profit=current_price * 1.02,
                    timestamp=df.iloc[-1]['time'] if 'time' in df.columns else datetime.now(),
                    timeframe=timeframe,
                    symbol=symbol,
                    description=f"支撑位 {support_level:.4f}，看涨信号"
                )
                patterns.append(pattern)
            
            # 寻找阻力位（价格多次触及但未突破的高点）
            resistance_level = recent_prices.max()
            touches = len([p for p in recent_prices if abs(p - resistance_level) / resistance_level < 0.005])
            
            if touches >= 2 and current_price < resistance_level * 0.99:
                confidence = min(0.8, 0.5 + touches * 0.1)
                
                pattern = PatternSignal(
                    pattern_name="Resistance Level",
                    pattern_type="bearish",
                    confidence=confidence,
                    entry_point=current_price,
                    stop_loss=resistance_level * 1.005,
                    take_profit=current_price * 0.98,
                    timestamp=df.iloc[-1]['time'] if 'time' in df.columns else datetime.now(),
                    timeframe=timeframe,
                    symbol=symbol,
                    description=f"阻力位 {resistance_level:.4f}，看跌信号"
                )
                patterns.append(pattern)
                
        except Exception as e:
            logger.error(f"支撑阻力检测失败: {e}")
        
        return patterns
