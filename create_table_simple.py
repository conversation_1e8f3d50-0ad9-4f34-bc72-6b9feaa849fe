#!/usr/bin/env python3
import sqlite3

# 连接数据库
conn = sqlite3.connect('trading_system.db')
cursor = conn.cursor()

print("检查entry_signals表...")

# 检查表是否存在
cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='entry_signals'")
if cursor.fetchone():
    print("✅ entry_signals表已存在")
else:
    print("❌ entry_signals表不存在，正在创建...")
    
    # 创建表
    cursor.execute("""
        CREATE TABLE entry_signals (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            signal_type VARCHAR(50) NOT NULL,
            symbol VARCHAR(20) NOT NULL,
            action VARCHAR(10) NOT NULL,
            strength FLOAT,
            confidence FLOAT,
            signal_price FLOAT,
            entry_price FLOAT,
            is_executed BOOLEAN DEFAULT 0,
            execution_result VARCHAR(50),
            execution_reason TEXT,
            trade_id VARCHAR(100),
            market_analysis TEXT,
            signal_time DATETIME DEFAULT CURRENT_TIMESTAMP,
            execution_time DATETIME
        )
    """)
    
    print("✅ entry_signals表创建成功")

# 提交并关闭
conn.commit()
conn.close()
print("完成！")
