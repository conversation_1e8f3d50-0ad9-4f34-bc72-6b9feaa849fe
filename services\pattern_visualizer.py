#!/usr/bin/env python3
"""
形态可视化器
用于展示从MT5获取的真实数据中识别出的技术形态
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import Rectangle, Polygon
import pandas as pd
import numpy as np
from typing import List, Dict, Optional
import seaborn as sns
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class PatternVisualizer:
    """形态可视化器"""
    
    def __init__(self, figsize=(15, 10)):
        self.figsize = figsize
        self.colors = {
            'bullish': '#2E8B57',  # 看涨绿色
            'bearish': '#DC143C',  # 看跌红色
            'neutral': '#4682B4'   # 中性蓝色
        }
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
        plt.rcParams['axes.unicode_minus'] = False
    
    def plot_patterns_overview(self, df: pd.DataFrame, patterns: List[Dict], 
                             symbol: str, timeframe: str) -> plt.Figure:
        """绘制形态总览图"""
        fig, ax = plt.subplots(figsize=self.figsize)
        
        # 绘制K线图
        self._plot_candlesticks(ax, df)
        
        # 绘制所有识别的形态
        for pattern in patterns:
            self._draw_pattern(ax, df, pattern)
        
        # 设置标题和标签
        ax.set_title(f'{symbol} {timeframe} - 技术形态识别 (共{len(patterns)}个形态)', 
                    fontsize=16, fontweight='bold')
        ax.set_xlabel('时间', fontsize=12)
        ax.set_ylabel('价格', fontsize=12)
        
        # 添加图例
        self._add_legend(ax, patterns)
        
        # 格式化x轴
        self._format_time_axis(ax, df)
        
        plt.tight_layout()
        return fig
    
    def plot_pattern_detail(self, df: pd.DataFrame, pattern: Dict, 
                          context_bars: int = 50) -> plt.Figure:
        """绘制单个形态的详细图"""
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=self.figsize, 
                                      gridspec_kw={'height_ratios': [3, 1]})
        
        # 确定显示范围
        start_idx = max(0, pattern['start_idx'] - context_bars)
        end_idx = min(len(df), pattern['end_idx'] + context_bars)
        
        df_subset = df.iloc[start_idx:end_idx].copy()
        
        # 主图：价格和形态
        self._plot_candlesticks(ax1, df_subset)
        self._draw_pattern(ax1, df, pattern)
        
        # 设置主图标题
        pattern_name = pattern.get('pattern_name', pattern['pattern_type'])
        direction = pattern.get('direction', 'neutral')
        confidence = pattern.get('confidence', 0) * 100
        
        ax1.set_title(f'{pattern_name} - {direction.upper()} (置信度: {confidence:.1f}%)', 
                     fontsize=14, fontweight='bold',
                     color=self.colors.get(direction, 'black'))
        
        # 副图：成交量
        if 'Volume' in df.columns:
            self._plot_volume(ax2, df_subset, start_idx)
        
        # 添加交易信息
        self._add_trading_info(ax1, pattern)
        
        plt.tight_layout()
        return fig
    
    def _plot_candlesticks(self, ax, df: pd.DataFrame):
        """绘制K线图"""
        for i, (idx, row) in enumerate(df.iterrows()):
            open_price = row['Open']
            high_price = row['High']
            low_price = row['Low']
            close_price = row['Close']
            
            # 确定颜色
            color = '#2E8B57' if close_price >= open_price else '#DC143C'
            
            # 绘制影线
            ax.plot([i, i], [low_price, high_price], color='black', linewidth=0.5)
            
            # 绘制实体
            body_height = abs(close_price - open_price)
            body_bottom = min(open_price, close_price)
            
            rect = Rectangle((i-0.3, body_bottom), 0.6, body_height, 
                           facecolor=color, edgecolor='black', linewidth=0.5)
            ax.add_patch(rect)
    
    def _draw_pattern(self, ax, df: pd.DataFrame, pattern: Dict):
        """在图上绘制形态标记"""
        pattern_type = pattern['pattern_type']
        direction = pattern.get('direction', 'neutral')
        color = self.colors.get(direction, 'blue')
        
        start_idx = pattern['start_idx']
        end_idx = pattern['end_idx']
        
        # 绘制形态区域
        if pattern_type in ['head_shoulders_top', 'head_shoulders_bottom']:
            self._draw_head_shoulders(ax, df, pattern, color)
        elif pattern_type in ['double_top', 'double_bottom']:
            self._draw_double_pattern(ax, df, pattern, color)
        elif 'triangle' in pattern_type:
            self._draw_triangle(ax, df, pattern, color)
        else:
            # 默认绘制矩形框
            self._draw_pattern_box(ax, df, pattern, color)
        
        # 添加形态标签
        mid_idx = (start_idx + end_idx) // 2
        if mid_idx < len(df):
            price_level = df.iloc[mid_idx]['High'] * 1.02
            pattern_name = pattern.get('pattern_name', pattern_type)
            ax.annotate(pattern_name, xy=(mid_idx, price_level), 
                       xytext=(10, 10), textcoords='offset points',
                       bbox=dict(boxstyle='round,pad=0.3', facecolor=color, alpha=0.7),
                       fontsize=8, color='white', fontweight='bold')
    
    def _draw_head_shoulders(self, ax, df: pd.DataFrame, pattern: Dict, color: str):
        """绘制头肩形态"""
        key_levels = pattern.get('key_levels', {})
        
        if 'neckline' in key_levels:
            # 绘制颈线
            start_x = pattern['start_idx']
            end_x = pattern['end_idx']
            neckline = key_levels['neckline']
            
            ax.plot([start_x, end_x], [neckline, neckline], 
                   color=color, linestyle='--', linewidth=2, alpha=0.8)
            
            # 标记关键点
            if pattern['pattern_type'] == 'head_shoulders_top':
                # 标记左肩、头部、右肩
                points = ['left_shoulder', 'head', 'right_shoulder']
                for point in points:
                    if point in key_levels:
                        # 这里需要找到对应的索引位置，简化处理
                        pass
    
    def _draw_double_pattern(self, ax, df: pd.DataFrame, pattern: Dict, color: str):
        """绘制双顶/双底形态"""
        key_levels = pattern.get('key_levels', {})
        
        if 'neckline' in key_levels:
            start_x = pattern['start_idx']
            end_x = pattern['end_idx']
            neckline = key_levels['neckline']
            
            ax.plot([start_x, end_x], [neckline, neckline], 
                   color=color, linestyle='--', linewidth=2, alpha=0.8)
    
    def _draw_triangle(self, ax, df: pd.DataFrame, pattern: Dict, color: str):
        """绘制三角形形态"""
        start_x = pattern['start_idx']
        end_x = pattern['end_idx']
        key_levels = pattern.get('key_levels', {})
        
        # 绘制趋势线
        if pattern['pattern_type'] == 'ascending_triangle':
            if 'resistance' in key_levels:
                resistance = key_levels['resistance']
                ax.plot([start_x, end_x], [resistance, resistance], 
                       color=color, linestyle='-', linewidth=2, alpha=0.8)
            
            if 'support_start' in key_levels and 'support_end' in key_levels:
                support_start = key_levels['support_start']
                support_end = key_levels['support_end']
                ax.plot([start_x, end_x], [support_start, support_end], 
                       color=color, linestyle='-', linewidth=2, alpha=0.8)
        
        elif pattern['pattern_type'] == 'descending_triangle':
            if 'support' in key_levels:
                support = key_levels['support']
                ax.plot([start_x, end_x], [support, support], 
                       color=color, linestyle='-', linewidth=2, alpha=0.8)
    
    def _draw_pattern_box(self, ax, df: pd.DataFrame, pattern: Dict, color: str):
        """绘制形态边界框"""
        start_idx = pattern['start_idx']
        end_idx = pattern['end_idx']
        
        if start_idx < len(df) and end_idx < len(df):
            # 计算价格范围
            price_data = df.iloc[start_idx:end_idx+1]
            min_price = price_data['Low'].min()
            max_price = price_data['High'].max()
            
            # 绘制矩形框
            width = end_idx - start_idx
            height = max_price - min_price
            
            rect = Rectangle((start_idx, min_price), width, height, 
                           linewidth=2, edgecolor=color, facecolor='none', alpha=0.7)
            ax.add_patch(rect)
    
    def _plot_volume(self, ax, df: pd.DataFrame, offset: int = 0):
        """绘制成交量"""
        volumes = df['Volume'].values
        colors = ['#2E8B57' if df.iloc[i]['Close'] >= df.iloc[i]['Open'] 
                 else '#DC143C' for i in range(len(df))]
        
        bars = ax.bar(range(len(volumes)), volumes, color=colors, alpha=0.6)
        ax.set_ylabel('成交量', fontsize=10)
        ax.set_xlabel('时间', fontsize=10)
    
    def _add_trading_info(self, ax, pattern: Dict):
        """添加交易信息"""
        info_text = []
        
        if 'entry_point' in pattern and pattern['entry_point']:
            info_text.append(f"入场点: {pattern['entry_point']:.5f}")
        
        if 'stop_loss' in pattern and pattern['stop_loss']:
            info_text.append(f"止损: {pattern['stop_loss']:.5f}")
        
        if 'take_profit' in pattern and pattern['take_profit']:
            info_text.append(f"止盈: {pattern['take_profit']:.5f}")
        
        if info_text:
            info_str = '\n'.join(info_text)
            ax.text(0.02, 0.98, info_str, transform=ax.transAxes, 
                   verticalalignment='top', fontsize=10,
                   bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    def _add_legend(self, ax, patterns: List[Dict]):
        """添加图例"""
        pattern_types = {}
        for pattern in patterns:
            direction = pattern.get('direction', 'neutral')
            pattern_name = pattern.get('pattern_name', pattern['pattern_type'])
            if direction not in pattern_types:
                pattern_types[direction] = []
            if pattern_name not in pattern_types[direction]:
                pattern_types[direction].append(pattern_name)
        
        legend_elements = []
        for direction, names in pattern_types.items():
            color = self.colors.get(direction, 'blue')
            label = f"{direction.upper()}: {', '.join(names[:3])}"  # 限制显示数量
            legend_elements.append(plt.Line2D([0], [0], color=color, lw=2, label=label))
        
        if legend_elements:
            ax.legend(handles=legend_elements, loc='upper left', fontsize=10)
    
    def _format_time_axis(self, ax, df: pd.DataFrame):
        """格式化时间轴"""
        # 简化时间轴显示
        n_ticks = min(10, len(df))
        tick_indices = np.linspace(0, len(df)-1, n_ticks, dtype=int)
        
        ax.set_xticks(tick_indices)
        ax.set_xticklabels([df.index[i].strftime('%m-%d %H:%M') 
                           for i in tick_indices], rotation=45)
    
    def create_pattern_summary(self, patterns: List[Dict]) -> pd.DataFrame:
        """创建形态汇总表"""
        summary_data = []
        
        for i, pattern in enumerate(patterns):
            summary_data.append({
                '序号': i + 1,
                '形态名称': pattern.get('pattern_name', pattern['pattern_type']),
                '方向': pattern.get('direction', 'neutral'),
                '置信度': f"{pattern.get('confidence', 0) * 100:.1f}%",
                '入场点': f"{pattern.get('entry_point', 0):.5f}" if pattern.get('entry_point') else 'N/A',
                '止损': f"{pattern.get('stop_loss', 0):.5f}" if pattern.get('stop_loss') else 'N/A',
                '止盈': f"{pattern.get('take_profit', 0):.5f}" if pattern.get('take_profit') else 'N/A'
            })
        
        return pd.DataFrame(summary_data)
