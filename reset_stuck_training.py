#!/usr/bin/env python3
"""
重置卡住的训练任务
"""

import sqlite3
from datetime import datetime

def reset_stuck_training():
    """重置卡住的训练任务"""
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 卡住的任务ID
        task_id = '0b77f71a-0c7e-4455-b46d-fa1843e2d833'
        
        print(f"🔧 重置卡住的训练任务: {task_id}")
        
        # 重置任务状态
        cursor.execute('''
            UPDATE training_tasks 
            SET status = 'failed', 
                logs = COALESCE(logs, '') || char(10) || '[' || datetime('now') || '] 任务卡住已重置，可重新开始训练',
                updated_at = datetime('now')
            WHERE id = ?
        ''', (task_id,))
        
        affected_rows = cursor.rowcount
        conn.commit()
        
        if affected_rows > 0:
            print(f"✅ 任务已成功重置")
            
            # 验证更新
            cursor.execute('SELECT id, status, progress, updated_at FROM training_tasks WHERE id = ?', (task_id,))
            result = cursor.fetchone()
            
            if result:
                print(f"📋 任务状态:")
                print(f"  ID: {result[0]}")
                print(f"  状态: {result[1]}")
                print(f"  进度: {result[2]}%")
                print(f"  更新时间: {result[3]}")
        else:
            print(f"❌ 没有找到任务或任务已经是正确状态")
        
        # 检查是否还有其他卡住的任务
        cursor.execute('''
            SELECT id, status, progress, updated_at 
            FROM training_tasks 
            WHERE status = 'running'
        ''')
        
        other_running = cursor.fetchall()
        if other_running:
            print(f"\n⚠️ 发现其他运行中的任务:")
            for task in other_running:
                print(f"  ID: {task[0]} | 状态: {task[1]} | 进度: {task[2]}% | 更新: {task[3]}")
        else:
            print(f"\n✅ 没有其他运行中的任务")
        
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 重置失败: {e}")
        return False

def clear_gpu_memory():
    """清理GPU内存"""
    
    try:
        import torch
        if torch.cuda.is_available():
            print(f"\n🧹 清理GPU内存...")
            
            # 清理GPU缓存
            torch.cuda.empty_cache()
            
            # 检查内存使用
            allocated = torch.cuda.memory_allocated(0) / 1024**3
            cached = torch.cuda.memory_reserved(0) / 1024**3
            
            print(f"  GPU内存使用: {allocated:.1f}GB (分配) / {cached:.1f}GB (缓存)")
            
            if cached > 0:
                print(f"  ✅ GPU缓存已清理")
            else:
                print(f"  ✅ GPU内存干净")
                
            return True
        else:
            print(f"\n❌ GPU不可用")
            return False
            
    except Exception as e:
        print(f"\n❌ GPU内存清理失败: {e}")
        return False

def main():
    """主函数"""
    
    print("🔧 深度学习训练故障修复工具")
    print("=" * 60)
    
    # 重置卡住的训练任务
    reset_success = reset_stuck_training()
    
    # 清理GPU内存
    gpu_success = clear_gpu_memory()
    
    print(f"\n📋 修复结果")
    print("=" * 60)
    
    if reset_success:
        print(f"✅ 卡住的训练任务已重置")
        print(f"💡 现在可以重新开始训练:")
        print(f"   1. 访问深度学习训练页面")
        print(f"   2. 选择模型和参数")
        print(f"   3. 点击'开始训练'")
        print(f"   4. 监控训练进度")
    else:
        print(f"❌ 任务重置失败")
    
    if gpu_success:
        print(f"✅ GPU内存已清理")
    else:
        print(f"⚠️ GPU内存清理可能有问题")
    
    print(f"\n🎯 预防措施")
    print("=" * 60)
    print(f"为避免训练再次卡住，建议:")
    print(f"1. 降低批次大小 (batch_size)")
    print(f"2. 减少数据点数量")
    print(f"3. 使用较小的模型")
    print(f"4. 定期监控训练进度")
    print(f"5. 设置合理的训练轮次")
    
    print(f"\n🚀 推荐训练参数")
    print("=" * 60)
    print(f"• 批次大小: 16-32")
    print(f"• 学习率: 0.001")
    print(f"• 训练轮次: 50-100")
    print(f"• 数据点数: 1000-5000")
    print(f"• 序列长度: 20-50")

if __name__ == '__main__':
    main()
