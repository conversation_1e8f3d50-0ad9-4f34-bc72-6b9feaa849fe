{% extends "base.html" %}

{% block title %}策略交易 - AI自动交易系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">
            <i class="fas fa-robot text-primary me-2"></i>
            策略交易
            <span class="badge bg-primary ms-2">AI自动交易</span>
        </h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <div class="btn-group me-2">
                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshPage()">
                    <i class="fas fa-sync-alt"></i> 刷新
                </button>
            </div>
        </div>
    </div>

    <!-- 系统状态指示器 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center">
                            <div class="status-indicator me-3" id="systemStatus">
                                <div class="status-dot bg-secondary"></div>
                                <span class="status-text">系统未启动</span>
                            </div>
                            <div class="trading-stats ms-4">
                                <span class="text-muted">今日交易: </span>
                                <span class="fw-bold" id="todayTrades">0</span>
                                <span class="text-muted ms-3">总盈亏: </span>
                                <span class="fw-bold" id="totalPnL">$0.00</span>
                            </div>
                        </div>
                        <div class="system-controls">
                            <button class="btn btn-success me-2" id="startTradingBtn" onclick="startStrategyTrading()">
                                <i class="fas fa-play"></i> 启动交易
                            </button>
                            <button class="btn btn-danger" id="stopTradingBtn" onclick="stopStrategyTrading()" style="display: none;">
                                <i class="fas fa-stop"></i> 停止交易
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 左侧：交易配置 -->
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cog text-primary me-2"></i>
                        交易配置
                    </h5>
                </div>
                <div class="card-body">
                    <form id="strategyTradingForm">
                        <!-- AI策略选择 -->
                        <div class="mb-3">
                            <label class="form-label">AI策略模型</label>
                            <select class="form-select" id="aiStrategySelect" required>
                                <option value="">选择AI策略模型</option>
                                <!-- 动态加载AI策略 -->
                            </select>
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                选择您训练好的AI策略模型进行自动交易
                            </small>
                        </div>

                        <!-- 策略配置 -->
                        <div class="mb-3">
                            <label class="form-label">策略配置</label>
                            <select class="form-select" id="strategyConfigType" onchange="updateStrategyConfigDisplay()">
                                <option value="auto" selected>自动选择 (推荐)</option>
                                <option value="trend_following">趋势跟踪</option>
                                <option value="mean_reversion">均值回归</option>
                                <option value="breakout">突破策略</option>
                                <option value="high_frequency">高频策略</option>
                                <option value="conservative">保守策略</option>
                                <option value="aggressive">激进策略</option>
                                <option value="custom">自定义配置</option>
                            </select>
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                选择适合当前市场环境的策略配置
                            </small>
                        </div>

                        <!-- 策略配置详情 -->
                        <div id="strategyConfigDetails" class="mb-3" style="display: none;">
                            <div class="card border-info bg-light">
                                <div class="card-body p-3">
                                    <h6 class="card-title mb-3">
                                        <i class="fas fa-cogs me-2"></i>
                                        策略参数
                                    </h6>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">止损比例 (%)</label>
                                            <input type="number" class="form-control" id="strategyStopLoss"
                                                   value="0.8" min="0.1" max="5.0" step="0.1">
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">止盈比例 (%)</label>
                                            <input type="number" class="form-control" id="strategyTakeProfit"
                                                   value="1.6" min="0.2" max="10.0" step="0.1">
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">最大持仓数</label>
                                            <input type="number" class="form-control" id="strategyMaxPositions"
                                                   value="3" min="1" max="10" step="1">
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">信号阈值</label>
                                            <input type="number" class="form-control" id="strategySignalThreshold"
                                                   value="0.7" min="0.5" max="1.0" step="0.05">
                                        </div>
                                    </div>

                                    <div class="mt-2">
                                        <div class="alert alert-info py-2">
                                            <i class="fas fa-lightbulb me-2"></i>
                                            <small id="strategyConfigDescription">
                                                自动选择 - 系统将根据市场环境自动选择最适合的策略配置
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 货币对 -->
                        <div class="mb-3">
                            <label class="form-label">交易品种</label>
                            <select class="form-select" id="symbolSelect" required>
                                <option value="XAUUSD" selected>XAU/USD (黄金/美元)</option>
                            </select>
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                当前仅支持黄金交易
                            </small>
                        </div>

                        <!-- 交易手数 -->
                        <div class="mb-3">
                            <label class="form-label">单次交易手数</label>
                            <select class="form-select" id="lotSizeSelect" required>
                                <option value="0.01" selected>0.01手 (推荐)</option>
                                <option value="0.02">0.02手</option>
                            </select>
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                建议使用小手数进行测试
                            </small>
                        </div>

                        <!-- 时间间隔 -->
                        <div class="mb-3">
                            <label class="form-label">交易时间间隔</label>
                            <select class="form-select" id="timeframeSelect" required>
                                <option value="">请先选择AI策略</option>
                            </select>
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                必须与AI策略训练时使用的时间框架保持一致
                            </small>
                        </div>

                        <!-- 交易次数限制 -->
                        <div class="mb-3">
                            <label class="form-label">单次运行交易次数限制</label>
                            <select class="form-select" id="tradeLimitSelect" required>
                                <option value="10">10次</option>
                                <option value="20">20次</option>
                                <option value="30" selected>30次 (推荐)</option>
                                <option value="50">50次</option>
                                <option value="unlimited">不限制</option>
                            </select>
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                建议设置交易次数限制以控制风险
                            </small>
                        </div>

                        <!-- 智能止盈止损设置 -->
                        <div class="mb-3">
                            <label class="form-label">智能止盈止损设置</label>
                            <div class="row">
                                <div class="col-6">
                                    <div class="input-group">
                                        <span class="input-group-text">止损</span>
                                        <input type="number" class="form-control" id="stopLossPercent"
                                               value="1.2" min="0.5" max="5.0" step="0.1">
                                        <span class="input-group-text">%</span>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="input-group">
                                        <span class="input-group-text">止盈</span>
                                        <input type="number" class="form-control" id="takeProfitPercent"
                                               value="2.4" min="1.0" max="10.0" step="0.1">
                                        <span class="input-group-text">%</span>
                                    </div>
                                </div>
                            </div>
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                智能止盈止损会根据市场波动性自动调整
                            </small>
                        </div>

                        <!-- 配置操作按钮 -->
                        <div class="d-grid gap-2">
                            <button type="button" class="btn btn-primary" onclick="saveStrategyConfig()">
                                <i class="fas fa-save"></i> 保存配置
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="loadStrategyConfig()">
                                <i class="fas fa-upload"></i> 加载配置
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 右侧：当前持仓和交易状态 -->
        <div class="col-lg-6">
            <!-- 当前持仓 -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-briefcase text-success me-2"></i>
                        当前持仓
                    </h5>
                    <button class="btn btn-sm btn-outline-danger" onclick="closeAllStrategyPositions()">
                        <i class="fas fa-times"></i> 一键平仓
                    </button>
                </div>
                <div class="card-body">
                    <div id="strategyPositionsContainer">
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-inbox fa-2x mb-2"></i>
                            <p>暂无持仓</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 交易日志 -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list-alt text-info me-2"></i>
                        交易日志
                    </h5>
                </div>
                <div class="card-body">
                    <div id="tradingLogContainer" style="max-height: 300px; overflow-y: auto;">
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-file-alt fa-2x mb-2"></i>
                            <p>暂无交易记录</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 样式 -->
<style>
.status-indicator {
    display: flex;
    align-items: center;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
    animation: pulse 2s infinite;
}

.status-dot.bg-success {
    background-color: #28a745 !important;
}

.status-dot.bg-danger {
    background-color: #dc3545 !important;
}

.status-dot.bg-warning {
    background-color: #ffc107 !important;
}

.status-dot.bg-secondary {
    background-color: #6c757d !important;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.position-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 10px;
    color: white;
    margin-bottom: 10px;
}

.position-card:hover {
    transform: translateY(-2px);
    transition: all 0.3s ease;
}

.trading-log-item {
    border-left: 3px solid #007bff;
    padding-left: 10px;
    margin-bottom: 10px;
    background: #f8f9fa;
    border-radius: 5px;
    padding: 10px;
}

.trading-log-item.success {
    border-left-color: #28a745;
}

.trading-log-item.error {
    border-left-color: #dc3545;
}

.trading-log-item.warning {
    border-left-color: #ffc107;
}
</style>

<script>
// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeStrategyTrading();
});

function initializeStrategyTrading() {
    console.log('🚀 初始化策略交易页面...');

    // 加载AI策略列表
    loadAIStrategies();

    // 加载当前持仓
    loadStrategyPositions();

    // 检查交易状态
    checkTradingStatus();

    // 加载保存的配置
    loadStrategyConfig();

    // 设置定时刷新
    setInterval(function() {
        loadStrategyPositions();
        updateTradingStats();
    }, 5000); // 每5秒刷新一次
}

function loadAIStrategies() {
    console.log('📊 加载AI策略列表...');

    fetch('/api/ai-strategies/list')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const select = document.getElementById('aiStrategySelect');
                select.innerHTML = '<option value="">选择AI策略模型</option>';

                data.strategies.forEach(strategy => {
                    const option = document.createElement('option');
                    option.value = strategy.id;
                    option.textContent = `${strategy.name} (${strategy.timeframe || '1h'})`;
                    option.dataset.timeframe = strategy.timeframe || '1h';
                    select.appendChild(option);
                });

                console.log(`✅ 加载了 ${data.strategies.length} 个AI策略`);
            } else {
                console.error('❌ 加载AI策略失败:', data.error);
                addTradingLog('加载AI策略失败: ' + data.error, 'error');
            }
        })
        .catch(error => {
            console.error('❌ 加载AI策略异常:', error);
            addTradingLog('加载AI策略异常: ' + error.message, 'error');
        });
}

// AI策略选择变化时更新时间间隔
document.addEventListener('change', function(e) {
    if (e.target.id === 'aiStrategySelect') {
        const selectedOption = e.target.selectedOptions[0];
        const timeframe = selectedOption ? selectedOption.dataset.timeframe : '';

        const timeframeSelect = document.getElementById('timeframeSelect');
        if (timeframe) {
            timeframeSelect.innerHTML = `<option value="${timeframe}" selected>${getTimeframeText(timeframe)} (策略训练时间框架)</option>`;
            timeframeSelect.disabled = true;
        } else {
            timeframeSelect.innerHTML = '<option value="">请先选择AI策略</option>';
            timeframeSelect.disabled = false;
        }
    }
});

function getTimeframeText(timeframe) {
    const timeframeMap = {
        '1m': '1分钟',
        '5m': '5分钟',
        '15m': '15分钟',
        '30m': '30分钟',
        '1h': '1小时',
        '4h': '4小时',
        '1d': '1天'
    };
    return timeframeMap[timeframe] || timeframe;
}

function loadStrategyPositions() {
    // 加载策略交易持仓
    fetch('/api/strategy-trading/positions')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayStrategyPositions(data.positions);
            }
        })
        .catch(error => {
            console.error('加载持仓失败:', error);
        });
}

function displayStrategyPositions(positions) {
    const container = document.getElementById('strategyPositionsContainer');

    if (!positions || positions.length === 0) {
        container.innerHTML = `
            <div class="text-center text-muted py-4">
                <i class="fas fa-inbox fa-2x mb-2"></i>
                <p>暂无持仓</p>
            </div>
        `;
        return;
    }

    let html = '';
    positions.forEach(pos => {
        const pnlClass = pos.pnl >= 0 ? 'text-success' : 'text-danger';
        const directionText = pos.type === 'buy' ? '多' : '空';
        const directionClass = pos.type === 'buy' ? 'text-success' : 'text-danger';

        html += `
            <div class="position-card p-3 mb-3">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="d-flex align-items-center mb-1">
                            <h6 class="${directionClass} mb-0 me-2">${directionText}</h6>
                            <span class="badge bg-primary" style="font-size: 0.7rem;">🤖 ${pos.strategy_name || 'AI策略'}</span>
                        </div>
                        <div class="text-white mb-1">${pos.volume}手</div>
                        <small class="text-light">入场: ${pos.entry_price}</small>
                    </div>
                    <div class="text-end">
                        <div class="${pnlClass} mb-2">${pos.pnl >= 0 ? '+' : ''}${pos.pnl.toFixed(2)}</div>
                        <button class="btn btn-sm btn-outline-warning" onclick="closeStrategyPosition('${pos.ticket}')">平仓</button>
                    </div>
                </div>
            </div>
        `;
    });

    container.innerHTML = html;
}

function saveStrategyConfig() {
    const config = {
        aiStrategy: document.getElementById('aiStrategySelect').value,
        symbol: document.getElementById('symbolSelect').value,
        lotSize: document.getElementById('lotSizeSelect').value,
        timeframe: document.getElementById('timeframeSelect').value,
        tradeLimit: document.getElementById('tradeLimitSelect').value,
        stopLoss: document.getElementById('stopLossPercent').value,
        takeProfit: document.getElementById('takeProfitPercent').value
    };

    localStorage.setItem('strategyTradingConfig', JSON.stringify(config));
    addTradingLog('配置已保存', 'success');
}

function loadStrategyConfig() {
    const savedConfig = localStorage.getItem('strategyTradingConfig');
    if (savedConfig) {
        const config = JSON.parse(savedConfig);

        document.getElementById('aiStrategySelect').value = config.aiStrategy || '';
        document.getElementById('symbolSelect').value = config.symbol || 'XAUUSD';
        document.getElementById('lotSizeSelect').value = config.lotSize || '0.01';
        document.getElementById('timeframeSelect').value = config.timeframe || '';
        document.getElementById('tradeLimitSelect').value = config.tradeLimit || '30';
        document.getElementById('stopLossPercent').value = config.stopLoss || '1.2';
        document.getElementById('takeProfitPercent').value = config.takeProfit || '2.4';

        addTradingLog('配置已加载', 'success');
    }
}

function startStrategyTrading() {
    // 验证配置
    const aiStrategy = document.getElementById('aiStrategySelect').value;
    if (!aiStrategy) {
        alert('请选择AI策略模型');
        return;
    }

    // 获取并验证策略配置
    const strategyConfig = getCurrentStrategyConfig();
    const configValidation = validateStrategyConfig(strategyConfig);

    if (!configValidation.valid) {
        alert('策略配置错误:\n' + configValidation.errors.join('\n'));
        return;
    }

    const config = {
        aiStrategy: aiStrategy,
        symbol: document.getElementById('symbolSelect').value,
        lotSize: parseFloat(document.getElementById('lotSizeSelect').value),
        timeframe: document.getElementById('timeframeSelect').value,
        tradeLimit: document.getElementById('tradeLimitSelect').value,
        stopLoss: parseFloat(document.getElementById('stopLossPercent').value),
        takeProfit: parseFloat(document.getElementById('takeProfitPercent').value),
        strategyConfig: strategyConfig  // 添加策略配置
    };

    console.log('🚀 启动策略交易，配置:', config);

    // 启动策略交易
    fetch('/api/strategy-trading/start', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(config)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateSystemStatus('running', '策略交易运行中');
            document.getElementById('startTradingBtn').style.display = 'none';
            document.getElementById('stopTradingBtn').style.display = 'inline-block';
            addTradingLog('策略交易已启动', 'success');
        } else {
            addTradingLog('启动失败: ' + data.error, 'error');
        }
    })
    .catch(error => {
        console.error('启动策略交易失败:', error);
        addTradingLog('启动异常: ' + error.message, 'error');
    });
}

function stopStrategyTrading() {
    fetch('/api/strategy-trading/stop', {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateSystemStatus('stopped', '策略交易已停止');
            document.getElementById('startTradingBtn').style.display = 'inline-block';
            document.getElementById('stopTradingBtn').style.display = 'none';
            addTradingLog('策略交易已停止', 'warning');
        } else {
            addTradingLog('停止失败: ' + data.error, 'error');
        }
    })
    .catch(error => {
        console.error('停止策略交易失败:', error);
        addTradingLog('停止异常: ' + error.message, 'error');
    });
}

function updateSystemStatus(status, text) {
    const statusIndicator = document.getElementById('systemStatus');
    const statusDot = statusIndicator.querySelector('.status-dot');
    const statusText = statusIndicator.querySelector('.status-text');

    statusDot.className = 'status-dot';

    switch(status) {
        case 'running':
            statusDot.classList.add('bg-success');
            break;
        case 'stopped':
            statusDot.classList.add('bg-danger');
            break;
        case 'paused':
            statusDot.classList.add('bg-warning');
            break;
        default:
            statusDot.classList.add('bg-secondary');
    }

    statusText.textContent = text;
}

function checkTradingStatus() {
    fetch('/api/strategy-trading/status')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                if (data.status === 'running') {
                    updateSystemStatus('running', '策略交易运行中');
                    document.getElementById('startTradingBtn').style.display = 'none';
                    document.getElementById('stopTradingBtn').style.display = 'inline-block';
                } else {
                    updateSystemStatus('stopped', '策略交易已停止');
                    document.getElementById('startTradingBtn').style.display = 'inline-block';
                    document.getElementById('stopTradingBtn').style.display = 'none';
                }
            }
        })
        .catch(error => {
            console.error('检查交易状态失败:', error);
        });
}

function updateTradingStats() {
    fetch('/api/strategy-trading/stats')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('todayTrades').textContent = data.todayTrades || 0;
                document.getElementById('totalPnL').textContent = `$${(data.totalPnL || 0).toFixed(2)}`;
            }
        })
        .catch(error => {
            console.error('更新交易统计失败:', error);
        });
}

function closeStrategyPosition(ticket) {
    if (confirm('确定要平仓这个持仓吗？')) {
        fetch('/api/strategy-trading/close-position', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ ticket: ticket })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                addTradingLog(`持仓 ${ticket} 已平仓`, 'success');
                loadStrategyPositions();
            } else {
                addTradingLog(`平仓失败: ${data.error}`, 'error');
            }
        })
        .catch(error => {
            console.error('平仓失败:', error);
            addTradingLog(`平仓异常: ${error.message}`, 'error');
        });
    }
}

function closeAllStrategyPositions() {
    if (confirm('确定要一键平仓所有持仓吗？')) {
        fetch('/api/strategy-trading/close-all-positions', {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                addTradingLog(`已平仓 ${data.closedCount} 个持仓`, 'success');
                loadStrategyPositions();
            } else {
                addTradingLog(`一键平仓失败: ${data.error}`, 'error');
            }
        })
        .catch(error => {
            console.error('一键平仓失败:', error);
            addTradingLog(`一键平仓异常: ${error.message}`, 'error');
        });
    }
}

function addTradingLog(message, type = 'info') {
    const container = document.getElementById('tradingLogContainer');
    const timestamp = new Date().toLocaleTimeString();

    const logItem = document.createElement('div');
    logItem.className = `trading-log-item ${type}`;
    logItem.innerHTML = `
        <div class="d-flex justify-content-between align-items-start">
            <div>
                <small class="text-muted">${timestamp}</small>
                <div>${message}</div>
            </div>
            <i class="fas fa-${type === 'success' ? 'check-circle text-success' :
                              type === 'error' ? 'exclamation-circle text-danger' :
                              type === 'warning' ? 'exclamation-triangle text-warning' :
                              'info-circle text-info'}"></i>
        </div>
    `;

    // 如果容器为空或只有占位符，清空它
    if (container.querySelector('.text-center')) {
        container.innerHTML = '';
    }

    container.insertBefore(logItem, container.firstChild);

    // 限制日志数量
    const logs = container.querySelectorAll('.trading-log-item');
    if (logs.length > 50) {
        logs[logs.length - 1].remove();
    }
}

// ==================== 策略配置管理 ====================

// 策略配置默认值
const strategyConfigs = {
    trend_following: {
        stop_loss_percent: 0.8,
        take_profit_percent: 1.6,
        max_positions: 3,
        signal_threshold: 0.7,
        description: '趋势跟踪策略 - 适合趋势明显的市场，通过跟随主要趋势方向进行交易，具有较高的胜率和稳定的收益。'
    },
    mean_reversion: {
        stop_loss_percent: 0.6,
        take_profit_percent: 1.2,
        max_positions: 5,
        signal_threshold: 0.75,
        description: '均值回归策略 - 适合震荡市场，基于价格回归均值的理论，在价格偏离均值时进行反向交易。'
    },
    breakout: {
        stop_loss_percent: 1.0,
        take_profit_percent: 2.0,
        max_positions: 2,
        signal_threshold: 0.8,
        description: '突破策略 - 适合波动较大的市场，在价格突破关键阻力或支撑位时进行交易，追求较大的利润空间。'
    },
    high_frequency: {
        stop_loss_percent: 0.4,
        take_profit_percent: 0.8,
        max_positions: 8,
        signal_threshold: 0.6,
        description: '高频策略 - 适合短期交易，通过快速进出场获取小幅利润，交易频率较高但单次风险较小。盈利效果较好，适合活跃的市场环境。'
    },
    conservative: {
        stop_loss_percent: 0.5,
        take_profit_percent: 1.0,
        max_positions: 2,
        signal_threshold: 0.85,
        description: '保守策略 - 风险较低，采用严格的风险控制和较高的信号阈值，适合风险承受能力较低的投资者。'
    },
    aggressive: {
        stop_loss_percent: 1.5,
        take_profit_percent: 3.0,
        max_positions: 5,
        signal_threshold: 0.65,
        description: '激进策略 - 高风险高收益，采用较大的止损止盈比例和较低的信号阈值，适合风险承受能力较强的投资者。'
    }
};

// 更新策略配置显示
function updateStrategyConfigDisplay() {
    const configType = document.getElementById('strategyConfigType').value;
    const configDetails = document.getElementById('strategyConfigDetails');
    const configDescription = document.getElementById('strategyConfigDescription');

    if (configType === 'auto') {
        configDetails.style.display = 'none';
        configDescription.textContent = '自动选择 - 系统将根据市场环境和AI策略模型自动选择最适合的策略配置';
        return;
    }

    if (configType === 'custom') {
        configDetails.style.display = 'block';
        configDescription.textContent = '自定义配置 - 您可以根据自己的交易经验和风险偏好自定义策略参数';
        return;
    }

    // 显示配置详情
    configDetails.style.display = 'block';

    // 更新配置值
    if (strategyConfigs[configType]) {
        const config = strategyConfigs[configType];
        document.getElementById('strategyStopLoss').value = config.stop_loss_percent;
        document.getElementById('strategyTakeProfit').value = config.take_profit_percent;
        document.getElementById('strategyMaxPositions').value = config.max_positions;
        document.getElementById('strategySignalThreshold').value = config.signal_threshold;
        configDescription.textContent = config.description;

        console.log(`📊 策略配置已更新: ${configType}`, config);
    }
}

// 获取当前策略配置
function getCurrentStrategyConfig() {
    const configType = document.getElementById('strategyConfigType').value;

    if (configType === 'auto') {
        return { type: 'auto' };
    }

    const config = {
        type: configType,
        stop_loss_percent: parseFloat(document.getElementById('strategyStopLoss').value),
        take_profit_percent: parseFloat(document.getElementById('strategyTakeProfit').value),
        max_positions: parseInt(document.getElementById('strategyMaxPositions').value),
        signal_threshold: parseFloat(document.getElementById('strategySignalThreshold').value)
    };

    return config;
}

// 验证策略配置
function validateStrategyConfig(config) {
    if (config.type === 'auto') {
        return { valid: true };
    }

    const errors = [];

    if (config.stop_loss_percent < 0.1 || config.stop_loss_percent > 5.0) {
        errors.push('止损比例应在0.1%到5.0%之间');
    }

    if (config.take_profit_percent < 0.2 || config.take_profit_percent > 10.0) {
        errors.push('止盈比例应在0.2%到10.0%之间');
    }

    if (config.max_positions < 1 || config.max_positions > 10) {
        errors.push('最大持仓数应在1到10之间');
    }

    if (config.signal_threshold < 0.5 || config.signal_threshold > 1.0) {
        errors.push('信号阈值应在0.5到1.0之间');
    }

    return {
        valid: errors.length === 0,
        errors: errors
    };
}

function refreshPage() {
    location.reload();
}
</script>
{% endblock %}
