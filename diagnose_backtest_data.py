#!/usr/bin/env python3
"""
诊断交易回测数据获取问题
"""

import requests
import json
import sqlite3
from datetime import datetime, timed<PERSON>ta

def test_mt5_connection():
    """测试MT5连接"""
    print("🔍 测试MT5连接状态")
    print("=" * 60)
    
    try:
        session = requests.Session()
        login_data = {'username': 'admin', 'password': 'admin123'}
        session.post('http://127.0.0.1:5000/login', data=login_data)
        
        # 测试MT5连接状态
        response = session.get('http://127.0.0.1:5000/api/mt5/connection-status')
        
        if response.status_code == 200:
            result = response.json()
            print("MT5连接状态:", result.get('connected', False))
            print("MT5版本:", result.get('version', 'N/A'))
            print("账户信息:", result.get('account_info', {}))
            return result.get('connected', False)
        else:
            print("❌ 无法获取MT5连接状态")
            return False
            
    except Exception as e:
        print(f"❌ MT5连接测试失败: {e}")
        return False

def test_historical_data_direct():
    """直接测试历史数据获取"""
    print(f"\n📊 直接测试历史数据获取")
    print("=" * 60)
    
    try:
        session = requests.Session()
        login_data = {'username': 'admin', 'password': 'admin123'}
        session.post('http://127.0.0.1:5000/login', data=login_data)
        
        # 测试获取XAUUSD历史数据
        symbols = ['XAUUSD', 'EURUSD', 'GBPUSD']
        timeframes = ['H1', 'M15', 'D1']
        
        for symbol in symbols:
            for timeframe in timeframes:
                print(f"\n测试 {symbol} {timeframe}:")
                
                response = session.get(f'http://127.0.0.1:5000/api/mt5/historical-data/{symbol}/{timeframe}?count=100')
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get('success'):
                        data = result.get('data', [])
                        print(f"  ✅ 成功获取 {len(data)} 条数据")
                        
                        if len(data) > 0:
                            latest = data[-1]
                            print(f"  最新数据: 时间={latest.get('time')}, 收盘={latest.get('close')}")
                        else:
                            print(f"  ⚠️ 数据为空")
                    else:
                        print(f"  ❌ 获取失败: {result.get('error')}")
                else:
                    print(f"  ❌ HTTP错误: {response.status_code}")
                    
                # 只测试第一个组合，避免过多请求
                if symbol == 'XAUUSD' and timeframe == 'H1':
                    return len(data) if 'data' in locals() and data else 0
        
        return 0
        
    except Exception as e:
        print(f"❌ 历史数据测试失败: {e}")
        return 0

def test_deep_learning_data_fetch():
    """测试深度学习服务的数据获取"""
    print(f"\n🧠 测试深度学习服务数据获取")
    print("=" * 60)
    
    try:
        session = requests.Session()
        login_data = {'username': 'admin', 'password': 'admin123'}
        session.post('http://127.0.0.1:5000/login', data=login_data)
        
        # 获取可用模型
        response = session.get('http://127.0.0.1:5000/api/deep-learning/models')
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                models = result.get('models', [])
                completed_models = [m for m in models if m.get('status') == 'completed']
                
                print(f"总模型数: {len(models)}")
                print(f"完成训练的模型: {len(completed_models)}")
                
                if completed_models:
                    test_model = completed_models[0]
                    print(f"测试模型: {test_model['name']} ({test_model['symbol']} {test_model['timeframe']})")
                    
                    # 测试推理数据获取
                    inference_data = {
                        'model_id': test_model['id'],
                        'symbol': test_model['symbol'],
                        'timeframe': test_model['timeframe'],
                        'data_points': 100
                    }
                    
                    print(f"测试推理数据获取...")
                    response = session.post('http://127.0.0.1:5000/api/deep-learning/get-inference-data', json=inference_data)
                    
                    if response.status_code == 200:
                        result = response.json()
                        if result.get('success'):
                            data = result.get('data', [])
                            print(f"✅ 推理数据获取成功: {len(data)} 条")
                            return len(data)
                        else:
                            print(f"❌ 推理数据获取失败: {result.get('error')}")
                    else:
                        print(f"❌ 推理数据请求失败: {response.status_code}")
                else:
                    print("❌ 没有可用的训练完成模型")
            else:
                print(f"❌ 获取模型列表失败: {result.get('error')}")
        else:
            print(f"❌ 模型列表请求失败: {response.status_code}")
        
        return 0
        
    except Exception as e:
        print(f"❌ 深度学习数据测试失败: {e}")
        return 0

def test_backtest_execution():
    """测试回测执行"""
    print(f"\n🔄 测试回测执行")
    print("=" * 60)
    
    try:
        session = requests.Session()
        login_data = {'username': 'admin', 'password': 'admin123'}
        session.post('http://127.0.0.1:5000/login', data=login_data)
        
        # 获取可用模型
        response = session.get('http://127.0.0.1:5000/api/deep-learning/models')
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                models = result.get('models', [])
                completed_models = [m for m in models if m.get('status') == 'completed']
                
                if completed_models:
                    test_model = completed_models[0]
                    print(f"使用模型: {test_model['name']}")
                    
                    # 执行回测
                    backtest_data = {
                        'model_id': test_model['id'],
                        'symbol': test_model['symbol'],
                        'timeframe': test_model['timeframe'],
                        'start_date': '2024-07-01',
                        'end_date': '2024-07-29',
                        'initial_balance': 10000,
                        'lot_size': 0.01,
                        'stop_loss_pips': 50,
                        'take_profit_pips': 100,
                        'min_confidence': 0.5
                    }
                    
                    print(f"执行回测...")
                    response = session.post('http://127.0.0.1:5000/api/deep-learning/inference-backtest', json=backtest_data)
                    
                    if response.status_code == 200:
                        result = response.json()
                        if result.get('success'):
                            stats = result.get('statistics', {})
                            trades = result.get('trades', [])
                            
                            print(f"✅ 回测成功!")
                            print(f"总收益: {stats.get('total_return', 0):.2f}%")
                            print(f"总交易: {stats.get('total_trades', 0)}")
                            print(f"交易记录: {len(trades)} 笔")
                            
                            return True
                        else:
                            error_msg = result.get('error', '未知错误')
                            print(f"❌ 回测失败: {error_msg}")
                            
                            # 检查是否是数据问题
                            if '数据不足' in error_msg or '跳过预测' in error_msg:
                                print("🔍 这是数据获取问题!")
                                return False
                    else:
                        print(f"❌ 回测请求失败: {response.status_code}")
                else:
                    print("❌ 没有可用的训练完成模型")
        
        return False
        
    except Exception as e:
        print(f"❌ 回测测试失败: {e}")
        return False

def check_data_sources():
    """检查数据源配置"""
    print(f"\n🔧 检查数据源配置")
    print("=" * 60)
    
    try:
        # 检查MT5服务是否正确初始化
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        from services.mt5_service import mt5_service
        
        print("MT5服务状态:")
        print(f"  初始化状态: {mt5_service.is_initialized if hasattr(mt5_service, 'is_initialized') else '未知'}")
        print(f"  连接状态: {mt5_service.is_connected() if hasattr(mt5_service, 'is_connected') else '未知'}")
        
        # 测试直接获取数据
        if hasattr(mt5_service, 'get_historical_data'):
            print("测试直接数据获取:")
            data = mt5_service.get_historical_data('XAUUSD', 'H1', 10)
            print(f"  直接获取结果: {len(data) if data else 0} 条数据")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据源检查失败: {e}")
        return False

def main():
    """主诊断函数"""
    print("🔍 交易回测数据获取问题诊断")
    print("=" * 80)
    
    # 1. 测试MT5连接
    mt5_connected = test_mt5_connection()
    
    # 2. 测试历史数据获取
    historical_data_count = test_historical_data_direct()
    
    # 3. 测试深度学习数据获取
    inference_data_count = test_deep_learning_data_fetch()
    
    # 4. 测试回测执行
    backtest_success = test_backtest_execution()
    
    # 5. 检查数据源配置
    data_source_ok = check_data_sources()
    
    # 综合分析
    print(f"\n📊 诊断结果汇总")
    print("=" * 80)
    
    print(f"MT5连接: {'✅ 正常' if mt5_connected else '❌ 异常'}")
    print(f"历史数据: {'✅ 正常' if historical_data_count > 0 else '❌ 异常'} ({historical_data_count} 条)")
    print(f"推理数据: {'✅ 正常' if inference_data_count > 0 else '❌ 异常'} ({inference_data_count} 条)")
    print(f"回测执行: {'✅ 正常' if backtest_success else '❌ 异常'}")
    print(f"数据源配置: {'✅ 正常' if data_source_ok else '❌ 异常'}")
    
    # 问题分析和建议
    print(f"\n🔧 问题分析和解决建议")
    print("=" * 80)
    
    if not mt5_connected:
        print("🔴 MT5连接问题:")
        print("  • 检查MT5是否正在运行")
        print("  • 确认MT5账户登录状态")
        print("  • 检查MT5 API权限设置")
        print("  • 重启MT5终端")
    
    if historical_data_count == 0:
        print("🔴 历史数据获取问题:")
        print("  • MT5数据源可能没有XAUUSD数据")
        print("  • 检查MT5市场观察窗口中是否有相关品种")
        print("  • 尝试手动在MT5中查看XAUUSD图表")
        print("  • 考虑使用其他数据源作为备选")
    
    if inference_data_count == 0:
        print("🔴 推理数据获取问题:")
        print("  • 深度学习服务数据获取逻辑可能有问题")
        print("  • 检查数据预处理流程")
        print("  • 验证数据格式转换")
    
    if not backtest_success:
        print("🔴 回测执行问题:")
        print("  • 这是导致'数据严重不足'错误的直接原因")
        print("  • 需要修复上述数据获取问题")
        print("  • 检查回测逻辑中的数据验证")
    
    # 提供解决方案
    print(f"\n💡 立即解决方案")
    print("=" * 80)
    
    if not mt5_connected or historical_data_count == 0:
        print("1. 检查MT5连接和数据:")
        print("   - 确保MT5正在运行并已登录")
        print("   - 在MT5中手动打开XAUUSD图表")
        print("   - 检查网络连接和数据订阅")
        
    print("2. 修复数据获取逻辑:")
    print("   - 增加数据获取的重试机制")
    print("   - 添加备选数据源")
    print("   - 改进错误处理和日志记录")
    
    print("3. 优化回测参数:")
    print("   - 使用更长的时间范围")
    print("   - 降低最小置信度阈值")
    print("   - 增加数据点数量")

if __name__ == '__main__':
    main()
