#!/usr/bin/env python3
"""
深度学习训练进度卡住问题的最终修复方案
"""

import sqlite3
import json
import time
import requests
from datetime import datetime
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def login_session():
    """登录并获取会话"""
    session = requests.Session()
    
    try:
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post('http://127.0.0.1:5000/login', data=login_data)
        
        if response.status_code == 200 and 'dashboard' in response.url:
            print("✅ 登录成功")
            return session
        else:
            print(f"❌ 登录失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return None

def stop_all_stuck_training():
    """停止所有卡住的训练任务"""
    print("\n🛑 停止所有卡住的训练任务")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 查找所有运行中的训练任务
        cursor.execute("""
            SELECT id, model_id, status, progress, current_epoch, total_epochs,
                   (julianday('now') - julianday(updated_at)) * 24 * 60 as minutes_since_update
            FROM training_tasks 
            WHERE status = 'running'
            ORDER BY created_at DESC 
        """)
        
        running_tasks = cursor.fetchall()
        
        if not running_tasks:
            print("✅ 没有运行中的训练任务")
            conn.close()
            return True
        
        print(f"📋 发现 {len(running_tasks)} 个运行中的训练任务")
        
        # 停止所有运行中的任务
        cursor.execute("""
            UPDATE training_tasks 
            SET status = 'stopped', updated_at = ?
            WHERE status = 'running'
        """, (datetime.now().isoformat(),))
        
        affected_rows = cursor.rowcount
        conn.commit()
        conn.close()
        
        print(f"✅ 停止了 {affected_rows} 个训练任务")
        
        # 清理训练控制状态
        try:
            from services.deep_learning_service import DeepLearningService
            dl_service = DeepLearningService()
            
            if hasattr(dl_service, 'training_control'):
                old_count = len(dl_service.training_control)
                dl_service.training_control.clear()
                print(f"✅ 清理了 {old_count} 个训练控制状态")
        except Exception as e:
            print(f"⚠️ 清理训练控制状态失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 停止训练失败: {e}")
        return False

def start_test_training():
    """启动测试训练"""
    print("\n🚀 启动测试训练")
    print("=" * 50)
    
    session = login_session()
    if not session:
        return False
    
    # 使用正确的参数格式
    training_config = {
        'model_name': 'test_model_' + datetime.now().strftime('%Y%m%d_%H%M%S'),
        'model_type': 'LSTM',  # 添加必要的模型类型
        'symbol': 'XAUUSD',
        'timeframe': 'H1',
        'days': 7,  # 只用7天数据
        'epochs': 5,  # 只训练5轮
        'batch_size': 8,  # 很小的批次
        'learning_rate': 0.001,
        'sequence_length': 10,  # 很短的序列
        'hidden_size': 32,  # 很小的隐藏层
        'num_layers': 1,  # 只有1层
        'dropout': 0.1,
        'early_stopping': True,
        'patience': 3,
        'validation_split': 0.2,
        'use_gpu': True
    }
    
    print("📊 测试训练配置:")
    for key, value in training_config.items():
        print(f"   {key}: {value}")
    
    try:
        response = session.post(
            'http://127.0.0.1:5000/api/deep-learning/start-training',
            json=training_config,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                task_id = result.get('task_id')
                print(f"✅ 测试训练启动成功!")
                print(f"   任务ID: {task_id}")
                
                return monitor_training_progress(session, task_id)
                
            else:
                print(f"❌ 测试训练启动失败: {result.get('error')}")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text[:200]}...")
            return False
            
    except Exception as e:
        print(f"❌ 启动测试训练失败: {e}")
        return False

def monitor_training_progress(session, task_id, duration=120):
    """监控训练进度"""
    print(f"\n📊 监控训练进度 (任务: {task_id[:8]}..., 时长: {duration}秒)")
    print("=" * 60)
    
    progress_updates = 0
    last_progress = -1
    last_epoch = -1
    stuck_count = 0
    max_stuck_count = 6  # 连续6次无变化认为卡住
    
    for i in range(duration // 10):  # 每10秒检查一次
        try:
            response = session.get(f'http://127.0.0.1:5000/api/deep-learning/training-progress/{task_id}')
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get('success'):
                    progress_data = result['progress']
                    progress = progress_data.get('progress', 0)
                    epoch = progress_data.get('epoch', 0)
                    status = progress_data.get('status', 'unknown')
                    train_loss = progress_data.get('train_loss', 0)
                    val_loss = progress_data.get('val_loss', 0)
                    
                    print(f"[{(i+1)*10:3d}s] 进度: {progress:5.1f}%, 轮次: {epoch:2d}, 状态: {status:10s}, 训练损失: {train_loss:.4f}, 验证损失: {val_loss:.4f}")
                    
                    # 检查进度更新
                    if progress != last_progress or epoch != last_epoch:
                        progress_updates += 1
                        stuck_count = 0
                        print(f"        ✅ 进度更新 (总更新: {progress_updates})")
                    else:
                        stuck_count += 1
                        if stuck_count >= max_stuck_count:
                            print(f"        ⚠️ 进度卡住 (连续{stuck_count}次无变化)")
                    
                    last_progress = progress
                    last_epoch = epoch
                    
                    # 如果训练完成或失败
                    if status in ['completed', 'failed', 'stopped']:
                        print(f"        🏁 训练结束: {status}")
                        break
                        
                    # 如果进度超过50%，认为训练正常
                    if progress > 50:
                        print(f"        ✅ 训练进度正常，超过50%")
                        break
                        
                else:
                    print(f"[{(i+1)*10:3d}s] ❌ API错误: {result.get('error')}")
            else:
                print(f"[{(i+1)*10:3d}s] ❌ HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"[{(i+1)*10:3d}s] ❌ 监控异常: {e}")
        
        time.sleep(10)
    
    print(f"\n📈 训练监控结果:")
    print(f"   总进度更新次数: {progress_updates}")
    print(f"   最终进度: {last_progress}%")
    print(f"   最终轮次: {last_epoch}")
    
    # 判断训练是否正常
    if progress_updates >= 3 and last_progress > 25:
        print(f"   ✅ 训练进度正常更新，问题已修复")
        return True
    elif progress_updates >= 1 and last_progress > last_progress:
        print(f"   ⚠️ 训练有进度但较慢，可能需要优化配置")
        return True
    else:
        print(f"   ❌ 训练进度仍然卡住")
        return False

def main():
    print("🔧 深度学习训练进度卡住问题 - 最终修复")
    print("=" * 60)
    
    # 步骤1: 停止所有卡住的训练
    if not stop_all_stuck_training():
        print("❌ 停止卡住训练失败")
        return
    
    # 步骤2: 等待系统稳定
    print("\n⏳ 等待系统稳定...")
    time.sleep(5)
    
    # 步骤3: 启动测试训练
    if start_test_training():
        print("\n🎉 修复成功！深度学习训练功能已恢复正常")
        print("\n💡 使用建议:")
        print("1. 使用较小的数据集开始训练 (7-30天)")
        print("2. 使用较小的模型配置 (hidden_size: 32-64, num_layers: 1-2)")
        print("3. 使用较小的批次大小 (batch_size: 8-16)")
        print("4. 监控GPU内存使用情况")
        print("5. 如果训练时间过长，启用早停机制")
        
        print("\n🔧 如果问题再次出现:")
        print("1. 检查MT5连接状态")
        print("2. 重启应用程序")
        print("3. 清理GPU内存缓存")
        print("4. 考虑使用CPU模式训练")
    else:
        print("\n❌ 修复失败，训练进度仍然卡住")
        print("\n🔧 进一步修复建议:")
        print("1. 重启应用程序: python app.py")
        print("2. 检查MT5连接和数据权限")
        print("3. 检查GPU驱动和CUDA环境")
        print("4. 查看应用程序日志获取详细错误信息")
        print("5. 考虑联系技术支持")

if __name__ == '__main__':
    main()
