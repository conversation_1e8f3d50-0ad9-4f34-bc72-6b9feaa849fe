{% extends "base.html" %}

{% block title %}深度学习仪表板{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0 text-gray-800">
                    <i class="fas fa-brain text-primary me-2"></i>
                    深度学习仪表板
                </h1>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-primary" onclick="refreshDashboard()">
                        <i class="fas fa-sync-alt me-1"></i>刷新
                    </button>
                    <button class="btn btn-primary" onclick="location.href='{{ url_for('model_training') }}'">
                        <i class="fas fa-plus me-1"></i>新建训练
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- GPU状态卡片 -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                GPU状态
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="gpuStatus">
                                <i class="fas fa-spinner fa-spin"></i> 检查中...
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-microchip fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                已训练模型
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="trainedModels">
                                <i class="fas fa-spinner fa-spin"></i> 加载中...
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-database fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                训练中任务
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="trainingTasks">
                                <i class="fas fa-spinner fa-spin"></i> 加载中...
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dumbbell fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                GPU内存使用
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="gpuMemory">
                                <i class="fas fa-spinner fa-spin"></i> 检查中...
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-memory fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="row">
        <!-- 最近训练任务 -->
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-history me-2"></i>最近训练任务
                    </h6>
                    <a href="{{ url_for('model_training') }}" class="btn btn-sm btn-primary">
                        <i class="fas fa-plus me-1"></i>新建训练
                    </a>
                </div>
                <div class="card-body">
                    <div id="recentTrainingTasks">
                        <div class="text-center py-4">
                            <i class="fas fa-spinner fa-spin fa-2x text-muted mb-3"></i>
                            <p class="text-muted">加载训练任务...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 系统信息 -->
        <div class="col-xl-4 col-lg-5">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle me-2"></i>系统信息
                    </h6>
                </div>
                <div class="card-body">
                    <div id="systemInfo">
                        <div class="text-center py-4">
                            <i class="fas fa-spinner fa-spin fa-2x text-muted mb-3"></i>
                            <p class="text-muted">加载系统信息...</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 快速操作 -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-bolt me-2"></i>快速操作
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('model_training') }}" class="btn btn-primary">
                            <i class="fas fa-dumbbell me-2"></i>开始训练模型
                        </a>
                        <a href="{{ url_for('model_management') }}" class="btn btn-success">
                            <i class="fas fa-database me-2"></i>管理现有模型
                        </a>
                        <a href="{{ url_for('model_inference') }}" class="btn btn-info">
                            <i class="fas fa-magic me-2"></i>模型推理测试
                        </a>
                        <a href="{{ url_for('gpu_monitor') }}" class="btn btn-warning">
                            <i class="fas fa-microchip me-2"></i>GPU性能监控
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- GPU监控图表 -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-line me-2"></i>GPU使用率监控
                    </h6>
                </div>
                <div class="card-body">
                    <canvas id="gpuUsageChart" width="400" height="100"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
let gpuChart;
let gpuData = {
    labels: [],
    datasets: [{
        label: 'GPU使用率 (%)',
        data: [],
        borderColor: 'rgb(75, 192, 192)',
        backgroundColor: 'rgba(75, 192, 192, 0.2)',
        tension: 0.1
    }, {
        label: 'GPU内存使用率 (%)',
        data: [],
        borderColor: 'rgb(255, 99, 132)',
        backgroundColor: 'rgba(255, 99, 132, 0.2)',
        tension: 0.1
    }]
};

// 初始化GPU监控图表
function initGPUChart() {
    const ctx = document.getElementById('gpuUsageChart').getContext('2d');
    gpuChart = new Chart(ctx, {
        type: 'line',
        data: gpuData,
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100
                }
            },
            plugins: {
                legend: {
                    display: true
                }
            }
        }
    });
}

// 加载仪表板数据
async function loadDashboardData() {
    try {
        // 加载GPU状态
        console.log('正在加载GPU状态...');
        const gpuResponse = await fetch('/api/deep-learning/gpu-status');
        console.log('GPU API响应状态:', gpuResponse.status);

        if (!gpuResponse.ok) {
            throw new Error(`HTTP ${gpuResponse.status}: ${gpuResponse.statusText}`);
        }

        const gpuData = await gpuResponse.json();
        console.log('GPU API响应数据:', gpuData);

        if (gpuData.success) {
            updateGPUStatus(gpuData.gpu_status);
        } else {
            // 显示错误信息
            const gpuStatusElement = document.getElementById('gpuStatus');
            gpuStatusElement.innerHTML = `<span class="text-danger"><i class="fas fa-exclamation-circle"></i> 检查GPU状态失败</span>`;
            console.error('GPU状态检查失败:', gpuData.error);
        }
        
        // 加载训练任务
        const tasksResponse = await fetch('/api/deep-learning/training-tasks');
        const tasksData = await tasksResponse.json();
        
        if (tasksData.success) {
            updateTrainingTasks(tasksData.tasks);
        }
        
        // 加载系统信息
        const systemResponse = await fetch('/api/deep-learning/system-info');
        const systemData = await systemResponse.json();
        
        if (systemData.success) {
            updateSystemInfo(systemData.info);
        }
        
    } catch (error) {
        console.error('加载仪表板数据失败:', error);
        // 显示详细错误信息
        const gpuStatusElement = document.getElementById('gpuStatus');
        if (gpuStatusElement) {
            gpuStatusElement.innerHTML = `<span class="text-danger"><i class="fas fa-exclamation-circle"></i> 检查GPU状态失败<br><small>${error.message || '请检查网络连接'}</small></span>`;
        }
    }
}

// 更新GPU状态显示
function updateGPUStatus(status) {
    const gpuStatusElement = document.getElementById('gpuStatus');
    const gpuMemoryElement = document.getElementById('gpuMemory');
    
    if (status.gpu_available) {
        gpuStatusElement.innerHTML = `<span class="text-success"><i class="fas fa-check-circle"></i> ${status.gpu_name}</span>`;
        gpuMemoryElement.innerHTML = `${status.memory_used.toFixed(1)}GB / ${status.memory_total.toFixed(1)}GB`;
    } else {
        gpuStatusElement.innerHTML = `<span class="text-warning"><i class="fas fa-exclamation-triangle"></i> CPU模式</span>`;
        gpuMemoryElement.innerHTML = `<span class="text-muted">N/A</span>`;
    }
}

// 更新训练任务显示
function updateTrainingTasks(tasks) {
    const trainedModelsElement = document.getElementById('trainedModels');
    const trainingTasksElement = document.getElementById('trainingTasks');
    const recentTasksElement = document.getElementById('recentTrainingTasks');
    
    const completedTasks = tasks.filter(t => t.status === 'completed');
    const runningTasks = tasks.filter(t => t.status === 'running');
    
    trainedModelsElement.textContent = completedTasks.length;
    trainingTasksElement.textContent = runningTasks.length;
    
    // 显示最近任务
    if (tasks.length > 0) {
        let tasksHtml = '';
        tasks.slice(0, 5).forEach(task => {
            const statusBadge = getStatusBadge(task.status);
            tasksHtml += `
                <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                    <div>
                        <h6 class="mb-1">${task.name}</h6>
                        <small class="text-muted">${task.model_type} | ${task.created_at}</small>
                    </div>
                    <div class="text-end">
                        ${statusBadge}
                        <br>
                        <small class="text-muted">${task.progress || 0}%</small>
                    </div>
                </div>
            `;
        });
        recentTasksElement.innerHTML = tasksHtml;
    } else {
        recentTasksElement.innerHTML = '<p class="text-muted text-center py-4">暂无训练任务</p>';
    }
}

// 更新系统信息显示
function updateSystemInfo(info) {
    const systemInfoElement = document.getElementById('systemInfo');
    
    let infoHtml = `
        <div class="row">
            <div class="col-6">
                <strong>Python版本:</strong><br>
                <span class="text-muted">${info.python_version}</span>
            </div>
            <div class="col-6">
                <strong>PyTorch版本:</strong><br>
                <span class="text-muted">${info.pytorch_version || 'N/A'}</span>
            </div>
        </div>
        <hr>
        <div class="row">
            <div class="col-6">
                <strong>CUDA版本:</strong><br>
                <span class="text-muted">${info.cuda_version || 'N/A'}</span>
            </div>
            <div class="col-6">
                <strong>可用内存:</strong><br>
                <span class="text-muted">${info.available_memory}GB</span>
            </div>
        </div>
    `;
    
    systemInfoElement.innerHTML = infoHtml;
}

// 获取状态徽章
function getStatusBadge(status) {
    const badges = {
        'running': '<span class="badge bg-primary">训练中</span>',
        'completed': '<span class="badge bg-success">已完成</span>',
        'failed': '<span class="badge bg-danger">失败</span>',
        'pending': '<span class="badge bg-warning">等待中</span>'
    };
    return badges[status] || '<span class="badge bg-secondary">未知</span>';
}

// 刷新仪表板
function refreshDashboard() {
    loadDashboardData();
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    initGPUChart();
    loadDashboardData();
    
    // 每30秒刷新一次数据
    setInterval(loadDashboardData, 30000);
});
</script>

<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

.text-xs {
    font-size: 0.7rem;
}

.shadow {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
}
</style>
{% endblock %}
